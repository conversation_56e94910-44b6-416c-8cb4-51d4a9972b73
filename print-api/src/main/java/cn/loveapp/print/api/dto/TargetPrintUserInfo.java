package cn.loveapp.print.api.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 打印用户信息实体
 *
 * <AUTHOR>
 * @Date 2024/9/20 11:02 AM
 */
@Data
public class TargetPrintUserInfo {

    /**
     * 目标用户商家id
     */
    @ApiModelProperty(value = "目标用户商家id")
    private String sellerId;

    /**
     * 目标用户Nick
     */
    @ApiModelProperty(value = "目标用户Nick")
    private String sellerNick;

    /**
     * 目标用户 targetId
     */
    @ApiModelProperty(value = "目标用户targetId")
    private String storeId;

    /**
     * 目标用户 appName
     */
    @ApiModelProperty(value = "目标用户appName")
    private String appName;

    /**
     * 目标用户mallName
     */
    @ApiModelProperty(value = "目标用户mallName")
    private String mallName;

}
