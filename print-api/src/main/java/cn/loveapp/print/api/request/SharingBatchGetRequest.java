package cn.loveapp.print.api.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * 面单共享获取
 *
 * <AUTHOR>
 * @Date 2023/9/28 2:02 PM
 */
@Data
@ApiModel
public class SharingBatchGetRequest {

    /**
     * 共享id
     */
    @ApiModelProperty(value = "共享id", required = true)
    @NotEmpty
    private List<String> shareIdList;

    /**
     * 共享状态
     */
    @ApiModelProperty(value = "共享状态")
    private List<String> statusList;

}
