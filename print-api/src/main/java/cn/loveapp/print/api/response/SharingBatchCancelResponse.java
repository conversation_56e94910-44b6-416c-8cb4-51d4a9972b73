package cn.loveapp.print.api.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * 面单共享关系取消response
 *
 * <AUTHOR>
 * @Date 2023/9/28 4:33 PM
 */
@Data
@ApiModel
public class SharingBatchCancelResponse {

    /**
     * 取消成功的面单
     */
    @ApiModelProperty("取消成功的面单")
    private List<String> successShareIdList;

    /**
     * 取消失败的面单
     */
    @ApiModelProperty("取消失败的面单")
    private List<String> errorShareIdList;


    public void setSuccess(String shareId) {
        if (shareId == null) {
            return;
        }

        if (successShareIdList == null) {
            successShareIdList = new ArrayList<>();
        }
        successShareIdList.add(shareId);
    }

    public void setError(String shareId) {
        if (shareId == null) {
            return;
        }

        if (errorShareIdList == null) {
            errorShareIdList = new ArrayList<>();
        }
        errorShareIdList.add(shareId);
    }

}
