package cn.loveapp.print.api.proto;

import lombok.Data;

import javax.validation.constraints.NotNull;
import io.protostuff.Tag;

/**
 * 面单分享关系变更消息传输对象
 *
 * <AUTHOR>
 * @Date 2024/9/27 9:57 AM
 */
@Data
public class ShareRelationChangeRequestProto {

    @NotNull
    @Tag(1)
    private String shareId;

    /**
     * 更新备注
     */
    @Tag(2)
    private String newShareMemo;

    /**
     * 更新业务员备注
     */
    @Tag(3)
    private String newSalesman;

}
