package cn.loveapp.print.api.service;

import cn.loveapp.print.api.request.AyCloudprintCmdRenderInnerRequest;
import cn.loveapp.print.api.response.AyCloudprintCmdRenderInnerResponse;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import cn.loveapp.common.web.CommonApiResponse;
import cn.loveapp.print.api.request.AyCloudprintBindPrinterInnerRequest;
import cn.loveapp.print.api.request.AyCloudprintGetVerifyCodeInnerRequest;
import cn.loveapp.print.api.request.AyCloudprintSendPrintTaskInnerRequest;
import cn.loveapp.print.api.response.AyCloudprintBindPrinterInnerResponse;
import cn.loveapp.print.api.response.AyCloudprintPublicResponse;
import cn.loveapp.common.constant.HttpMethodsConstants;

/**
 * @Author: zhongzijie
 * @Date: 2022/3/8 15:45
 * @Description: 云打印服务 内部接口
 */
@Api(tags = "云打印服务内部接口(RPC)", consumes = "application/json")
@FeignClient(name = "cloudPrint")
@RequestMapping("export/cloudPrint")
public interface CloudPrintApiService {

    @ApiOperation(value = "发送打印指令", httpMethod = HttpMethodsConstants.POST)
    @RequestMapping(value = "/cmd.render", method = {RequestMethod.POST})
    CommonApiResponse<AyCloudprintCmdRenderInnerResponse> cmdRender(@RequestBody @Validated AyCloudprintCmdRenderInnerRequest request);

    /**
     * 获取验证码
     *
     * @param request
     * @return
     */
    @ApiOperation(value = "获取验证码", httpMethod = HttpMethodsConstants.POST)
    @RequestMapping(value = "/verify.code.get", method = {RequestMethod.POST})
    CommonApiResponse<AyCloudprintPublicResponse> getVerifyCode(@RequestBody @Validated AyCloudprintGetVerifyCodeInnerRequest request);

    /**
     * 绑定打印机
     *
     * @param request
     * @return
     */
    @ApiOperation(value = "绑定打印机", httpMethod = HttpMethodsConstants.POST)
    @RequestMapping(value = "/printer.bind", method = {RequestMethod.POST})
    CommonApiResponse<AyCloudprintBindPrinterInnerResponse> bindPrinter(@RequestBody @Validated AyCloudprintBindPrinterInnerRequest request);

    /**
     * 发送打印任务
     *
     * @param request
     * @return
     */
    @ApiOperation(value = "发送打印任务", httpMethod = HttpMethodsConstants.POST)
    @RequestMapping(value = "/print.task.send", method = {RequestMethod.POST})
    CommonApiResponse<AyCloudprintPublicResponse> sendPrintTask(@RequestBody @Validated AyCloudprintSendPrintTaskInnerRequest request);
}
