package cn.loveapp.print.api.request;

import javax.validation.constraints.NotEmpty;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Author: zhong<PERSON><PERSON>e
 * @Date: 2022/3/8 16:33
 * @Description: 云打印 绑定打印机 请求实体类
 */
@Data
@ApiModel(value = "云打印绑定打印机请求实体")
public class AyCloudprintBindPrinterInnerRequest {

    /**
     * 云打印平台id
     */
    @ApiModelProperty(value = "云打印平台id", required = true)
    @NotEmpty
    private String printerPlatformId;

    /**
     * 小程序网关requestId
     */
    @ApiModelProperty(value = "小程序网关requestId")
    private String requestId;

    /**
     * 平台id
     */
    @ApiModelProperty(value = "平台id")
    private String storeId;

    /**
     * 应用名称
     */
    @ApiModelProperty(value = "应用名称")
    private String appName;

    /**
     * 卖家nick
     */
    @ApiModelProperty(value = "卖家nick")
    private String sellerNick;

    /**
     * 打印机id
     */
    @ApiModelProperty(value = "打印机id")
    private String printerId;

    /**
     * 验证码
     */
    @ApiModelProperty(value = "验证码")
    private String verifyCode;

}
