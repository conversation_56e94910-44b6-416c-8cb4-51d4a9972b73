package cn.loveapp.print.api.dto;

import cn.loveapp.common.utils.DateUtil;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 面单列表获取请求传输DTO
 *
 * <AUTHOR>
 * @Date 2024/9/19 3:31 PM
 */
@Data
public class MultiWaybillOperateLogListGetDTO {

    /**
     * 页
     */
    @NotNull
    protected Integer page = 1;

    /**
     * 每页数量
     */
    @NotNull
    protected Integer pageSize = 20;

    /**
     * 订单类型
     */
    protected Integer tradeType;

    /**
     * 面单是否被取消
     */
    protected Boolean isCancel;

    /**
     * 面单共享Id
     */
    protected List<String> shareIdList;

    /**
     * 订单号搜索
     */
    protected List<String> tidList;

    /**
     * 运单号
     */
    protected List<String> waybillCodeList;

    /**
     * 物流公司
     */
    protected String cpCode;

    /**
     * 物流公司
     */
    protected List<String> cpCodeList;

    /**
     * 面单平台
     */
    protected String provider;

    /**
     * 开始时间
     */
    protected LocalDateTime startTime;

    /**
     * 结束时间
     */
    protected LocalDateTime endTime;

    /**
     * 备注（模糊）
     */
    protected String shareMemo;

    /**
     * 查询身份 {@link cn.loveapp.print.common.constant.ElefaceLogUserRole}
     */
    protected Integer elefaceLogUserRole = 1;

    /**
     * 业务员
     */
    protected String salesman;

    /**
     * 面单账号id
     */
    protected String ownerSellerId;

    /**
     * 面单账号id列表
     */
    protected List<String> ownerSellerIdList;

    /**
     * 被分享（使用账号id）
     */
    protected List<String> targetSellerId;

    /**
     * 被分享（使用账号id列表）
     */
    protected List<String> targetSellerIdList;

    /**
     * 网点名称
     */
    protected String branchName;

    /**
     * 收件人姓名
     */
    private String receiverName;

    /**
     * 收件人手机号
     */
    private String receiverMobile;

    /**
     * 收件人地址（省市区详细地址）
     */
    protected String receiverAddress;

    /**
     * 面单平台
     */
    private String elefacePlatformId;

    /**
     * 面单模板名称
     */
    protected String elefaceTemplateName;

    /**
     * 操作人
     */
    protected String operatorName;

    /**
     * 是否需要统计
     */
    protected boolean needStatistics;

    /**
     * 需要统计已发货面单计数
     */
    protected boolean needCountSendGood;

    /**
     * 需要统计未发货面单计数
     */
    protected boolean needCountNotSendGood;

    /**
     * 需要统计已打印面单计数
     */
    protected boolean needCountPrinted;

    /**
     * 需要统计未打印面单计数
     */
    protected boolean needCountNotPrinted;

    /**
     * 需要统计已取消面单计数
     */
    protected boolean needCountCancel;

    /**
     * 是否不使用使用统计条件（为true时使用搜索条件）
     */
    protected Boolean isNotUseDefaultStatistic;

    /**
     * 是否已打印 true:已打印 false：未打印
     */
    protected Boolean isPrint;

    /**
     * 是否已发货 true:已发货 false：未发货
     */
    protected Boolean isSendGood;

    /**
     * 面单服务商
     */
    protected String elefaceProvider;

    public void setStartTime(String startTime) {
        this.startTime = DateUtil.parseString(startTime);
    }

    public void setEndTime(String endTime) {
        this.endTime = DateUtil.parseString(endTime);
    }

    public LocalDateTime getStartTime() {
        if (startTime == null) {
            return LocalDateTime.now().minusDays(90);
        }

        return startTime;
    }
}
