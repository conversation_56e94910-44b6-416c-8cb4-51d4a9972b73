package cn.loveapp.print.api.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import javax.validation.constraints.NotEmpty;

/***
 * <AUTHOR>
 * @Description 打印rpc接口基础请求实体
 * @Date 18:16 2023/9/5
 **/
@Data
@ApiModel(value = "打印rpc接口基础请求实体")
public class AyPrintBaseRequest {

    /**
     * 平台id
     */
    @ApiModelProperty(value = "平台id", required = true)
    @NotEmpty
    private String storeId;

    /**
     * 应用名称
     */
    @ApiModelProperty(value = "应用名称", required = true)
    @NotEmpty
    private String appName;

    /**
     * 卖家id 店铺id
     */
    @ApiModelProperty(value = "卖家id 店铺id", required = true)
    @NotEmpty
    private String sellerId;

}
