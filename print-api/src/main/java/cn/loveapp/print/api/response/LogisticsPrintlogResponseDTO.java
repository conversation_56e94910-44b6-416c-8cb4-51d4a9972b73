package cn.loveapp.print.api.response;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;

import cn.loveapp.print.api.dto.AyDistributeInfoDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.springframework.util.StringUtils;

import lombok.Data;

/**
 * <AUTHOR>
 */

@Data
@ApiModel(value = "物流单打印日志")
public class LogisticsPrintlogResponseDTO implements Serializable {

    private static final long serialVersionUID = 4057680802750227052L;

    /**
     * 查询总数量
     */
    @ApiModelProperty(value = "查询总数量")
    private Long totalResult;

    /**
     * 打印日志列表
     */
    @ApiModelProperty(value = "打印日志列表")
    private List<LogisticsPrintlogDTO> printlogList;

    /**
     * tid总数
     */
    @ApiModelProperty(value = "tid总数")
    private Long tidTotal;

    /**
     * 物流公司总数
     */
    @ApiModelProperty(value = "物流公司总数")
    private Long LogisticsCompanyTotal;

    /**
     * 电子面单号总数
     */
    @ApiModelProperty(value = "电子面单号总数")
    private Long WaybillCodeTotal;

    /**
     * 打印日志DTO
     *
     * <AUTHOR>
     */
    @Data
    public static class LogisticsPrintlogDTO implements Serializable {

        private static final long serialVersionUID = -2507547988932529490L;

        /**
         * 打印日志主键id
         */
        @ApiModelProperty(value = "打印日志主键id")
        private Long id;

        /**
         * 商家Nick
         */
        @ApiModelProperty(value = "商家Nick")
        private String sellerNick;

        /**
         * 应用
         */
        @ApiModelProperty(value = "应用")
        private String appName;

        /**
         * 平台
         */
        @ApiModelProperty(value = "平台")
        private String storeId;

        /**
         * 订单号
         */
        @ApiModelProperty(value = "订单号")
        private String tid;

        /**
         * 订单类型 0-普通订单 1-自由打印订单
         */
        @ApiModelProperty(value = "订单类型 0-普通订单 1-自由打印订单")
        private Integer tradeType;

        /**
         * 打印类型 express-快递单 eleface-电子面单
         */
        @ApiModelProperty(value = "打印类型 express-快递单 eleface-电子面单")
        private String printType;

        /**
         * 是否拆单打印
         */
        @ApiModelProperty(value = "是否拆单打印")
        private Boolean isSplit;

        /**
         * 打印的购物车子单号
         */
        @ApiModelProperty(value = "打印的购物车子单号")
        private List<String> oids;

        /**
         * 快递公司
         */
        @ApiModelProperty(value = "快递公司")
        private String logisticsCompany;

        /**
         * 面单服务商
         */
        @ApiModelProperty(value = "面单服务商")
        private String elefaceProvider;

        /**
         * 物流公司code
         */
        @ApiModelProperty(value = "物流公司code")
        private String cpCode;

        /**
         * 真实物流公司code
         */
        @ApiModelProperty(value = "真实物流公司code")
        private String realCpCode;

        /**
         * 运单号
         */
        @ApiModelProperty(value = "运单号")
        private String waybillCode;

        /**
         * 是否是快运面单
         */
        @ApiModelProperty(value = "是否是快运面单")
        private Boolean isChild;

        /**
         * 快运子单号
         */
        @ApiModelProperty(value = "快运子单号")
        private String childWaybillCode;

        /**
         * 收件人手机号
         */
        @ApiModelProperty(value = "收件人手机号")
        private String receiverMobile;

        /**
         * 收件人姓名
         */
        @ApiModelProperty(value = "收件人姓名")
        private String receiverName;

        /**
         * 收件人固定电话
         */
        @ApiModelProperty(value = "收件人固定电话")
        private String receiverPhone;

        /**
         * 收件人城市
         */
        @ApiModelProperty(value = "收件人城市")
        private String receiverCity;

        /**
         * 收件人详细地址
         */
        @ApiModelProperty(value = "收件人详细地址")
        private String receiverDetail;

        /**
         * 收件人区
         */
        @ApiModelProperty(value = "收件人区")
        private String receiverDistrict;

        /**
         * 收件人省
         */
        @ApiModelProperty(value = "收件人省")
        private String receiverProvince;

        /**
         * 收件人街道
         */
        @ApiModelProperty(value = "收件人街道")
        private String receiverTown;

        /**
         * 收件人邮编
         */
        @ApiModelProperty(value = "收件人邮编")
        private String receiverZip;

        /**
         * 发货人手机号
         */
        @ApiModelProperty(value = "发货人手机号")
        private String senderMobile;

        /**
         * 发货人姓名
         */
        @ApiModelProperty(value = "发货人姓名")
        private String senderName;

        /**
         * 发货人固定电话
         */
        @ApiModelProperty(value = "发货人固定电话")
        private String senderPhone;

        /**
         * 发货人城市
         */
        @ApiModelProperty(value = "发货人城市")
        private String senderCity;

        /**
         * 发货人详细地址
         */
        @ApiModelProperty(value = "发货人详细地址")
        private String senderDetail;

        /**
         * 发货人区
         */
        @ApiModelProperty(value = "发货人区")
        private String senderDistrict;

        /**
         * 发货人省
         */
        @ApiModelProperty(value = "发货人省")
        private String senderProvince;

        /**
         * 发货人街道
         */
        @ApiModelProperty(value = "发货人街道")
        private String senderTown;

        /**
         * 发货人邮编
         */
        @ApiModelProperty(value = "发货人邮编")
        private String senderZip;

        /**
         * 打印机
         */
        @ApiModelProperty(value = "打印机")
        private String printer;

        /**
         * 操作用户
         */
        @ApiModelProperty(value = "操作用户")
        private String operator;

        /**
         * 操作用户平台 TAO、PDD、JD、1688
         */
        @ApiModelProperty(value = "操作用户平台 TAO、PDD、JD、1688")
        private String operatorStoreId;

        /**
         * 操作端
         */
        @ApiModelProperty(value = "操作端")
        private String operateTerminal;

        /**
         * 打印时间
         */
        @ApiModelProperty(value = "打印时间")
        private LocalDateTime printTime;

        /**
         * 打印内容
         */
        @ApiModelProperty(value = "打印内容")
        private String printData;

        /**
         * 快递单模板图片
         */
        @ApiModelProperty(value = "快递单模板图片")
        private String expressImage;

        /**
         * 打印模板数据
         */
        @ApiModelProperty(value = "打印模板数据")
        private String printModule;

        /**
         * 自定义区域打印内容
         */
        @ApiModelProperty(value = "自定义区域打印内容")
        private String customData;

        /**
         * 子品牌
         */
        @ApiModelProperty(value = "子品牌")
        private String brandCode;

        /**
         * 交易（TAO） 扩展字段
         */
        @ApiModelProperty(value = "交易（TAO） 扩展字段")
        private String externalInfo;

        /**
         * 面单是否被回收
         */
        @ApiModelProperty(value = "面单是否被回收")
        private Boolean waybillIsCancel;

        /**
         * 合单id
         */
        @ApiModelProperty(value = "合单id")
        private String mergeTid;

        /**
         * 打印模板名称
         */
        @ApiModelProperty(value = "打印模板id")
        private String logisticsTemplateId;

        /**
         * 电子面单版本号，1-默认值旧版电子面单 2-新版电子面单 (XHS使用)
         */
        @ApiModelProperty(value = "电子面单版本号")
        private Integer billVersion;

        /**
         * 批次号
         */
        @ApiModelProperty(value = "批次号")
        private String batchId;

        /**
         * 当前批次序号
         */
        @ApiModelProperty(value = "当前批次序号")
        private List<Integer> numbersInBatch;

        /**
         * 当前批次总数
         */
        @ApiModelProperty(value = "当前批次总数")
        private Integer batchTotals;

        /**
         * 分销信息
         */
        @ApiModelProperty(value = "分销信息")
        private AyDistributeInfoDTO ayDistributeInfo;

        public void setOids(List<String> oids) {
            this.oids = oids;
        }

        public void setOids(String oids) {
            if (!StringUtils.isEmpty(oids)) {
                this.oids = Arrays.asList(oids.split(","));
            }
        }
    }
}
