package cn.loveapp.print.api.request;

import cn.loveapp.print.api.dto.ShareInfo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 面单共享关系取消request
 *
 * <AUTHOR>
 * @Date 2023/9/28 3:40 PM
 */
@Data
@ApiModel
public class SharingBatchCancelRequest {

    /**
     * 面单共享信息列表
     */
    @ApiModelProperty(name = "面单共享信息列表")
    private List<ShareInfo> shareInfoList;

    /**
     * 操作用户
     */
    private String operator;

    /**
     * 操作用户的storeId
     */
    private String operatorStoreId;

    /**
     * 操作用户的登录端
     */
    private String operateTerminal;

}
