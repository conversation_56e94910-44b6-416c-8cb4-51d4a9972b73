package cn.loveapp.print.api.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;

/**
 * 统计不同打印类型的打印日志记录数
 *
 * <AUTHOR>
 */
@Data
@ApiModel(value = "统计不同打印类型的打印日志记录数")
public class CountPrintLogRequest extends AyPrintBaseRequest {

    /**
     * 查询开始时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @NotNull
    @ApiModelProperty(value = "查询开始时间", required = true)
    private LocalDateTime startTime;

    /**
     * 查询结束时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @NotNull
    @ApiModelProperty(value = "查询结束时间", required = true)
    private LocalDateTime endTime;




}
