package cn.loveapp.print.api.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import javax.validation.constraints.AssertTrue;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @Description 获取打印日志列表请求体
 * @Date 10:36 2023/9/6
 **/
@Data
@ApiModel(value = "获取打印日志列表请求体")
public class PrintLogListGetInnerRequest extends AyPrintBaseRequest {

    /**
     * 使用maxId方式查询
     */
    private Long maxId;

    /**
     * 页
     */
    @ApiModelProperty(value = "页", required = true)
    private Integer page;

    /**
     * 每页数量
     */
    @ApiModelProperty(value = "每页数量", required = true)
    @NotNull
    private Integer pageSize;

    /**
     * 打印类型 express-快递单 eleface-电子面单 deliver-发货单
     * 为空默认查询面单、快递单
     */
    private String printType;

    /**
     * 是否需要查询面单获取记录和快递单打印日志,默认false
     */
    private boolean needQueryElefaceAndExpressLog;

    /**
     * 运单号集合
     */
    @ApiModelProperty(value = "运单号集合")
    @NotEmpty
    private List<String> waybillCodeList;


    @AssertTrue(message = "page参数和maxId参数不能同时为空")
    private boolean isValid() {
        return maxId != null || page != null;
    }


}
