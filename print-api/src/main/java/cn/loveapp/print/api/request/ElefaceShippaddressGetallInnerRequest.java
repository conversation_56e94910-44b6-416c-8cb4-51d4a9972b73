package cn.loveapp.print.api.request;

import javax.validation.constraints.NotEmpty;

import cn.loveapp.print.api.contant.ElefaceGetType;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * eleface.shippaddress.getall 获取面单发货地址 request
 *
 * <AUTHOR>
 */
@Data
@ApiModel(value = "获取面单发货地址请求")
public class ElefaceShippaddressGetallInnerRequest {
    /**
     * 用户Nick
     */
    @ApiModelProperty(value = "用户Nick", required = true)
    @NotEmpty
    private String sellerNick;

    /**
     * 用户id
     */
    @ApiModelProperty(value = "用户id", required = true)
    @NotEmpty
    private String sellerId;

    /**
     * 平台 {@link cn.loveapp.common.constant.CommonPlatformConstants}
     */
    @ApiModelProperty(value = "平台", required = true)
    @NotEmpty
    private String storeId;

    /**
     * 应用 {@link cn.loveapp.common.constant.CommonAppConstants}
     */
    @ApiModelProperty(value = "应用", required = true)
    @NotEmpty
    private String appName;

    /**
     * 是否返回不可用面单信息
     */
    @ApiModelProperty(value = "是否返回不可用面单信息")
    private Boolean isIncludeUnAvailable = false;

    /**
     * 获取的面单类型-默认全部
     */
    private String getType = ElefaceGetType.GET_ALL;

}
