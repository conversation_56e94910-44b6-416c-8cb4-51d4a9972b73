package cn.loveapp.print.api.service;

import cn.loveapp.print.api.dto.ElefaceSharingRelationDTO;
import cn.loveapp.print.api.dto.TradeLogisticsBindingHistoryResponseDTO;
import cn.loveapp.print.api.dto.WaybillOperatelogResponseDTO;
import cn.loveapp.print.api.request.*;
import cn.loveapp.print.api.response.CountPrintLogResponse;
import cn.loveapp.print.api.response.ElefaceIsCancelGetInnerResponse;
import cn.loveapp.print.api.response.LogisticsPrintlogResponseDTO;
import cn.loveapp.print.api.response.SharingBatchCancelResponse;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import cn.loveapp.common.web.CommonApiResponse;
import cn.loveapp.common.constant.HttpMethodsConstants;

import java.util.List;

/**
 * 打印服务 内部接口
 *
 * <AUTHOR>
 */
@Api(tags = "打印内部接口(RPC)", consumes = "application/json")
@FeignClient(name = "print")
@RequestMapping("export/print")
public interface PrintInnerApiService {

    /**
     * 创建面单共享关系
     *
     * @param request
     * @return
     */
    @ApiOperation(value = "创建面单共享关系", httpMethod = HttpMethodsConstants.POST)
    @RequestMapping(value = "/eleface.sharing.create", method = {RequestMethod.POST})
    CommonApiResponse<ElefaceSharingRelationDTO> elefaceSharingCreate(@RequestBody @Validated ElefaceSharingCreateInnerRequest request);

    /**
     * 创建面单共享关系(多店版)
     *
     * @param request
     * @return
     */
    @ApiOperation(value = "创建面单共享关系", httpMethod = HttpMethodsConstants.POST)
    @RequestMapping(value = "/multi.eleface.sharing.create", method = {RequestMethod.POST})
    CommonApiResponse<ElefaceSharingRelationDTO> multiElefaceSharingCreate(@RequestBody @Validated ElefaceSharingCreateInnerRequest request);

    /**
     * 获取面单发货地址信息
     *
     * @param request
     * @return
     */
    @ApiOperation(value = "获取面单发货地址信息", httpMethod = HttpMethodsConstants.POST)
    @RequestMapping(value = "/eleface.shippaddress.getall", method = {RequestMethod.POST})
    CommonApiResponse elefaceShippaddressGetall(@RequestBody @Validated ElefaceShippaddressGetallInnerRequest request);

    /**
     * 获取面单发货地址信息(多店版)
     *
     * @param request
     * @return
     */
    @ApiOperation(value = "获取面单发货地址信息", httpMethod = HttpMethodsConstants.POST)
    @RequestMapping(value = "/multi.eleface.shippaddress.getall", method = {RequestMethod.POST})
    CommonApiResponse multiElefaceShippaddressGetall(@RequestBody @Validated ElefaceShippaddressGetallInnerRequest request);

    /**
     * 查询指定订单的打印情况，是否打印或获取过面单、快递单、发货单
     *  打印过快递单或面单直接返回true
     *  没打印过快递单或面单，但是获取过面单也返回true
     *  以上都不满足则返回false
     * @param request
     * @return
     */
    @ApiOperation(value = "查询指定订单是否打印了面单和快递单", httpMethod = HttpMethodsConstants.POST)
    @RequestMapping(value = "/order.hasprint.get", method = {RequestMethod.POST})
    CommonApiResponse<Boolean> queryOrderHasPrintHistory(@Validated OrderHasprintGetInnerRequest request);

    /**
     * 批量保存面单操作日志
     * @param request
     * @return
     */
    @ApiOperation(value = "批量保存面单操作日志", httpMethod = HttpMethodsConstants.POST)
    @RequestMapping(value = "waybill.operatelog.save", method = {RequestMethod.POST})
    CommonApiResponse waybillOperateLogSave(@RequestBody @Validated WaybillOperateLogSaveRequest request);

    /**
     * 统计不同打印类型的打印日志记录数
     * @param request
     * @return
     */
    @ApiOperation(value = "统计不同打印类型的打印日志记录数", httpMethod = HttpMethodsConstants.POST)
    @RequestMapping(value = "printlog.count.get", method = {RequestMethod.POST})
    CommonApiResponse<CountPrintLogResponse> countPrintLogByPrintType(@RequestBody @Validated CountPrintLogRequest request);

    /**
     * 获取打印日志列表
     * @param request
     * @return
     */
    @ApiOperation(value = "获取打印日志列表", httpMethod = HttpMethodsConstants.POST)
    @RequestMapping(value = "printlog.list.get", method = {RequestMethod.POST})
    CommonApiResponse<LogisticsPrintlogResponseDTO> printLogListGet(@RequestBody @Validated PrintLogListGetInnerRequest request);

    /**
     * 批量查询面单是否被回收
     *
     * @param request
     * @return
     */
    @ApiOperation(value = "批量查询面单是否被回收", httpMethod = HttpMethodsConstants.POST)
    @RequestMapping(value = "eleface.iscancel.get", method = {RequestMethod.POST})
    CommonApiResponse<List<ElefaceIsCancelGetInnerResponse>> elefaceIsCancelGet(@RequestBody @Validated ElefaceIsCancelGetInnerRequest request);


    /**
     * 批量获取面单共享关系信息
     *
     * @param request
     * @return
     */
    @ApiOperation(value = "批量获取面单共享关系信息", httpMethod = HttpMethodsConstants.POST)
    @RequestMapping(value = "sharing.batch.get", method = {RequestMethod.POST})
    CommonApiResponse<List<ElefaceSharingRelationDTO>> sharingBatchGet(@RequestBody @Validated SharingBatchGetRequest request);


    /**
     * 批量取消面单共享
     *
     * @param request
     * @return
     */
    @ApiOperation(value = "批量取消面单共享", httpMethod = HttpMethodsConstants.POST)
    @RequestMapping(value = "sharing.batch.cancel", method = {RequestMethod.POST})
    CommonApiResponse<SharingBatchCancelResponse> sharingBatchCancel(@RequestBody @Validated SharingBatchCancelRequest request);

    /**
     * 面单共享充值
     *
     * @param request
     * @return
     */
    @ApiOperation(value = "面单共享充值", httpMethod = HttpMethodsConstants.POST)
    @RequestMapping(value = "sharing.topUp", method = {RequestMethod.POST})
    CommonApiResponse<String> sharingTopUpNum(@RequestBody @Validated ShareTopUpNumRequest request);

    /**
     * 面单共享数量修改
     *
     * @param request
     * @return
     */
    @ApiOperation(value = "面单共享数量修改", httpMethod = HttpMethodsConstants.POST)
    @RequestMapping(value = "sharing.quantityModify", method = {RequestMethod.POST})
    CommonApiResponse<String> sharingQuantityModify(@RequestBody @Validated ShareTopUpNumRequest request);


    /**
     * 面单共享恢复
     *
     * @param request
     * @return
     */
    @ApiOperation(value = "面单共享恢复", httpMethod = HttpMethodsConstants.POST)
    @RequestMapping(value = "sharing.recovery", method = {RequestMethod.POST})
    CommonApiResponse<String> sharingRecovery(@RequestBody @Validated ShareRecoveryRequest request);


    /**
     * 获取电子面单操作日志
     *
     * @param request
     * @return
     */
    @ApiOperation(value = "获取电子面单操作日志", httpMethod = HttpMethodsConstants.POST)
    @RequestMapping(value = "waybill.shareoperatelog.list.get", method = {RequestMethod.POST})
    CommonApiResponse<WaybillOperatelogResponseDTO> waybillShareOperateLogListGet(@RequestBody @Validated WaybillShareOperateLogListGetRpcRequest request);

    /**
     * 获取指定订单的物流单绑定记录
     *
     * @param request
     * @return
     */
    @ApiOperation(value = "获取电子面单操作日志", httpMethod = HttpMethodsConstants.POST)
    @RequestMapping(value = "logistics.binding.history.get.batch", method = {RequestMethod.POST})
    CommonApiResponse<TradeLogisticsBindingHistoryResponseDTO> logisticsBindingHistoryGetBatch(@RequestBody @Validated LogisticsBindHistoryGetApiRequest request);


    /**
     * 获取面单操作列表
     *
     * @param request
     * @return
     */
    @ApiOperation(value = "获取共享的电子面单操作列表", httpMethod = HttpMethodsConstants.POST)
    @RequestMapping("/multi/waybill.operate.log.list.get")
    CommonApiResponse<WaybillOperatelogResponseDTO> multiWaybillOperateLogListGet(@RequestBody @Validated MultiWaybillOperateLogListGetInnerRequest request);

}
