package cn.loveapp.print.api.request;

import javax.validation.constraints.NotEmpty;

import lombok.Data;

/**
 * @Author: z<PERSON><PERSON><PERSON><PERSON>
 * @Date: 2022/3/8 16:32
 * @Description: 云打印 获取验证码 请求实体类
 */
@Data
public class AyCloudprintGetVerifyCodeInnerRequest {

    /**
     * 云打印平台id
     */
    @NotEmpty
    private String printerPlatformId;

    /**
     * 小程序网关requestId
     */
    private String requestId;

    /**
     * 平台id
     */
    private String storeId;

    /**
     * 应用名称
     */
    private String appName;

    /**
     * 卖家nick
     */
    private String sellerNick;

    /**
     * 打印机id
     */
    private String printerId;
}
