package cn.loveapp.print.api.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

/**
 * 面单共享充值request
 *
 * <AUTHOR>
 * @Date 2023/10/8 4:11 PM
 */
@Data
@ApiModel
public class ShareTopUpNumRequest {

    /**
     * 是否无限分享
     */
    @ApiModelProperty(value = "是否无限分享")
    private Boolean shareNumUnlimited;

    @ApiModelProperty(value = "共享面单充值数量", required = true)
    private Long topUpNum;

    @ApiModelProperty(value = "分享Id", required = true)
    @NotEmpty
    private String shareId;

    /**
     * 面单所有者的sellerId
     */
    @ApiModelProperty(value = "面单所有者的sellerId", required = true)
    @NotEmpty
    private String ownerSellerId;

    /**
     * 面单所有者的平台id
     */
    @ApiModelProperty(value = "面单所有者的平台id", required = true)
    @NotEmpty
    private String ownerStoreId;

    /**
     * 面单所有者的应用名称
     */
    @ApiModelProperty(value = "面单所有者的应用名称", required = true)
    @NotEmpty
    private String ownerAppName;

    /**
     * 操作用户
     */
    @ApiModelProperty(value = "操作用户")
    private String operator;

    /**
     * 操作用户的storeId
     */
    @ApiModelProperty(value = "操作用户的storeId")
    private String operatorStoreId;

    /**
     * 操作用户的登录端
     */
    @ApiModelProperty(value = "操作用户的登录端")
    private String operateTerminal;

}
