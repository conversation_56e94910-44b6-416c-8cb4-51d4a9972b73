package cn.loveapp.print.api.request;

import cn.loveapp.print.api.dto.WaybillOperateLogSaveDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @program: print-services-group
 * @description: 面单操作日志保存请求request
 * @author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @create: 2022/11/30 18:45
 **/
@Data
@ApiModel(value = "面单操作日志保存请求体")
public class WaybillOperateLogSaveRequest {

    /**
     * 面单操作日志
     */
    @ApiModelProperty(value = "面单操作日志")
    private List<WaybillOperateLogSaveDTO> waybillOperateLogSaves;

}
