package cn.loveapp.print.api.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

/**
 * 面单共享信息请求DTO
 *
 * <AUTHOR>
 * @Date 2023/9/28 4:18 PM
 */
@Data
@ApiModel
public class ShareInfo {

    /**
     * 面单分享Id
     */
    @ApiModelProperty(value = "面单分享Id", required = true)
    @NotNull
    private String shareId;

    /**
     * 平台id
     */
    @ApiModelProperty(value = "平台id", required = true)
    @NotEmpty
    private String storeId;

    /**
     * 应用名称
     */
    @ApiModelProperty(value = "应用名称", required = true)
    @NotEmpty
    private String appName;

    /**
     * 卖家id 店铺id
     */
    @ApiModelProperty(value = "卖家id 店铺id", required = true)
    @NotEmpty
    private String sellerId;

}
