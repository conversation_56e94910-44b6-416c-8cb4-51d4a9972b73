package cn.loveapp.print.api.dto;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
@ApiModel(value = "电子面单操作记录响应体")
public class WaybillOperatelogResponseDTO {

    /**
     * 总数
     */
    @ApiModelProperty(value = "总数")
    private Long totalResult;

    /**
     * 电子面单操作日志列表
     */
    @ApiModelProperty(value = "电子面单操作日志列表")
    private List<WaybillOperatelogDTO> operatelogDTOList;

    /**
     * 面单取号后打印计数
     */
    @ApiModelProperty(value = "面单取号后打印计数")
    private Long printAfterGetWaybillCount;

    /**
     * 面单取号后未打印计数
     */
    @ApiModelProperty(value = "面单取号后未打印计数")
    private Long notPrintAfterGetWaybillCount;

    /**
     * 面单取号后发货计数
     */
    @ApiModelProperty(value = "面单取号后发货计数")
    private Long sendGoodAfterGetWaybillCount;

    /**
     * 面单取号后未发货计数
     */
    @ApiModelProperty(value = "面单取号后未发货计数")
    private Long notSendGoodAfterGetWaybillCount;

    /**
     * 面单取号后未已回收
     */
    @ApiModelProperty(value = "面单取号后未已回收")
    private Long cancelAfterGetWaybillCount;

    @Data
    public static class WaybillOperatelogDTO implements Serializable {
        private static final long serialVersionUID = -825292335620297214L;

        /**
         * 订单号
         */
        @ApiModelProperty(value = "订单号")
        private String tid;

        /**
         * 订单号
         */
        @ApiModelProperty(value = "订单号")
        private List<String> tidList;

        /**
         * 订单类型 0-普通订单 1-自由打印订单
         */
        @ApiModelProperty(value = "订单类型 0-普通订单 1-自由打印订单")
        private Integer tradeType;

        /**
         * 物流公司
         */
        @ApiModelProperty(value = "物流公司")
        private String logisticsCompany;

        /**
         * 物流公司code
         */
        @ApiModelProperty(value = "物流公司code")
        private String cpCode;

        /**
         * 面单的服务商 CAINIAO、PDD
         */
        @ApiModelProperty(value = "面单的服务商 CAINIAO、PDD")
        private String provider;

        /**
         * 面单号
         */
        @ApiModelProperty(value = "面单号")
        private String waybillCode;

        /**
         * 是否是快运字母单
         */
        @ApiModelProperty(value = "是否是快运字母单")
        private Boolean isChild;

        /**
         * 母单号
         */
        @ApiModelProperty(value = "母单号")
        private String childWaybillCode;

        /**
         * 共享的用户
         */
        @ApiModelProperty(value = "共享的用户")
        private String ownerNick;

        /**
         * 面单获取的操作用户
         */
        @ApiModelProperty(value = "面单获取的操作用户")
        private String getOperator;

        /**
         * 操作用户的平台 TAO、PDD、JD、1688
         */
        @ApiModelProperty(value = "操作用户的平台 TAO、PDD、JD、1688")
        private String getOperatorStoreId;

        /**
         * 操作终端
         */
        @ApiModelProperty(value = "操作终端")
        private String getOperateTerminal;

        /**
         * 面单获取时间
         */
        @ApiModelProperty(value = "面单获取时间")
        private LocalDateTime getTime;

        /**
         * 面单是否取消
         */
        @ApiModelProperty(value = "面单是否取消")
        private Boolean isCancel;

        /**
         * 面单取消的操作用户
         */
        @ApiModelProperty(value = "面单取消的操作用户")
        private String cancelOperator;

        /**
         * 操作用户的平台 TAO、PDD、JD、1688
         */
        @ApiModelProperty(value = "操作用户的平台 TAO、PDD、JD、1688")
        private String cancelOperatorStoreId;

        /**
         * 操作终端
         */
        @ApiModelProperty(value = "操作终端")
        private String cancelOperateTerminal;

        /**
         * 面单取消时间
         */
        @ApiModelProperty(value = "面单取消时间")
        private LocalDateTime cancelTime;

        /**
         * 子品牌
         */
        @ApiModelProperty(value = "子品牌")
        private String brandCode;

        /**
         * 交易（TAO） 扩展字段
         */
        @ApiModelProperty(value = "交易（TAO） 扩展字段")
        private String externalInfo;

        /**
         * 网点名称
         */
        @ApiModelProperty("网点名称")
        private String branchName;

        /**
         * 面单账号
         */
        @ApiModelProperty(value = "面单账号")
        private String ownerMallName;

        /**
         * 被分享店铺名
         */
        @ApiModelProperty(value = "被分享店铺名")
        private String targetMallName;

        /**
         * 收件地址
         */
        @ApiModelProperty(value = "收件地址")
        private String receiverAddress;

        /**
         * 重量
         */
        @ApiModelProperty(value = "重量")
        private String weight;

        /**
         * 运费
         */
        @ApiModelProperty(value = "运费")
        private String freight;

        /**
         * 业务员备注
         */
        @ApiModelProperty("业务员备注")
        private String salesman;

        /**
         * 分享备注
         */
        @ApiModelProperty("分享备注")
        private String shareMemo;

        /**
         * 订单号
         */
        @ApiModelProperty(value = "订单号")
        private String orderSellerId;

        /**
         * 订单号
         */
        @ApiModelProperty(value = "订单号")
        private String orderStoreId;

        /**
         * 订单号
         */
        @ApiModelProperty(value = "订单号")
        private String orderAppName;

        /**
         * 订单号
         */
        @ApiModelProperty(value = "订单号")
        private String orderSellerNick;

        /**
         * 订单号
         */
        @ApiModelProperty(value = "订单号")
        private String orderMallName;

        /**
         * 物流模版id
         */
        @ApiModelProperty("物流模版id")
        private String logisticsTemplateId;

        /**
         * 电子面单版本号，1-默认值旧版电子面单 2-新版电子面单 (XHS使用)
         */
        @ApiModelProperty(value = "电子面单版本号")
        private Integer billVersion;

        /**
         * 其他扩展信息包含以下： 1. 收件人信息（省市区、详细地址、手机号、收件人）
         */
        @ApiModelProperty(value = "其他扩展信息")
        private String otherExternalInfo;

        /**
         * 是否已打印
         */
        @ApiModelProperty(value = "是否已打印")
        private Boolean isPrint;

        /**
         * 是否已发货
         */
        @ApiModelProperty(value = "是否已发货")
        private Boolean isSendGood;
    }
}
