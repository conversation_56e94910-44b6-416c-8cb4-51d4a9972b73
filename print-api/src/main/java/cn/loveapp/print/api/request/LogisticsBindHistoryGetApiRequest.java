package cn.loveapp.print.api.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * 物流单绑定记录获取
 *
 * <AUTHOR>
 * @Date 2023/12/20 2:11 PM
 */
@Data
public class LogisticsBindHistoryGetApiRequest {

    /**
     * 订单类型 0-普通订单 1-自由打印订单
     */
    @ApiModelProperty(value = "订单类型 0-普通订单 1-自由打印订单", required = true)
    private Integer tradeType;

    /**
     * 是否需要返回printData
     */
    @ApiModelProperty(value = "是否需要返回printData")
    private Boolean needPrintData;

    /**
     * 订单多店列表
     */
    @ApiModelProperty(value = "订单列表", required = true)
    @NotEmpty
    private List<TargetPrintInfoDTO> targetPrintInfoList;

    @Data
    public static class TargetPrintInfoDTO {
        /**
         * 目标用户商家id
         */
        @ApiModelProperty(value = "目标用户商家id")
        private String sellerId;

        /**
         * 目标用户Nick
         */
        @ApiModelProperty(value = "目标用户Nick")
        private String sellerNick;

        /**
         * 目标用户 targetId
         */
        @ApiModelProperty(value = "目标用户targetId")
        private String storeId;

        /**
         * 目标用户 appName
         */
        @ApiModelProperty(value = "目标用户appName")
        private String appName;

        /**
         * 订单号列表
         */
        @ApiModelProperty(value = "订单号列表")
        private List<String> tidList;
    }
}
