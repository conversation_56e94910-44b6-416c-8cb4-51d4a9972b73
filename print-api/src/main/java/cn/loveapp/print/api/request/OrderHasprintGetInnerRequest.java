package cn.loveapp.print.api.request;

import javax.validation.constraints.NotEmpty;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 查询指定订单是否打印了面单和发货单
 *
 * <AUTHOR>
 */
@Data
@ApiModel(value = "查询指定订单是否打印了面单和发货单请求")
public class OrderHasprintGetInnerRequest {
    /**
     * 订单id
     */
    @ApiModelProperty(value = "订单id", required = true)
    @NotEmpty
    private String tid;

    /**
     * 平台id
     */
    @ApiModelProperty(value = "平台id", required = true)
    @NotEmpty
    private String storeId;

    /**
     * 应用名称
     */
    @ApiModelProperty(value = "应用名称", required = true)
    @NotEmpty
    private String appName;

    /**
     * 卖家id 店铺id
     */
    @ApiModelProperty(value = "卖家id 店铺id", required = true)
    @NotEmpty
    private String sellerId;

    /**
     * 打印类型集合
     */
    @ApiModelProperty(value = "打印类型集合")
    private List<String> printTypes;

    /**
     * 是否包含面单获取记录
     */
    @ApiModelProperty(value = "是否包含面单获取记录")
    private Boolean includeElefaceOperateLog;

}
