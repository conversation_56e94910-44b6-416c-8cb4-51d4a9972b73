package cn.loveapp.print.api.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @program: print-services-group
 * @description: 面单操作日志保存传输DTO
 * @author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @create: 2022/12/1 10:43
 **/
@Data
@ApiModel(value = "面单操作日志保存传输对象")
public class WaybillOperateLogSaveDTO {

    /**
     * 面单的服务商
     */
    @ApiModelProperty(value = "面单的服务商")
    private String provider;

    /**
     * 采购单id
     */
    @ApiModelProperty(value = "采购单id")
    private String objectId;

    /**
     * 快运字母单的母单号
     */
    @ApiModelProperty(value = "快运字母单的母单号")
    private String parentWaybillCode;

    /**
     * 面单号（快运下为子单号）
     */
    @ApiModelProperty(value = "面单号（快运下为子单号）")
    private String waybillCode;

    /**
     * 打印内容
     */
    @ApiModelProperty(value = "打印内容")
    private String printData;

    /**
     * 面单所有者nick
     */
    @ApiModelProperty(value = "面单所有者nick")
    private String ownerSellerNick;

    /**
     * 面单所有者id
     */
    @ApiModelProperty(value = "面单所有者id")
    private String ownerSellerId;

    /**
     * 面单所有者平台
     */
    @ApiModelProperty(value = "面单所有者平台")
    private String ownerStoreId;

    /**
     * 面单所有者应用
     */
    @ApiModelProperty(value = "面单所有者应用")
    private String ownerAppName;

    /**
     * 流水号
     */
    @ApiModelProperty(value = "流水号")
    private String serial;

    /**
     * 合单订单的主单tid
     */
    @ApiModelProperty(value = "合单订单的主单tid")
    private String mergeTid;

    /**
     * 订单号
     */
    @ApiModelProperty(value = "订单号")
    private String tid;

    /**
     * 购物车子单号列表
     */
    @ApiModelProperty(value = "购物车子单号列表")
    private List<String> oids;

    /**
     * 订单类型
     */
    @ApiModelProperty(value = "订单类型")
    private Integer tradeType;

    /**
     * 物流公司
     */
    @ApiModelProperty(value = "物流公司")
    private String logisticsCompany;

    /**
     * 物流公司code
     */
    @ApiModelProperty(value = "物流公司code")
    private String cpCode;

    /**
     * 面单获取的操作用户
     */
    @ApiModelProperty(value = "面单获取的操作用户")
    private String operator;

    /**
     * 操作用户的平台 TAO、PDD、JD、1688
     */
    @ApiModelProperty(value = "操作用户的平台 TAO、PDD、JD、1688")
    private String operatorStoreId;

    /**
     * 操作终端
     */
    @ApiModelProperty(value = "操作终端")
    private String operateTerminal;

    /**
     * 商家平台 TAO、PDD、JD、1688
     */
    @ApiModelProperty(value = "商家平台 TAO、PDD、JD、1688")
    private String storeId;

    /**
     * 商家id
     */
    @ApiModelProperty(value = "商家id")
    private String sellerId;

    /**
     * 商家应用
     */
    @ApiModelProperty(value = "商家应用")
    private String appName;

    /**
     * 商家Nick
     */
    @ApiModelProperty(value = "商家Nick")
    private String sellerNick;

    /**
     * 子品牌
     */
    @ApiModelProperty(value = "子品牌")
    private String brandCode;

    /**
     * 交易（TAO） 扩展字段
     */
    @ApiModelProperty(value = "交易（TAO） 扩展字段")
    private String externalInfo;

    /**
     * 物流公司code
     */
    @ApiModelProperty("真实物流公司code")
    private String realCpCode;


    /**
     * 面单被分享者用户Nick
     */
    @ApiModelProperty("面单被分享者用户Nick")
    private String targetNick;

    /**
     * 面单被分享者的sellerId
     */
    @ApiModelProperty("面单被分享者的sellerId")
    private String targetSellerId;

    /**
     * 面单被分享者的平台id
     */
    @ApiModelProperty("面单被分享者的平台id")
    private String targetStoreId;

    /**
     * 面单被分享者的应用名称
     */
    @ApiModelProperty("面单被分享者的应用名称")
    private String targetAppName;

    /**
     * 使用的面单分享Id
     */
    @ApiModelProperty("使用的面单分享Id")
    private String shareId;

    /**
     * 电子面单版本号，1-默认值旧版电子面单 2-新版电子面单 (XHS使用)
     */
    @ApiModelProperty("电子面单版本号")
    private Integer billVersion;
}
