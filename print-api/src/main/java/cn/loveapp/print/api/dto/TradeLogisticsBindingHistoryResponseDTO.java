package cn.loveapp.print.api.dto;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.springframework.util.StringUtils;

import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
@ApiModel("物流单绑定记录响应体")
public class TradeLogisticsBindingHistoryResponseDTO implements Serializable {

    private static final long serialVersionUID = -6736487305919754284L;

    /**
     * 物流单绑定记录列表
     */
    @ApiModelProperty(value = "物流单绑定记录列表")
    List<TradeLogisticsBindingHistoryDTO> tradeLogisticsBindingHistoryList;

    @Data
    public static class TradeLogisticsBindingHistoryDTO implements Serializable {
        private static final long serialVersionUID = -6233205764979978100L;

        /**
         * 订单tid
         */
        @ApiModelProperty(value = "订单tid")
        private String tid;

        /**
         * 物流单绑定记录
         */
        @ApiModelProperty(value = "物流单绑定记录")
        private List<LogisticsBindingHistoryDTO> bindingHistoryList;
    }

    @Data
    public static class LogisticsBindingHistoryDTO implements Serializable {
        private static final long serialVersionUID = -7393295683052793335L;

        /**
         * 打印的购物车子单列表
         */
        @ApiModelProperty(value = "打印的购物车子单列表")
        private List<String> oids;

        /**
         * 打印类型 0-快递单 1-电子面单
         */
        @ApiModelProperty(value = "打印类型 0-快递单 1-电子面单")
        private String printType;

        /**
         * 流水号
         */
        @ApiModelProperty(value = "流水号")
        private String serial;

        /**
         * 物流公司
         */
        @ApiModelProperty(value = "物流公司")
        private String logisticsCompany;

        /**
         * 面单服务商 CAINIAO、PDD
         */
        private String provider;

        /**
         * 电子面单订单id，全局唯一id(微信视频号专有字段)
         */
        @ApiModelProperty(value = "电子面单订单id(微信视频号专有字段)")
        private String waybillOrderId;

        /**
         * 面单共享用户
         */
        @ApiModelProperty(value = "面单共享用户")
        private String ownerNick;

        /**
         * 物流公司code
         */
        @ApiModelProperty(value = "物流公司code")
        private String cpCode;

        /**
         * 真实物流公司code
         */
        @ApiModelProperty(value = "真实物流公司code")
        private String realCpCode;

        /**
         * 是否为快运字母单
         */
        @ApiModelProperty(value = "是否为快运字母单")
        private Boolean isChild;

        /**
         * 面单号
         */
        @ApiModelProperty(value = "面单号")
        private String waybillCode;

        /**
         * 母子单的子单号
         */
        @ApiModelProperty(value = "母子单的子单号")
        private String childWaybillCode;

        /**
         * 打印内容
         */
        @ApiModelProperty(value = "打印内容")
        private String printData;

        /**
         * 面单是否被回收
         */
        @ApiModelProperty(value = "面单是否被回收")
        private Boolean waybillIsCancel;

        /**
         * 绑定时间
         */
        @ApiModelProperty(value = "绑定时间")
        private LocalDateTime bindingTime;

        /**
         * 面单打印次数
         */
        @ApiModelProperty(value = "面单打印次数")
        private Integer printCount;

        /**
         * 交易（TAO） 扩展字段
         */
        @ApiModelProperty(value = "交易（TAO） 扩展字段")
        private String externalInfo;

        /**
         * 电子面单版本号，1-默认值旧版电子面单 2-新版电子面单 (XHS使用)
         */
        @ApiModelProperty(value = "电子面单版本号")
        private Integer billVersion;

        /**
         * 其他扩展信息包含以下： 1. 收件人信息（省市区、详细地址、手机号、收件人）
         */
        @ApiModelProperty(value = "其他扩展信息")
        private String otherExternalInfo;

        public void setOids(String oids) {
            if (!StringUtils.isEmpty(oids)) {
                this.oids = Arrays.asList(oids.split(","));
            }
        }

        public void setOids(List<String> oids) {
            this.oids = oids;
        }
    }

}
