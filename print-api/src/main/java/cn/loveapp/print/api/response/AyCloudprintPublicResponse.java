package cn.loveapp.print.api.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Author: z<PERSON><PERSON><PERSON>e
 * @Date: 2022/3/9 11:06
 * @Description: 云打印 公共响应体
 */
@Data
@ApiModel(value = "云打印公共响应体")
public class AyCloudprintPublicResponse {
    /**
     * 是否成功
     */
    @ApiModelProperty(value = "是否成功")
    private boolean isSuccess;

    /**
     * 错误码
     */
    @ApiModelProperty(value = "错误码")
    private String errorCode;

    /**
     * 错误信息
     */
    @ApiModelProperty(value = "错误信息")
    private String errorMsg;
}
