package cn.loveapp.print.api.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * <AUTHOR>
 * @Description 批量查询面单是否被回收
 * @Date 15:07 2023/8/28
 **/
@Data
@ApiModel(value = "查询指定订单是否打印了面单和发货单请求")
public class ElefaceIsCancelGetInnerRequest {
    /**
     * 平台id
     */
    @ApiModelProperty(value = "平台id", required = true)
    @NotEmpty
    private String storeId;

    /**
     * 应用名称
     */
    @ApiModelProperty(value = "应用名称", required = true)
    @NotEmpty
    private String appName;

    /**
     * 卖家id 店铺id
     */
    @ApiModelProperty(value = "卖家id 店铺id", required = true)
    @NotEmpty
    private String sellerId;

    /**
     * 面单所有人平台id
     */
    @ApiModelProperty(value = "平台id", required = true)
    @NotEmpty
    private String ownerStoreId;

    /**
     * 面单所有人应用名称
     */
    @ApiModelProperty(value = "应用名称", required = true)
    @NotEmpty
    private String ownerAppName;

    /**
     * 面单所有人卖家id 店铺id
     */
    @ApiModelProperty(value = "卖家id 店铺id", required = true)
    @NotEmpty
    private String ownerSellerId;


    /**
     * 面单号集合
     */
    @ApiModelProperty(value = "面单号集合", required = true)
    @NotEmpty
    private List<String> waybillCodeList;
}
