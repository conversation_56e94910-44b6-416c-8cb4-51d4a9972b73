package cn.loveapp.print.api.dto;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
/**
 * (eleface_sharing_relation) 电子面单共享关系 传输对象
 *
 * <AUTHOR>
 */
@ApiModel
@Data
public class ElefaceSharingRelationDTO {
    /**
     * 主键自增id
     */
    @ApiModelProperty("主键自增id")
    private Long id;

    /**
     * 分享关系id
     */
    @ApiModelProperty("分享关系id")
    private String shareId;

    /**
     * 面单所有者的sellerNick
     */
    @ApiModelProperty("面单所有者的sellerNick")
    private String ownerSellerNick;

    /**
     * 面单所有者的sellerId
     */
    @ApiModelProperty("面单所有者的sellerId")
    private String ownerSellerId;

    /**
     * 面单所有者的平台id
     */
    @ApiModelProperty("面单所有者的平台id")
    private String ownerStoreId;

    /**
     * 面单所有者的应用名称
     */
    @ApiModelProperty("面单所有者的应用名称")
    private String ownerAppName;

    /**
     * 被分享用户sellerNick
     */
    @ApiModelProperty("被分享用户sellerNick")
    private String targetSellerNick;

    /**
     * 被分享用户sellerId
     */
    @ApiModelProperty("被分享用户sellerId")
    private String targetSellerId;

    /**
     * 被分享用户平台id
     */
    @ApiModelProperty("被分享用户平台id")
    private String targetStoreId;

    /**
     * 被分享用户应用名称
     */
    @ApiModelProperty("被分享用户应用名称")
    private String targetAppName;

    /**
     * 面单共享状态 1-有效的 0-无效待删除的
     */
    @ApiModelProperty("面单共享状态")
    private Integer status;

    /**
     * 面单服务商 CN PDD
     */
    @ApiModelProperty("面单服务商")
    private String provider;

    /**
     * 快递公司code
     */
    @ApiModelProperty("快递公司code")
    private String cpCode;

    /**
     * 物流服务商 业务类型
     */
    @ApiModelProperty("物流服务商 业务类型")
    private Long cpType;

    /**
     * 物流网点code
     */
    @ApiModelProperty("物流网点code")
    private String branchCode;

    /**
     * 号段信息 菜鸟专有字段 其他平台为空字符串
     */
    @ApiModelProperty("号段信息")
    private String segmentCode;

    /**
     * 发货地址 省
     */
    @ApiModelProperty("发货地址 省")
    private String shippAddressProvince;

    /**
     * 发货地址 市
     */
    @ApiModelProperty("发货地址 市")
    private String shippAddressCity;

    /**
     * 发货地址 区
     */
    @ApiModelProperty("发货地址 区")
    private String shippAddressDistrict;

    /**
     * 发货地址 详细
     */
    @ApiModelProperty("发货地址 详细")
    private String shippAddressDetail;

    /**
     * 网点发货信息MD5 cpCode cpType branchCode segmentCode shippAddressProvince shippAddressCity shippAddressDistrict
     * shippAddressDetail 取MD5
     */
    @ApiModelProperty("网点发货信息MD5")
    private String shippAddressMd5;

    /**
     * 共享的面单数量（-1表示不限制数量）
     */
    @ApiModelProperty("共享的面单数量（-1表示不限制数量）")
    private Long shareNum;

    /**
     * 已使用的数量
     */
    @ApiModelProperty("已使用的数量")
    private Long usedNum;

    /**
     * 子品牌code
     */
    @ApiModelProperty("子品牌code")
    private String brandCode;

    /**
     * 变更版本号
     */
    @ApiModelProperty("变更版本号")
    private Integer version;

    /**
     * 分享类型， 1: 标志代理使用
     */
    @ApiModelProperty("分享类型， 1: 标志代理使用")
    private Integer shareType;

    /**
     * 分享备注
     */
    @ApiModelProperty("分享备注")
    private String shareMemo;

    /**
     * 代理用户id
     */
    @ApiModelProperty("代理用户id")
    private String proxySellerId;

    /**
     * 代理用户nick
     */
    @ApiModelProperty("代理用户nick")
    private String proxySellerNick;

    /**
     * 代理用户平台
     */
    @ApiModelProperty("代理用户id")
    private String proxyStoreId;

    /**
     * 代理用户应用
     */
    @ApiModelProperty("代理用户id")
    private String proxyAppName;

    /**
     * 所有者店铺名
     */
    @ApiModelProperty("所有者店铺名")
    private String ownerMallName;

    /**
     * 被分享店铺名
     */
    @ApiModelProperty("被分享店铺名")
    private String targetMallName;

    /**
     * 代理店铺名
     */
    @ApiModelProperty("代理店铺名")
    private String proxyMallName;

    /**
     * 网点名称
     */
    @ApiModelProperty("网点名称")
    private String branchName;

    /**
     * 业务员备注
     */
    @ApiModelProperty("业务员备注")
    private String salesman;

    @ApiModelProperty("创建时间")
    private String gmtCreate;

    @ApiModelProperty("更新时间")
    private String gmtModified;

    @ApiModelProperty("电子面单版本号")
    private Integer billVersion;
}
