package cn.loveapp.print.api.request;

import cn.loveapp.common.utils.DateUtil;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;

/**
 * 共享面单操作日志获取内部请求request
 *
 * <AUTHOR>
 * @Date 2023/10/9 10:29 AM
 */
@Data
@ApiModel
public class WaybillShareOperateLogListGetRpcRequest {
    private static final DateTimeFormatter DF = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    /**
     * 页
     */
    @ApiModelProperty(value = "页", required = true)
    @NotNull
    private Integer page = 1;

    /**
     * 每页数量
     */
    @ApiModelProperty(value = "每页数量", required = true)
    @NotNull
    private Integer pageSize = 20;

    /**
     * 订单类型
     */
    @ApiModelProperty(value = "订单类型")
    private Integer tradeType;

    /**
     * 面单是否被取消
     */
    @ApiModelProperty(value = "面单是否被取消")
    private Boolean isCancel;

    /**
     * 面单服务商
     */
    @ApiModelProperty(value = "面单服务商")
    private String provider;

    /**
     * 物流公司编码
     */
    @ApiModelProperty(value = "物流公司编码")
    private String cpCode;

    /**
     * 订单号
     */
    @ApiModelProperty(value = "订单号")
    private String tid;

    /**
     * 订单号列表
     */
    @ApiModelProperty(value = "订单号列表")
    private List<String> tidList;

    /**
     * 运单号
     */
    @ApiModelProperty(value = "运单号")
    private String waybillCode;

    /**
     * 开始时间
     */
    @ApiModelProperty(value = "开始时间")
    private LocalDateTime startTime;

    /**
     * 结束时间
     */
    @ApiModelProperty(value = "结束时间")
    private LocalDateTime endTime;

    /**
     * 面单所有者的sellerNick
     */
    @ApiModelProperty(value = "面单所有者的sellerNick")
    private String ownerSellerNick;

    /**
     * 面单所有者的sellerId
     */
    @ApiModelProperty(value = "面单所有者的sellerId", required = true)
    @NotEmpty
    private String ownerSellerId;

    /**
     * 面单所有者的平台id
     */
    @ApiModelProperty(value = "面单所有者的平台id", required = true)
    @NotEmpty
    private String ownerStoreId;

    /**
     * 面单所有者的应用名称
     */
    @ApiModelProperty(value = "面单所有者的应用名称", required = true)
    @NotEmpty
    private String ownerAppName;

    /**
     * 被分享用户sellerNick
     */
    @ApiModelProperty(value = "被分享用户sellerNick")
    private String targetSellerNick;

    /**
     * 被分享用户sellerId
     */
    @ApiModelProperty(value = "被分享用户sellerId", required = true)
    @NotEmpty
    private String targetSellerId;

    /**
     * 被分享用户平台id
     */
    @ApiModelProperty(value = "被分享用户平台id", required = true)
    @NotEmpty
    private String targetStoreId;

    /**
     * 被分享用户应用名称
     */
    @ApiModelProperty(value = "被分享用户应用名称", required = true)
    @NotEmpty
    private String targetAppName;


    public void setStartTime(String startTime) {
        this.startTime = DateUtil.parseString(startTime);
    }

    public void setEndTime(String endTime) {
        this.endTime = DateUtil.parseString(endTime);
    }
}
