package cn.loveapp.print.api.request;

import cn.loveapp.print.api.dto.MultiWaybillOperateLogListGetDTO;
import cn.loveapp.print.api.dto.TargetPrintUserInfo;
import lombok.Data;

import java.util.List;

/**
 * 面单列表获取内部接口请求request
 *
 * <AUTHOR>
 * @Date 2024/9/20 10:59 AM
 */
@Data
public class MultiWaybillOperateLogListGetInnerRequest extends MultiWaybillOperateLogListGetDTO {

    /**
     * 用户信息
     */
    private List<TargetPrintUserInfo> userInfoList;
}
