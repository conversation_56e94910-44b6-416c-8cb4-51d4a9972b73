package cn.loveapp.print.api.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Author: z<PERSON><PERSON><PERSON>e
 * @Date: 2022/3/8 16:33
 * @Description: 云打印 绑定打印机 响应实体类
 */
@Data
@ApiModel(value = "云打印绑定打印机响应实体类")
public class AyCloudprintBindPrinterInnerResponse extends AyCloudprintPublicResponse {
    /**
     * 绑定打印机成功后获得的号码，发送打印任务时需要上送
     */
    @ApiModelProperty(value = "绑定打印机成功后获得的号码，发送打印任务时需要上送")
    private String shardCode;
}
