package cn.loveapp.print.api.request;

import javax.validation.constraints.NotEmpty;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Author: zhong<PERSON><PERSON>e
 * @Date: 2022/3/8 16:35
 * @Description: 云打印 发送打印任务 请求实体类
 */
@Data
@ApiModel(value = "云打印发送打印任务请求实体")
public class AyCloudprintSendPrintTaskInnerRequest {

    /**
     * 云打印平台id
     */
    @ApiModelProperty(value = "云打印平台id", required = true)
    @NotEmpty
    private String printerPlatformId;

    /**
     * 小程序网关requestId
     */
    @ApiModelProperty(value = "小程序网关requestId")
    private String requestId;

    /**
     * 平台id
     */
    @ApiModelProperty(value = "平台id")
    private String storeId;

    /**
     * 应用名称
     */
    @ApiModelProperty(value = "应用名称")
    private String appName;

    /**
     * 卖家nick
     */
    @ApiModelProperty(value = "卖家nick")
    private String sellerNick;

    /**
     * 绑定打印机成功后获得的号码，发送打印任务时需要上送
     */
    @ApiModelProperty(value = "绑定打印机成功后获得的号码，发送打印任务时需要上送")
    private Integer shardCode;

    /**
     * 打印机id
     */
    @ApiModelProperty(value = "打印机id")
    private String printerId;

    /**
     * 打印模板地址
     */
    @ApiModelProperty(value = "打印模板地址")
    private String printTemplateUrl;

    /**
     * 打印内容（printEncrypted为true时内容会加密）
     */
    @ApiModelProperty(value = "打印内容（printEncrypted为true时内容会加密）")
    private String printData;

    /**
     * 打印内容是否加密
     */
    @ApiModelProperty(value = "打印内容是否加密")
    private Boolean printEncrypted;

    /**
     * 打印额外内容
     */
    @ApiModelProperty(value = "打印额外内容")
    private String printAddData;

    /**
     * 签名
     */
    @ApiModelProperty(value = "签名")
    private String printSignature;

    /**
     * 自定义模板地址
     */
    @ApiModelProperty(value = "自定义模板地址")
    private String customTemplateUrl;

    /**
     * 自定义内容
     */
    @ApiModelProperty(value = "自定义内容")
    private String customData;

    /**
     * 当前页码
     */
    @ApiModelProperty(value = "当前页码")
    private Integer currentPageCount = 1;

    /**
     * 总页码
     */
    @ApiModelProperty(value = "总页码")
    private Integer totalPageCount = 1;


}
