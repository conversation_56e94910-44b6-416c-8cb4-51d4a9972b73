package cn.loveapp.print.api.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Author: zhong<PERSON><PERSON>e
 * @Api: 2022/8/16 12:14
 * @Description:
 */
@Data
@ApiModel("发送打印指令请求体")
public class AyCloudprintCmdRenderInnerResponse extends AyCloudprintPublicResponse {

    /**
     * 指令内容
     */
    @ApiModelProperty(value = "指令内容")
    private String cmdContent;

    /**
     * 指令编码格式
     */
    @ApiModelProperty(value = "指令编码格式")
    private String cmdEncoding;
}
