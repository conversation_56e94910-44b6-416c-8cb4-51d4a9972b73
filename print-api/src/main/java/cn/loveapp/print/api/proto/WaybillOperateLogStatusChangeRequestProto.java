package cn.loveapp.print.api.proto;

import com.alibaba.fastjson.JSON;
import io.protostuff.Tag;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2025-02-28 18:26
 * @description: 面单操作日志状态变更消息传输对象
 */
@Data
public class WaybillOperateLogStatusChangeRequestProto {

    /**
     * 用户id
     */
    @Tag(1)
    private String sellerId;

    /**
     * 用户id
     */
    @Tag(2)
    private String sellerNick;

    /**
     * 用户id
     */
    @Tag(3)
    private String storeId;

    /**
     * 用户id
     */
    @Tag(4)
    private String appName;

    /**
     * 是否打印
     */
    @Tag(5)
    private Boolean isPrint;

    /**
     * 是否发货
     */
    @Tag(6)
    private Boolean isSendGood;

    /**
     * 物流公司code
     */
    @Tag(7)
    private String cpCode;

    /**
     * 运单号
     */
    @Tag(8)
    private String waybillCode;
}
