package cn.loveapp.print.api.request;

import lombok.Data;

import javax.validation.constraints.NotEmpty;

/**
 * 面单分享恢复接口 request
 *
 * <AUTHOR>
 * @Date 2024/8/7 3:57 PM
 */
@Data
public class ShareRecoveryRequest {

    /**
     * 面单共享id
     */
    private String shareId;

    /**
     * 恢复数量
     */
    private Long shareRecoveryNum;

    /**
     * 无限分享
     */
    private Boolean shareNumUnlimited;

    /**
     * 面单所有者（代理）的sellerId 活
     */
    @NotEmpty
    private String sellerId;

    /**
     * 面单所有者（代理）的平台id
     */
    @NotEmpty
    private String storeId;

    /**
     * 面单所有者（代理）的应用名称
     */
    @NotEmpty
    private String appName;

    /**
     * 操作用户
     */
    private String operator;

    /**
     * 操作用户的登录端
     */
    private String operateTerminal;

}
