package cn.loveapp.print.api.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.util.List;
import java.util.Map;

/**
 * @Author: zhong<PERSON><PERSON>e
 * @Date: 2022/8/16 11:39
 * @Description: 云打印命令行打印渲染接口 请求实体类
 */
@Data
@ApiModel(value = "云打印命令行打印渲染接口请求实体")
public class AyCloudprintCmdRenderInnerRequest {

    /**
     * 云打印平台id
     */
    @ApiModelProperty(value = "云打印平台id", required = true)
    @NotEmpty
    private String printerPlatformId;

    /**
     * 小程序网关requestId
     */
    @ApiModelProperty(value = "小程序网关requestId")
    private String requestId;

    /**
     * 平台id
     */
    @ApiModelProperty(value = "平台id")
    private String storeId;

    /**
     * 应用名称
     */
    @ApiModelProperty(value = "应用名称")
    private String appName;

    /**
     * 客户端id
     */
    @ApiModelProperty(value = "客户端id")
    private String clientId;

    /**
     * 客户端类型
     */
    @ApiModelProperty(value = "客户端类型")
    private String clientType;

    /**
     * 渲染配置
     */
    @ApiModelProperty(value = "渲染配置")
    private RenderConfig config;

    /**
     * 打印机名称
     */
    @ApiModelProperty(value = "打印机名称")
    private String printerName;

    /**
     * 打印内容
     */
    @ApiModelProperty(value = "打印内容")
    private Document document;

    /**
     * 渲染配置 内部类
     */
    @Data
    public static class RenderConfig {
        /**
         * 是否需要顶部logo
         */
        @ApiModelProperty(value = "是否需要顶部logo")
        private Boolean needTopLogo;

        /**
         * 是否需要中间logo
         */
        @ApiModelProperty(value = "是否需要中间logo")
        private Boolean needMiddleLogo;

        /**
         * 是否需要底部logo
         */
        @ApiModelProperty(value = "是否需要底部logo")
        private Boolean needBottomLogo;

        /**
         * 方向
         */
        @ApiModelProperty(value = "方向")
        private String orientation;

        /**
         * 额外参数
         */
        @ApiModelProperty(value = "额外参数")
        private Map<String, String> extra;
    }

    /**
     * 打印内容 内部类
     */
    @Data
    public static class Document {

        /**
         * 渲染内容集合
         */
        @ApiModelProperty(value = "渲染内容集合")
        private List<RenderContent> contents;
    }

    /**
     * 渲染内容 内部类
     */
    @Data
    public static class RenderContent {
        /**
         * 模板url
         */
        @ApiModelProperty(value = "模板url")
        private String templateUrl;

        /**
         * 打印数据
         */
        @ApiModelProperty(value = "打印数据")
        private String printData;

        /**
         * 是否加密
         */
        @ApiModelProperty(value = "是否加密")
        private Boolean encrypted;

        /**
         * ver
         */
        @ApiModelProperty(value = "ver")
        private String ver;

        /**
         * 附加数据
         */
        @ApiModelProperty(value = "附加数据")
        private String addData;

        /**
         * 签名
         */
        @ApiModelProperty(value = "签名")
        private String signature;
    }
}
