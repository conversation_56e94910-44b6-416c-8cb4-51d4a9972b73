package cn.loveapp.print.api.response;
import io.swagger.annotations.ApiModel;
import lombok.Data;

/**
 * 统计不同打印类型的打印日志记录数响应
 *
 * <AUTHOR>
 */
@Data
@ApiModel(value = "统计不同打印类型的打印日志记录数响应")
public class CountPrintLogResponse {

    /**
     * 电子面单
     */
    private long printedElefaceCount;

    /**
     * 快递单
     */
    private long printedExpressCount;

    /**
     * 发货单
     */
    private long printedDeliverCount;

}
