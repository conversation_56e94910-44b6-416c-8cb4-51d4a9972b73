<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>cn.loveapp.common</groupId>
        <artifactId>common-spring-boot-parent</artifactId>
        <version>1.35.20-SNAPSHOT</version>
        <relativePath/>
    </parent>

    <modelVersion>4.0.0</modelVersion>
    <groupId>cn.loveapp.print</groupId>
    <artifactId>print-api</artifactId>

    <name>爱用-print-api</name>
    <description>爱用-print rpc api接口定义模块</description>
    <version>1.3-SNAPSHOT</version>


    <dependencies>
        <dependency>
            <groupId>cn.loveapp.common</groupId>
            <artifactId>common-spring-boot-web-starter</artifactId>
        </dependency>
        <!-- https://mvnrepository.com/artifact/org.springframework.cloud/spring-cloud-starter -->
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter</artifactId>
        </dependency>
        <!-- https://mvnrepository.com/artifact/org.springframework.cloud/spring-cloud-starter-openfeign -->
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-openfeign</artifactId>
        </dependency>
        <dependency>
            <groupId>io.protostuff</groupId>
            <artifactId>protostuff-core</artifactId>
        </dependency>
        <dependency>
            <groupId>io.protostuff</groupId>
            <artifactId>protostuff-runtime</artifactId>
        </dependency>
    </dependencies>

    <distributionManagement>
        <repository>
            <id>aiyongbao-maven-releases</id>
            <url>http://mavenzjk.aiyongtech.com/repository/maven-releases/</url>
        </repository>
        <snapshotRepository>
            <id>aiyongbao-maven-snapshots</id>
            <url>http://mavenzjk.aiyongtech.com/repository/maven-snapshots/</url>
        </snapshotRepository>
    </distributionManagement>

</project>
