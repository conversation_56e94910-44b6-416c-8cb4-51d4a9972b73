#!/bin/bash

# 面单备注修改功能测试脚本
# 用于逐步验证测试用例的修复情况

echo "开始运行面单备注修改功能测试..."

# 1. 首先运行最简单的常量测试
echo "1. 运行常量测试..."
mvn test -Dtest=ElefaceSharingRelationOperateConstantTest -q
if [ $? -eq 0 ]; then
    echo "✅ 常量测试通过"
else
    echo "❌ 常量测试失败"
    exit 1
fi

# 2. 运行服务层测试
echo "2. 运行服务层测试..."
mvn test -Dtest=ElefaceAccountServiceImplTest -q
if [ $? -eq 0 ]; then
    echo "✅ 服务层测试通过"
else
    echo "❌ 服务层测试失败"
    exit 1
fi

# 3. 运行控制器测试
echo "3. 运行控制器测试..."
mvn test -Dtest=ElefaceSharingRelationOperateControllerTest -q
if [ $? -eq 0 ]; then
    echo "✅ 控制器测试通过"
else
    echo "❌ 控制器测试失败"
    exit 1
fi

# 4. 运行数据访问层测试
echo "4. 运行数据访问层测试..."
mvn test -Dtest=ElefaceSharingRelationOperateLogDaoTest -q
if [ $? -eq 0 ]; then
    echo "✅ 数据访问层测试通过"
else
    echo "❌ 数据访问层测试失败"
    exit 1
fi

# 5. 运行简化测试套件
echo "5. 运行简化测试套件..."
mvn test -Dtest=SimpleTestRunner -q
if [ $? -eq 0 ]; then
    echo "✅ 简化测试套件通过"
else
    echo "❌ 简化测试套件失败"
    exit 1
fi

echo ""
echo "🎉 所有测试都通过了！"
echo ""
echo "测试覆盖的功能："
echo "- ✅ 新增操作类型常量 (MEMO_UPDATE = 5)"
echo "- ✅ 面单备注修改时记录操作日志"
echo "- ✅ 接口兼容性处理（老客户端排除备注更新日志）"
echo "- ✅ 数据库查询的操作类型过滤"
echo "- ✅ 异常处理和边界条件"
echo ""
echo "可以安全部署到生产环境！"
