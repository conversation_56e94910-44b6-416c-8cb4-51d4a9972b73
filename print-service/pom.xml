<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>cn.loveapp.print</groupId>
        <artifactId>print-services-group</artifactId>
        <version>1.0-SNAPSHOT</version>
        <relativePath>../pom.xml</relativePath>
    </parent>
    <modelVersion>4.0.0</modelVersion>
    <artifactId>print-service</artifactId>

    <name>爱用-print-service</name>
    <description>爱用-print web接口模块</description>
    <version>1.0-SNAPSHOT</version>


    <dependencies>
        <dependency>
            <groupId>cn.loveapp.common</groupId>
            <artifactId>common-spring-boot-web-starter</artifactId>
            <exclusions>
                <!-- dubbo依赖-->
                <exclusion>
                    <groupId>com.alibaba.boot</groupId>
                    <artifactId>dubbo-spring-boot-starter</artifactId>
                </exclusion>

                <!-- 数据库相关依赖, 不需要操作数据库时去放开以下注释 -->
                <!--
                <exclusion>
                    <artifactId>mybatis-spring-boot-starter</artifactId>
                    <groupId>org.mybatis.spring.boot</groupId>
                </exclusion>
                <exclusion>
                    <groupId>com.alibaba</groupId>
                    <artifactId>druid-spring-boot-starter</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.alibaba</groupId>
                    <artifactId>druid</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>mysql</groupId>
                    <artifactId>mysql-connector-java</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.postgresql</groupId>
                    <artifactId>postgresql</artifactId>
                </exclusion>
                -->
                <exclusion>
                    <groupId>io.shardingjdbc</groupId>
                    <artifactId>sharding-jdbc-core</artifactId>
                </exclusion>
                <exclusion>
                    <artifactId>mapstruct</artifactId>
                    <groupId>org.mapstruct</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>cn.loveapp.common</groupId>
            <artifactId>common-user-session-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.loveapp.print</groupId>
            <artifactId>print-api</artifactId>
            <version>${print-api.version}</version>
        </dependency>
        <dependency>
            <groupId>cn.loveapp.print</groupId>
            <artifactId>print-common</artifactId>
            <version>1.0-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>cn.loveapp.orders</groupId>
            <artifactId>orders-api</artifactId>
            <version>${orders-api.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>cn.loveapp.common</groupId>
                    <artifactId>common-spring-boot-parent</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>cn.loveapp.shops</groupId>
            <artifactId>shops-api</artifactId>
            <version>${shops-api.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>cn.loveapp.common</groupId>
                    <artifactId>common-spring-boot-parent</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <!-- 可选依赖 -->
        <!-- aliyun ons -->
        <!--
        <dependency>
            <groupId>com.aliyun.openservices</groupId>
            <artifactId>ons-client</artifactId>
        </dependency>
        -->
        <!-- taobao top sdk -->
        <dependency>
            <groupId>com.taobao</groupId>
            <artifactId>topsdk</artifactId>
            <version>${topsdk.version}</version>
        </dependency>
        <dependency>
            <groupId>com.pdd</groupId>
            <artifactId>popsdk</artifactId>
            <version>${popsdk.version}</version>
        </dependency>
        <!-- memcached -->
        <!--
        <dependency>
            <groupId>net.spy</groupId>
		    <artifactId>spymemcached</artifactId>
        </dependency>
        -->

        <dependency>
            <groupId>org.mapstruct</groupId>
            <artifactId>mapstruct</artifactId>
            <version>${org.mapstruct.version}</version>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
            </plugin>
        </plugins>
    </build>


</project>
