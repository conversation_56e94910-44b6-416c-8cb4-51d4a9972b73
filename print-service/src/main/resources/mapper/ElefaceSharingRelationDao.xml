<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.loveapp.print.service.dao.print.ElefaceSharingRelationDao">

    <resultMap type="cn.loveapp.print.service.entity.ElefaceSharingRelation" id="ElefaceSharingRelationMap">
        <result property="id" column="id"/>
        <result property="shareId" column="share_id"/>
        <result property="ownerSellerNick" column="owner_seller_nick"/>
        <result property="ownerSellerId" column="owner_seller_id"/>
        <result property="ownerStoreId" column="owner_store_id"/>
        <result property="ownerAppName" column="owner_app_name"/>
        <result property="ownerMallName" column="owner_mall_name"/>
        <result property="targetSellerNick" column="target_seller_nick"/>
        <result property="targetSellerId" column="target_seller_id"/>
        <result property="targetStoreId" column="target_store_id"/>
        <result property="targetAppName" column="target_app_name"/>
        <result property="targetMallName" column="target_mall_name"/>
        <result property="status" column="status"/>
        <result property="provider" column="provider"/>
        <result property="cpCode" column="cp_code"/>
        <result property="cpType" column="cp_type"/>
        <result property="branchCode" column="branch_code"/>
        <result property="segmentCode" column="segment_code"/>
        <result property="shippAddressProvince" column="shipp_address_province"/>
        <result property="shippAddressCity" column="shipp_address_city"/>
        <result property="shippAddressDistrict" column="shipp_address_district"/>
        <result property="shippAddressDetail" column="shipp_address_detail"/>
        <result property="shippAddressMd5" column="shipp_address_md5"/>
        <result property="shareNum" column="share_num"/>
        <result property="usedNum" column="used_num"/>
        <result property="brandCode" column="brand_code"/>
        <result property="branchName" column="branch_name"/>
        <result property="shareType" column="share_type"/>
        <result property="shareMemo" column="share_memo"/>
        <result property="proxySellerNick" column="proxy_seller_nick"/>
        <result property="proxySellerId" column="proxy_seller_id"/>
        <result property="proxyStoreId" column="proxy_store_id"/>
        <result property="proxyAppName" column="proxy_app_name"/>
        <result property="proxyMallName" column="proxy_mall_name"/>
        <result property="salesman" column="salesman"/>
        <result property="cancelNum" column="cancel_num"/>
        <result property="gmtCreate" column="gmt_create"/>
        <result property="gmtModified" column="gmt_modified"/>
        <result property="billVersion" column="bill_version"/>
    </resultMap>

    <sql id="tablename">eleface_sharing_relation</sql>

    <sql id="fields">
        id,
        share_id,
        owner_seller_nick,
        owner_seller_id,
        owner_store_id,
        owner_app_name,
        owner_mall_name,
        target_seller_nick,
        target_seller_id,
        target_store_id,
        target_app_name,
        target_mall_name,
        status,
        provider,
        cp_code,
        cp_type,
        branch_code,
        segment_code,
        shipp_address_province,
        shipp_address_city,
        shipp_address_district,
        shipp_address_detail,
        shipp_address_md5,
        share_num,
        used_num,
        cancel_num,
        brand_code,
        share_type,
        share_memo,
        proxy_seller_id,
        proxy_seller_nick,
        proxy_app_name,
        proxy_store_id,
        proxy_mall_name,
        branch_name,
        salesman,
        gmt_create,
        gmt_modified,
        bill_version
    </sql>

    <!--新增所有列-->
    <insert id="insert" keyProperty="id" useGeneratedKeys="true">
        insert into
        <include refid="tablename"/>(share_id, owner_seller_nick, owner_seller_id, owner_store_id,
        owner_app_name, target_seller_nick, target_seller_id,
        target_store_id, target_app_name, status, provider, cp_code,
        cp_type, branch_code, segment_code, shipp_address_province, shipp_address_city, shipp_address_district,
        shipp_address_detail, shipp_address_md5, share_num, used_num, brand_code, share_type, share_memo,
        proxy_seller_id, proxy_seller_nick, proxy_app_name, proxy_store_id, branch_name, salesman, cancel_num,
        owner_mall_name, target_mall_name, proxy_mall_name, bill_version)
        values (#{shareId}, #{ownerSellerNick}, #{ownerSellerId}, #{ownerStoreId}, #{ownerAppName}, #{targetSellerNick},
        #{targetSellerId}, #{targetStoreId}, #{targetAppName}, #{status}, #{provider}, #{cpCode}, #{cpType},
        #{branchCode}, #{segmentCode}, #{shippAddressProvince}, #{shippAddressCity}, #{shippAddressDistrict},
        #{shippAddressDetail}, #{shippAddressMd5}, #{shareNum}, #{usedNum}, #{brandCode}, #{shareType}, #{shareMemo},
        #{proxySellerId}, #{proxySellerNick}, #{proxyAppName}, #{proxyStoreId}, #{branchName}, #{salesman}, #{cancelNum},
        #{ownerMallName}, #{targetMallName}, #{proxyMallName}, #{billVersion})
    </insert>

    <!--通过共享id删除-->
    <delete id="deleteByShareId">
        delete from
        <include refid="tablename"/>
        where share_id = #{shareId}
    </delete>

    <delete id="deleteByShareIds">
        delete from
        <include refid="tablename"/>
        where share_id in
        <foreach collection="list" index="index" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
    </delete>

    <select id="queryByOwnerAndTargetUser" resultMap="ElefaceSharingRelationMap">
        select
        <include refid="fields"/>
        from
        <include refid="tablename"/>
        where owner_seller_id = #{ownerSellerId}
        and owner_store_id = #{ownerStoreId}
        and owner_app_name = #{ownerAppName}
        and target_seller_id = #{targetSellerId}
        and target_store_id = #{targetStoreId}
        and target_app_name = #{targetAppName}
    </select>

    <update id="addUsedNum">
        update
        <include refid="tablename"/>
        set used_num=used_num + #{useNum}
        where share_id = #{shareId}
    </update>

    <update id="addShareNum">
        update
        <include refid="tablename"/>
        set share_num=share_num + #{topUpNum}
        where share_id = #{shareId}
    </update>

     <update id="updateShareNum">
        update
        <include refid="tablename"/>
        set share_num= #{shareNum}
        where share_id = #{shareId}
    </update>

    <update id="updateStatus">
        update
        <include refid="tablename"/>
        set status = #{status}
        where share_id = #{shareId}
    </update>

    <update id="updateStatusAndShareNum">
        update
        <include refid="tablename"/>
        set status = #{status}
        <if test="shareNum != null">
            ,share_num = #{shareNum}
        </if>
        where share_id = #{shareId}
    </update>


    <update id="updateShareMemoOrSalesmanByShareId">
        update
        <include refid="tablename"/>
        set share_memo = #{shareMemo},
        salesman = #{salesman}
        where share_id = #{shareId}
    </update>

    <select id="queryByOwnerUser" resultMap="ElefaceSharingRelationMap">
        select
        <include refid="fields"/>
        from
        <include refid="tablename"/>
        where owner_seller_id = #{ownerSellerId} and owner_store_id = #{ownerStoreId} and owner_app_name =
        #{ownerAppName}
    </select>

    <select id="queryByOwnerUserAndStatus" resultMap="ElefaceSharingRelationMap">
        select
        <include refid="fields"/>
        from
        <include refid="tablename"/>
        where owner_seller_id = #{ownerSellerId} and owner_store_id = #{ownerStoreId} and owner_app_name =
        #{ownerAppName} and status = #{status}
    </select>

    <select id="queryByTargetUser" resultMap="ElefaceSharingRelationMap">
        select
        <include refid="fields"/>
        from
        <include refid="tablename"/>
        where target_seller_id = #{targetSellerId} and target_store_id = #{targetStoreId} and target_app_name =
        #{targetAppName}
    </select>

    <select id="queryByTargetUserAndStatus" resultMap="ElefaceSharingRelationMap">
        select
        <include refid="fields"/>
        from
        <include refid="tablename"/>
        where target_seller_id = #{targetSellerId} and target_store_id = #{targetStoreId} and target_app_name =
        #{targetAppName} and status = #{status}
    </select>

    <select id="queryByShareId" resultMap="ElefaceSharingRelationMap">
        select
        <include refid="fields"/>
        from
        <include refid="tablename"/>
        where share_id = #{shareId}
    </select>

    <select id="queryByShareIds" resultMap="ElefaceSharingRelationMap">
        select
        <include refid="fields"/>
        from
        <include refid="tablename"/>
        where share_id in
        <foreach collection="list" index="index" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
    </select>

    <select id="queryByShareIdsAndStatusList" resultMap="ElefaceSharingRelationMap">
        select
        <include refid="fields"/>
        from
        <include refid="tablename"/>
        where share_id in
        <foreach collection="shareIds" index="index" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
        <if test="statusList != null and statusList.size() > 0">
            and status in
            <foreach collection="statusList" index="index" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
    </select>


    <select id="searchByTargetUserAndQuery" resultMap="ElefaceSharingRelationMap">
        select
        <include refid="fields"/>
        from
        <include refid="tablename"/>
        where target_seller_id = #{targetSellerId} and target_store_id = #{targetStoreId} and target_app_name = #{targetAppName}
        <if test="queryBo.shareIdList != null and queryBo.shareIdList.size() > 0">
            and share_id in
            <foreach collection="queryBo.shareIdList" index="index" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
        <if test="queryBo.shareType != null">
            and share_type = #{queryBo.shareType}
        </if>
        <if test="queryBo.status != null">
            and status = #{queryBo.status}
        </if>
        <if test="queryBo.elefaceTargetStoreId != null">
            and target_store_id = #{queryBo.elefaceTargetStoreId}
        </if>
        <if test="queryBo.ownerStoreId != null">
            and owner_store_id = #{queryBo.ownerStoreId}
        </if>
        <if test="queryBo.ownerSellerId != null">
            and owner_seller_id = #{queryBo.ownerSellerId}
        </if>
        <if test="queryBo.ownerAppName != null">
            and owner_app_name = #{queryBo.ownerAppName}
        </if>
        <if test="queryBo.cpCodeList != null and queryBo.cpCodeList.size() > 0">
            and cp_code in
            <foreach collection="queryBo.cpCodeList" index="index" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
        <choose>
            <when test="queryBo.limit != null">
                limit #{queryBo.limit}
            </when>
            <otherwise>
                limit 10000
            </otherwise>
        </choose>
    </select>


    <select id="searchByOwnerUserAndQuery" resultMap="ElefaceSharingRelationMap">
        select
        <include refid="fields"/>
        from
        <include refid="tablename"/>
        where owner_seller_id = #{sellerId} and owner_store_id = #{storeId} and owner_app_name = #{appName}
        <if test="queryBo.shareIdList != null and queryBo.shareIdList.size() > 0">
            and share_id in
            <foreach collection="queryBo.shareIdList" index="index" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
        <if test="queryBo.shareType != null">
            and share_type = #{queryBo.shareType}
        </if>
        <if test="queryBo.status != null">
            and status = #{queryBo.status}
        </if>
        <if test="queryBo.elefaceTargetStoreId != null">
            and target_store_id = #{queryBo.elefaceTargetStoreId}
        </if>
        <if test="queryBo.ownerStoreId != null">
            and owner_store_id = #{queryBo.ownerStoreId}
        </if>
        <if test="queryBo.ownerSellerId != null">
            and owner_seller_id = #{queryBo.ownerSellerId}
        </if>
        <if test="queryBo.ownerAppName != null">
            and owner_app_name = #{queryBo.ownerAppName}
        </if>
        <if test="queryBo.cpCodeList != null and queryBo.cpCodeList.size() > 0">
            and cp_code in
            <foreach collection="queryBo.cpCodeList" index="index" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
        <choose>
            <when test="queryBo.limit != null">
                limit #{queryBo.limit}
            </when>
            <otherwise>
                limit 10000
            </otherwise>
        </choose>
    </select>

    <select id="searchByProxyUserAndQuery" resultMap="ElefaceSharingRelationMap">
        select
        <include refid="fields"/>
        from
        <include refid="tablename"/>
        <if test="queryBo.fuzzySearchStr != null">
            FORCE INDEX (IDX_proxy_sellerid_storeid_appname)
        </if>
        where proxy_seller_id = #{sellerId} and proxy_store_id = #{storeId} and proxy_app_name = #{appName}
        <include refid="queryBoSearchSql"/>
        order by id desc
        <choose>
            <when test="queryBo.limit != null and queryBo.offset != null">
                limit #{queryBo.offset}, #{queryBo.limit}
            </when>
            <otherwise>
                limit 10000
            </otherwise>
        </choose>
    </select>

    <update id="updateShareTypeByShareId">
        update
        <include refid="tablename"/>
        set share_type = #{shareType}
        where share_id in
        <foreach collection="updateShareIdList" index="index" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
    </update>

    <select id="countByProxyUserAndQuery" resultType="integer">
        select
        count(*)
        from
        <include refid="tablename"/>
        <if test="queryBo.fuzzySearchStr != null">
            FORCE INDEX (IDX_proxy_sellerid_storeid_appname)
        </if>
        where proxy_seller_id = #{sellerId} and proxy_store_id = #{storeId} and proxy_app_name = #{appName}
        <include refid="queryBoSearchSql"/>
    </select>

    <select id="countByProxyRelation" resultMap="ElefaceSharingRelationMap">
        SELECT
        sum( used_num ) as used_num,
        sum( cancel_num ) as cancel_num,
        cp_code,
        cp_type,
        branch_code,
        segment_code,
        shipp_address_md5,
        brand_code,
        proxy_seller_id,
        proxy_seller_nick,
        proxy_app_name,
        proxy_store_id,
        owner_seller_nick,
        owner_seller_id,
        owner_store_id,
        owner_app_name
        FROM
        <include refid="tablename"/>
        where proxy_seller_id = #{sellerId} and proxy_store_id = #{storeId} and proxy_app_name = #{appName}
        and status = 1
        and share_type = 1
        GROUP BY
        cp_code,
        cp_type,
        branch_code,
        segment_code,
        shipp_address_md5,
        brand_code,
        proxy_seller_id,
        proxy_seller_nick,
        proxy_app_name,
        proxy_store_id,
        owner_seller_nick,
        owner_seller_id,
        owner_store_id,
        owner_app_name
    </select>

    <sql id="queryBoSearchSql">
        <if test="queryBo.shareIdList != null and queryBo.shareIdList.size() > 0">
            and share_id in
            <foreach collection="queryBo.shareIdList" index="index" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
        <if test="queryBo.shareType != null">
            and share_type = #{queryBo.shareType}
        </if>
        <if test="queryBo.status != null">
            and status = #{queryBo.status}
        </if>
        <if test="queryBo.elefaceTargetStoreId != null">
            and target_store_id = #{queryBo.elefaceTargetStoreId}
        </if>
        <if test="queryBo.ownerStoreId != null">
            and owner_store_id = #{queryBo.ownerStoreId}
        </if>
        <if test="queryBo.ownerSellerId != null">
            and owner_seller_id = #{queryBo.ownerSellerId}
        </if>
        <if test="queryBo.ownerAppName != null">
            and owner_app_name = #{queryBo.ownerAppName}
        </if>
        <if test="queryBo.provider != null">
            and provider = #{queryBo.provider}
        </if>
        <if test="queryBo.cpCodeList != null and queryBo.cpCodeList.size() > 0">
            and cp_code in
            <foreach collection="queryBo.cpCodeList" index="index" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
        <if test="queryBo.shippAddressMd5 != null">
            and shipp_address_md5 = #{queryBo.shippAddressMd5}
        </if>
        <if test="queryBo.branchName != null">
            and branch_name = #{queryBo.branchName}
        </if>
        <if test="queryBo.salesman != null">
            and salesman = #{queryBo.salesman}
        </if>
        <if test="queryBo.shareMemo != null">
            and share_memo like CONCAT('%',#{queryBo.shareMemo, jdbcType=VARCHAR},'%')
        </if>
        <if test="queryBo.ownerSearchStr != null">
            and (owner_mall_name like CONCAT('%',#{queryBo.ownerSearchStr, jdbcType=VARCHAR},'%')
            or owner_seller_nick like CONCAT('%',#{queryBo.ownerSearchStr, jdbcType=VARCHAR},'%')
            )
        </if>
        <if test="queryBo.targetSearchStr != null">
            and (target_mall_name like CONCAT('%',#{queryBo.targetSearchStr, jdbcType=VARCHAR},'%')
            or target_seller_nick like CONCAT('%',#{queryBo.targetSearchStr, jdbcType=VARCHAR},'%')
            )
        </if>
        <if test="queryBo.proxySearchStr != null">
            and (proxy_mall_name like CONCAT('%',#{queryBo.proxySearchStr, jdbcType=VARCHAR},'%')
            or proxy_seller_nick like CONCAT('%',#{queryBo.proxySearchStr, jdbcType=VARCHAR},'%')
            )
        </if>
        <if test="queryBo.fuzzySearchStr != null">
            and (proxy_mall_name like CONCAT('%',#{queryBo.fuzzySearchStr, jdbcType=VARCHAR},'%')
            or proxy_seller_nick like CONCAT('%',#{queryBo.fuzzySearchStr, jdbcType=VARCHAR},'%')
            or owner_mall_name like CONCAT('%',#{queryBo.fuzzySearchStr, jdbcType=VARCHAR},'%')
            or owner_seller_nick like CONCAT('%',#{queryBo.fuzzySearchStr, jdbcType=VARCHAR},'%')
            or target_mall_name like CONCAT('%',#{queryBo.fuzzySearchStr, jdbcType=VARCHAR},'%')
            or target_seller_nick like CONCAT('%',#{queryBo.fuzzySearchStr, jdbcType=VARCHAR},'%')
            or share_memo like CONCAT('%',#{queryBo.fuzzySearchStr, jdbcType=VARCHAR},'%')
            )
        </if>
    </sql>

    <update id="updateShareNumAndCancelNum">
        update
        <include refid="tablename"/>
        set share_num= #{shareNum},
        cancel_num = #{cancelNum}
        where share_id = #{shareId}
    </update>

    <select id="countByProxyUserAndGroupByTarget" resultType="integer">
        select
        count(*)
        from (
        select target_seller_nick,
        target_seller_id,
        target_store_id,
        target_app_name,
        target_mall_name
        from <include refid="tablename"/>
        <if test="queryBo.fuzzySearchStr != null">
            FORCE INDEX (IDX_proxy_sellerid_storeid_appname)
        </if>
        where proxy_seller_id = #{sellerId} and proxy_store_id = #{storeId} and proxy_app_name = #{appName}
        <include refid="queryBoSearchSql"/>
        group by
        target_seller_nick,
        target_seller_id,
        target_store_id,
        target_app_name,
        target_mall_name
        ) t
    </select>


    <select id="searchByProxyUserAndGroupByTarget" resultType="cn.loveapp.print.api.dto.TargetPrintUserInfo">
        select
        target_seller_nick as sellerNick,
        target_seller_id as sellerId,
        target_store_id as storeId,
        target_app_name as appName,
        target_mall_name as mallName
        from (
        select target_seller_nick,
        target_seller_id,
        target_store_id,
        target_app_name,
        target_mall_name
        from <include refid="tablename"/>
        <if test="queryBo.fuzzySearchStr != null">
            FORCE INDEX (IDX_proxy_sellerid_storeid_appname)
        </if>
        where proxy_seller_id = #{sellerId} and proxy_store_id = #{storeId} and proxy_app_name = #{appName}
        <include refid="queryBoSearchSql"/>
        group by
        target_seller_nick,
        target_seller_id,
        target_store_id,
        target_app_name,
        target_mall_name
        ) t
        <choose>
            <when test="queryBo.limit != null and queryBo.offset != null">
                limit #{queryBo.offset}, #{queryBo.limit}
            </when>
            <otherwise>
                limit 10000
            </otherwise>
        </choose>
    </select>

</mapper>
