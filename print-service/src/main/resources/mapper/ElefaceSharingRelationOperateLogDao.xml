<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.loveapp.print.service.dao.print.ElefaceSharingRelationOperateLogDao">

    <resultMap type="cn.loveapp.print.service.entity.ElefaceSharingRelationOperateLog"
               id="ElefaceSharingRelationOperateMap">
        <result property="id" column="id"/>
        <result property="shareId" column="share_id"/>
        <result property="shareMemo" column="share_memo"/>
        <result property="targetSellerNick" column="target_seller_nick"/>
        <result property="targetSellerId" column="target_seller_id"/>
        <result property="targetStoreId" column="target_store_id"/>
        <result property="targetAppName" column="target_app_name"/>
        <result property="lastShareNum" column="last_share_num"/>
        <result property="newShareNum" column="new_share_num"/>
        <result property="operateTerminal" column="operate_terminal"/>
        <result property="operator" column="OPERATOR"/>
        <result property="operateType" column="operate_type"/>
        <result property="operatorTime" column="operator_time"/>
        <result property="gmtCreate" column="gmt_create"/>
    </resultMap>

    <sql id="tablename">eleface_sharing_relation_operate_log</sql>

    <sql id="fields">
        id
        , share_id, target_seller_id, target_seller_nick, target_store_id, target_app_name, share_memo, operate_type,
        last_share_num, new_share_num, operate_terminal, operator, operator_time, gmt_create, gmt_modified
    </sql>

    <!--新增所有列-->
    <insert id="insert" keyProperty="id" useGeneratedKeys="true">
        insert into
        <include refid="tablename"/>(share_id, target_seller_id, target_seller_nick, target_store_id, target_app_name,
        share_memo,
        last_share_num, new_share_num, operate_terminal, operator, operator_time, operate_type)
        VALUES (#{shareId}, #{targetSellerId}, #{targetSellerNick}, #{targetStoreId}, #{targetAppName}, #{shareMemo},
        #{lastShareNum}, #{newShareNum}, #{operateTerminal}, #{operator}, #{operatorTime, jdbcType=TIMESTAMP},
        #{operateType})
    </insert>


    <select id="searchListByQuery" resultMap="ElefaceSharingRelationOperateMap">
        select
        <include refid="fields"/>
        from
        <include refid="tablename"/>
        where share_id in
        <foreach collection="queryBo.shareIdList" index="index" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        <if test="queryBo.operateTimeStart != null">
            and operator_time &gt; #{queryBo.operateTimeStart}
        </if>
        <if test="queryBo.operateTimeEnd != null">
            and operator_time &lt; #{queryBo.operateTimeEnd}
        </if>
        <if test="queryBo.operateTypeList != null and queryBo.operateTypeList.size() > 0">
            and operate_type in
            <foreach collection="queryBo.operateTypeList" index="index" item="operateType" open="(" separator="," close=")">
                #{operateType}
            </foreach>
        </if>
        order by operator_time desc
        <choose>
            <when test="queryBo.limit != null and queryBo.offset != null">
                limit #{queryBo.offset}, #{queryBo.limit}
            </when>
            <otherwise>
                limit 10000
            </otherwise>
        </choose>
    </select>

    <select id="countByQuery" resultType="integer">
        select
        count(*)
        from
        <include refid="tablename"/>
        where share_id in
        <foreach collection="queryBo.shareIdList" index="index" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        <if test="queryBo.operateTimeStart != null">
            and operator_time &gt; #{queryBo.operateTimeStart}
        </if>
        <if test="queryBo.operateTimeEnd != null">
            and operator_time &lt; #{queryBo.operateTimeEnd}
        </if>
        <if test="queryBo.operateTypeList != null and queryBo.operateTypeList.size() > 0">
            and operate_type in
            <foreach collection="queryBo.operateTypeList" index="index" item="operateType" open="(" separator="," close=")">
                #{operateType}
            </foreach>
        </if>
    </select>

</mapper>
