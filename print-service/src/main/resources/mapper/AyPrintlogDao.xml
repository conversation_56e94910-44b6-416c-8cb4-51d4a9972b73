<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.loveapp.print.service.dao.newprint.AyPrintlogDao">

    <resultMap type="cn.loveapp.print.common.entity.AyPrintlog" id="AyPrintlog">
        <result property="id" column="id"/>
        <result property="appName" column="app_name"/>
        <result property="sellerId" column="seller_id"/>
        <result property="storeId" column="store_id"/>
        <result property="sellerNick" column="seller_nick"/>
        <result property="tid" column="tid"/>
        <result property="tradeType" column="trade_type"/>
        <result property="mergeTid" column="merge_tid"/>
        <result property="serial" column="serial"/>
        <result property="buyerNick" column="buyer_nick"/>
        <result property="isSplit" column="is_split"/>
        <result property="oids" column="oids"/>
        <result property="printType" column="print_type"/>
        <result property="printId" column="print_id"/>
        <result property="printer" column="printer"/>
        <result property="printCount" column="print_count"/>
        <result property="operator" column="operator"/>
        <result property="operatorStoreId" column="operator_store_id"/>
        <result property="operateTerminal" column="operate_terminal"/>
        <result property="printTime" column="print_time"/>
        <result property="elefaceProvider" column="eleface_provider"/>
        <result property="cpCode" column="cp_code"/>
        <result property="logisticsCompany" column="logistics_company"/>
        <result property="waybillCode" column="waybill_code"/>
        <result property="isChild" column="is_child"/>
        <result property="childWaybillCode" column="child_waybill_code"/>
        <result property="waybillIsCancel" column="waybill_is_cancel"/>
        <result property="receiverMobile" column="receiver_mobile"/>
        <result property="receiverName" column="receiver_name"/>
        <result property="receiverPhone" column="receiver_phone"/>
        <result property="receiverCity" column="receiver_city"/>
        <result property="receiverDetail" column="receiver_detail"/>
        <result property="receiverDistrict" column="receiver_district"/>
        <result property="receiverProvince" column="receiver_province"/>
        <result property="receiverTown" column="receiver_town"/>
        <result property="receiverZip" column="receiver_zip"/>
        <result property="logisticsTemplateName" column="logistics_template_name"/>
        <result property="ayDistribute" column="ay_distribute"/>
        <result property="batchId" column="batch_id"/>
        <result property="numbersInBatch" column="numbers_in_batch"/>
        <result property="batchTotals" column="batch_totals"/>
        <result property="senderName" column="sender_name"/>
        <result property="senderMobile" column="sender_mobile"/>
        <result property="senderPhone" column="sender_phone"/>
        <result property="senderCity" column="sender_city"/>
        <result property="senderDetail" column="sender_detail"/>
        <result property="senderDistrict" column="sender_district"/>
        <result property="senderProvince" column="sender_province"/>
        <result property="senderTown" column="sender_town"/>
        <result property="senderZip" column="sender_zip"/>
        <result property="gmtCreate" column="gmt_create"/>
        <result property="gmtModified" column="gmt_modified"/>
    </resultMap>

    <resultMap type="cn.loveapp.print.service.dto.GroupedElefacePrintLogDTO" id="GroupedElefacePrintLogDTO">
        <result property="cpCode" column="cp_code"/>
        <result property="logisticsCompany" column="logistics_company"/>
        <result property="receiverProvince" column="receiver_province"/>
        <result property="totalPrint" column="total_print"/>
        <result property="totalCancel" column="total_cancel"/>
        <result property="elefaceProvider" column="eleface_provider"/>
    </resultMap>

    <sql id="fileds">
        id, app_name, seller_id, store_id, seller_nick, tid, trade_type, merge_tid, serial, buyer_nick, oids, is_split,
        print_type, print_id, printer, print_count, operator, operator_store_id, operate_terminal, print_time,
        eleface_provider, cp_code, logistics_company, waybill_code, is_child, child_waybill_code, waybill_is_cancel, receiver_mobile, receiver_name, receiver_phone,
        receiver_city, receiver_detail, receiver_district, receiver_province, receiver_town,
        receiver_zip, gmt_create, gmt_modified, ay_distribute,logistics_template_name, batch_id, numbers_in_batch, batch_totals,
        sender_name, sender_mobile, sender_phone, sender_city, sender_detail, sender_district, sender_province,
        sender_town, sender_zip
    </sql>

    <sql id="customFileds">
        <choose>
            <when test="queryDTO.fields != null and queryDTO.fields.size() > 0">
                <foreach collection="queryDTO.fields" item="item" open="" separator="," close="">
                    ${item}
                </foreach>
            </when>
            <otherwise>
                <include refid="fileds"/>
            </otherwise>
        </choose>
    </sql>


    <insert id="insert">
        insert into ay_printlog(app_name, seller_id, store_id, seller_nick, tid, trade_type, merge_tid, serial,
                                buyer_nick, oids, is_split,
                                print_type, print_id, printer, print_count, operator, operator_store_id,
                                operate_terminal, print_time,
                                eleface_provider, cp_code, logistics_company, waybill_code, is_child,
                                child_waybill_code, receiver_mobile, receiver_name, receiver_phone,
                                receiver_city, receiver_detail, receiver_district, receiver_province, receiver_town,
                                receiver_zip, ay_distribute, logistics_template_name, batch_id, numbers_in_batch,
                                batch_totals, sender_name, sender_mobile, sender_phone, sender_city, sender_detail,
                                sender_district, sender_province,sender_town, sender_zip)
        values (#{appName}, #{sellerId}, #{storeId}, #{sellerNick}, #{tid}, #{tradeType}, #{mergeTid}, #{serial},
                #{buyerNick}, #{oids}, #{isSplit},
                #{printType}, #{printId}, #{printer}, #{printCount}, #{operator}, #{operatorStoreId},
                #{operateTerminal}, #{printTime},
                #{elefaceProvider}, #{cpCode}, #{logisticsCompany}, #{waybillCode}, #{isChild}, #{childWaybillCode},
                #{receiverMobile}, #{receiverName}, #{receiverPhone},
                #{receiverCity}, #{receiverDetail}, #{receiverDistrict}, #{receiverProvince}, #{receiverTown},
                #{receiverZip}, #{ayDistribute}, #{logisticsTemplateName}, #{batchId}, #{numbersInBatch},
                #{batchTotals}, #{senderName}, #{senderMobile}, #{senderPhone}, #{senderCity}, #{senderDetail},
                #{senderDistrict}, #{senderProvince}, #{senderTown}, #{senderZip})
    </insert>

    <insert id="insertBatch" useGeneratedKeys="true" keyProperty="id">
        insert into ay_printlog(app_name, seller_id, store_id, seller_nick, tid, trade_type, merge_tid, serial,
        buyer_nick, oids, is_split, print_type, print_id, printer, print_count, operator, operator_store_id,
        operate_terminal,print_time,
        eleface_provider, cp_code, logistics_company, waybill_code, is_child, child_waybill_code, receiver_mobile,
        receiver_name,receiver_phone,receiver_city, receiver_detail, receiver_district, receiver_province,
        receiver_town, receiver_zip, ay_distribute, logistics_template_name, batch_id, numbers_in_batch, batch_totals,
        sender_name, sender_mobile, sender_phone, sender_city, sender_detail, sender_district, sender_province,
        sender_town, sender_zip)
        values
        <foreach collection="list" index="index" item="item" separator=",">
            (#{item.appName}, #{item.sellerId}, #{item.storeId}, #{item.sellerNick}, #{item.tid}, #{item.tradeType},
            #{item.mergeTid}, #{item.serial}, #{item.buyerNick}, #{item.oids}, #{item.isSplit},
            #{item.printType}, #{item.printId}, #{item.printer}, #{item.printCount}, #{item.operator},
            #{item.operatorStoreId},
            #{item.operateTerminal}, #{item.printTime},
            #{item.elefaceProvider}, #{item.cpCode}, #{item.logisticsCompany}, #{item.waybillCode}, #{item.isChild},
            #{item.childWaybillCode},
            #{item.receiverMobile}, #{item.receiverName},
            #{item.receiverPhone},
            #{item.receiverCity}, #{item.receiverDetail}, #{item.receiverDistrict}, #{item.receiverProvince},
            #{item.receiverTown}, #{item.receiverZip}, #{item.ayDistribute}, #{item.logisticsTemplateName},
            #{item.batchId}, #{item.numbersInBatch}, #{item.batchTotals}, #{item.senderName}, #{item.senderMobile},
            #{item.senderPhone}, #{item.senderCity}, #{item.senderDetail},#{item.senderDistrict},
            #{item.senderProvince}, #{item.senderTown}, #{item.senderZip})
        </foreach>
    </insert>

    <select id="queryByTidsAndPrintType" resultMap="AyPrintlog">
        select
        <include refid="fileds"/>
        from ay_printlog
        where tid in
        <foreach collection="tids" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        and store_id = #{storeId}
        and app_name = #{appName}
        and print_type = #{printType}
        and seller_id = #{sellerId}
    </select>

    <select id="queryLogisticsListOrderByPrintTime" resultMap="AyPrintlog">
        <choose>
            <when test="queryDTO.isDistinctWayBill != null and queryDTO.isDistinctWayBill">
                <!-- 重复打印订单时去重，取最新的一条数据信息，当使用人数较多时，这里可能产生性能问题，修改方案可改为：分组时随便取一条数据 -->
                SELECT <include refid="customFileds"/> FROM ay_printlog log
                join
                    (
                        select
                        max(id) maxid
                        from ay_printlog
                        <include refid="printLogCommonQuerySql" />
                        GROUP BY tid, waybill_code, child_waybill_code
                        <include refid="printLogCommonOrderBySql" />
                        limit #{queryDTO.offset}, #{queryDTO.limit}
                    ) tb
                on log.id = tb.maxid
            </when>
            <otherwise>
                select
                <include refid="customFileds"/>
                from ay_printlog
                <include refid="printLogCommonQuerySql" />
                <include refid="printLogCommonOrderBySql" />
                limit #{queryDTO.offset}, #{queryDTO.limit}
            </otherwise>
        </choose>
    </select>


    <select id="queryLogisticsCount" resultType="long">
        <choose>
            <when test="queryDTO.isDistinctWayBill != null and queryDTO.isDistinctWayBill">
                select COUNT(*) FROM
                (
                    select tid FROM ay_printlog
                    <include refid="printLogCommonQuerySql" />
                    GROUP BY tid, waybill_code, child_waybill_code
                ) tb
            </when>
            <otherwise>
                select count(*) from ay_printlog
                <include refid="printLogCommonQuerySql" />
            </otherwise>
        </choose>
    </select>

    <update id="updateWaybillIsCancel">
        update ay_printlog
        set waybill_is_cancel = #{isCancel}
        where waybill_code = #{waybillCode}
          and cp_code = #{cpCode}
          and seller_id = #{sellerId}
          and store_id = #{storeId}
          and app_name = #{appName}
    </update>

    <select id="queryBySellerIdAndWaybillCode" resultMap="AyPrintlog">
        select
        <include refid="fileds"/>
        from ay_printlog
        where waybill_code = #{waybillCode}
          and cp_code = #{cpCode}
          and seller_id = #{sellerId}
          and store_id = #{storeId}
          and app_name = #{appName}
    </select>

    <select id="queryRepetitionPrintWaybill" resultMap="AyPrintlog">
        select
        <include refid="fileds"/>
        from ay_printlog
        where seller_id = #{sellerId}
        and store_id = #{storeId}
        and app_name = #{appName}
        and print_type = #{printType}
        and tid = #{tid}
        <if test="waybillCode != null and waybillCode != ''">
            and waybill_code = #{waybillCode}
        </if>
        <if test="childWaybillCode != null and childWaybillCode != ''">
            and child_waybill_code = #{childWaybillCode}
        </if>
    </select>

    <select id="queryByIds" resultMap="AyPrintlog">
        select
        <include refid="fileds"/>
        from ay_printlog
        where seller_id = #{sellerId} and
        id in
        <foreach collection="ids" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>


    <select id="groupElefacePrintLogByCpCodeAndReceiverProvince" resultMap="GroupedElefacePrintLogDTO">
        SELECT cp_code,
        logistics_company,
        receiver_province,
        eleface_provider,
        COUNT(waybill_code) as total_print,
        SUM(waybill_is_cancel) as total_cancel
        from (
        SELECT DISTINCT waybill_code,
        cp_code,
        logistics_company,
        waybill_is_cancel,
        eleface_provider,
        receiver_province
        from ay_printlog
        WHERE seller_id = #{sellerId}
        and store_id = #{storeId}
        and app_name = #{appName}
        and print_type = '${@cn.loveapp.print.common.constant.PrintTypeConstant@ELEFACE}'
        <if test="queryBo.elefaceProvider != null">
            and eleface_provider = #{queryBo.elefaceProvider}
        </if>
        <if test="queryBo.cpCodeList != null and queryBo.cpCodeList.size() > 0">
            and cp_code in
            <foreach collection="queryBo.cpCodeList" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        and receiver_province in
        <foreach collection="queryBo.receiverProvinceList" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        <if test="queryBo.startTime != null">
            and gmt_create &gt; #{queryBo.startTime}
        </if>
        <if test="queryBo.endTime != null">
            and gmt_create &lt; #{queryBo.endTime}
        </if>
        ) as table_distinct_waybillcode
        GROUP BY cp_code, receiver_province,eleface_provider
    </select>

    <select id="groupElefacePrintLogByCpCode" resultMap="GroupedElefacePrintLogDTO">
        SELECT cp_code,
        logistics_company,
        eleface_provider,
        COUNT(waybill_code) as total_print,
        SUM(waybill_is_cancel) as total_cancel
        from (
        SELECT DISTINCT waybill_code,
        cp_code,
        logistics_company,
        waybill_is_cancel,
        eleface_provider
        from ay_printlog
        WHERE seller_id = #{sellerId}
        and store_id = #{storeId}
        and app_name = #{appName}
        and print_type = '${@cn.loveapp.print.common.constant.PrintTypeConstant@ELEFACE}'
        <if test="queryBo.elefaceProvider != null">
            and eleface_provider = #{queryBo.elefaceProvider}
        </if>
        <if test="queryBo.cpCodeList != null and queryBo.cpCodeList.size() > 0">
            and cp_code in
            <foreach collection="queryBo.cpCodeList" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="queryBo.startTime != null">
            and gmt_create &gt; #{queryBo.startTime}
        </if>
        <if test="queryBo.endTime != null">
            and gmt_create &lt; #{queryBo.endTime}
        </if>
        ) as table_distinct_waybillcode
        GROUP BY cp_code,eleface_provider
    </select>

    <select id="queryHasPrintHistoryByTid" resultMap="AyPrintlog">
        select
        <include refid="fileds"/>
        from ay_printlog
        where tid = #{tid}
        <if test="printTypes != null and printTypes.size() > 0">
            and print_type in
            <foreach collection="printTypes" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        and seller_id = #{sellerId}
        and store_id = #{storeId}
        and app_name = #{appName}
        <if test="tradeType != null and tradeType != ''">
            and trade_type = #{tradeType}
        </if>
        limit 1
    </select>


    <select id="countPrintLogByPrintType" resultType="cn.loveapp.print.service.dto.GroupedPrintTypePrintLogCountDTO">
        select print_type printType, count(*) printCount
        from ay_printlog
        where seller_id = #{queryDTO.sellerId}
          and store_id = #{queryDTO.storeId}
          and app_name = #{queryDTO.appName}
          and print_type in
              ('${@cn.loveapp.print.common.constant.PrintTypeConstant@EXPRESS}',
               '${@cn.loveapp.print.common.constant.PrintTypeConstant@ELEFACE}',
               '${@cn.loveapp.print.common.constant.PrintTypeConstant@DELIVER}')
        <if test="queryDTO.startTime != null and queryDTO.endTime != null">
            and gmt_create between #{queryDTO.startTime} and #{queryDTO.endTime}
        </if>
        group by print_type
    </select>

    <select id="queryExistByPrintType" resultType="String">
        <foreach collection="printTypeList" separator="union all" item="item">
            select * from
            (
                select print_type as printType from ${tableName}
                where seller_id = #{sellerId}
                and store_id = #{storeId}
                and app_name =#{appName}
                and print_type = #{item}
                limit 1
            ) t
        </foreach>
    </select>

    <sql id="printLogCommonQuerySql">
        where seller_id = #{sellerId}
        and store_id = #{storeId}
        and app_name =#{appName}
        <choose>
            <when test="queryDTO.printType != null and queryDTO.printType != ''">
                and print_type = #{queryDTO.printType}
            </when>
            <otherwise>
                <choose>
                    <when test="queryDTO.printTypeList != null and queryDTO.printTypeList.size() > 0">
                        and print_type in
                        <foreach collection="queryDTO.printTypeList" open="(" separator="," close=")" item="item">
                            #{item}
                        </foreach>
                    </when>
                    <otherwise>
                        and print_type in
                        ( '${@cn.loveapp.print.common.constant.PrintTypeConstant@EXPRESS}' ,
                        '${@cn.loveapp.print.common.constant.PrintTypeConstant@ELEFACE}' )
                    </otherwise>
                </choose>
            </otherwise>
        </choose>
        <if test="queryDTO.elefaceProvider != null and queryDTO.elefaceProvider != ''">
            and eleface_provider = #{queryDTO.elefaceProvider}
        </if>
        <if test="queryDTO.tradeType != null and queryDTO.tradeType.size() > 0">
            and trade_type in
            <foreach collection="queryDTO.tradeType" open="(" separator="," close=")" item="item">
                #{item}
            </foreach>
        </if>
        <if test="queryDTO.receiverName != null and queryDTO.receiverName != ''">
            and receiver_name = #{queryDTO.receiverName}
        </if>
        <choose>
        <!--  当标识查询方式为是否是订单集合或手机号查询时，条件使用or拼接，其他情况下依然使用and拼接-->
            <when test="queryDTO.isTidListAndMobileQueryWithOR != null and queryDTO.isTidListAndMobileQueryWithOR">
                and (
                        <if test="queryDTO.receiverContact != null and queryDTO.receiverContact != ''">
                            ( receiver_phone = #{queryDTO.receiverContact} or receiver_mobile = #{queryDTO.receiverContact} )
                        </if>
                    or
                        <if test="queryDTO.tidList != null and queryDTO.tidList.size() > 0">
                            ( tid in
                            <foreach collection="queryDTO.tidList" open="(" separator="," close=")" item="item">
                                #{item}
                            </foreach>
                            )
                        </if>
                    )
            </when>
            <otherwise>
                <if test="queryDTO.receiverContact != null and queryDTO.receiverContact != ''">
                    and ( receiver_phone = #{queryDTO.receiverContact} or receiver_mobile = #{queryDTO.receiverContact} )
                </if>
                <if test="queryDTO.tidList != null and queryDTO.tidList.size() > 0">
                    and tid in
                    <foreach collection="queryDTO.tidList" open="(" separator="," close=")" item="item">
                        #{item}
                    </foreach>
                </if>
            </otherwise>
        </choose>
        <if test="queryDTO.receiverAddressKeyWord != null and queryDTO.receiverAddressKeyWord != ''">
            and receiver_detail like CONCAT('%',#{queryDTO.receiverAddressKeyWord, jdbcType=VARCHAR},'%')
        </if>
        <if test="queryDTO.waybillCodeList != null and queryDTO.waybillCodeList.size() > 0">
            and waybill_code in
            <foreach collection="queryDTO.waybillCodeList" open="(" separator="," close=")" item="item">
                #{item}
            </foreach>
        </if>
        <if test="queryDTO.childWaybillCode != null and queryDTO.childWaybillCode != ''">
            and child_waybill_code = #{queryDTO.childWaybillCode}
        </if>
        <if test="queryDTO.receiverProvinceList != null and queryDTO.receiverProvinceList.size() > 0">
            and receiver_province in
            <foreach collection="queryDTO.receiverProvinceList" open="(" separator="," close=")" item="item">
                #{item}
            </foreach>
        </if>
        <if test="queryDTO.receiverCityList != null and queryDTO.receiverCityList.size() > 0">
            and receiver_city in
            <foreach collection="queryDTO.receiverCityList" open="(" separator="," close=")" item="item">
                #{item}
            </foreach>
        </if>
        <if test="queryDTO.logisticsCompany != null and queryDTO.logisticsCompany.size() > 0">
            and logistics_company in
            <foreach collection="queryDTO.logisticsCompany" open="(" separator="," close=")" item="item">
                #{item}
            </foreach>
        </if>
        <if test="queryDTO.cpCodes != null and queryDTO.cpCodes.size() > 0">
            and cp_code in
            <foreach collection="queryDTO.cpCodes" open="(" separator="," close=")" item="item">
                #{item}
            </foreach>
        </if>
        <if test="queryDTO.startTime != null and queryDTO.endTime != null">
            and gmt_create between #{queryDTO.startTime} and #{queryDTO.endTime}
        </if>
        <if test="queryDTO.valueOfCancelWayBill != null">
            and waybill_is_cancel = #{queryDTO.valueOfCancelWayBill}
        </if>
    </sql>

    <sql id="printLogCommonOrderBySql">
        <choose>
            <when test="queryDTO.maxId != null">
                <if test="queryDTO.maxId != 0">
                    and id &lt; #{queryDTO.maxId}
                </if>
                ORDER BY id DESC
            </when>
            <otherwise>
                ORDER BY gmt_create DESC
            </otherwise>
        </choose>
    </sql>

</mapper>
