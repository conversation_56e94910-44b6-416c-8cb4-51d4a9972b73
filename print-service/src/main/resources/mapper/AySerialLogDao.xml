<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.loveapp.print.service.dao.newprint.AyTradeSerialLogDao">

    <resultMap type="cn.loveapp.print.common.entity.AySerialLog" id="AyTradeSerialLog">
        <result property="id" column="id"/>
        <result property="appName" column="app_name"/>
        <result property="sellerId" column="seller_id"/>
        <result property="storeId" column="store_id"/>
        <result property="sellerNick" column="seller_nick"/>
        <result property="tid" column="tid"/>
        <result property="tradeType" column="trade_type"/>
        <result property="mergeTid" column="merge_tid"/>
        <result property="oids" column="oids"/>
        <result property="tradeType" column="trade_type"/>
        <result property="indexNumber" column="index_number"/>
        <result property="serial" column="serial"/>
        <result property="isInvalid" column="is_invalid"/>
        <result property="createTime" column="create_time"/>
        <result property="operator" column="operator"/>
        <result property="operatorStoreId" column="operator_store_id"/>
        <result property="operateTerminal" column="operate_terminal"/>
        <result property="gmtCreate" column="gmt_create"/>
        <result property="gmtModified" column="gmt_modified"/>
    </resultMap>

    <sql id="fileds">
        id, app_name, seller_id, store_id, seller_nick, tid, oids, trade_type, merge_tid, index_number, serial, is_invalid, create_time, operator,
		operator_store_id, operate_terminal ,gmt_create, gmt_modified
    </sql>

    <insert id="insert">
        insert into ay_serial_log
        (app_name, seller_id, store_id, seller_nick, tid, trade_type, merge_tid, oids, index_number, serial,
         create_time,
         operator, operator_store_id, operate_terminal)
        values (#{appName}, #{sellerId}, #{storeId}, #{sellerNick}, #{tid}, #{tradeType}, #{mergeTid}, #{oids},
                #{indexNumber}, #{serial}, #{createTime},
                #{operator}, #{operatorStoreId}, #{operateTerminal})
    </insert>

    <insert id="batchInsert">
        insert into
        ay_serial_log
        (app_name, seller_id, store_id, seller_nick, tid, trade_type, oids, merge_tid, index_number, serial,
        create_time,operator, operator_store_id, operate_terminal)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.appName}, #{item.sellerId}, #{item.storeId}, #{item.sellerNick}, #{item.tid}, #{item.tradeType},
            #{item.oids}, #{item.mergeTid}, #{item.indexNumber}, #{item.serial},
            #{item.createTime}, #{item.operator}, #{item.operatorStoreId}, #{item.operateTerminal})
        </foreach>
    </insert>

    <select id="queryByTid" resultMap="AyTradeSerialLog">
        select
        <include refid="fileds"/>
        from ay_serial_log
        where seller_id = #{sellerId}
        and tid = #{tid}
        and store_id = #{storeId}
        and trade_type = #{tradeType}
        and app_name = #{appName}
        and is_invalid = 0
    </select>

    <select id="queryByTidList" resultMap="AyTradeSerialLog">
        select id,tid,oids,merge_tid,serial
        from ay_serial_log
        where seller_id = #{sellerId}
        and store_id = #{storeId}
        and app_name = #{appName}
        and is_invalid = 0
        <if test="tradeType != null">
            and trade_type = #{tradeType}
        </if>
        <if test="tidList != null and tidList.size() > 0">
            and tid in
            <foreach collection="tidList" open="(" separator="," close=")" item="item">
                #{item}
            </foreach>
        </if>
        and id > #{maxId}
        order by id
        limit #{pageSize}
    </select>

    <update id="updateIsInvalidByTids">
        update ay_serial_log
        set is_invalid = #{isInvalid}
        where seller_id = #{sellerId}
        and tid in
        <foreach collection="tids" open="(" separator="," close=")" item="item">
            #{item}
        </foreach>
        and store_id = #{storeId}
        and trade_type = #{tradeType}
        and app_name = #{appName}
    </update>

</mapper>
