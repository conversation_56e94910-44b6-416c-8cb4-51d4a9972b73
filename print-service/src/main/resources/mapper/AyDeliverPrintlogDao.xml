<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.loveapp.print.service.dao.newprint.AyDeliverPrintlogDao">

    <resultMap type="cn.loveapp.print.common.entity.AyDeliverPrintlog" id="AyDeliverPrintlog">
        <result property="id" column="id"/>
        <result property="appName" column="app_name"/>
        <result property="sellerId" column="seller_id"/>
        <result property="storeId" column="store_id"/>
        <result property="sellerNick" column="seller_nick"/>
        <result property="printer" column="printer"/>
        <result property="printCount" column="print_count"/>
        <result property="operator" column="operator"/>
        <result property="operatorStoreId" column="operator_store_id"/>
        <result property="operateTerminal" column="operate_terminal"/>
        <result property="printTime" column="print_time"/>
        <result property="receiverMobile" column="receiver_mobile"/>
        <result property="receiverName" column="receiver_name"/>
        <result property="receiverPhone" column="receiver_phone"/>
        <result property="receiverCity" column="receiver_city"/>
        <result property="receiverDetail" column="receiver_detail"/>
        <result property="receiverDistrict" column="receiver_district"/>
        <result property="receiverProvince" column="receiver_province"/>
        <result property="receiverTown" column="receiver_town"/>
        <result property="receiverZip" column="receiver_zip"/>
        <result property="printModule" column="print_module"/>
        <result property="printData" column="print_data"/>
        <result property="gmtCreate" column="gmt_create"/>
        <result property="gmtModified" column="gmt_modified"/>
    </resultMap>

    <sql id="fileds">
        id, app_name, seller_id, store_id, seller_nick, printer, print_count, operator, operator_store_id, operate_terminal, print_time, receiver_name,
                                receiver_mobile, receiver_phone, receiver_city, receiver_detail, receiver_district,
                                receiver_province, receiver_town, receiver_zip, print_module, print_data, gmt_create, gmt_modified
    </sql>

    <insert id="insert" useGeneratedKeys="true" keyProperty="id"
            parameterType="cn.loveapp.print.common.entity.AyDeliverPrintlog">
        insert into ay_deliver_printlog(app_name, seller_id, store_id, seller_nick, printer, print_count, operator,
                                        operator_store_id, operate_terminal, print_time, receiver_name,
                                        receiver_mobile, receiver_phone, receiver_city, receiver_detail,
                                        receiver_district,
                                        receiver_province, receiver_town, receiver_zip, print_module, print_data)
        values (#{appName}, #{sellerId}, #{storeId}, #{sellerNick}, #{printer}, #{printCount}, #{operator},
                #{operatorStoreId}, #{operateTerminal}, #{printTime}, #{receiverName},
                #{receiverMobile}, #{receiverPhone}, #{receiverCity}, #{receiverDetail}, #{receiverDistrict},
                #{receiverProvince}, #{receiverTown}, #{receiverZip}, #{printModule}, #{printData})
    </insert>

</mapper>
