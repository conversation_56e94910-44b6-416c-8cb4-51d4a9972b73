<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.loveapp.print.service.transfer.dao.ExpiredWaybillShareRelationshipDao">

    <sql id="tablename">expired_waybill_share_relationship</sql>

    <!--新增所有列-->
    <insert id="insert" keyProperty="id" useGeneratedKeys="true">
        insert into
        <include refid="tablename"/>(owner_nick, owner_seller_id, shared_nick, shared_seller_id, version, source_id, share_id, reason)
        values (#{ownerNick}, #{ownerSellerId}, #{sharedNick}, #{sharedSellerId}, #{version}, #{sourceId}, #{shareId}, #{reason})
        ON DUPLICATE KEY UPDATE
        owner_nick = VALUES(owner_nick),owner_seller_id = VALUES(owner_seller_id),shared_nick = VALUES(shared_nick),shared_seller_id = VALUES(shared_seller_id),
        version = VALUES(version),source_id = VALUES(source_id),share_id = VALUES(share_id),reason = VALUES(reason)
    </insert>

</mapper>
