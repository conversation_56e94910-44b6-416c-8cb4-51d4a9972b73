<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.loveapp.print.service.dao.print.DtbInfoNewDao">

    <select id="queryDtbInfo" resultType="java.lang.String">
        select dtb_info
        from dtb_info_new
        where sprovince = #{senderProvince}
          and scity = #{senderCity}
          and rprovince = #{receiverProvince}
          and rcity = #{receiverCity}
          and rdistrict = #{receiverDistrict}
          and cp_code = #{cpCode}
        limit 1
    </select>

</mapper>
