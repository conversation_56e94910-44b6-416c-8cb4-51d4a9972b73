<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.loveapp.print.service.dao.newprint.AyElefacePrintlogDao">

    <resultMap type="cn.loveapp.print.common.entity.AyElefacePrintlog" id="AyElefacePrintlog">
        <result property="id" column="id"/>
        <result property="appName" column="app_name"/>
        <result property="sellerId" column="seller_id"/>
        <result property="storeId" column="store_id"/>
        <result property="sellerNick" column="seller_nick"/>
        <result property="printer" column="printer"/>
        <result property="printCount" column="print_count"/>
        <result property="operator" column="operator"/>
        <result property="operatorStoreId" column="operator_store_id"/>
        <result property="operateTerminal" column="operate_terminal"/>
        <result property="printTime" column="print_time"/>
        <result property="provider" column="provider"/>
        <result property="cpCode" column="cp_code"/>
        <result property="logisticsCompany" column="logistics_company"/>
        <result property="waybillCode" column="waybill_code"/>
        <result property="isChild" column="is_child"/>
        <result property="childWaybillCode" column="child_waybill_code"/>
        <result property="receiverMobile" column="receiver_mobile"/>
        <result property="receiverName" column="receiver_name"/>
        <result property="receiverPhone" column="receiver_phone"/>
        <result property="receiverCity" column="receiver_city"/>
        <result property="receiverDetail" column="receiver_detail"/>
        <result property="receiverDistrict" column="receiver_district"/>
        <result property="receiverProvince" column="receiver_province"/>
        <result property="receiverTown" column="receiver_town"/>
        <result property="receiverZip" column="receiver_zip"/>
        <result property="printData" column="print_data"/>
        <result property="customData" column="custom_data"/>
        <result property="gmtCreate" column="gmt_create"/>
        <result property="gmtModified" column="gmt_modified"/>
        <result property="brandCode" column="brand_code"/>
        <result property="externalInfo" column="external_info"/>
        <result property="realCpCode" column="real_cp_code"/>
        <result property="billVersion" column="bill_version"/>
    </resultMap>

    <sql id="fileds">
        id, app_name, seller_id, store_id, seller_nick, printer, print_count, operator, operator_store_id, operate_terminal, provider, cp_code,
		logistics_company, waybillCode, child_waybill_code, waybill_is_cancel, receiver_mobile, receiver_name, receiver_phone,
		receiver_city, receiver_detail, receiver_district, receiver_province, receiver_town, receiver_zip, print_data, custom_data,
		gmt_create, gmt_modified, brand_code, external_info, bill_version
    </sql>

    <insert id="insert" useGeneratedKeys="true" keyProperty="id"
            parameterType="cn.loveapp.print.common.entity.AyElefacePrintlog">
        insert into ay_eleface_printlog(app_name, seller_id, store_id, seller_nick, printer, print_count, operator,
                                        operator_store_id, operate_terminal, provider, cp_code,
                                        logistics_company, waybill_code, is_child, child_waybill_code, receiver_mobile,
                                        receiver_name, receiver_phone,
                                        receiver_city, receiver_detail, receiver_district, receiver_province,
                                        receiver_town, receiver_zip, print_data, custom_data, brand_code, external_info, real_cp_code,
                                        bill_version)
        values (#{appName}, #{sellerId}, #{storeId}, #{sellerNick}, #{printer}, #{printCount}, #{operator},
                #{operatorStoreId}, #{operateTerminal}, #{provider}, #{cpCode},
                #{logisticsCompany}, #{waybillCode}, #{isChild}, #{childWaybillCode}, #{receiverMobile},
                #{receiverName}, #{receiverPhone},
                #{receiverCity}, #{receiverDetail}, #{receiverDistrict}, #{receiverProvince}, #{receiverTown},
                #{receiverZip}, #{printData}, #{customData}, #{brandCode}, #{externalInfo}, #{realCpCode}, #{billVersion})
    </insert>

    <select id="queryPrintData" resultMap="AyElefacePrintlog">
        select id, print_data, custom_data, brand_code, external_info, real_cp_code, bill_version
        from ay_eleface_printlog
        where id in
        <foreach collection="ids" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
        and seller_id = #{sellerId}
    </select>

</mapper>
