## \u5E94\u7528\u540D\u79F0
spring.application.name=${APPLICATION_NAME:print-service}
## springboot\u5F53\u524D\u8FD0\u884C\u73AF\u5883
spring.profiles.active=dev
## apollo\u529F\u80FD\u5F00\u5173
loveapp.apollo.enabled=true
apollo.bootstrap.enabled=${loveapp.apollo.enabled}
## apollo \u5E94\u7528id\u8BBE\u7F6E
app.id=cn.loveapp.print
## apollo namespace\u8BBE\u7F6E
apollo.bootstrap.namespaces=print-service,application,service-registry
## apollo env\u8BBE\u7F6E
env=${spring.profiles.active}
## \u662F\u5426\u626B\u63CFdubbo consumer
dubbo.enabled=false
## dubbo \u5E94\u7528\u540D\u79F0
#dubbo.application.name=print-service
## dubbo \u9700\u8981\u626B\u63CF\u7684\u5305\u8DEF\u5F84
#dubbo.scan.base-packages=cn.loveapp.print.print.service.controller,cn.loveapp.print.print.service.rpc
## dubbo \u662F\u5426\u542F\u52A8\u65F6\u89C1\u68C0\u67E5consumer\u4F9D\u8D56\u7684\u670D\u52A1\u662F\u5426\u6B63\u5E38
#dubbo.consumer.check=false
## ehcache\u914D\u7F6E\u6587\u4EF6
spring.cache.jcache.config=classpath:ehcache.xml
mybatis.configuration.log-impl=org.apache.ibatis.logging.stdout.StdOutImpl
