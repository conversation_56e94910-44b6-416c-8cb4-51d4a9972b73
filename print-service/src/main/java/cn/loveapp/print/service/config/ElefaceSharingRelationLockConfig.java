package cn.loveapp.print.service.config;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

import lombok.Data;

/**
 * 面单共享关系 锁 相关配置
 *
 * <AUTHOR>
 */
@Configuration
@Data
public class ElefaceSharingRelationLockConfig {
    /**
     * 锁超时时间
     * <p>
     * 单位:秒
     */
    @Value("${eleface.sharing.relation.lock.timeout:10}")
    private int lockTimeout;
}
