package cn.loveapp.print.service.dao.redis;

import java.time.Duration;

import javax.validation.constraints.NotEmpty;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

import cn.loveapp.common.utils.LoggerHelper;
import cn.loveapp.common.utils.RedisUtil;
import cn.loveapp.print.common.exception.LockElefaceSharingRelationException;
import cn.loveapp.print.service.config.ElefaceSharingRelationLockConfig;

/**
 * 面单共享关系 redis锁 dao
 *
 * <AUTHOR>
 */
@Component
public class ElefaceSharingRelationLockRedisDao {
    private static final LoggerHelper LOGGER = LoggerHelper.getLogger(ElefaceSharingRelationLockRedisDao.class);

    /**
     * 面单共享关系 锁 前缀
     */
    private static final String PREFIX_SHARING_RELATION_KEY = "eleface:sharing:relation:shareId:";

    @Autowired
    private StringRedisTemplate stringRedisTemplate;

    @Autowired
    private ElefaceSharingRelationLockConfig lockConfig;

    /**
     * 初始化 key
     *
     * @param shareId
     *            面单共享关系 shareId
     * @return
     */
    private String initKey(@NotEmpty String shareId) {
        return PREFIX_SHARING_RELATION_KEY + shareId;
    }

    /**
     * 给 面单共享关系 操作加锁
     * <p>
     * 默认无限制重试
     *
     * @param shareId
     * @return
     */
    public String lockSharingRelation(String shareId) {
        try {
            return lockSharingRelation(shareId, -1);
        } catch (LockElefaceSharingRelationException ignored) {
        }
        return null;
    }

    /**
     * 给 面单共享关系 操作加锁
     *
     * @param shareId
     * @param retryCount
     *            重试次数
     * @return 用来解锁的value
     */
    public String lockSharingRelation(String shareId, int retryCount) throws LockElefaceSharingRelationException {
        if (StringUtils.isEmpty(shareId)) {
            return null;
        }
        String key = initKey(shareId);
        String lockValue = null;
        try {
            lockValue = RedisUtil.timedLock(key, Duration.ofSeconds(lockConfig.getLockTimeout()), retryCount,
                stringRedisTemplate);
        } catch (Exception e) {
            LOGGER.logError("-", shareId, "面单分享关系锁定失败", e);
            throw new LockElefaceSharingRelationException("面单分享关系锁定失败: " + shareId, e);
        }
        if (lockValue == null) {
            throw new LockElefaceSharingRelationException("面单分享关系锁定失败: " + shareId);
        }
        return lockValue;
    }

    /**
     * 面单共享关系 解锁
     *
     * @param shareId
     * @param lockValue
     * @return
     */
    public void unlockSharingRelation(String shareId, String lockValue) {
        if (StringUtils.isAnyEmpty(shareId, lockValue)) {
            return;
        }
        String key = initKey(shareId);
        try {
            RedisUtil.timedUnlock(key, lockValue, stringRedisTemplate);
        } catch (Exception e) {
            LOGGER.logError("-", shareId, "面单分享关系解锁失败", e);
        }
    }
}
