package cn.loveapp.print.service.platform.api;

import cn.loveapp.common.autoconfigure.platform.CommonPlatformHandler;
import cn.loveapp.print.service.api.request.AyWaybillCancelRequest;
import cn.loveapp.print.service.api.request.AyWaybillGetRequest;
import cn.loveapp.print.service.api.request.AyWaybillSearchRequest;
import cn.loveapp.print.service.api.response.AyWaybillCancelResponse;
import cn.loveapp.print.service.api.response.AyWaybillGetResponse;
import cn.loveapp.print.service.api.response.AyWaybillSearchResponse;

/**
 * 面单api service
 *
 * <AUTHOR>
 */
public interface PlatformWaybillApiService extends CommonPlatformHandler {

    /**
     * 查询面单服务订购及面单使用情况
     *
     * @param request
     * @param topSession
     * @param platformId
     * @param appName
     * @return
     */
    AyWaybillSearchResponse waybillSearch(AyWaybillSearchRequest request, String topSession, String platformId,
        String appName);

    /**
     * 电子面单取号
     *
     * @param request
     * @param topSession
     * @param platformId
     * @param appName
     * @return
     */
    AyWaybillGetResponse waybillGet(AyWaybillGetRequest request, String topSession, String platformId, String appName);

    /**
     * 取消已获取的电子面单号
     *
     * @param request
     * @param topSession
     * @param platformId
     * @param appName
     * @return
     */
    AyWaybillCancelResponse waybillCancel(AyWaybillCancelRequest request, String topSession, String platformId,
        String appName);
}
