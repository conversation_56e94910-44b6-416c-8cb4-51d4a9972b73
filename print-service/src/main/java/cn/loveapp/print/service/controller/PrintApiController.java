package cn.loveapp.print.service.controller;

import cn.loveapp.common.user.session.annotation.CheckUserSession;
import cn.loveapp.common.utils.LoggerHelper;
import cn.loveapp.common.web.CommonApiResponse;
import cn.loveapp.print.api.request.ElefaceSharingCreateInnerRequest;
import cn.loveapp.print.api.request.ElefaceShippaddressGetallInnerRequest;
import cn.loveapp.print.api.request.OrderHasprintGetInnerRequest;
import cn.loveapp.print.api.request.WaybillOperateLogSaveRequest;
import cn.loveapp.print.service.service.PrintApiService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;
import cn.loveapp.common.constant.HttpMethodsConstants;

/**
 * 打印服务外部接口
 *
 * @Author: zhongzijie
 * @Date: 2022/7/28 11:20
 * @Description: 打印服务 外部接口 实现类
 * @ignore
 */
@Api(tags = "打印服务外部接口")
@RequestMapping("print/export/print")
@RestController
public class PrintApiController {

	private static final LoggerHelper LOGGER = LoggerHelper.getLogger(PrintApiController.class);

	@Autowired
	private PrintApiService printApiService;

	@ApiOperation(value = "创建面单共享关系", httpMethod = HttpMethodsConstants.POST)
	@CheckUserSession
    @RequestMapping(value = "/eleface.sharing.create", method = {RequestMethod.POST})
	public CommonApiResponse elefaceSharingCreate(@RequestBody @Validated ElefaceSharingCreateInnerRequest request) {
		return printApiService.elefaceSharingCreate(request);
	}

	@ApiOperation(value = "创建面单共享关系", httpMethod = HttpMethodsConstants.POST)
	@CheckUserSession
    @RequestMapping(value = "/eleface.shippaddress.getall", method = {RequestMethod.POST})
	public CommonApiResponse elefaceShippaddressGetall(@RequestBody @Validated ElefaceShippaddressGetallInnerRequest request) {
		return printApiService.elefaceShippaddressGetall(request);
	}

	@ApiOperation(value = "查询指定订单是否打印了面单和快递单", httpMethod = HttpMethodsConstants.POST)
	@CheckUserSession
    @RequestMapping(value = "/order.hasprint.get", method = {RequestMethod.POST})
	public CommonApiResponse<Boolean> queryOrderHasPrintHistory(@RequestBody @Validated OrderHasprintGetInnerRequest request) {
		return printApiService.queryOrderHasPrintHistory(request);
	}

	@ApiOperation(value = "保存运单号操作日志", httpMethod = HttpMethodsConstants.POST)
    @CheckUserSession
    @RequestMapping(value = "/waybill.operatelog.save", method = {RequestMethod.POST})
    public CommonApiResponse waybillOperatelogSave(@RequestBody @Validated WaybillOperateLogSaveRequest request) {
        return printApiService.waybillOperatelogSave(request);
    }
}
