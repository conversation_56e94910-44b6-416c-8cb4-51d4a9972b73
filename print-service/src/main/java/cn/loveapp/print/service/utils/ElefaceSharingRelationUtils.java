package cn.loveapp.print.service.utils;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import cn.loveapp.common.utils.LoggerHelper;
import cn.loveapp.print.api.dto.ElefaceSharingRelationDTO;
import cn.loveapp.print.common.service.UserCenterService;
import cn.loveapp.print.common.utils.ListUtil;
import cn.loveapp.print.service.bo.ElefaceSharingRelationQueryBo;
import cn.loveapp.print.service.convert.CommonConvertMapper;
import cn.loveapp.print.service.entity.ElefaceSharingRelation;
import cn.loveapp.uac.response.UserInfoResponse;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections4.ListUtils;
import org.apache.commons.lang3.StringUtils;
import cn.loveapp.print.common.dto.TargetSellerInfo;

/**
 * 面单共享关系工具类
 *
 * <AUTHOR>
 */
public class ElefaceSharingRelationUtils {

    private static final LoggerHelper LOGGER = LoggerHelper.getLogger(ElefaceSharingRelationUtils.class);

    /**
     * 生成物流网点发货地址的MD5
     *
     * @param shippAddressProvince
     *            发货地址 省
     * @param shippAddressCity
     *            发货地址 市
     * @param shippAddressDistrict
     *            发货地址 区
     * @param shippAddressDetail
     *            发货地址 详细
     * @return
     */
    public static String generateShippAddressMd5(String shippAddressProvince, String shippAddressCity,
        String shippAddressDistrict, String shippAddressDetail) {
        List<String> branchInfoItems =
            Arrays.asList(StringUtils.trimToEmpty(shippAddressProvince), StringUtils.trimToEmpty(shippAddressCity),
                StringUtils.trimToEmpty(shippAddressDistrict), StringUtils.trimToEmpty(shippAddressDetail));
        return DigestUtils.md5Hex(branchInfoItems.toString()).toUpperCase();
    }

    /**
     * 生成返回分享面单必要信息-支持模糊搜索
     *
     * @param allRelation
     * @return
     */
    public static List<ElefaceSharingRelationDTO> generalElefaceSharingRelationResult(List<ElefaceSharingRelation> allRelation, UserCenterService userCenterService, ElefaceSharingRelationQueryBo queryBo) {

        if (CollectionUtils.isEmpty(allRelation)) {
            return Collections.emptyList();
        }

        List<ElefaceSharingRelationDTO> results = CommonConvertMapper.INSTANCE.toElefaceSharingRelationDTOList(allRelation);

        List<TargetSellerInfo> requests = new ArrayList<>();

        Set<String> userKey = new HashSet<>();
        for (ElefaceSharingRelation sharingRelation : allRelation) {
            String ownerSellerNick = sharingRelation.getOwnerSellerNick();
            String ownerAppName = sharingRelation.getOwnerAppName();
            String ownerStoreId = sharingRelation.getOwnerStoreId();
            if (!checkUserNeedRequest(userKey, ownerSellerNick, ownerAppName, ownerStoreId)) {
                TargetSellerInfo request = new TargetSellerInfo();
                request.setTargetNick(ownerSellerNick);
                request.setTargetAppName(ownerAppName);
                request.setTargetStoreId(ownerStoreId);
                requests.add(request);
            }

            String targetSellerNick = sharingRelation.getTargetSellerNick();
            String targetAppName = sharingRelation.getTargetAppName();
            String targetStoreId = sharingRelation.getTargetStoreId();
            if (!checkUserNeedRequest(userKey, targetSellerNick, targetAppName, targetStoreId)) {
                TargetSellerInfo request = new TargetSellerInfo();
                request.setTargetNick(targetSellerNick);
                request.setTargetAppName(targetAppName);
                request.setTargetStoreId(targetStoreId);
                requests.add(request);
            }

            String proxySellerNick = sharingRelation.getProxySellerNick();
            String proxyAppName = sharingRelation.getProxyAppName();
            String proxyStoreId = sharingRelation.getProxyStoreId();
            if (!checkUserNeedRequest(userKey, proxySellerNick, proxyAppName, proxyStoreId)) {
                TargetSellerInfo request = new TargetSellerInfo();
                request.setTargetNick(proxySellerNick);
                request.setTargetAppName(proxyAppName);
                request.setTargetStoreId(proxyStoreId);
                requests.add(request);
            }
        }

        if (requests.isEmpty()) {
            return results;
        }

        // 获取用户信息
        Map<String, UserInfoResponse> userInfoResponseMap = new HashMap<>();
        if (requests.size() < 20) {
            List<UserInfoResponse> userInfos = userCenterService.getUserInfo(requests);
            if (CollectionUtils.isNotEmpty(userInfos)) {
                userInfoResponseMap = userInfos.stream().collect(Collectors.toMap(u -> u.getSellerId() + u.getAppName() + u.getPlatformId(), Function.identity(), (v1, v2) -> v1));
            }
        } else {
            List<List<TargetSellerInfo>> groups = ListUtils.partition(requests, 20);
            List<UserInfoResponse> userInfos = new ArrayList<>();
            for (List<TargetSellerInfo> targetSellerInfos : groups) {
                List<UserInfoResponse> users = userCenterService.getUserInfo(targetSellerInfos);
                if (CollectionUtils.isNotEmpty(users)) {
                    userInfos.addAll(users);
                }
            }
            if (!userInfos.isEmpty()) {
                userInfoResponseMap = userInfos.stream().collect(Collectors.toMap(u -> u.getSellerId() + u.getAppName() + u.getPlatformId(), Function.identity(), (v1, v2) -> v1));
            }
        }


        Iterator<ElefaceSharingRelationDTO> iterator = results.iterator();
        while (iterator.hasNext()) {
            ElefaceSharingRelationDTO result = iterator.next();
            // 默认取nick
            result.setOwnerMallName(result.getOwnerSellerNick());
            result.setTargetMallName(result.getTargetSellerNick());
            result.setProxyMallName(result.getProxySellerNick());

            String ownerUser = result.getOwnerSellerId() + result.getOwnerAppName() + result.getOwnerStoreId();
            String targetUser = result.getTargetSellerId() + result.getTargetAppName() + result.getTargetStoreId();
            String proxyUser = result.getProxySellerId() + result.getProxyAppName() + result.getProxyStoreId();
            UserInfoResponse ownerUserInfo = userInfoResponseMap.get(ownerUser);
            UserInfoResponse targetUserInfo = userInfoResponseMap.get(targetUser);
            UserInfoResponse proxyUserInfo = userInfoResponseMap.get(proxyUser);

            // 模糊搜索串
            List<String> fuzzySearchList = new ArrayList<>();

            ListUtil.addListIfNotNull(fuzzySearchList, result.getOwnerSellerNick());
            ListUtil.addListIfNotNull(fuzzySearchList, result.getTargetSellerNick());
            ListUtil.addListIfNotNull(fuzzySearchList, result.getProxySellerNick());

            if (ownerUserInfo != null) {
                result.setOwnerMallName(ownerUserInfo.getMallName());
                ListUtil.addListIfNotNull(fuzzySearchList, ownerUserInfo.getMallName());
            }
            if (targetUserInfo != null) {
                result.setTargetMallName(targetUserInfo.getMallName());
                ListUtil.addListIfNotNull(fuzzySearchList, targetUserInfo.getMallName());
            }
            if (proxyUserInfo != null) {
                result.setProxyMallName(proxyUserInfo.getMallName());
                ListUtil.addListIfNotNull(fuzzySearchList, proxyUserInfo.getMallName());
            }

            // 模糊搜索过滤 (所有者、被分享者、代理者)
            if (!fuzzySearch(queryBo, fuzzySearchList, result.getShareMemo())) {
                iterator.remove();
            }
        }

        results.sort(new Comparator<ElefaceSharingRelationDTO>() {
            @Override
            public int compare(ElefaceSharingRelationDTO o1, ElefaceSharingRelationDTO o2) {
                return Long.compare(o2.getId(), o1.getId());
            }
        });

        return results;
    }


    private static boolean fuzzySearch(ElefaceSharingRelationQueryBo queryBo, List<String> namefuzzySearchList, String shareMemo) {

        String fuzzySearchStr = queryBo.getFuzzySearchStr();

        // 不存在模糊搜索项
        if (StringUtils.isEmpty(fuzzySearchStr)) {
            return true;
        }

        // nick，mallName 模糊匹配 （分享（代理）者、被分享者）
        if (CollectionUtils.isNotEmpty(namefuzzySearchList) && namefuzzySearchList.stream().anyMatch(item -> item.contains(fuzzySearchStr))) {
            return true;
        }

        if (StringUtils.isNotEmpty(shareMemo) && shareMemo.contains(fuzzySearchStr)) {
            return true;
        }

        LOGGER.logInfo("面单模糊搜索过滤未搜索到符合条件的数据");
        return false;
    }



    private static boolean checkUserNeedRequest(Set<String> userKey, String nick, String appName, String storeId) {
        if (StringUtils.isAnyEmpty(nick, storeId)) {
            return true;
        }

        String key = nick + appName + storeId;

        if (!userKey.contains(key)) {
            userKey.add(key);
            return false;
        }
        return true;
    }
}
