package cn.loveapp.print.service.dto;

import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2023/9/5 5:26 PM
 */
@Data
@ApiModel(value = "目标打印信息(多店分组)")
@NoArgsConstructor
@AllArgsConstructor
public class TargetPrintInfoDTO extends TargetUserInfoDTO{

    private List<String> tidList;

}
