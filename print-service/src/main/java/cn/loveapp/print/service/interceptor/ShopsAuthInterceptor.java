package cn.loveapp.print.service.interceptor;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.MethodParameter;
import org.springframework.web.bind.support.WebDataBinderFactory;
import org.springframework.web.context.request.NativeWebRequest;
import org.springframework.web.method.HandlerMethod;
import org.springframework.web.method.support.HandlerMethodArgumentResolver;
import org.springframework.web.method.support.ModelAndViewContainer;
import org.springframework.web.servlet.handler.HandlerInterceptorAdapter;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;

import cn.loveapp.common.constant.CommonAppConstants;
import cn.loveapp.common.dto.UserSessionInfo;
import cn.loveapp.common.user.session.constant.SessionConstants;
import cn.loveapp.common.web.CommonApiResponse;
import cn.loveapp.common.web.CommonApiStatus;
import cn.loveapp.print.common.dto.TargetSellerInfo;
import cn.loveapp.print.common.dto.UserMultiInfoDTO;
import cn.loveapp.print.common.service.UserCenterService;
import cn.loveapp.print.service.annotation.ShopsAuth;
import cn.loveapp.print.service.dto.TargetUserInfoDTO;
import cn.loveapp.print.service.service.ShopsService;
import cn.loveapp.uac.response.UserInfoResponse;

/**
 * 多店鉴权拦截器
 *
 * <AUTHOR>
 * @date 2020-02-08 16:07:13
 */
public class ShopsAuthInterceptor extends HandlerInterceptorAdapter implements HandlerMethodArgumentResolver {
    private static final String COMMON_ATTRIBUTE_TARGETUSERINFO = "COMMON_ATTRIBUTE_TARGETUSERINFO";

    /**
     * 多店（targetNickList）用户信息
     */
    private static final String COMMON_ATTRIBUTE_TARGETUSERINFOS = "COMMON_ATTRIBUTE_TARGETUSERINFOS";

    /**
     * 用户信息分隔符
     */
    private static final String SELLER_INFO_SPLIT_STR = ",";

    private static final String TARGET_NICK = "targetNick";
    private static final String TARGET_STORE_ID = "targetStoreId";
    private static final String TARGET_APP_NAME = "targetAppName";

    private static final String TARGET_NICK_LIST = "targetNickList";
    private static final String TARGET_STORE_ID_LIST = "targetStoreIdList";
    private static final String TARGET_APP_NAME_LIST = "targetAppNameList";

    public static final String PHPSESSIONID = "PHPSESSID";

    @Autowired
    private ShopsService shopsService;

    @Autowired
    private UserCenterService userCenterService;

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler)
        throws Exception {
        if (request.getAttribute(COMMON_ATTRIBUTE_TARGETUSERINFO) == null) {
            if (handler instanceof HandlerMethod) {
                HandlerMethod handlerMethod = (HandlerMethod)handler;
                if (!handlerMethod.hasMethodAnnotation(ShopsAuth.class)) {
                    // 没有@ShopsAuth注解不需要添加多店鉴权
                    return true;
                }

                String targetNick = request.getParameter(TARGET_NICK);
                String targetStoreId = request.getParameter(TARGET_STORE_ID);
                String targetAppName = request.getParameter(TARGET_APP_NAME);

                String targetNickListStr = request.getParameter(TARGET_NICK_LIST);
                String targetStoreIdListStr = request.getParameter(TARGET_STORE_ID_LIST);
                String targetAppNameListStr = request.getParameter(TARGET_APP_NAME_LIST);
                if ((StringUtils.isBlank(targetNick) || StringUtils.isBlank(targetStoreId))
                    && (StringUtils.isBlank(targetNickListStr) || StringUtils.isBlank(targetStoreIdListStr))) {
                    // 没有传入目标用户信息，不需要鉴权
                    request.setAttribute(COMMON_ATTRIBUTE_TARGETUSERINFO, null);
                    return true;
                }

                UserSessionInfo userSessionInfo =
                    (UserSessionInfo)request.getAttribute(SessionConstants.REQUEST_ATTRIBUTE_SESSIONINFO);
                if (userSessionInfo == null) {
                    // 缺失session信息
                    CommonApiResponse errorResponse =
                        CommonApiResponse.of(CommonApiStatus.ForbiddenError.code(), "缺失session信息");
                    response.setContentType("application/json; charset=utf-8");
                    response.getWriter().append(JSON.toJSONString(errorResponse)).flush();
                    return false;
                }

                List<TargetSellerInfo> targetSellerInfoList = null;
                if (StringUtils.isNotBlank(targetNickListStr)) {
                    // 多店混单鉴权
                    String[] targetNicks = targetNickListStr.split(SELLER_INFO_SPLIT_STR);
                    String[] targetStoreIds = targetStoreIdListStr.split(SELLER_INFO_SPLIT_STR);
                    String[] targetAppNames = targetAppNameListStr.split(SELLER_INFO_SPLIT_STR);
                    targetSellerInfoList = Lists.newArrayList();
                    for (int i = 0; i < targetNicks.length; i++) {
                        TargetSellerInfo targetSellerInfo = new TargetSellerInfo();
                        targetSellerInfo.setTargetNick(targetNicks[i]);
                        targetSellerInfo.setTargetStoreId(targetStoreIds[i]);
                        if (targetAppNames.length == 0) {
                            targetSellerInfo.setTargetAppName(CommonAppConstants.APP_TRADE);
                        } else {
                            targetSellerInfo.setTargetAppName(targetAppNames[i]);
                        }
                        targetSellerInfoList.add(targetSellerInfo);
                    }

                    if (!shopsService.multiShopsAuth(userSessionInfo.getNick(), userSessionInfo.getStoreId(),
                        userSessionInfo.getAppName(), targetSellerInfoList)) {
                        // 多店鉴权失败
                        CommonApiResponse errorResponse =
                            CommonApiResponse.of(CommonApiStatus.ForbiddenError.code(), "多店鉴权失败");
                        response.setContentType("application/json; charset=utf-8");
                        response.getWriter().append(JSON.toJSONString(errorResponse)).flush();
                        return false;
                    }
                } else {
                    if (!shopsService.shopsAuth(userSessionInfo.getNick(), userSessionInfo.getStoreId(),
                        userSessionInfo.getAppName(), targetNick, targetStoreId, targetAppName)) {
                        // 多店鉴权失败
                        CommonApiResponse errorResponse =
                            CommonApiResponse.of(CommonApiStatus.ForbiddenError.code(), "多店鉴权失败");
                        response.setContentType("application/json; charset=utf-8");
                        response.getWriter().append(JSON.toJSONString(errorResponse)).flush();
                        return false;
                    }
                    if (StringUtils.isEmpty(targetAppName)) {
                        targetAppName = CommonAppConstants.APP_TRADE;
                    }

                    TargetSellerInfo targetSellerInfo = new TargetSellerInfo();
                    targetSellerInfo.setTargetNick(targetNick);
                    targetSellerInfo.setTargetStoreId(targetStoreId);
                    targetSellerInfo.setTargetAppName(targetAppName);
                    targetSellerInfoList = Lists.newArrayList(targetSellerInfo);
                }

                List<UserInfoResponse> userInfoList = userCenterService.getUserInfo(targetSellerInfoList);

                if (CollectionUtils.isEmpty(userInfoList)) {
                    CommonApiResponse errorResponse =
                        CommonApiResponse.of(CommonApiStatus.ForbiddenError.code(), "目标用户不存在");
                    response.setContentType("application/json; charset=utf-8");
                    response.getWriter().append(JSON.toJSONString(errorResponse)).flush();
                    return false;
                }

                Map<String, TargetSellerInfo> sellerNickAndTargetSellerInfoMap = targetSellerInfoList.stream()
                    .collect(Collectors.toMap(targetSellerInfo -> targetSellerInfo.getTargetNick() + targetSellerInfo.getTargetStoreId() + targetSellerInfo.getTargetAppName(), Function.identity()));

                for (UserInfoResponse userInfoResponse : userInfoList) {
                    TargetSellerInfo targetSellerInfo =
                        sellerNickAndTargetSellerInfoMap.get(userInfoResponse.getSellerNick() + userInfoResponse.getPlatformId() + userInfoResponse.getAppName());
                    targetSellerInfo.setTargetSellerId(userInfoResponse.getSellerId());
                }

                List<TargetSellerInfo> targetSellerInfos = new ArrayList<>(sellerNickAndTargetSellerInfoMap.values());
                if (StringUtils.isNotEmpty(targetNickListStr)) {
                    // 新混店
                    UserMultiInfoDTO userMultiInfoDTO = new UserMultiInfoDTO();
                    userMultiInfoDTO.setTargetSellerList(targetSellerInfos);
                    request.setAttribute(COMMON_ATTRIBUTE_TARGETUSERINFOS, userMultiInfoDTO);
                } else {
                    // 原多店
                    TargetSellerInfo targetSellerInfo = targetSellerInfos.get(0);
                    TargetUserInfoDTO targetUserInfoDTO = new TargetUserInfoDTO();
                    targetUserInfoDTO.setSellerNick(targetNick);
                    targetUserInfoDTO.setStoreId(targetStoreId);
                    targetUserInfoDTO.setSellerId(targetSellerInfo.getTargetSellerId());
                    targetUserInfoDTO.setAppName(targetAppName);
                    request.setAttribute(COMMON_ATTRIBUTE_TARGETUSERINFO, targetUserInfoDTO);
                }
                return true;
            }
        }
        return true;
    }

    @Override
    public boolean supportsParameter(MethodParameter methodParameter) {
        // 原多店
        if (methodParameter.getParameterType().isAssignableFrom(TargetUserInfoDTO.class)) {
            return true;
        }

        // 混店
        return methodParameter.getParameterType().isAssignableFrom(UserMultiInfoDTO.class);
    }

    @Override
    public Object resolveArgument(MethodParameter methodParameter, ModelAndViewContainer modelAndViewContainer,
        NativeWebRequest nativeWebRequest, WebDataBinderFactory webDataBinderFactory) throws Exception {
        if (methodParameter.getParameterType().isAssignableFrom(TargetUserInfoDTO.class)) {
            return nativeWebRequest.getAttribute(COMMON_ATTRIBUTE_TARGETUSERINFO, 0);
        } else if (methodParameter.getParameterType().isAssignableFrom(UserMultiInfoDTO.class)) {
            return nativeWebRequest.getAttribute(COMMON_ATTRIBUTE_TARGETUSERINFOS, 0);
        }
        return null;
    }

    private String getPhpSessionIdValue(Cookie[] cookies) {
        if (null != cookies) {
            for (Cookie cookie : cookies) {
                if (cookie.getName().equals(PHPSESSIONID)) {
                    return cookie.getValue();
                }
            }
        }
        return "";
    }
}
