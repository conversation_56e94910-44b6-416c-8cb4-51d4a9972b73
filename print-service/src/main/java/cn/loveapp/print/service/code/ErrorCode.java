package cn.loveapp.print.service.code;

public interface ErrorCode {
    enum BaseCode implements ErrorCode {
        /**
         * basecode
         */
        SUCCESS(0, "成功"), SYS_ERR(1, "程序异常"), PARAMS_ERR(2, "参数错误"), REQUEST_ERR(3, "请求错误"),
        SIGN_PRIAVET_KEY_ERR(4, "签名秘钥错误"), PACK_PRIAVET_KEY_ERR(5, "数据加密密钥错误"), SIGN_ERR(6, "签名错误"),
        SYS_MAINTAIN(7, "系统维护"), SYS_DB_ERR(8, "DB错误"), VERSION_ERR(9, "版本异常");

        private Integer code;

        public Integer getCode() {
            return code;
        }

        public void setCode(Integer code) {
            this.code = code;
        }

        public String getMessage() {
            return message;
        }

        public void setMessage(String message) {
            this.message = message;
        }

        private String message;

        private BaseCode(Integer code, String message) {
            this.code = code;
            this.message = message;
        }
    }
}
