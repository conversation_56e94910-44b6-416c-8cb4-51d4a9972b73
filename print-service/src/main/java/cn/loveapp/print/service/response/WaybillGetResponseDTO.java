package cn.loveapp.print.service.response;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

import cn.loveapp.print.service.bo.ApiErrorBo;
import org.springframework.util.StringUtils;

import cn.loveapp.print.service.bo.WaybillGetResponseBo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 电子面单打印信息
 *
 * <AUTHOR>
 */
@ApiModel
@Data
public class WaybillGetResponseDTO implements Serializable {
    private static final long serialVersionUID = -263930682540501587L;

    /**
     * 面单取号打印信息列表
     */
    @ApiModelProperty("面单取号打印信息列表")
    private List<WaybillPrintInfoDTO> waybillPrintInfoList;

    public static WaybillGetResponseDTO of(String cpCode, List<WaybillGetResponseBo> waybillGetResponseBoList) {
        WaybillGetResponseDTO waybillGetResponseDTO = new WaybillGetResponseDTO();
        List<WaybillPrintInfoDTO> waybillPrintInfoList = new ArrayList<>();
        for (WaybillGetResponseBo waybillGetResponseBo : waybillGetResponseBoList) {
            waybillPrintInfoList.add(WaybillPrintInfoDTO.of(cpCode, waybillGetResponseBo));
        }
        waybillGetResponseDTO.setWaybillPrintInfoList(waybillPrintInfoList);
        return waybillGetResponseDTO;
    }

    @ApiModel
    @Data
    public static class WaybillPrintInfoDTO implements Serializable {
        private static final long serialVersionUID = 8624681345873919721L;
        /**
         * 物流公司code
         */
        @ApiModelProperty("物流公司code")
        private String cpCode;

        /**
         * 物流公司code
         */
        @ApiModelProperty("真实物流公司code")
        private String realCpCode;

        /**
         * 面单请求id
         */
        @ApiModelProperty("面单请求id")
        private String objectId;

        /**
         * 运单号
         */
        @ApiModelProperty("运单号")
        private String waybillCode;

        /**
         * 字母单中的子单号
         */
        @ApiModelProperty("字母单中的子单号")
        private String childWaybillCode;

        /**
         * 面单是否被取消
         */
        @ApiModelProperty("面单是否被取消")
        private Boolean isCancel;

        /**
         * 打印内容
         */
        @ApiModelProperty("打印内容")
        private String printData;

        /**
         * 电子面单订单id(微信视频号专有字段,用于取号/取消取号)
         */
        @ApiModelProperty("电子面单订单id(微信视频号专有字段,用于取号/取消取号)")
        private String waybillOrderId;

        /**
         * 是否取号成功
         */
        private boolean isSuccess;

        /**
         * 取号错误信息
         */
        private String errMsg;

        /**
         * 取号api调用错误详情
         */
        private ApiErrorBo apiError;

        public static WaybillPrintInfoDTO of(String cpCode, WaybillGetResponseBo waybillGetResponseBo) {
            WaybillPrintInfoDTO waybillPrintInfoDTO = new WaybillPrintInfoDTO();
            waybillPrintInfoDTO.setCpCode(cpCode);
            waybillPrintInfoDTO.setRealCpCode(waybillGetResponseBo.getRealCpCode());
            waybillPrintInfoDTO.setObjectId(waybillGetResponseBo.getObjectId());
            waybillPrintInfoDTO.setPrintData(waybillGetResponseBo.getPrintData());
            waybillPrintInfoDTO.setSuccess(waybillGetResponseBo.isSuccess());
            waybillPrintInfoDTO.setErrMsg(waybillGetResponseBo.getErrMsg());
            waybillPrintInfoDTO.setWaybillOrderId(waybillGetResponseBo.getWaybillOrderId());

            if (StringUtils.isEmpty(waybillGetResponseBo.getParentWaybillCode())) {
                waybillPrintInfoDTO.setWaybillCode(waybillGetResponseBo.getWaybillCode());
            } else {
                // 快运字母单
                waybillPrintInfoDTO.setWaybillCode(waybillGetResponseBo.getParentWaybillCode());
                waybillPrintInfoDTO.setChildWaybillCode(waybillGetResponseBo.getWaybillCode());
            }
            waybillPrintInfoDTO.setApiError(waybillGetResponseBo.getApiError());
            return waybillPrintInfoDTO;
        }
    }
}
