package cn.loveapp.print.service.bo;

import cn.loveapp.print.service.request.ElefacePrintRequest;
import lombok.Data;


/**
 * <AUTHOR>
 */
@Data
public class ElefacePrintBo extends BasePrintBo {

    /**
     * 面单服务商
     */
    private String provider;
    /**
     * 物流公司code
     */
    private String cpCode;
    /**
     * 物流公司
     */
    private String logisticsCompany;
    /**
     * 面单号
     */
    private String waybillCode;
    /**
     * 快运单子单面单号
     */
    private String childWaybillCode;
    /**
     * 打印信息
     */
    private String printData;
    /**
     * 自定义区域打印信息
     */
    private String customData;
    /**
     * 子品牌
     */
    private String brandCode;

    /**
     * 交易（TAO） 扩展字段
     */
    private String externalInfo;

    /**
     * 真实快递公司code
     */
    private String realCpCode;

    /**
     * 物流模板名
     */
    private String logisticsTemplateName;

    /**
     * 电子面单版本号，1-默认值旧版电子面单 2-新版电子面单 (XHS使用)
     */
    private Integer billVersion;

    public static ElefacePrintBo of(ElefacePrintRequest request) {
        ElefacePrintBo elefacePrintBo = new ElefacePrintBo();
        elefacePrintBo.setBasePrintInfo(request);
        elefacePrintBo.setProvider(request.getProvider());
        elefacePrintBo.setCpCode(request.getCpCode());
        elefacePrintBo.setLogisticsCompany(request.getLogisticsCompany());
        elefacePrintBo.setWaybillCode(request.getWaybillCode());
        elefacePrintBo.setChildWaybillCode(request.getChildWaybillCode());
        elefacePrintBo.setRecipient(request.getRecipient());
        elefacePrintBo.setSender(request.getSender());
        elefacePrintBo.setPrintData(request.getPrintData());
        elefacePrintBo.setCustomData(request.getCustomData());
        elefacePrintBo.setBrandCode(request.getBrandCode());
        elefacePrintBo.setExternalInfo(request.getExternalInfo());
        elefacePrintBo.setRealCpCode(request.getRealCpCode());
        elefacePrintBo.setOperatorName(request.getOperatorName());
        elefacePrintBo.setLogisticsTemplateName(request.getLogisticsTemplateName());
        elefacePrintBo.setBillVersion(request.getBillVersion());
        return elefacePrintBo;
    }
}
