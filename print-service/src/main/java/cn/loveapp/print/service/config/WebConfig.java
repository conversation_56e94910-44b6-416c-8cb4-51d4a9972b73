package cn.loveapp.print.service.config;

import java.util.List;

import cn.loveapp.print.service.interceptor.UserAuthInterceptor;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.method.support.HandlerMethodArgumentResolver;
import org.springframework.web.servlet.HandlerInterceptor;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

import cn.loveapp.print.service.interceptor.ShopsAuthInterceptor;

/**
 * <AUTHOR>
 */
@Configuration
public class WebConfig implements WebMvcConfigurer {

    @Bean
    public HandlerInterceptor getShopsAuthInterceptor() {
        return new ShopsAuthInterceptor();
    }

    @Bean
    public HandlerInterceptor getUserAuthInterceptor() {
        return new UserAuthInterceptor();
    }

    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        registry.addInterceptor(getShopsAuthInterceptor()).addPathPatterns("/print/**");
        registry.addInterceptor(getShopsAuthInterceptor()).addPathPatterns("/account/**");
        registry.addInterceptor(getUserAuthInterceptor()).addPathPatterns("/print/**");
        registry.addInterceptor(getUserAuthInterceptor()).addPathPatterns("/account/**");
    }

    @Override
    public void addArgumentResolvers(List<HandlerMethodArgumentResolver> resolvers) {
        resolvers.add(new ShopsAuthInterceptor());
        resolvers.add(new UserAuthInterceptor());
    }
}
