package cn.loveapp.print.service.request;

import java.util.List;

import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

import cn.loveapp.print.service.annotation.TradeTypeValidation;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
@ApiModel(value = "流失号获取请求体")
public class SerialGetRequest {

    /**
     * 打印的订单信息
     */
    @ApiModelProperty(value = "打印的订单信息", required = true)
    @NotEmpty
    @Valid
    private List<PrintTradeInfoDTO> tradeInfoList;

    /**
     * 合单订单的主单tid
     */
    @ApiModelProperty(value = "合单订单的主单tid")
    private String mergeTid;

    /**
     * 订单类型 0-普通订单 1-自由打印订单
     */
    @ApiModelProperty(value = "订单类型 0-普通订单 1-自由打印订单")
    @TradeTypeValidation
    @NotNull
    private Integer tradeType;

    /**
     * 是否获取最新流水号（订单重复获取时是否取最新）
     */
    @ApiModelProperty(value = "是否获取最新流水号（订单重复获取时是否取最新） true:取最新流水号 false：取之前取过的流水号")
    private Boolean isFetchNewSerial = false;

    /**
     * 用户Nick
     */
    @ApiModelProperty(value = "用户Nick")
    private String sellerNick;

    /**
     * 商家Id
     */
    @ApiModelProperty(value = "商家Id")
    private String sellerId;

    /**
     * 平台Id TAO、PDD
     */
    @ApiModelProperty(value = "平台Id TAO、PDD")
    private String storeId;

    /**
     * 应用名称 trade guanDian
     */
    @ApiModelProperty(value = "应用名称 trade guanDian")
    private String appName;
}
