package cn.loveapp.print.service.api.response;

import java.util.List;

import cn.loveapp.print.service.api.entity.WaybillCloudPrintInfo;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * waybill.get 电子面单取号 返回结果
 *
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class AyWaybillGetResponse extends AyBaseResponse {
    /**
     * 取号结果
     */
    private List<WaybillCloudPrintInfo> waybillCloudPrintInfoList;
}
