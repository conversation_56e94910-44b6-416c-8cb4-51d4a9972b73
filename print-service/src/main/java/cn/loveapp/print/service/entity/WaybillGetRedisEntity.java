package cn.loveapp.print.service.entity;

import cn.loveapp.print.service.bo.ApiErrorBo;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;
import lombok.ToString;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;

/**
 * 面单取号缓存实体
 * @program: print-services-group
 * @description:
 * @author: zhang<PERSON><PERSON><PERSON>
 * @create: 2023/4/3 11:16
 **/
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(value = JsonInclude.Include.NON_EMPTY)
@ToString
public class WaybillGetRedisEntity {

    /**
     * 物流公司code
     */
    private String cpCode;


    /**
     * 调用状态
     */
    private String status;


    /**
     * 获取成功的面单号列表
     */
    private List<String> successWaybillCodeList;


    /**
     * 获取失败信息列表
     */
    private List<ApiErrorBo> apiErrorList;


    public void addSuccessWaybillCode(Set<String> waybillCodes) {
        if (successWaybillCodeList == null) {
            successWaybillCodeList = new ArrayList<>();
        }
        successWaybillCodeList.addAll(waybillCodes);
    }


    public void addError(ApiErrorBo apiError) {
        if (apiErrorList == null) {
            apiErrorList = new ArrayList<>();
        }
        apiErrorList.add(apiError);
    }

}
