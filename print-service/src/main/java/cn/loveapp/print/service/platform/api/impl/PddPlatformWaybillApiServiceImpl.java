package cn.loveapp.print.service.platform.api.impl;

import java.util.Collections;
import java.util.stream.Collectors;

import cn.loveapp.print.service.config.PrintConfig;
import com.pdd.pop.sdk.http.api.ark.request.PddCloudWaybillGetRequest;
import com.pdd.pop.sdk.http.api.ark.response.PddCloudWaybillGetResponse;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSON;
import com.pdd.pop.sdk.http.api.pop.request.PddWaybillCancelRequest;
import com.pdd.pop.sdk.http.api.pop.request.PddWaybillGetRequest;
import com.pdd.pop.sdk.http.api.pop.request.PddWaybillSearchRequest;
import com.pdd.pop.sdk.http.api.pop.response.PddWaybillCancelResponse;
import com.pdd.pop.sdk.http.api.pop.response.PddWaybillGetResponse;
import com.pdd.pop.sdk.http.api.pop.response.PddWaybillSearchResponse;

import cn.loveapp.common.constant.CommonPlatformConstants;
import cn.loveapp.common.platformsdk.pdd.PddSDKService;
import cn.loveapp.common.utils.LoggerHelper;
import cn.loveapp.print.service.api.entity.AyWaybillApplySubscriptionInfo;
import cn.loveapp.print.service.api.entity.WaybillCloudPrintInfo;
import cn.loveapp.print.service.api.request.AyWaybillCancelRequest;
import cn.loveapp.print.service.api.request.AyWaybillGetRequest;
import cn.loveapp.print.service.api.request.AyWaybillSearchRequest;
import cn.loveapp.print.service.api.response.AyWaybillCancelResponse;
import cn.loveapp.print.service.api.response.AyWaybillGetResponse;
import cn.loveapp.print.service.api.response.AyWaybillSearchResponse;
import cn.loveapp.print.service.code.ErrorCode;
import cn.loveapp.print.service.platform.api.PlatformWaybillApiService;

/**
 * 面单api service 拼多多实现类
 *
 * <AUTHOR>
 */
@Service
public class PddPlatformWaybillApiServiceImpl implements PlatformWaybillApiService {

    private static final LoggerHelper LOGGER = LoggerHelper.getLogger(PddPlatformWaybillApiServiceImpl.class);

    @Autowired
    private PddSDKService pddAppsSDKService;

    @Autowired
    private PrintConfig printConfig;

    /**
     * 查询面单服务订购及面单使用情况
     *
     * @param request
     * @param topSession
     * @param appName
     * @return
     */
    @Override
    public AyWaybillSearchResponse waybillSearch(AyWaybillSearchRequest request, String topSession, String platformId,
        String appName) {
        PddWaybillSearchRequest pddWaybillSearchRequest = new PddWaybillSearchRequest();
        pddWaybillSearchRequest.setWpCode(StringUtils.trimToEmpty(request.getCpCode()));

        PddWaybillSearchResponse pddWaybillSearchResponse =
            pddAppsSDKService.execute(pddWaybillSearchRequest, topSession, appName);

        AyWaybillSearchResponse response = new AyWaybillSearchResponse();
        response.init(pddWaybillSearchResponse);
        if (!response.isSuccess()) {
            // 失败直接返回
            return response;
        }
        if (pddWaybillSearchResponse.getPddWaybillSearchResponse() != null
            && pddWaybillSearchResponse.getPddWaybillSearchResponse().getWaybillApplySubscriptionCols() != null) {
            response.setWaybillApplySubscriptionCols(
                pddWaybillSearchResponse.getPddWaybillSearchResponse().getWaybillApplySubscriptionCols().stream()
                    .map(AyWaybillApplySubscriptionInfo::of).collect(Collectors.toList()));
        } else {
            response.setWaybillApplySubscriptionCols(Collections.emptyList());
        }

        return response;
    }

    public AyWaybillGetResponse doWaybillGet(AyWaybillGetRequest request, String topSession, String platformId, String appName) {
        AyWaybillGetResponse response = new AyWaybillGetResponse();
        PddWaybillGetRequest pddWaybillGetRequest = new PddWaybillGetRequest();
        try {
            pddWaybillGetRequest.setParamWaybillCloudPrintApplyNewRequest(
                JSON.parseObject(request.getParamWaybillCloudPrintApplyNewRequest(),
                    PddWaybillGetRequest.ParamWaybillCloudPrintApplyNewRequest.class));
        } catch (Exception e) {
            LOGGER.logError("非法的参数：paramWaybillCloudPrintApplyNewRequest", e);
            response.setErrorCode(ErrorCode.BaseCode.PARAMS_ERR.getCode().toString());
            response.setMsg(
                "非法的参数：paramWaybillCloudPrintApplyNewRequest = " + request.getParamWaybillCloudPrintApplyNewRequest());
            return response;
        }
        PddWaybillGetResponse pddWaybillGetResponse =
            pddAppsSDKService.execute(pddWaybillGetRequest, topSession, appName);
        response.init(pddWaybillGetResponse);
        if (!response.isSuccess()) {
            return response;
        }
        response.setWaybillCloudPrintInfoList(pddWaybillGetResponse.getPddWaybillGetResponse().getModules().stream()
            .map(WaybillCloudPrintInfo::of).collect(Collectors.toList()));
        return response;
    }

    public AyWaybillGetResponse doArkWaybillGet(AyWaybillGetRequest request, String topSession, String platformId,
                                String appName){
        PddCloudWaybillGetRequest requestDto = new PddCloudWaybillGetRequest();
        PddCloudWaybillGetRequest.ParamWaybillCloudPrintApplyNewRequest waybillReq = null;
        AyWaybillGetResponse ayResp = new AyWaybillGetResponse();
        try {
            waybillReq = JSON.parseObject(request.getParamWaybillCloudPrintApplyNewRequest(),
                PddCloudWaybillGetRequest.ParamWaybillCloudPrintApplyNewRequest.class);
            requestDto.setParamWaybillCloudPrintApplyNewRequest(waybillReq);
        } catch (Exception e) {
            LOGGER.logError("非法的参数：paramWaybillCloudPrintApplyNewRequest", e);
            ayResp.setErrorCode(ErrorCode.BaseCode.PARAMS_ERR.getCode().toString());
            ayResp.setMsg(
                "非法的参数：paramWaybillCloudPrintApplyNewRequest = " + request.getParamWaybillCloudPrintApplyNewRequest());
            return ayResp;
        }
        waybillReq.setToken(topSession);
        requestDto.setParamWaybillCloudPrintApplyNewRequest(waybillReq);
        PddCloudWaybillGetResponse cloudResp = pddAppsSDKService.execute(requestDto, topSession, appName);
        if (cloudResp == null || cloudResp.getPddWaybillGetResponse() == null) {
            LOGGER.logError("ark查询接口响应结果为空：" + JSON.toJSONString(cloudResp));
            // 失败直接返回
            ayResp.setErrorCode(ErrorCode.BaseCode.REQUEST_ERR.getCode().toString());
            return ayResp;
        }else if (!cloudResp.getPddWaybillGetResponse().getIsSuccess()){
            ayResp.setErrorCode(cloudResp.getPddWaybillGetResponse().getErrorCode().toString());
            ayResp.setSubCode(cloudResp.getPddWaybillGetResponse().getSubCode().toString());
            ayResp.setSubMsg(cloudResp.getPddWaybillGetResponse().getSubMsg());
            return ayResp;
        }
        ayResp.setWaybillCloudPrintInfoList(cloudResp.getPddWaybillGetResponse().getModules().stream()
                .map(WaybillCloudPrintInfo::of).collect(Collectors.toList()));

        return ayResp;
    }

    @Override
    public AyWaybillGetResponse waybillGet(AyWaybillGetRequest request, String topSession, String platformId,
        String appName) {
        AyWaybillGetResponse response = new AyWaybillGetResponse();
        if (printConfig.getArkAPiAppNames().contains(appName)) {
            // 如果配置项中存在的此平台此应用，则需要调用ark服务
            response = doArkWaybillGet(request, topSession, platformId, appName);
        } else {
            response = doWaybillGet(request, topSession, platformId, appName);
        }
        return response;
    }

    @Override
    public AyWaybillCancelResponse waybillCancel(AyWaybillCancelRequest request, String topSession, String platformId,
        String appName) {
        AyWaybillCancelResponse response = new AyWaybillCancelResponse();
        PddWaybillCancelRequest pddWaybillCancelRequest = new PddWaybillCancelRequest();
        pddWaybillCancelRequest.setWaybillCode(request.getWaybillCode());
        pddWaybillCancelRequest.setWpCode(request.getCpCode());
        PddWaybillCancelResponse pddWaybillCancelResponse =
            pddAppsSDKService.execute(pddWaybillCancelRequest, topSession, appName);
        response.init(pddWaybillCancelResponse);
        if (!response.isSuccess()) {
            return response;
        }
        response.setCancelResult(pddWaybillCancelResponse.getPddWaybillCancelResponse().getCancelResult());
        return response;
    }

    /**
     * 获取平台id
     *
     * @return
     */
    @Override
    public String getPlatformId() {
        return CommonPlatformConstants.PLATFORM_PDD;
    }


}
