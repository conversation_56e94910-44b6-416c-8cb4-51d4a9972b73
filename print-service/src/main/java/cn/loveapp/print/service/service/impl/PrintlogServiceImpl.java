package cn.loveapp.print.service.service.impl;

import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import cn.loveapp.print.api.dto.AyDistributeInfoDTO;
import cn.loveapp.print.common.constant.EsFields;
import cn.loveapp.print.common.utils.ListUtil;
import cn.loveapp.print.common.dao.newprint.AyElefaceOperatelogDao;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;

import cn.loveapp.common.utils.LoggerHelper;
import cn.loveapp.common.utils.RedisUtil;
import cn.loveapp.print.api.dto.TradeLogisticsBindingHistoryResponseDTO;
import cn.loveapp.print.api.dto.WaybillOperateLogSaveDTO;
import cn.loveapp.print.api.request.CountPrintLogRequest;
import cn.loveapp.print.api.response.CountPrintLogResponse;
import cn.loveapp.print.api.response.LogisticsPrintlogResponseDTO;
import cn.loveapp.print.common.constant.PrintTypeConstant;
import cn.loveapp.print.common.constant.TradeTypeConstant;
import cn.loveapp.print.common.dto.RecipientDTO;
import cn.loveapp.print.common.entity.*;
import cn.loveapp.print.common.service.AesEncryptionService;
import cn.loveapp.print.common.utils.ElasticsearchUtil;
import cn.loveapp.print.common.utils.ShardingUtils;
import cn.loveapp.print.service.bo.*;
import cn.loveapp.print.service.config.PrintLogConfig;
import cn.loveapp.print.service.convert.CommonConvertMapper;
import cn.loveapp.print.service.dao.es.ElasticsearchAyPrintLogQueryDao;
import cn.loveapp.print.service.dao.newprint.*;
import cn.loveapp.print.service.dao.redis.PrintLogRedisDao;
import cn.loveapp.print.service.dto.*;
import cn.loveapp.print.service.platform.biz.PlatformPrintLogInfoService;
import cn.loveapp.print.service.request.PrintTradeInfoDTO;
import cn.loveapp.print.service.service.ElefaceWaybillService;
import cn.loveapp.print.service.service.OrdersService;
import cn.loveapp.print.service.service.PrintlogService;
import cn.loveapp.print.service.service.es.AyPrintLogSearchService;

/**
 * <AUTHOR>
 */
@Service
public class PrintlogServiceImpl implements PrintlogService {

    private final static LoggerHelper LOGGER = LoggerHelper.getLogger(PrintlogServiceImpl.class);

    /**
     * 打印记录表分表前缀
     */
    private static final String TABLE_PREFIX = "ay_printlog";

    /**
     * 日志搜索redis缓存key
     */
    private static final String PRINT_LOG_SEARCH_COUNT_PREFIX = " print:logList:count";

    /**
     * 日志计数聚合redis缓存key
     */
    private static final String PRINT_LOG_SEARCH_COUNT_AGG_PREFIX = "print:logList:statistics";

    /**
     * 是否启动打印日志MySQL搜索
     */
    @Value("${print.service.printLog.mysqlSearch.enable:true}")
    private boolean ayPrintLogSearchMysqlEnable;

    @Autowired
    private StringRedisTemplate redisTemplate;

    @Resource
    private AyPrintlogDao ayPrintlogDao;

    @Resource
    private AyElefacePrintlogDao ayElefacePrintlogDao;

    @Resource
    private AyExpressPrintlogDao ayExpressPrintlogDao;

    @Resource
    private AyDeliverPrintlogDao ayDeliverPrintlogDao;

    @Resource
    private AyElefaceOperatelogDao ayElefaceOperatelogDao;

    @Autowired
    private AesEncryptionService aesEncryptionService;

    @Autowired
    private OrdersService ordersService;

    @Autowired
    private ElefaceWaybillService elefaceWaybillService;

    @Autowired
    private PrintLogConfig printLogConfig;

    @Autowired
    private PlatformPrintLogInfoService platformPrintLogInfoService;

    @Autowired
    private PrintLogRedisDao printLogRedisDao;

    @Autowired
    private ElasticsearchAyPrintLogQueryDao elasticsearchAyPrintLogQueryDao;

    @Autowired
    private AyPrintLogSearchService ayPrintLogSearchService;

    /**
     * 面单打印
     *
     * @param elefacePrintBo
     * @param userInfoBo
     */
    @Override
    public void elefacePrint(ElefacePrintBo elefacePrintBo, UserInfoBo userInfoBo) {
        List<PrintTradeInfoDTO> tradeInfoList = elefacePrintBo.getTradeInfoList();
        String mergeTid = elefacePrintBo.getMergeTid();

        Integer tradeType = elefacePrintBo.getTradeType();

        String cpCode = elefacePrintBo.getCpCode();
        String logisticsCompany = elefacePrintBo.getLogisticsCompany();
        String waybillCode = elefacePrintBo.getWaybillCode();

        // 保存打印日志
        AyElefacePrintlog ayElefacePrintlog = saveElefacePrintLog(elefacePrintBo, userInfoBo);

        savePrintLog(elefacePrintBo, mergeTid, tradeInfoList, ayElefacePrintlog, userInfoBo);

        // 面单操作日志表保存打印信息
        elefaceWaybillService.saveWaybillPrintLog(cpCode, waybillCode, elefacePrintBo.getExternalInfo(), userInfoBo);

        if (!tradeType.equals(TradeTypeConstant.CUSTOM_TRADE) ) {
            Map<String, List<String>> tidAndOids = new HashMap<>(tradeInfoList.size());
            for (PrintTradeInfoDTO printTradeInfoDTO : tradeInfoList) {
                tidAndOids.put(printTradeInfoDTO.getTid(), printTradeInfoDTO.getOids());
            }

            // 同步打印记录到订单service
            ordersService.syncPrintLog(userInfoBo, mergeTid,
                tradeInfoList.stream().map(PrintTradeInfoDTO::getTid).collect(Collectors.toList()), tidAndOids,
                PrintTypeConstant.ELEFACE, waybillCode, logisticsCompany, ayElefacePrintlog.getPrintTime());
        }
    }


    /**
     * 快递单打印
     *
     * @param expressPrintBo
     * @param userInfoBo
     * @throws Exception
     *             入库异常
     */
    @Override
    public void expressPrint(ExpressPrintBo expressPrintBo, UserInfoBo userInfoBo) {
        List<PrintTradeInfoDTO> tradeInfoList = expressPrintBo.getTradeInfoList();
        String mergeTid = expressPrintBo.getMergeTid();

        Integer tradeType = expressPrintBo.getTradeType();
        Boolean isSplit = expressPrintBo.getIsSplit();
        String serial = expressPrintBo.getSerial();
        String printer = expressPrintBo.getPrinter();
        Integer printCount = expressPrintBo.getPrintCount();
        String logisticsCompany = expressPrintBo.getLogisticsCompany();

        RecipientDTO recipient = expressPrintBo.getRecipient();
        RecipientDTO sender = expressPrintBo.getSender();
        String buyerNick = expressPrintBo.getBuyerNick();

        String printModule = expressPrintBo.getPrintModule();
        String printData = expressPrintBo.getPrintData();
        String expressImage = expressPrintBo.getExpressImage();
        String operatorName = expressPrintBo.getOperatorName();
        LocalDateTime printTime = LocalDateTime.now();
        AyExpressPrintlog ayExpressPrintlog = saveExpressPrintLog(printer, printCount, logisticsCompany, recipient,
            printModule, printData, expressImage, userInfoBo, printTime, operatorName);

        if (!StringUtils.isEmpty(mergeTid)) {
            // 合单
            savePrintLog(mergeTid, tradeInfoList, isSplit, tradeType, serial, buyerNick, ayExpressPrintlog, userInfoBo,
                sender);
        } else {
            savePrintLog(tradeInfoList.get(0), isSplit, tradeType, serial, buyerNick, ayExpressPrintlog, userInfoBo,
                sender);
        }

        if (!tradeType.equals(TradeTypeConstant.CUSTOM_TRADE)) {
            // 同步打印记录到订单service
            ordersService.syncPrintLog(userInfoBo, mergeTid,
                tradeInfoList.stream().map(PrintTradeInfoDTO::getTid).collect(Collectors.toList()), null,
                PrintTypeConstant.EXPRESS, null, logisticsCompany, ayExpressPrintlog.getPrintTime());
        }
    }

    /**
     * 发货单打印
     *
     * @param deliverPrintBo
     * @param userInfoBo
     * @throws Exception
     *             入库异常
     */
    @Override
    public void deliverPrint(DeliverPrintBo deliverPrintBo, UserInfoBo userInfoBo) {
        List<PrintTradeInfoDTO> tradeInfoList = deliverPrintBo.getTradeInfoList();
        String mergeTid = deliverPrintBo.getMergeTid();

        Integer tradeType = deliverPrintBo.getTradeType();
        Boolean isSplit = deliverPrintBo.getIsSplit();

        String serial = deliverPrintBo.getSerial();
        String buyerNick = deliverPrintBo.getBuyerNick();
        String printer = deliverPrintBo.getPrinter();
        Integer printCount = deliverPrintBo.getPrintCount();

        RecipientDTO recipient = deliverPrintBo.getRecipient();
        RecipientDTO sender = deliverPrintBo.getSender();

        String printModule = deliverPrintBo.getPrintModule();
        String printData = deliverPrintBo.getPrintData();
        String operatorName = deliverPrintBo.getOperatorName();

        AyDeliverPrintlog ayDeliverPrintlog =
            saveDeliverPrintLog(printer, printCount, recipient, printModule, printData,  userInfoBo, operatorName);

        if (!StringUtils.isEmpty(mergeTid)) {
            // 合单
            savePrintLog(mergeTid, tradeInfoList, isSplit, tradeType, serial, buyerNick, ayDeliverPrintlog, userInfoBo,
                sender);
        } else {
            savePrintLog(tradeInfoList.get(0), isSplit, tradeType, serial, buyerNick, ayDeliverPrintlog, userInfoBo,
                sender);
        }
        if (!tradeType.equals(TradeTypeConstant.CUSTOM_TRADE)) {
            // 同步打印记录到订单service
            ordersService.syncPrintLog(userInfoBo, mergeTid,
                tradeInfoList.stream().map(PrintTradeInfoDTO::getTid).collect(Collectors.toList()), null,
                PrintTypeConstant.DELIVER, null, null, null);
        }
    }

    /**
     * 保存电子面单打印日志
     *
     * @param elefacePrintBo
     * @param userInfoBo
     * @return printId
     */
    private AyElefacePrintlog saveElefacePrintLog(ElefacePrintBo elefacePrintBo, UserInfoBo userInfoBo) {
        AyElefacePrintlog ayElefacePrintlog = new AyElefacePrintlog();
        ayElefacePrintlog.setAppName(userInfoBo.getAppName());
        ayElefacePrintlog.setSellerId(userInfoBo.getSellerId());
        ayElefacePrintlog.setStoreId(userInfoBo.getStoreId());
        ayElefacePrintlog.setSellerNick(userInfoBo.getSellerNick());
        ayElefacePrintlog.setOperatorStoreId(userInfoBo.getOperatorStoreId());
        ayElefacePrintlog.setOperateTerminal(userInfoBo.getOperateTerminal());
        ayElefacePrintlog.setPrinter(elefacePrintBo.getPrinter());
        ayElefacePrintlog.setPrintCount(elefacePrintBo.getPrintCount());
        if (elefacePrintBo.getPrintTime() != null) {
            ayElefacePrintlog.setPrintTime(elefacePrintBo.getPrintTime());
        } else {
            ayElefacePrintlog.setPrintTime(LocalDateTime.now());
        }
        ayElefacePrintlog.setProvider(elefacePrintBo.getProvider());
        ayElefacePrintlog.setCpCode(elefacePrintBo.getCpCode());
        ayElefacePrintlog.setLogisticsCompany(elefacePrintBo.getLogisticsCompany());
        ayElefacePrintlog.setWaybillCode(elefacePrintBo.getWaybillCode());
        ayElefacePrintlog.setIsChild(null != elefacePrintBo.getChildWaybillCode());
        ayElefacePrintlog.setChildWaybillCode(elefacePrintBo.getChildWaybillCode());
        if (StringUtils.isEmpty(elefacePrintBo.getOperatorName())) {
            ayElefacePrintlog.setOperator(userInfoBo.getOperator());
        } else {
            ayElefacePrintlog.setOperator(elefacePrintBo.getOperatorName());
        }

        ayElefacePrintlog.setPrintData(elefacePrintBo.getPrintData());
        ayElefacePrintlog.setCustomData(elefacePrintBo.getCustomData());
        ayElefacePrintlog.setBrandCode(elefacePrintBo.getBrandCode());
        ayElefacePrintlog.setExternalInfo(elefacePrintBo.getExternalInfo());
        ayElefacePrintlog.setRealCpCode(elefacePrintBo.getRealCpCode());

        ayElefacePrintlog.saveRecipient(encryptRecipient(elefacePrintBo.getRecipient()));
        ayElefacePrintlog.setBillVersion(elefacePrintBo.getBillVersion());
        ayElefacePrintlogDao.insert(ayElefacePrintlog);

        return ayElefacePrintlog;
    }

    /**
     * 保存快递单打印日志
     *
     * @param printer
     * @param printCount
     * @param logisticsCompany
     * @param printModule
     * @param printData
     * @param expressImage
     * @param recipient
     * @param printTime
     * @return
     */
    private AyExpressPrintlog saveExpressPrintLog(String printer, Integer printCount, String logisticsCompany,
        RecipientDTO recipient, String printModule, String printData, String expressImage, UserInfoBo userInfoBo,
        LocalDateTime printTime, String operatorName) {

        AyExpressPrintlog ayExpressPrintlog = new AyExpressPrintlog();
        ayExpressPrintlog.setAppName(userInfoBo.getAppName());
        ayExpressPrintlog.setSellerId(userInfoBo.getSellerId());
        ayExpressPrintlog.setStoreId(userInfoBo.getStoreId());
        ayExpressPrintlog.setSellerNick(userInfoBo.getSellerNick());
        ayExpressPrintlog.setOperatorStoreId(userInfoBo.getOperatorStoreId());
        ayExpressPrintlog.setOperateTerminal(userInfoBo.getOperateTerminal());
        ayExpressPrintlog.setPrinter(printer);
        ayExpressPrintlog.setPrintCount(printCount);
        ayExpressPrintlog.setPrintTime(printTime);
        ayExpressPrintlog.setLogisticsCompany(logisticsCompany);
        if (StringUtils.isEmpty(operatorName)) {
            ayExpressPrintlog.setOperator(userInfoBo.getOperator());
        } else {
            ayExpressPrintlog.setOperator(operatorName);
        }

        ayExpressPrintlog.setPrintModule(printModule);
        ayExpressPrintlog.setPrintData(printData);
        ayExpressPrintlog.setExpressImage(expressImage);

        ayExpressPrintlog.saveRecipient(encryptRecipient(recipient));

        ayExpressPrintlogDao.insert(ayExpressPrintlog);

        return ayExpressPrintlog;
    }

    private AyDeliverPrintlog saveDeliverPrintLog(String printer, Integer printCount, RecipientDTO recipient,
        String printModule, String printData, UserInfoBo userInfoBo, String operatorName) {

        LocalDateTime printTime = LocalDateTime.now();
        AyDeliverPrintlog ayDeliverPrintlog = new AyDeliverPrintlog();
        ayDeliverPrintlog.setAppName(userInfoBo.getAppName());
        ayDeliverPrintlog.setSellerId(userInfoBo.getSellerId());
        ayDeliverPrintlog.setStoreId(userInfoBo.getStoreId());
        ayDeliverPrintlog.setSellerNick(userInfoBo.getSellerNick());
        ayDeliverPrintlog.setOperatorStoreId(userInfoBo.getOperatorStoreId());
        ayDeliverPrintlog.setOperateTerminal(userInfoBo.getOperateTerminal());
        ayDeliverPrintlog.setPrinter(printer);
        ayDeliverPrintlog.setPrintCount(printCount);
        ayDeliverPrintlog.setPrintTime(printTime);
        if (StringUtils.isEmpty(operatorName)) {
            ayDeliverPrintlog.setOperator(userInfoBo.getOperator());
        } else {
            ayDeliverPrintlog.setOperator(operatorName);
        }

        ayDeliverPrintlog.setPrintModule(printModule);
        ayDeliverPrintlog.setPrintData(printData);
        ayDeliverPrintlog.saveRecipient(encryptRecipient(recipient));

        // 根据配置判断是否需要存储发货单打印日志
        Boolean isSaveDeliver = printLogConfig.getIsSaveDeliver();
        if (BooleanUtils.isTrue(isSaveDeliver)) {
            ayDeliverPrintlogDao.insert(ayDeliverPrintlog);
        }

        return ayDeliverPrintlog;
    }

    private void savePrintLog(ElefacePrintBo elefacePrintBo, String mergeTid, List<PrintTradeInfoDTO> tradeInfoList, AyElefacePrintlog ayElefacePrintlog, UserInfoBo userInfoBo) {
        List<AyPrintlog> ayPrintlogList = new ArrayList<>();
        List<AyPrintLogSearchEs> ayPrintLogSearchEsList = new ArrayList<>();

        Integer tradeType = elefacePrintBo.getTradeType();
        Boolean isSplit = elefacePrintBo.getIsSplit();
        String serial = elefacePrintBo.getSerial();
        String buyerNick = elefacePrintBo.getBuyerNick();
        String logisticsTemplateName = elefacePrintBo.getLogisticsTemplateName();

        List<Integer> numbersInBatch = elefacePrintBo.getNumbersInBatch();
        Integer batchTotals = elefacePrintBo.getBatchTotals();
        String batchId = elefacePrintBo.getBatchId();
        RecipientDTO sender = elefacePrintBo.getSender();
        RecipientDTO encryptSenderInfo = encryptRecipient(sender);
        for (PrintTradeInfoDTO tradeInfo : tradeInfoList) {
            AyPrintlog ayPrintlog = new AyPrintlog();
            ayPrintlog.setAppName(userInfoBo.getAppName());
            ayPrintlog.setSellerId(userInfoBo.getSellerId());
            ayPrintlog.setSellerNick(userInfoBo.getSellerNick());
            ayPrintlog.setStoreId(userInfoBo.getStoreId());
            ayPrintlog.setTid(tradeInfo.getTid());
            ayPrintlog.setMergeTid(mergeTid);
            ayPrintlog.setIsSplit(isSplit);
            ayPrintlog.setTradeType(tradeType);
            ayPrintlog.saveOids(tradeInfo.getOids());
            ayPrintlog.setSerial(serial);
            ayPrintlog.setBuyerNick(aesEncryptionService.encryptForTrade(buyerNick));
            ayPrintlog.savePrintInfo(ayElefacePrintlog);
            ayPrintlog.setLogisticsTemplateName(logisticsTemplateName);
            ayPrintlog.setGmtModified(LocalDateTime.now());
            ayPrintlog.saveSendsInfo(encryptSenderInfo);
            if (Objects.nonNull(tradeInfo.getAyDistributeInfo())) {
                AyDistributeInfo ayDistributeInfo = CommonConvertMapper.INSTANCE.toAyDistribute(tradeInfo.getAyDistributeInfo());
                ayPrintlog.setAyDistribute(ayDistributeInfo.toString());
            }
            if (CollectionUtils.isNotEmpty(numbersInBatch)) {
                Collections.sort(numbersInBatch);
                ayPrintlog.setNumbersInBatch(StringUtils.join(numbersInBatch, ","));
            }
            ayPrintlog.setBatchId(batchId);
            ayPrintlog.setBatchTotals(batchTotals);
            ayPrintlogList.add(ayPrintlog);

            // 生成需要标记重复打印的es存储列表
            List<AyPrintLogSearchEs> updateAyPrintLogSearchEs = getNeedUpdateAyPrintLogSearchEs(ayPrintlog);
            if (CollectionUtils.isNotEmpty(updateAyPrintLogSearchEs)) {
                ayPrintLogSearchEsList.addAll(updateAyPrintLogSearchEs);
            }
        }

        ayPrintlogDao.insertBatch(ayPrintlogList);

        List<AyPrintLogSearchEs> insertAyPrintLogSearchEsList =
                AyPrintLogSearchEs.ayPrintLogListToAyPrintLogSearchEsList(ayPrintlogList, null, null);
        if (CollectionUtils.isNotEmpty(insertAyPrintLogSearchEsList)) {
            ayPrintLogSearchEsList.addAll(insertAyPrintLogSearchEsList);
        }

        // 处理下收件人信息（解密、脱敏）
        appendReceiverSearchInfo(ayPrintLogSearchEsList);
        elasticsearchAyPrintLogQueryDao.saveAll(ayPrintLogSearchEsList, Boolean.TRUE);
        printLogRedisDao.delUserPrintType(userInfoBo.getSellerId(), userInfoBo.getAppName(), userInfoBo.getStoreId());
    }

    /**
     * 追加打印日志查询信息
     * @param ayPrintLogSearchEsList
     */
    private void appendReceiverSearchInfo(List<AyPrintLogSearchEs> ayPrintLogSearchEsList) {
        for (AyPrintLogSearchEs ayPrintLogSearchEs : ayPrintLogSearchEsList) {

            String encryptReceiverName = ayPrintLogSearchEs.getReceiverName();
            String encryptReceiverPhone = ayPrintLogSearchEs.getReceiverPhone();
            String encryptReceiverMobile = ayPrintLogSearchEs.getReceiverMobile();
            String encryptSendersName = ayPrintLogSearchEs.getSenderName();
            String encryptSendersPhone = ayPrintLogSearchEs.getSenderPhone();
            String encryptSendersMobile = ayPrintLogSearchEs.getSenderMobile();

            String decryptReceiverName = null;
            String decryptReceiverPhone = null;
            String decryptReceiverMobile = null;
            String decryptSendersName = null;
            String decryptSendersPhone = null;
            String decryptSendersMobile = null;
            if (StringUtils.isEmpty(encryptReceiverName)) {
                decryptReceiverName =
                    ElasticsearchUtil.desensitiseName(aesEncryptionService.decryptForTrade(encryptReceiverName));
            }

            if (StringUtils.isNotEmpty(encryptReceiverPhone)) {
                decryptReceiverPhone = aesEncryptionService.decryptForPhone(encryptReceiverPhone);

            }

            if (StringUtils.isNotEmpty(encryptReceiverMobile)) {
                decryptReceiverMobile = aesEncryptionService.decryptForPhone(encryptReceiverMobile);
            }

            if (StringUtils.isNotEmpty(encryptSendersName)) {
                decryptSendersName =
                    ElasticsearchUtil.desensitiseName(aesEncryptionService.decryptForTrade(encryptSendersName));
            }

            if (StringUtils.isNotEmpty(encryptSendersPhone)) {
                decryptSendersPhone =
                    ElasticsearchUtil.desensitisePhone(aesEncryptionService.decryptForPhone(encryptSendersPhone));
            }

            if (StringUtils.isNotEmpty(encryptSendersMobile)) {
                decryptSendersMobile =
                    ElasticsearchUtil.desensitisePhone(aesEncryptionService.decryptForPhone(encryptSendersMobile));
            }

            // 将加密串存储到指定字段
            ayPrintLogSearchEs.setEncryptReceiverName(encryptReceiverName);

            // 收件人信息解密并脱敏
            ayPrintLogSearchEs.setReceiverName(decryptReceiverName);
            ayPrintLogSearchEs.setReceiverPhone(decryptReceiverPhone);
            ayPrintLogSearchEs.setReceiverMobile(decryptReceiverMobile);
            // 发货人解密并脱敏
            ayPrintLogSearchEs.setSenderName(decryptSendersName);
            ayPrintLogSearchEs.setSenderPhone(decryptSendersPhone);
            ayPrintLogSearchEs.setSenderMobile(decryptSendersMobile);
        }
    }

    /**
     * 查询当前面单是否存在重复打印并标记
     *
     * @param ayPrintlog
     * @return
     */
    private List<AyPrintLogSearchEs> getNeedUpdateAyPrintLogSearchEs(AyPrintlog ayPrintlog) {
        List<AyPrintLogSearchEs> updateAyPrintLogSearchEs = null;
        // 查询库中是否存在(只电子面单及快递单)，若存在标记为重复打印, 并插入最新的
        if ((PrintTypeConstant.ELEFACE.equals(ayPrintlog.getPrintType())
            || PrintTypeConstant.EXPRESS.equals(ayPrintlog.getPrintType()))
            && StringUtils.isNotBlank(ayPrintlog.getTid())) {
            List<AyPrintlog> needUpdateAyPrintLogs = ayPrintlogDao.queryRepetitionPrintWaybill(ayPrintlog.getSellerId(),
                ayPrintlog.getTid(), ayPrintlog.getWaybillCode(), ayPrintlog.getChildWaybillCode(),
                ayPrintlog.getPrintType(), ayPrintlog.getStoreId(), ayPrintlog.getAppName());

            if (CollectionUtils.isNotEmpty(needUpdateAyPrintLogs)) {
                Date nowDate = new Date();
                updateAyPrintLogSearchEs =
                    AyPrintLogSearchEs.ayPrintLogListToAyPrintLogSearchEsList(needUpdateAyPrintLogs, nowDate, true);
            }
        }

        return updateAyPrintLogSearchEs;
    }


    private void savePrintLog(String mergeTid, List<PrintTradeInfoDTO> tradeInfoList, Boolean isSplit,
        Integer tradeType, String serial, String buyerNick, AyExpressPrintlog ayExpressPrintlog,
        UserInfoBo userInfoBo, RecipientDTO sender) {
        List<AyPrintlog> ayPrintlogList = new ArrayList<>();
        LocalDateTime nowDate = LocalDateTime.now();
        List<AyPrintLogSearchEs> ayPrintLogSearchEsList = new ArrayList<>();
        for (PrintTradeInfoDTO tradeInfoDTO : tradeInfoList) {
            AyPrintlog ayPrintlog = new AyPrintlog();
            ayPrintlog.setAppName(userInfoBo.getAppName());
            ayPrintlog.setSellerId(userInfoBo.getSellerId());
            ayPrintlog.setSellerNick(userInfoBo.getSellerNick());
            ayPrintlog.setStoreId(userInfoBo.getStoreId());
            ayPrintlog.setTid(tradeInfoDTO.getTid());
            ayPrintlog.setTradeType(tradeType);
            ayPrintlog.setMergeTid(mergeTid);
            ayPrintlog.saveOids(tradeInfoDTO.getOids());
            ayPrintlog.setIsSplit(isSplit);
            ayPrintlog.setSerial(serial);
            ayPrintlog.setBuyerNick(aesEncryptionService.encryptForTrade(buyerNick));
            ayPrintlog.savePrintInfo(ayExpressPrintlog);
            ayPrintlog.setGmtModified(nowDate);
            ayPrintlog.saveSendsInfo(sender);
            if (Objects.nonNull(tradeInfoDTO.getAyDistributeInfo())) {
                AyDistributeInfo ayDistributeInfo = CommonConvertMapper.INSTANCE.toAyDistribute(tradeInfoDTO.getAyDistributeInfo());
                ayPrintlog.setAyDistribute(ayDistributeInfo.toString());
            }
            ayPrintlogList.add(ayPrintlog);

            // 生成需要标记重复打印的es存储列表
            List<AyPrintLogSearchEs> updateAyPrintLogSearchEs = getNeedUpdateAyPrintLogSearchEs(ayPrintlog);
            if (CollectionUtils.isNotEmpty(updateAyPrintLogSearchEs)) {
                ayPrintLogSearchEsList.addAll(updateAyPrintLogSearchEs);
            }
        }

        ayPrintlogDao.insertBatch(ayPrintlogList);
        List<AyPrintLogSearchEs> insertAyPrintLogSearchEsList =
            AyPrintLogSearchEs.ayPrintLogListToAyPrintLogSearchEsList(ayPrintlogList, null, null);
        if (CollectionUtils.isNotEmpty(insertAyPrintLogSearchEsList)){
            ayPrintLogSearchEsList.addAll(insertAyPrintLogSearchEsList);
        }

        // 处理下收件人信息（解密、脱敏）
        appendReceiverSearchInfo(ayPrintLogSearchEsList);
        elasticsearchAyPrintLogQueryDao.saveAll(ayPrintLogSearchEsList, Boolean.TRUE);
        printLogRedisDao.delUserPrintType(userInfoBo.getSellerId(), userInfoBo.getAppName(), userInfoBo.getStoreId());
    }

    private void savePrintLog(PrintTradeInfoDTO tradeInfo, Boolean isSplit, Integer tradeType, String serial,
        String buyerNick, AyExpressPrintlog ayExpressPrintlog, UserInfoBo userInfoBo, RecipientDTO sender) {
        savePrintLog(null, Collections.singletonList(tradeInfo), isSplit, tradeType, serial, buyerNick,
            ayExpressPrintlog, userInfoBo, sender);
    }

    private void savePrintLog(String mergeTid, List<PrintTradeInfoDTO> tradeInfoList, Boolean isSplit,
        Integer tradeType, String serial, String buyerNick, AyDeliverPrintlog ayDeliverPrintlog, UserInfoBo userInfoBo,
        RecipientDTO sender) {
        List<AyPrintlog> ayPrintlogList = new ArrayList<>();
        for (PrintTradeInfoDTO tradeInfoDTO : tradeInfoList) {
            AyPrintlog ayPrintlog = new AyPrintlog();
            LocalDateTime nowDate = LocalDateTime.now();
            ayPrintlog.setAppName(userInfoBo.getAppName());
            ayPrintlog.setSellerId(userInfoBo.getSellerId());
            ayPrintlog.setSellerNick(userInfoBo.getSellerNick());
            ayPrintlog.setStoreId(userInfoBo.getStoreId());
            ayPrintlog.setTid(tradeInfoDTO.getTid());
            ayPrintlog.setTradeType(tradeType);
            ayPrintlog.setMergeTid(mergeTid);
            ayPrintlog.setIsSplit(isSplit);
            ayPrintlog.saveOids(tradeInfoDTO.getOids());
            ayPrintlog.setSerial(serial);
            ayPrintlog.setBuyerNick(aesEncryptionService.encryptForTrade(buyerNick));
            ayPrintlog.savePrintInfo(ayDeliverPrintlog);
            ayPrintlog.saveSendsInfo(sender);
            ayPrintlog.setGmtModified(nowDate);
            if (Objects.nonNull(tradeInfoDTO.getAyDistributeInfo())) {
                AyDistributeInfo ayDistributeInfo = CommonConvertMapper.INSTANCE.toAyDistribute(tradeInfoDTO.getAyDistributeInfo());
                ayPrintlog.setAyDistribute(ayDistributeInfo.toString());
            }
            ayPrintlogList.add(ayPrintlog);
        }

        ayPrintlogDao.insertBatch(ayPrintlogList);
        List<AyPrintLogSearchEs> insertAyPrintLogSearchEsList =
            AyPrintLogSearchEs.ayPrintLogListToAyPrintLogSearchEsList(ayPrintlogList, null, null);
        // 处理下收件人信息（解密、脱敏）
        appendReceiverSearchInfo(insertAyPrintLogSearchEsList);
        elasticsearchAyPrintLogQueryDao.saveAll(insertAyPrintLogSearchEsList, Boolean.TRUE);
        printLogRedisDao.delUserPrintType(userInfoBo.getSellerId(), userInfoBo.getAppName(), userInfoBo.getStoreId());
    }

    private void savePrintLog(PrintTradeInfoDTO tradeInfo, Boolean isSplit, Integer tradeType, String serial,
        String buyerNick, AyDeliverPrintlog ayDeliverPrintlog, UserInfoBo userInfoBo, RecipientDTO sender) {
        savePrintLog(null, Collections.singletonList(tradeInfo), isSplit, tradeType, serial, buyerNick,
            ayDeliverPrintlog, userInfoBo, sender);
    }

    @Override
    public void saveWaybillIsCancel(String cpCode, String waybillCode, UserInfoBo userInfoBo) {
        String sellerId = userInfoBo.getSellerId();
        String storeId = userInfoBo.getStoreId();
        String appName = userInfoBo.getAppName();
        ayPrintlogDao.updateWaybillIsCancel(true, cpCode, waybillCode, storeId, sellerId, appName);
        List<AyPrintlog> ayPrintLogList =
            ayPrintlogDao.queryBySellerIdAndWaybillCode(sellerId, cpCode, waybillCode, storeId, appName);
        List<AyPrintLogSearchEs> ayPrintLogSearchEsList =
            AyPrintLogSearchEs.ayPrintLogListToAyPrintLogSearchEsList(ayPrintLogList, null, null);
        if (CollectionUtils.isNotEmpty(ayPrintLogSearchEsList)) {
            elasticsearchAyPrintLogQueryDao.batchUpdateByIdsWithNotNull(ayPrintLogSearchEsList,
                Lists.newArrayList(EsFields.isCancel));
        }
    }

    @Override
    public TradeLogisticsBindingHistoryResponseDTO queryTradeLogisticsBindingHistoryBatch(List<String> tids,
        Integer tradeType, Boolean needPrintData, UserInfoBo userInfoBo) {
        // 先查快递单打印记录
        List<AyPrintlog> printlogList = ayPrintlogDao.queryByTidsAndPrintType(tids, tradeType,
            PrintTypeConstant.EXPRESS, userInfoBo.getStoreId(), userInfoBo.getSellerId(), userInfoBo.getAppName());
        HashMap<String,
            List<TradeLogisticsBindingHistoryResponseDTO.LogisticsBindingHistoryDTO>> bindingHistoryListMap =
                new HashMap<>(tids.size());
        for (AyPrintlog printlog : printlogList) {
            TradeLogisticsBindingHistoryResponseDTO.LogisticsBindingHistoryDTO logisticsBindingHistoryDTO =
                new TradeLogisticsBindingHistoryResponseDTO.LogisticsBindingHistoryDTO();
            String tid = printlog.getTid();
            logisticsBindingHistoryDTO.setSerial(printlog.getSerial());
            logisticsBindingHistoryDTO.setOids(printlog.getOids());
            logisticsBindingHistoryDTO.setPrintType(printlog.getPrintType());
            logisticsBindingHistoryDTO.setLogisticsCompany(printlog.getLogisticsCompany());
            logisticsBindingHistoryDTO.setBindingTime(printlog.getPrintTime());
            if (null == bindingHistoryListMap.get(tid)) {
                bindingHistoryListMap.put(tid, Lists.newArrayList(logisticsBindingHistoryDTO));
            } else {
                bindingHistoryListMap.get(tid).add(logisticsBindingHistoryDTO);
            }
        }

        // 查询电子面单的操作日志
        List<AyElefaceOperatelog> operatelogList = ayElefaceOperatelogDao.queryByTids(tids, tradeType,
            userInfoBo.getStoreId(), userInfoBo.getSellerId(), userInfoBo.getAppName());
        for (AyElefaceOperatelog operatelog : operatelogList) {
            TradeLogisticsBindingHistoryResponseDTO.LogisticsBindingHistoryDTO logisticsBindingHistoryDTO =
                new TradeLogisticsBindingHistoryResponseDTO.LogisticsBindingHistoryDTO();
            //获取平台Id
            String platformId = operatelog.getStoreId();
            //拼接各平台参数(差异)
            platformPrintLogInfoService.appendInfo(logisticsBindingHistoryDTO, operatelog,
                platformId, userInfoBo.getAppName());

            String tid = operatelog.getTid();
            logisticsBindingHistoryDTO.setSerial(operatelog.getSerial());
            logisticsBindingHistoryDTO.setOids(operatelog.getOids());
            logisticsBindingHistoryDTO.setPrintType(PrintTypeConstant.ELEFACE);
            logisticsBindingHistoryDTO.setLogisticsCompany(operatelog.getLogisticsCompany());
            logisticsBindingHistoryDTO.setProvider(operatelog.getProvider());
            logisticsBindingHistoryDTO.setOwnerNick(operatelog.getOwnerNick());
            logisticsBindingHistoryDTO.setCpCode(operatelog.getCpCode());
            logisticsBindingHistoryDTO.setRealCpCode(operatelog.getRealCpCode());
            logisticsBindingHistoryDTO.setWaybillCode(operatelog.getWaybillCode());
            logisticsBindingHistoryDTO.setIsChild(operatelog.getIsChild());
            logisticsBindingHistoryDTO.setChildWaybillCode(operatelog.getChildWaybillCode());
            logisticsBindingHistoryDTO.setWaybillIsCancel(operatelog.getIsCancel());
            logisticsBindingHistoryDTO.setPrintCount(operatelog.getPrintCount());
            logisticsBindingHistoryDTO.setBindingTime(operatelog.getGetTime());
            logisticsBindingHistoryDTO.setExternalInfo(operatelog.getExternalInfo());
            logisticsBindingHistoryDTO.setBillVersion(operatelog.getBillVersion());
            logisticsBindingHistoryDTO.setOtherExternalInfo(operatelog.getOtherExternalInfo());
            if (null != needPrintData && needPrintData) {
                logisticsBindingHistoryDTO.setPrintData(operatelog.getPrintData());
            }

            if (null == bindingHistoryListMap.get(tid)) {
                bindingHistoryListMap.put(tid, Lists.newArrayList(logisticsBindingHistoryDTO));
            } else {
                bindingHistoryListMap.get(tid).add(logisticsBindingHistoryDTO);
            }
        }

        List<
            TradeLogisticsBindingHistoryResponseDTO.TradeLogisticsBindingHistoryDTO> tradeLogisticsBindingHistoryDTOList =
                new ArrayList<>();
        for (String tid : bindingHistoryListMap.keySet()) {
            TradeLogisticsBindingHistoryResponseDTO.TradeLogisticsBindingHistoryDTO tradeLogisticsBindingHistoryDTO =
                new TradeLogisticsBindingHistoryResponseDTO.TradeLogisticsBindingHistoryDTO();
            tradeLogisticsBindingHistoryDTO.setTid(tid);
            tradeLogisticsBindingHistoryDTO.setBindingHistoryList(bindingHistoryListMap.get(tid));
            tradeLogisticsBindingHistoryDTOList.add(tradeLogisticsBindingHistoryDTO);
        }

        TradeLogisticsBindingHistoryResponseDTO responseDTO = new TradeLogisticsBindingHistoryResponseDTO();
        responseDTO.setTradeLogisticsBindingHistoryList(tradeLogisticsBindingHistoryDTOList);

        return responseDTO;
    }

    @Override
    public List<TradeDeliverPrintStatusDTO> queryTradeDeliverPrintStatus(List<String> tids, Integer tradeType,
        UserInfoBo userInfoBo) {
        List<AyPrintlog> printlogList = ayPrintlogDao.queryByTidsAndPrintType(tids, tradeType,
            PrintTypeConstant.DELIVER, userInfoBo.getStoreId(), userInfoBo.getSellerId(), userInfoBo.getAppName());
        Map<String, List<AyPrintlog>> tidToPrintLogListMap =
            printlogList.stream().collect(Collectors.groupingBy(AyPrintlog::getTid));
        return tids.stream().map(tid -> {
            TradeDeliverPrintStatusDTO tradeDeliverPrintStatusDTO = new TradeDeliverPrintStatusDTO();
            tradeDeliverPrintStatusDTO.setTid(tid);
            tradeDeliverPrintStatusDTO.setIsPrint(CollectionUtils.isNotEmpty(tidToPrintLogListMap.get(tid)));
            return tradeDeliverPrintStatusDTO;
        }).collect(Collectors.toList());
    }

    /**
     * 对打印日志结果排序
     *
     * @param logisticsPrintlogDTOList
     * @return
     */
    private List<LogisticsPrintlogResponseDTO.LogisticsPrintlogDTO>
        sortLogisticsPrintLogList(List<LogisticsPrintlogResponseDTO.LogisticsPrintlogDTO> logisticsPrintlogDTOList) {
        // 排序
        if (CollectionUtils.isNotEmpty(logisticsPrintlogDTOList)) {
            logisticsPrintlogDTOList = logisticsPrintlogDTOList.stream()
                .sorted(Comparator.comparing(LogisticsPrintlogResponseDTO.LogisticsPrintlogDTO::getPrintTime)
                    .thenComparing(LogisticsPrintlogResponseDTO.LogisticsPrintlogDTO::getId).reversed())
                .collect(Collectors.toList());
        }
        return logisticsPrintlogDTOList;
    }

    @Override
    public LogisticsPrintlogResponseDTO logisticsPrintLogListGetByESSearch(LogisticsPrintlogQueryDTO queryDTO, List<UserInfoBo> userInfoBoList) {
        // es搜索
        List<String> receiverCityList = Lists.newArrayList();
        List<String> receiverProvinceList = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(queryDTO.getReceiverRegionList())) {
            // 处理一些区域别名
            for (String region : queryDTO.getReceiverRegionList()) {
                if (CollectionUtils.isNotEmpty(printLogConfig.regionAliasToProvincesMap(region))) {
                    receiverProvinceList.addAll(printLogConfig.regionAliasToProvincesMap(region));
                } else if (CollectionUtils.isNotEmpty(printLogConfig.regionAliasToCitiesMap(region))) {
                    receiverCityList.addAll(printLogConfig.regionAliasToCitiesMap(region));
                } else {
                    // 不是别名, 传入的是正常的省份
                    receiverProvinceList.add(region);
                }
            }
            queryDTO.setReceiverProvinceList(receiverProvinceList);
            queryDTO.getReceiverCityList().addAll(receiverCityList);
        }

        AyPrintLogListSearchResultDTO searchResult = getSearchResult(queryDTO, userInfoBoList);
        Long totalResult = searchResult.getTotalResults();
        List<AyPrintlog> ayPrintlogList = searchResult.getAyPrintlogList();

        Map<String, List<AyPrintlog>> sellerIdAndAyPrintlogMap =
            ayPrintlogList.stream().collect(Collectors.groupingBy(AyPrintlog::getSellerId));
        List<LogisticsPrintlogResponseDTO.LogisticsPrintlogDTO> logisticsPrintlogDTOList = new ArrayList<>();
        for (UserInfoBo userInfoBo : userInfoBoList) {
            if (totalResult != null && totalResult > 0 && CollectionUtils.isNotEmpty(ayPrintlogList)) {
                Map<Long, AyElefacePrintlog> elefacePrintlogMap = new HashMap<>();
                Map<Long, AyExpressPrintlog> expressPrintlogMap = new HashMap<>();
                List<AyPrintlog> ayPrintLogs = sellerIdAndAyPrintlogMap.get(userInfoBo.getSellerId());
                if (CollectionUtils.isNotEmpty(ayPrintLogs)) {
                    if (queryDTO.isNeedQueryElefaceAndExpressLog()) {
                        // 过滤print_id
                        Map<String, List<Long>> printIdMap = ayPrintLogs.stream().collect(Collectors
                            .groupingBy(AyPrintlog::getPrintType, Collectors.mapping(AyPrintlog::getPrintId, Collectors.toList())));
                        // 查询电子面单打印日志
                        List<Long> elefacPrintIds = printIdMap.get(PrintTypeConstant.ELEFACE);
                        List<AyElefacePrintlog> elefacePrintlogList = CollectionUtils.isEmpty(elefacPrintIds) ? Lists.newArrayList()
                            : ayElefacePrintlogDao.queryPrintData(elefacPrintIds, userInfoBo.getSellerId());
                        // 查询快递单打印日志
                        List<Long> expressPrintIds = printIdMap.get(PrintTypeConstant.EXPRESS);
                        List<AyExpressPrintlog> expressPrintlogList = CollectionUtils.isEmpty(expressPrintIds)
                            ? Lists.newArrayList() : ayExpressPrintlogDao.queryPrintData(expressPrintIds, userInfoBo.getSellerId());
                        // 生成 id->printLog的map
                        elefacePrintlogMap =
                            elefacePrintlogList.stream().collect(Collectors.toMap(AyElefacePrintlog::getId, Function.identity()));
                        expressPrintlogMap =
                            expressPrintlogList.stream().collect(Collectors.toMap(AyExpressPrintlog::getId, Function.identity()));
                    }

                    for (AyPrintlog ayPrintlog : ayPrintLogs) {
                        LogisticsPrintlogResponseDTO.LogisticsPrintlogDTO logisticsPrintlogDTO =
                            new LogisticsPrintlogResponseDTO.LogisticsPrintlogDTO();
                        logisticsPrintlogDTO.setId(ayPrintlog.getId());
                        logisticsPrintlogDTO.setSellerNick(ayPrintlog.getSellerNick());
                        logisticsPrintlogDTO.setAppName(ayPrintlog.getAppName());
                        logisticsPrintlogDTO.setStoreId(ayPrintlog.getStoreId());
                        logisticsPrintlogDTO.setTid(ayPrintlog.getTid());
                        logisticsPrintlogDTO.setTradeType(ayPrintlog.getTradeType());
                        logisticsPrintlogDTO.setPrintType(ayPrintlog.getPrintType());
                        logisticsPrintlogDTO.setOids(ayPrintlog.getOids());
                        logisticsPrintlogDTO.setIsSplit(ayPrintlog.getIsSplit());
                        logisticsPrintlogDTO.setLogisticsCompany(ayPrintlog.getLogisticsCompany());
                        logisticsPrintlogDTO.setElefaceProvider(ayPrintlog.getElefaceProvider());
                        logisticsPrintlogDTO.setCpCode(ayPrintlog.getCpCode());
                        logisticsPrintlogDTO.setWaybillCode(ayPrintlog.getWaybillCode());
                        logisticsPrintlogDTO.setChildWaybillCode(ayPrintlog.getChildWaybillCode());
                        logisticsPrintlogDTO.setIsChild(ayPrintlog.getIsChild());
                        // 收件人信息
                        logisticsPrintlogDTO.setReceiverCity(ayPrintlog.getReceiverCity());
                        logisticsPrintlogDTO.setReceiverDetail(ayPrintlog.getReceiverDetail());
                        logisticsPrintlogDTO.setReceiverDistrict(ayPrintlog.getReceiverDistrict());
                        logisticsPrintlogDTO.setReceiverProvince(ayPrintlog.getReceiverProvince());
                        logisticsPrintlogDTO.setReceiverTown(ayPrintlog.getReceiverTown());
                        logisticsPrintlogDTO.setReceiverZip(ayPrintlog.getReceiverZip());
                        // 发货人信息
                        logisticsPrintlogDTO.setSenderCity(ayPrintlog.getSenderCity());
                        logisticsPrintlogDTO.setSenderDetail(ayPrintlog.getSenderDetail());
                        logisticsPrintlogDTO.setSenderDistrict(ayPrintlog.getSenderDistrict());
                        logisticsPrintlogDTO.setSenderProvince(ayPrintlog.getSenderProvince());
                        logisticsPrintlogDTO.setSenderTown(ayPrintlog.getSenderTown());
                        logisticsPrintlogDTO.setSenderZip(ayPrintlog.getSenderZip());

                        logisticsPrintlogDTO.setPrinter(ayPrintlog.getPrinter());
                        logisticsPrintlogDTO.setOperator(ayPrintlog.getOperator());
                        logisticsPrintlogDTO.setMergeTid(ayPrintlog.getMergeTid());
                        logisticsPrintlogDTO.setOperatorStoreId(ayPrintlog.getOperatorStoreId());
                        logisticsPrintlogDTO.setOperateTerminal(ayPrintlog.getOperateTerminal());
                        logisticsPrintlogDTO.setPrintTime(ayPrintlog.getPrintTime());
                        logisticsPrintlogDTO.setWaybillIsCancel(ayPrintlog.getWaybillIsCancel());
                        logisticsPrintlogDTO.setLogisticsTemplateId(ayPrintlog.getLogisticsTemplateName());
                        logisticsPrintlogDTO.setNumbersInBatch(ListUtil.convertWithComma(ayPrintlog.getNumbersInBatch(), Integer::parseInt));
                        logisticsPrintlogDTO.setBatchId(ayPrintlog.getBatchId());
                        logisticsPrintlogDTO.setBatchTotals(ayPrintlog.getBatchTotals());

                        if (ayPrintlog.getAyDistribute() != null) {
                            AyDistributeInfoDTO ayDistributeInfoDTO = JSON.parseObject(ayPrintlog.getAyDistribute(), AyDistributeInfoDTO.class);
                            logisticsPrintlogDTO.setAyDistributeInfo(ayDistributeInfoDTO);
                        }

                        // 加密字段进行解密
                        if (StringUtils.isNotEmpty(ayPrintlog.getReceiverName())) {
                            logisticsPrintlogDTO
                                .setReceiverName(aesEncryptionService.decryptForTrade(ayPrintlog.getReceiverName()));
                        }
                        if (StringUtils.isNotEmpty(ayPrintlog.getReceiverMobile())) {
                            logisticsPrintlogDTO
                                .setReceiverMobile(aesEncryptionService.decryptForPhone(ayPrintlog.getReceiverMobile()));
                        }
                        if (StringUtils.isNotEmpty(ayPrintlog.getReceiverPhone())) {
                            logisticsPrintlogDTO
                                .setReceiverPhone(aesEncryptionService.decryptForPhone(ayPrintlog.getReceiverPhone()));
                        }

                        if (StringUtils.isNotEmpty(ayPrintlog.getSenderName())) {
                            logisticsPrintlogDTO.setSenderName(ElasticsearchUtil
                                .desensitiseName(aesEncryptionService.decryptForTrade(ayPrintlog.getSenderName())));
                            // 脱敏
                        }

                        if (StringUtils.isNotEmpty(ayPrintlog.getSenderMobile())) {
                            logisticsPrintlogDTO.setSenderMobile(ElasticsearchUtil
                                .desensitisePhone(aesEncryptionService.decryptForPhone(ayPrintlog.getSenderMobile())));
                        }

                        if (StringUtils.isNotEmpty(ayPrintlog.getSenderPhone())) {
                            logisticsPrintlogDTO.setSenderPhone(ElasticsearchUtil
                                .desensitisePhone(aesEncryptionService.decryptForPhone(ayPrintlog.getSenderPhone())));
                        }

                        Long printId = ayPrintlog.getPrintId();
                        // 打印记录要透出的打印内容
                        if (PrintTypeConstant.ELEFACE.equals(ayPrintlog.getPrintType())
                            && elefacePrintlogMap.containsKey(printId)) {
                            AyElefacePrintlog printLog = elefacePrintlogMap.get(ayPrintlog.getPrintId());
                            logisticsPrintlogDTO.setPrintData(printLog.getPrintData());
                            logisticsPrintlogDTO.setCustomData(printLog.getCustomData());
                            logisticsPrintlogDTO.setBrandCode(printLog.getBrandCode());
                            logisticsPrintlogDTO.setExternalInfo(printLog.getExternalInfo());
                            logisticsPrintlogDTO.setRealCpCode(printLog.getRealCpCode());
                            logisticsPrintlogDTO.setBillVersion(printLog.getBillVersion());
                        } else if (PrintTypeConstant.EXPRESS.equals(ayPrintlog.getPrintType())
                            && expressPrintlogMap.containsKey(printId)) {
                            AyExpressPrintlog printLog = expressPrintlogMap.get(ayPrintlog.getPrintId());
                            logisticsPrintlogDTO.setPrintData(printLog.getPrintData());
                            logisticsPrintlogDTO.setPrintModule(printLog.getPrintModule());
                            logisticsPrintlogDTO.setExpressImage(printLog.getExpressImage());
                        }

                        logisticsPrintlogDTOList.add(logisticsPrintlogDTO);
                    }
                }
            }
        }

        // 排序
        logisticsPrintlogDTOList = sortLogisticsPrintLogList(logisticsPrintlogDTOList);

        LogisticsPrintlogResponseDTO responseDTO = new LogisticsPrintlogResponseDTO();
        responseDTO.setTotalResult(totalResult);
        responseDTO.setPrintlogList(logisticsPrintlogDTOList);
        responseDTO.setTidTotal(searchResult.getTidTotal());
        responseDTO.setLogisticsCompanyTotal(searchResult.getLogisticsCompanyTotal());
        responseDTO.setWaybillCodeTotal(searchResult.getWaybillCodeTotal());
        return responseDTO;
    }

    /**
     * 添加打印类型集合查询
     * 查询用户的打印日志,判断用户是否存在快递单的打印记录，不存在则只查询面单记录。以此来优化sql查询速度
     * @param queryDTO
     * @param storeId
     * @param sellerId
     * @param appName
     * @return
     */
    private void appendPrintTypeListQuery(LogisticsPrintlogQueryDTO queryDTO, String storeId, String sellerId, String appName){
        if (!printLogConfig.getIsPrintLogListAppendPrintTypeEnabled()) {
            return;
        }
        List<String> userPrintType = printLogRedisDao.getUserPrintType(sellerId, appName, storeId);
        if (CollectionUtils.isEmpty(userPrintType)) {
            //查询数据库，写入缓存 查询用户是否存在快递单打印记录
            List<String> printTypeList = Arrays.asList(PrintTypeConstant.EXPRESS, PrintTypeConstant.ELEFACE);
            String shardingTableName = ShardingUtils.getShardingTableName(sellerId, TABLE_PREFIX);
            userPrintType = ayPrintlogDao.queryExistByPrintType(printTypeList, shardingTableName, storeId, sellerId, appName);
            //缓存打印配置，在打印时删除缓存
            printLogRedisDao.setUserPrintType(userPrintType, sellerId, appName, storeId);
        }

        //当某一个打印类型打印数量为0时，则无需查询，达到优化sql的目的
        if (CollectionUtils.isNotEmpty(userPrintType)) {
            queryDTO.setPrintTypeList(userPrintType);
        }
    }

    /**
     * 面单打印记录统计
     *
     * @param queryBo
     * @param userInfoBo
     * @return
     */
    @Override
    public List<GroupedElefacePrintLogDTO> groupElefacePrintLog(GroupElefacePrintLogQueryBo queryBo,
        UserInfoBo userInfoBo) {
        LOGGER.logInfo(userInfoBo.getSellerId(), "-", "统计面单打印记录, queryBo => " + JSON.toJSONString(queryBo));
        List<GroupedElefacePrintLogDTO> result;
        if (CollectionUtils.isEmpty(queryBo.getReceiverProvinceList())) {
            // 只对快递公司做聚合
            result = ayPrintlogDao.groupElefacePrintLogByCpCode(queryBo, userInfoBo.getSellerId(),
                userInfoBo.getStoreId(), userInfoBo.getAppName());
        } else {
            // 同时聚合快递公司和收件人省份
            result = ayPrintlogDao.groupElefacePrintLogByCpCodeAndReceiverProvince(queryBo, userInfoBo.getSellerId(),
                userInfoBo.getStoreId(), userInfoBo.getAppName());
        }
        return result;
    }

    @Override
    public AyPrintlog queryHasPrintHistoryByTid(String tid, Integer tradeType, List<String> printTypes, String sellerId,
        String storeId, String appName) {
        return ayPrintlogDao.queryHasPrintHistoryByTid(tid, tradeType, printTypes, sellerId, storeId, appName);
    }

    /**
     * 加密收件人信息
     *
     * @param recipient
     */
    private RecipientDTO encryptRecipient(RecipientDTO recipient) {
        if (recipient == null) {
            return null;
        }

        // 部分字段进行加密存储
        String mobile = recipient.getMobile();
        String phone = recipient.getPhone();
        String name = recipient.getName();

        if (!StringUtils.isEmpty(mobile)) {
            recipient.setMobile(aesEncryptionService.encryptForPhone(mobile));
        }
        if (!StringUtils.isEmpty(phone)) {
            recipient.setPhone(aesEncryptionService.encryptForPhone(phone));
        }
        if (!StringUtils.isEmpty(name)) {
            recipient.setName(aesEncryptionService.encryptForTrade(name));
        }
        return recipient;
    }

    @Override
    public void waybillOperatelogSave(List<WaybillOperateLogSaveDTO> waybillOperatelogSaves) {
        elefaceWaybillService.saveWaybillOperateLogBatch(waybillOperatelogSaves);
    }

    @Override
    public CountPrintLogResponse countPrintLogByPrintType(CountPrintLogRequest request) {
        CountPrintLogResponse response = new CountPrintLogResponse();
        List<GroupedPrintTypePrintLogCountDTO> countDTOS = ayPrintlogDao.countPrintLogByPrintType(request);
        if (CollectionUtils.isNotEmpty(countDTOS)) {
            for (GroupedPrintTypePrintLogCountDTO countDTO : countDTOS) {
                Long printCount = countDTO.getPrintCount();
                switch (countDTO.getPrintType()) {
                    case PrintTypeConstant.ELEFACE:
                        response.setPrintedElefaceCount(printCount);
                        break;
                    case PrintTypeConstant.EXPRESS:
                        response.setPrintedExpressCount(printCount);
                        break;
                    case PrintTypeConstant.DELIVER:
                        response.setPrintedDeliverCount(printCount);
                        break;
                }
            }
        }
        return response;
    }

    @Override
    public AyPrintLogListSearchResultDTO getSearchResult(LogisticsPrintlogQueryDTO queryDTO, List<UserInfoBo> userInfoBoList) {
        AyPrintLogListSearchResultDTO ayPrintLogListSearchResultDTO = new AyPrintLogListSearchResultDTO();
        AyPrintLogListSearchDTO ayPrintLogListSearchDTO = new AyPrintLogListSearchDTO();
        BeanUtils.copyProperties(queryDTO, ayPrintLogListSearchDTO);
        AyPrintLogSearchListAndAggDTO ayPrintLogSearchListAndAggDTO =
            ayPrintLogSearchService.ayPrintLogListGetQueryByLimit(ayPrintLogListSearchDTO, userInfoBoList);
        List<AyPrintLogSearchEs> itemSearchESList = ayPrintLogSearchListAndAggDTO.getItemSearchESList();
        Map<String, List<AyPrintLogSearchEs>> sellerIdAndSearchInfoMap =
            itemSearchESList.stream().collect(Collectors.groupingBy(AyPrintLogSearchEs::getSellerId));

        List<AyPrintlog> printlogs = Lists.newArrayList();
        for (String sellerId : sellerIdAndSearchInfoMap.keySet()) {
            List<Long> mysqlIds = sellerIdAndSearchInfoMap.get(sellerId).stream()
                .map(AyPrintLogSearchEs::getAyPrintLogId).collect(Collectors.toList());
            List<AyPrintlog> ayPrintLogList = ayPrintlogDao.queryByIds(sellerId, mysqlIds);
            printlogs.addAll(ayPrintLogList);
        }

        // 查数据库
        ayPrintLogListSearchResultDTO.setTotalResults(ayPrintLogSearchListAndAggDTO.getTotalResults());
        ayPrintLogListSearchResultDTO.setAyPrintlogList(printlogs);
        ayPrintLogListSearchResultDTO.setTidTotal(ayPrintLogSearchListAndAggDTO.getTidTotal());
        ayPrintLogListSearchResultDTO.setLogisticsCompanyTotal(ayPrintLogSearchListAndAggDTO.getLogisticsCompanyTotal());
        ayPrintLogListSearchResultDTO.setWaybillCodeTotal(ayPrintLogSearchListAndAggDTO.getWaybillCodeTotal());

        return ayPrintLogListSearchResultDTO;
    }

    @Override
    public Integer queryPrintLogCountFromCache(AyPrintLogListSearchDTO ayPrintLogListSearchDTO,  List<UserInfoBo> userInfoBoList) {
        Integer countCacheTimeout = printLogConfig.getCountCacheTimeout();
        Integer totalResults = 0;
        String finalKey = null;
        if (countCacheTimeout <= 0) {
            totalResults = queryPrintLogCountFromDb(ayPrintLogListSearchDTO, userInfoBoList);
        } else {
            AyPrintLogListSearchDTO cacheKey = new AyPrintLogListSearchDTO();
            // 忽略与搜索结果数量无关的字段
            BeanUtils.copyProperties(ayPrintLogListSearchDTO, cacheKey, "pageNo", "pageSize");
            totalResults = RedisUtil.getCacheWithShortKey(redisTemplate,
                () -> queryPrintLogCountFromDb(ayPrintLogListSearchDTO, userInfoBoList), Integer.class, countCacheTimeout,
                PRINT_LOG_SEARCH_COUNT_PREFIX, cacheKey);
        }

        return totalResults;
    }

    @Override
    public void ayPrintLogListGetCountStatisticsFromCache(
        AyPrintLogSearchListAndAggDTO ayPrintLogSearchListAndAggResult, AyPrintLogListSearchDTO ayPrintLogListSearchDTO,
        List<UserInfoBo> userInfoBoList) {

        if (BooleanUtils.isTrue(ayPrintLogListSearchDTO.getIsNeedStatistics())) {
            AyPrintLogListSearchDTO cacheKey = new AyPrintLogListSearchDTO();
            BeanUtils.copyProperties(ayPrintLogListSearchDTO, cacheKey, "pageNo", "pageSize");
            AyPrintLogSearchListAndAggDTO ayPrintLogSearchListAndAggDTO = RedisUtil.getCacheWithShortKey(redisTemplate,
                () -> elasticsearchAyPrintLogQueryDao.printLogListCountStatistics(ayPrintLogListSearchDTO, userInfoBoList),
                AyPrintLogSearchListAndAggDTO.class, printLogConfig.getPrintLogListCountStatisticsCacheTimeout(),
                PRINT_LOG_SEARCH_COUNT_AGG_PREFIX, cacheKey, userInfoBoList);

            ayPrintLogSearchListAndAggResult.setTidTotal(ayPrintLogSearchListAndAggDTO.getTidTotal());
            ayPrintLogSearchListAndAggResult
                .setLogisticsCompanyTotal(ayPrintLogSearchListAndAggDTO.getLogisticsCompanyTotal());
            ayPrintLogSearchListAndAggResult.setWaybillCodeTotal(ayPrintLogSearchListAndAggDTO.getWaybillCodeTotal());
        }
    }

    protected Integer queryPrintLogCountFromDb(AyPrintLogListSearchDTO ayPrintLogListSearchDTO, List<UserInfoBo> userInfoBoList) {
        return elasticsearchAyPrintLogQueryDao.queryTotalCount(ayPrintLogListSearchDTO, userInfoBoList);
    }

}
