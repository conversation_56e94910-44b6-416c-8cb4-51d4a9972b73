package cn.loveapp.print.service.service;

import cn.loveapp.print.api.proto.ShareRelationChangeRequestProto;
import cn.loveapp.print.api.proto.WaybillOperateLogStatusChangeRequestProto;

/**
 * 打印消息发送处理接口
 *
 * <AUTHOR>
 * @Date 2024/9/28 4:54 PM
 */
public interface PrintMessageSendService {

    /**
     * 发送面单关系信息变更通知
     * @param proto
     */
    void pushRelationChangeMessage(ShareRelationChangeRequestProto proto);

    /**
     * 发送面单关系信息变更通知
     * @param proto
     */
    void pushOperateLogStatusChangeMessage(WaybillOperateLogStatusChangeRequestProto proto);

}
