package cn.loveapp.print.service.service;

import java.util.List;

import cn.loveapp.print.service.bo.SerialGetBo;
import cn.loveapp.print.service.bo.UserInfoBo;
import cn.loveapp.print.service.request.SerialBoundGetRequest;
import cn.loveapp.print.service.response.SerialBoundGetResponseDTO;

/**
 * <AUTHOR>
 */
public interface SerialService {

    /**
     * 获取订单流水号
     *
     * @param serialGetBo
     * @param userInfoBo
     * @return
     */
    String serialGet(SerialGetBo serialGetBo, UserInfoBo userInfoBo);


    /**
     * 批量查询订单流水号绑定记录
     *
     * @param serialGetBo
     * @param userInfoBo
     * @return
     */
    SerialBoundGetResponseDTO serialBoundGet(SerialBoundGetRequest request, UserInfoBo userInfoBo);

    /**
     * 订单流水号作废
     *
     * @param subTids
     * @param tradeType
     * @param userInfoBo
     */
    void serialInvalidate(List<String> subTids, Integer tradeType, UserInfoBo userInfoBo);

}
