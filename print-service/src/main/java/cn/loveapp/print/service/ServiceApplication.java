package cn.loveapp.print.service;

import cn.loveapp.shops.api.validator.EnableShopsAuthTargetInterceptor;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.cloud.openfeign.EnableFeignClients;


/**
 * ServiceApplication
 *
 * <AUTHOR>
 * @date 2020-03-21 12:26
 */
@EnableCaching
@EnableFeignClients(basePackages = {"cn.loveapp.orders", "cn.loveapp.uac", "cn.loveapp.shops"})
@SpringBootApplication(scanBasePackages = {"cn.loveapp.print.service", "cn.loveapp.print.common"})
@EnableShopsAuthTargetInterceptor
public class ServiceApplication {

    public static void main(String[] args) {
        SpringApplication.run(ServiceApplication.class, args);
    }
}
