package cn.loveapp.print.service.dao.newprint;

import java.util.List;

import cn.loveapp.print.api.request.CountPrintLogRequest;
import cn.loveapp.print.common.entity.AyPrintlog;
import cn.loveapp.print.service.bo.GroupElefacePrintLogQueryBo;
import cn.loveapp.print.service.dto.GroupedElefacePrintLogDTO;
import cn.loveapp.print.service.dto.GroupedPrintTypePrintLogCountDTO;
import cn.loveapp.print.service.dto.LogisticsPrintlogQueryDTO;
import org.apache.ibatis.annotations.Param;

/**
 * 订单打印日志（ay_trade_printlog）表数据库访问层
 *
 * <AUTHOR>
 */
public interface AyPrintlogDao {

    /**
     * 插入单条记录
     *
     * @param ayPrintlog
     * @return
     */
    int insert(AyPrintlog ayPrintlog);

    /**
     * 批量插入记录
     *
     * @param ayPrintlogList
     * @return
     */
    int insertBatch(List<AyPrintlog> ayPrintlogList);

    /**
     * 通过tids查询打印记录
     *
     * @param tids
     * @param tradeType
     * @param printType
     * @param storeId
     * @param sellerId
     * @param appName
     * @return
     * @pa
     */
    List<AyPrintlog> queryByTidsAndPrintType(List<String> tids, Integer tradeType, String printType, String storeId,
        String sellerId, String appName);

    /**
     * 查询物流单打印记录列表
     *
     * @param queryDTO
     * @param storeId
     * @param sellerId
     * @param appName
     * @return
     */
    List<AyPrintlog> queryLogisticsListOrderByPrintTime(LogisticsPrintlogQueryDTO queryDTO, String storeId,
        String sellerId, String appName);

    /**
     * 查询打印日志内是否存在对应的打印类型
     *
     * @param printTypeList
     * @param storeId
     * @param sellerId
     * @param appName
     * @return
     */
    List<String> queryExistByPrintType(List<String> printTypeList, String tableName, String storeId, String sellerId, String appName);

    /**
     * 查询物流单打印记录
     *
     * @param queryDTO
     * @param storeId
     * @param sellerId
     * @param appName
     * @return
     */
    Long queryLogisticsCount(LogisticsPrintlogQueryDTO queryDTO, String storeId, String sellerId, String appName);

    /**
     * 保存面单的回收信息
     *
     * @param isCancel
     * @param cpCode
     * @param waybillCode
     * @param storeId
     * @param sellerId
     * @param appName
     * @return
     */
    int updateWaybillIsCancel(Boolean isCancel, String cpCode, String waybillCode, String storeId, String sellerId,
        String appName);

    /**
     * 根据sellerId和waybillCode查询打印日志
     *
     * @param sellerId
     * @param cpCode
     * @param waybillCode
     * @param storeId
     * @param appName
     * @return
     */
    List<AyPrintlog> queryBySellerIdAndWaybillCode(String sellerId, String cpCode, String waybillCode, String storeId,
        String appName);

    /**
     * 查询指定id列表数据
     *
     * @param sellerId
     * @param ids
     * @return
     */
    List<AyPrintlog> queryByIds(String sellerId, List<Long> ids);

    /**
     * 查询重复打印的所有列表(面单、快递单)
     *
     * @param sellerId
     * @param tid
     * @param waybillCode
     * @param childWaybillCode
     * @param printType
     * @param storeId
     * @param appName
     * @return
     */
    List<AyPrintlog> queryRepetitionPrintWaybill(String sellerId, String tid, String waybillCode,
        String childWaybillCode, String printType, String storeId, String appName);

    /**
     * 统计面单打印日志 ( group by cp_code, receiver_province )
     *
     * @param queryBo
     * @param sellerId
     * @param storeId
     * @param appName
     * @return
     */
    List<GroupedElefacePrintLogDTO> groupElefacePrintLogByCpCodeAndReceiverProvince(GroupElefacePrintLogQueryBo queryBo,
        String sellerId, String storeId, String appName);

    /**
     * 统计面单打印日志 ( group by cp_code )
     *
     * @param queryBo
     * @param sellerId
     * @param storeId
     * @param appName
     * @return
     */
    List<GroupedElefacePrintLogDTO> groupElefacePrintLogByCpCode(GroupElefacePrintLogQueryBo queryBo, String sellerId,
        String storeId, String appName);

    /**
     * 根据订单id查询是否打印过单子
     *
     * @param tid
     * @param tradeType
     * @param storeId
     * @param appName
     * @return
     */
    AyPrintlog queryHasPrintHistoryByTid(String tid, Integer tradeType, List<String> printTypes, String sellerId,
        String storeId, String appName);

    /**
     * 统计不同打印类型的打印日志记录数
     *
     * @param queryDTO
     * @return
     */
    List<GroupedPrintTypePrintLogCountDTO> countPrintLogByPrintType(@Param("queryDTO") CountPrintLogRequest queryDTO);

}
