package cn.loveapp.print.service.service.impl;

import cn.loveapp.common.utils.LoggerHelper;
import cn.loveapp.print.common.dto.TargetSellerInfo;
import cn.loveapp.print.common.exception.CommonException;
import cn.loveapp.print.common.service.UserCenterService;
import cn.loveapp.print.service.bo.DistributeElefacePrintBo;
import cn.loveapp.print.service.bo.ElefacePrintBo;
import cn.loveapp.print.service.bo.UserInfoBo;
import cn.loveapp.print.service.convert.CommonConvertMapper;
import cn.loveapp.print.service.dto.DistributeOrderInfoDTO;
import cn.loveapp.print.service.request.PrintTradeInfoDTO;
import cn.loveapp.print.service.service.ElefaceWaybillService;
import cn.loveapp.print.service.service.PrintDistributeLogService;
import cn.loveapp.print.service.service.PrintlogService;
import cn.loveapp.uac.response.UserInfoResponse;
import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2023/11/6 3:08 PM
 */
@Service
public class PrintDistributeLogServiceImpl implements PrintDistributeLogService {

    private static final LoggerHelper LOGGER = LoggerHelper.getLogger(PrintDistributeLogServiceImpl.class);

    @Autowired
    private PrintlogService printlogService;

    @Autowired
    private ElefaceWaybillService elefaceWaybillService;

    @Autowired
    private UserCenterService userCenterService;

    @Override
    public void elefacePrint(DistributeElefacePrintBo elefacePrintBo, UserInfoBo userInfoBo) {

        ElefacePrintBo distributePrintBo = CommonConvertMapper.INSTANCE.cpElefacePrintBo(elefacePrintBo);

        // 更新采购单打印日志
        printlogService.elefacePrint(elefacePrintBo, userInfoBo);


        // 同步分销单打印日志
        // todo 20240409 旧逻辑，等前端发布结束后删除  ---- start
        if (CollectionUtils.isNotEmpty(elefacePrintBo.getDistributeOrderInfoList())) {
            LOGGER.logInfo("开始创建分销单打印日志");
            List<DistributeOrderInfoDTO> distributeOrderInfos = elefacePrintBo.getDistributeOrderInfoList();

            DistributeOrderInfoDTO distributeOrderInfoDTO = distributeOrderInfos.get(0);

            String sellerId = distributeOrderInfoDTO.getSellerId();
            String sellerNick = distributeOrderInfoDTO.getSellerNick();
            String storeId = distributeOrderInfoDTO.getStoreId();
            String appName = distributeOrderInfoDTO.getAppName();

            UserInfoResponse userInfo = userCenterService.getUserInfo(storeId, appName, sellerNick, sellerId);
            if (userInfo == null) {
                LOGGER.logError(sellerNick, "-", "获取用户异常，同步分销单打印日志失败");
                return;
            }

            userInfoBo.setSellerId(sellerId);
            userInfoBo.setSellerNick(sellerNick);
            userInfoBo.setAppName(appName);
            userInfoBo.setStoreId(storeId);

            for (DistributeOrderInfoDTO distributeOrderInfo : distributeOrderInfos) {
                PrintTradeInfoDTO printTradeInfo = new PrintTradeInfoDTO();
                printTradeInfo.setTid(distributeOrderInfo.getTid());
                printTradeInfo.setOids(distributeOrderInfo.getOidList());
                ElefacePrintBo distributePrint = CommonConvertMapper.INSTANCE.cpElefacePrintBo(distributePrintBo);
                distributePrint.setTradeInfoList(Lists.newArrayList(printTradeInfo));
                distributePrint.setMergeTid(null);
                printlogService.elefacePrint(distributePrint, userInfoBo);
                LOGGER.logInfo(sellerNick, distributeOrderInfo.getTid(), "打印日志生成");
            }
        // todo 20240409 旧逻辑，等前端发布结束后删除  ---- end
        } else if (CollectionUtils.isNotEmpty(elefacePrintBo.getTradeInfoList())){

            List<TargetSellerInfo> targetSellerInfoList = new ArrayList<>();
            elefacePrintBo.getTradeInfoList().forEach(tradeInfoDTO -> {
                if (!Objects.isNull(tradeInfoDTO.getAyDistributeInfo())) {
                    DistributeOrderInfoDTO ayDistributeInfo = tradeInfoDTO.getAyDistributeInfo();
                    TargetSellerInfo targetSellerInfo = new TargetSellerInfo();
                    targetSellerInfo.setTargetNick(ayDistributeInfo.getSellerNick());
                    targetSellerInfo.setTargetSellerId(ayDistributeInfo.getSellerId());
                    targetSellerInfo.setTargetAppName(ayDistributeInfo.getAppName());
                    targetSellerInfo.setTargetStoreId(ayDistributeInfo.getStoreId());
                    targetSellerInfoList.add(targetSellerInfo);
                }
            });

            if (CollectionUtils.isEmpty(targetSellerInfoList)) {
                LOGGER.logError("无可处理分销信息");
                return;
            }

            List<UserInfoResponse> userInfoResponses = userCenterService.getUserInfo(targetSellerInfoList);
            if (CollectionUtils.isEmpty(userInfoResponses)) {
                LOGGER.logError("获取用户异常，同步分销单打印日志失败");
                return;
            }
            Map<String, UserInfoResponse> userMap = userInfoResponses.stream().collect(Collectors.toMap(u -> u.getSellerId() + u.getAppName() + u.getPlatformId(), Function.identity(), (v1, v2) -> v1));

            for (PrintTradeInfoDTO tradeInfoDTO : elefacePrintBo.getTradeInfoList()) {
                if (Objects.isNull(tradeInfoDTO.getAyDistributeInfo())) {
                    continue;
                }
                DistributeOrderInfoDTO distributeOrderInfo = tradeInfoDTO.getAyDistributeInfo();

                String sellerId = distributeOrderInfo.getSellerId();
                String sellerNick = distributeOrderInfo.getSellerNick();
                String storeId = distributeOrderInfo.getStoreId();
                String appName = distributeOrderInfo.getAppName();

                String userKey = sellerId + appName + storeId;
                UserInfoResponse userInfo = userMap.get(userKey);
                if (userInfo == null) {
                    LOGGER.logError(sellerNick, "-", "获取用户异常，同步分销单打印日志失败");
                    return;
                }

                userInfoBo.setSellerId(sellerId);
                userInfoBo.setSellerNick(sellerNick);
                userInfoBo.setAppName(appName);
                userInfoBo.setStoreId(storeId);

                PrintTradeInfoDTO printTradeInfo = new PrintTradeInfoDTO();
                printTradeInfo.setTid(distributeOrderInfo.getTid());
                printTradeInfo.setOids(distributeOrderInfo.getOidList());
                ElefacePrintBo distributePrint = CommonConvertMapper.INSTANCE.cpElefacePrintBo(distributePrintBo);
                distributePrint.setTradeInfoList(Lists.newArrayList(printTradeInfo));
                distributePrint.setMergeTid(null);
                // 分销订单的来源订单类型
                distributePrint.setTradeType(distributeOrderInfo.getSourceTradeType());
                printlogService.elefacePrint(distributePrint, userInfoBo);
                LOGGER.logInfo(sellerNick, distributeOrderInfo.getTid(), "打印日志生成");
            }
        }


    }

    @Override
    public void waybillCancel(String cpCode, String waybillCode, Integer billVersion, Boolean isCancelFromApi,
        UserInfoBo userInfoBo, DistributeOrderInfoDTO distributeInfo) throws CommonException {

        boolean success =
            elefaceWaybillService.waybillCancel(cpCode, waybillCode, billVersion, isCancelFromApi, userInfoBo);

        // 同步分销单打印日志
        if (success && Objects.nonNull(distributeInfo)) {
            LOGGER.logInfo("开始取消分销单打印日志");

            String sellerId = distributeInfo.getSellerId();
            String sellerNick = distributeInfo.getSellerNick();
            String storeId = distributeInfo.getStoreId();
            String appName = distributeInfo.getAppName();

            UserInfoResponse userInfo = userCenterService.getUserInfo(storeId, appName, sellerNick, sellerId);
            if (userInfo == null) {
                LOGGER.logError(sellerNick, "-", "获取用户异常，同步分销单打印日志失败");
                return;
            }

            userInfoBo.setSellerId(sellerId);
            userInfoBo.setSellerNick(sellerNick);
            userInfoBo.setAppName(appName);
            userInfoBo.setStoreId(storeId);
            try {
                elefaceWaybillService.waybillCancel(cpCode, waybillCode, billVersion, true, userInfoBo);
            } catch (Exception e) {
                LOGGER.logError("分销单日志同步取消面单失败：" + e.getMessage(), e);
            }
        }

    }
}
