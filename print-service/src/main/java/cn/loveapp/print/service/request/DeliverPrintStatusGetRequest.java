package cn.loveapp.print.service.request;

import java.util.List;

import javax.validation.constraints.NotNull;

import cn.loveapp.print.service.annotation.TradeTypeValidation;
import cn.loveapp.print.service.dto.TargetPrintInfoDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 订单发货单打印状态获取
 *
 * <AUTHOR>
 */
@Data
@ApiModel(value = "订单发货单打印状态获取请求")
public class DeliverPrintStatusGetRequest {
    /**
     * 订单列表
     */
    @ApiModelProperty(value = "订单列表")
    private List<String> tids;

    /**
     * 订单类型 0-普通订单 1-自由打印订单
     */
    @ApiModelProperty(value = "订单类型 0-普通订单 1-自由打印订单", required = true)
    @NotNull
    @TradeTypeValidation
    private Integer tradeType;


    /**
     * 订单多店列表
     */
    @ApiModelProperty(value = "订单列表")
    private List<TargetPrintInfoDTO> targetPrintInfoList;
}
