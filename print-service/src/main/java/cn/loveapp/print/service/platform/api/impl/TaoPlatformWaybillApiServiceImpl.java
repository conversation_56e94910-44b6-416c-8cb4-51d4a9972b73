package cn.loveapp.print.service.platform.api.impl;

import java.util.Collections;
import java.util.stream.Collectors;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.taobao.api.request.CainiaoWaybillIiCancelRequest;
import com.taobao.api.request.CainiaoWaybillIiGetRequest;
import com.taobao.api.request.CainiaoWaybillIiSearchRequest;
import com.taobao.api.response.CainiaoWaybillIiCancelResponse;
import com.taobao.api.response.CainiaoWaybillIiGetResponse;
import com.taobao.api.response.CainiaoWaybillIiSearchResponse;

import cn.loveapp.common.constant.CommonPlatformConstants;
import cn.loveapp.common.platformsdk.taobao.TaobaoSDKService;
import cn.loveapp.print.service.api.entity.AyWaybillApplySubscriptionInfo;
import cn.loveapp.print.service.api.entity.WaybillCloudPrintInfo;
import cn.loveapp.print.service.api.request.AyWaybillCancelRequest;
import cn.loveapp.print.service.api.request.AyWaybillGetRequest;
import cn.loveapp.print.service.api.request.AyWaybillSearchRequest;
import cn.loveapp.print.service.api.response.AyWaybillCancelResponse;
import cn.loveapp.print.service.api.response.AyWaybillGetResponse;
import cn.loveapp.print.service.api.response.AyWaybillSearchResponse;
import cn.loveapp.print.service.platform.api.PlatformWaybillApiService;

/**
 * 面单api service 淘宝（菜鸟）实现类
 *
 * <AUTHOR>
 */
@Service
public class TaoPlatformWaybillApiServiceImpl implements PlatformWaybillApiService {

    @Autowired
    private TaobaoSDKService taobaoAppsSDKService;

    /**
     * 查询面单服务订购及面单使用情况
     *
     * @param request
     * @param topSession
     * @param platformId
     * @param appName
     * @return
     */
    @Override
    public AyWaybillSearchResponse waybillSearch(AyWaybillSearchRequest request, String topSession, String platformId,
        String appName) {
        CainiaoWaybillIiSearchRequest cainiaoWaybillIiSearchRequest = new CainiaoWaybillIiSearchRequest();
        cainiaoWaybillIiSearchRequest.setCpCode(StringUtils.trimToEmpty(request.getCpCode()));

        CainiaoWaybillIiSearchResponse cainiaoWaybillIiSearchResponse =
            taobaoAppsSDKService.execute(cainiaoWaybillIiSearchRequest, topSession, appName);

        AyWaybillSearchResponse response = new AyWaybillSearchResponse();
        response.init(cainiaoWaybillIiSearchResponse);
        if (!response.isSuccess()) {
            // 失败直接返回
            return response;
        }
        if (cainiaoWaybillIiSearchResponse.getWaybillApplySubscriptionCols() != null) {
            response.setWaybillApplySubscriptionCols(cainiaoWaybillIiSearchResponse.getWaybillApplySubscriptionCols()
                .stream().map(AyWaybillApplySubscriptionInfo::of).collect(Collectors.toList()));
        } else {
            response.setWaybillApplySubscriptionCols(Collections.emptyList());
        }
        return response;
    }

    @Override
    public AyWaybillGetResponse waybillGet(AyWaybillGetRequest request, String topSession, String platformId,
        String appName) {
        CainiaoWaybillIiGetRequest cainiaoWaybillIiGetRequest = new CainiaoWaybillIiGetRequest();
        cainiaoWaybillIiGetRequest
            .setParamWaybillCloudPrintApplyNewRequest(request.getParamWaybillCloudPrintApplyNewRequest());
        CainiaoWaybillIiGetResponse cainiaoWaybillIiGetResponse =
            taobaoAppsSDKService.execute(cainiaoWaybillIiGetRequest, topSession, appName);
        AyWaybillGetResponse response = new AyWaybillGetResponse();
        response.init(cainiaoWaybillIiGetResponse);
        if (!response.isSuccess()) {
            return response;
        }
        response.setWaybillCloudPrintInfoList(cainiaoWaybillIiGetResponse.getModules().stream()
            .map(WaybillCloudPrintInfo::of).collect(Collectors.toList()));
        return response;
    }

    @Override
    public AyWaybillCancelResponse waybillCancel(AyWaybillCancelRequest request, String topSession, String platformId,
        String appName) {
        AyWaybillCancelResponse response = new AyWaybillCancelResponse();
        CainiaoWaybillIiCancelRequest cainiaoWaybillIiCancelRequest = new CainiaoWaybillIiCancelRequest();
        cainiaoWaybillIiCancelRequest.setWaybillCode(request.getWaybillCode());
        cainiaoWaybillIiCancelRequest.setCpCode(request.getCpCode());
        CainiaoWaybillIiCancelResponse cainiaoWaybillIiCancelResponse =
            taobaoAppsSDKService.execute(cainiaoWaybillIiCancelRequest, topSession, appName);
        response.init(cainiaoWaybillIiCancelResponse);
        if (!response.isSuccess()) {
            return response;
        }
        response.setCancelResult(cainiaoWaybillIiCancelResponse.getCancelResult());
        return response;
    }

    /**
     * 获取平台id
     *
     * @return
     */
    @Override
    public String getPlatformId() {
        return CommonPlatformConstants.PLATFORM_TAO;
    }


}
