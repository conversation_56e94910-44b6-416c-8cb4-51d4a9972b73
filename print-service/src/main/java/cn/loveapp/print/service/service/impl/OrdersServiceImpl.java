package cn.loveapp.print.service.service.impl;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import javax.validation.constraints.NotEmpty;

import cn.loveapp.common.utils.DateUtil;
import cn.loveapp.orders.dto.SavePrintLogDTO;
import cn.loveapp.orders.dto.request.SavePrintLogBatchRequest;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSON;

import cn.loveapp.common.utils.LoggerHelper;
import cn.loveapp.common.web.CommonApiResponse;
import cn.loveapp.common.web.CommonApiStatus;
import cn.loveapp.orders.dto.request.SyncPrintLogRequest;
import cn.loveapp.orders.service.OrdersRpcInnerApiService;
import cn.loveapp.print.service.bo.UserInfoBo;
import cn.loveapp.print.service.service.OrdersService;

/**
 * 订单服务
 *
 * <AUTHOR>
 */
@Service
public class OrdersServiceImpl implements OrdersService {
    private final static LoggerHelper LOGGER = LoggerHelper.getLogger(OrdersServiceImpl.class);

    @Autowired
    private OrdersRpcInnerApiService ordersRpcInnerApiService;

    @Override
    public void syncPrintLog(UserInfoBo userInfoBo, String mergeTid, @NotEmpty List<String> tids, Map<String, List<String>> tidAndOids, String printType,
                             String waybillCode, String logisticsCompany, LocalDateTime printTime) {

        String sellerNick = userInfoBo.getSellerNick();

        SyncPrintLogRequest request = new SyncPrintLogRequest();

        request.setSellerNick(userInfoBo.getSellerNick());
        request.setSellerId(userInfoBo.getSellerId());
        request.setStoreId(userInfoBo.getStoreId());
        request.setAppName(userInfoBo.getAppName());

        request.setMergeTid(mergeTid);
        request.setPrintType(printType);
        request.setTids(tids);
        request.setWaybillCode(waybillCode);
        request.setLogisticsCompany(logisticsCompany);
        request.setTidAndOidsMap(tidAndOids);
        request.setPrintTime(DateUtil.convertLocalDateTimetoDate(printTime));

        LOGGER.logInfo(sellerNick, "-", "syncPrintLog request => " + JSON.toJSONString(request));

        CommonApiResponse response = ordersRpcInnerApiService.syncPrintLog(request);

        LOGGER.logInfo(sellerNick, "-", "syncPrintLog response => " + JSON.toJSONString(response));

        if (!response.getCode().equals(CommonApiStatus.Success.code())) {
            LOGGER.logWarn(sellerNick, "-", "同步订单打印记录失败，tid=>" + JSON.toJSONString(tids));
        }
    }

    @Override
    public void asyncSavePrintLogBatch(List<SavePrintLogDTO> savePrintLogs) {

        SavePrintLogBatchRequest request = new SavePrintLogBatchRequest();
        request.setSavePrintLogs(savePrintLogs);

        LOGGER.logInfo("asyncSavePrintLogBatch request => " + JSON.toJSONString(request));

        CommonApiResponse response = ordersRpcInnerApiService.savePrintLogBatch(request);

        LOGGER.logInfo("asyncSavePrintLogBatch response => " + JSON.toJSONString(response));

        if (response == null || !Objects.equals(response.getCode(), CommonApiStatus.Success.code())) {
            LOGGER.logWarn("批量异步保存订单打印记录失败=>" + JSON.toJSONString(savePrintLogs));
        }

    }
}
