package cn.loveapp.print.service.bo;

import java.util.List;

import cn.loveapp.print.service.request.PrintTradeInfoDTO;
import cn.loveapp.print.service.request.SerialGetRequest;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class SerialGetBo {
    /**
     * 打印的订单信息
     */
    private List<PrintTradeInfoDTO> tradeInfoList;

    /**
     * 合单订单的主单tid
     */
    private String mergeTid;

    /**
     * 订单类型
     */
    private Integer tradeType;

    /**
     * 是否获取最新流水号（订单重复获取时是否取最新）
     */
    private Boolean isFetchNewSerial;

    public static SerialGetBo of(SerialGetRequest request) {
        SerialGetBo serialGetBo = new SerialGetBo();
        serialGetBo.setTradeInfoList(request.getTradeInfoList());
        serialGetBo.setMergeTid(request.getMergeTid());
        serialGetBo.setTradeType(request.getTradeType());
        serialGetBo.setIsFetchNewSerial(request.getIsFetchNewSerial());
        return serialGetBo;
    }

}
