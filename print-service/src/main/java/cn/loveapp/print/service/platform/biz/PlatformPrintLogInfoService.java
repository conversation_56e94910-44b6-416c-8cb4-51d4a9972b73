package cn.loveapp.print.service.platform.biz;

import cn.loveapp.common.autoconfigure.platform.CommonPlatformHandler;
import cn.loveapp.print.common.entity.AyElefaceOperatelog;
import cn.loveapp.print.api.dto.TradeLogisticsBindingHistoryResponseDTO;

/**
 * 打印日志响应平台定制化服务
 * @Date  2023-09-26
 * <AUTHOR>
 */
public interface PlatformPrintLogInfoService extends CommonPlatformHandler {

    /**
     * 打印日志查询结果参数映射
     * @param logisticsBindingHistoryDTO
     * @param operatelog
     * @param platformId
     * @param appName
     */
    void appendInfo(TradeLogisticsBindingHistoryResponseDTO.LogisticsBindingHistoryDTO logisticsBindingHistoryDTO, AyElefaceOperatelog operatelog, String platformId,
                    String appName);
}
