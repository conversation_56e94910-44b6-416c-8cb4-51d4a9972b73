package cn.loveapp.print.service.request;

import java.util.List;

import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

import cn.loveapp.print.service.annotation.TradeTypeValidation;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 电子面单取号请求
 *
 * <AUTHOR>
 */
@ApiModel
@Data
public class ElefaceWaybillGetRequest {

    /**
     * 电子面单云打印接口请求参数
     */
    @ApiModelProperty(value = "电子面单云打印接口请求参数", required = true)
    @NotEmpty
    private List<String> paramWaybillCloudPrintApplyNewRequestList;

    /**
     * 打印的订单信息
     */
    @ApiModelProperty(value = "打印的订单信息", required = true)
    @NotEmpty
    @Valid
    private List<PrintTradeInfoDTO> tradeInfoList;

    /**
     * 合单订单的主单tid
     */
    @ApiModelProperty("合单订单的主单tid")
    private String mergeTid;

    /**
     * 订单类型
     */
    @ApiModelProperty(value = "订单类型", required = true)
    @NotNull
    @TradeTypeValidation
    private Integer tradeType;

    /**
     * 物流公司code
     */
    @ApiModelProperty(value = "物流公司code", required = true)
    @NotEmpty
    private String cpCode;

    /**
     * 物流公司
     */
    @ApiModelProperty(value = "物流公司", required = true)
    @NotEmpty
    private String logisticsCompany;

    /**
     * 流水号
     */
    @ApiModelProperty("流水号")
    private String serial;

    /**
     * 分享关系的id
     */
    @ApiModelProperty("分享关系的id")
    private String shareId;

    /**
     * 打印模版url
     */
    @ApiModelProperty("打印模版url")
    private String templateURL;
    /**
     * 物流子品牌
     */
    @ApiModelProperty("物流子品牌")
    private String brandCode;

    /**
     * 面单模板名称
     */
    @ApiModelProperty("面单模板名称")
    private String elefaceTemplateName;

    /**
     * 操作人(由前端传入当前操作的子账号名)
     */
    @ApiModelProperty("操作人")
    private String operator;

}
