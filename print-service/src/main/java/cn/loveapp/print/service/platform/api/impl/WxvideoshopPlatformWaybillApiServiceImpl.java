package cn.loveapp.print.service.platform.api.impl;

import cn.loveapp.common.constant.CommonPlatformConstants;
import cn.loveapp.common.platformsdk.wxvideoshop.WxvideoshopSDKService;
import cn.loveapp.common.utils.LoggerHelper;
import cn.loveapp.print.service.api.entity.AyWaybillApplySubscriptionInfo;
import cn.loveapp.print.service.api.entity.WaybillCloudPrintInfo;
import cn.loveapp.print.service.api.request.*;
import cn.loveapp.print.service.api.response.*;
import cn.loveapp.print.service.code.ErrorCode;
import cn.loveapp.print.service.platform.api.PlatformWaybillApiService;
import com.alibaba.fastjson.JSON;
import com.wxvideoshop.api.request.WxvideoshopElectronicBillOrdersCancelRequest;
import com.wxvideoshop.api.request.WxvideoshopElectronicBillOrdersCreateRequest;
import com.wxvideoshop.api.request.WxvideoshopElectronicBillSubscribesQueryRequest;
import com.wxvideoshop.api.response.WxvideoshopElectronicBillOrdersCancelResponse;
import com.wxvideoshop.api.response.WxvideoshopElectronicBillOrdersCreateResponse;
import com.wxvideoshop.api.response.WxvideoshopElectronicBillSubscribesQueryResponse;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 面单api service 微信视频号小店实现类
 *
 * <AUTHOR>
 */
@Service
public class WxvideoshopPlatformWaybillApiServiceImpl implements PlatformWaybillApiService {

    private static final LoggerHelper LOGGER = LoggerHelper.getLogger(XhsPlatformWaybillApiServiceImpl.class);
    /**
     * 微信视频号单次请求常量
     */
    private static final Long LIMIT = 100L;

    @Autowired
    private WxvideoshopSDKService wxvideoshopSDKService;

    /**
     * 查询面单服务订购及面单使用情况
     * @param request
     * @param topSession
     * @param platformId
     * @param appName
     * @return
     */
    @Override
    public AyWaybillSearchResponse waybillSearch(AyWaybillSearchRequest request, String topSession, String platformId, String appName) {
        WxvideoshopElectronicBillSubscribesQueryRequest waybillIiSearchRequest = new WxvideoshopElectronicBillSubscribesQueryRequest();
        waybillIiSearchRequest.setDeliveryId(StringUtils.trimToEmpty(request.getCpCode()));
        waybillIiSearchRequest.setLimit(LIMIT);
        waybillIiSearchRequest.setNeedBalance(true);
        //仅查询已绑定的电子面单
        waybillIiSearchRequest.setStatus(WxvideoshopElectronicBillSubscribesQueryRequest.Status.BOUND.getStatusCode());
        //页数
        int page = 0;
        boolean isContinue = true;

        AyWaybillSearchResponse response = null;
        List<AyWaybillApplySubscriptionInfo> waybillApplySubscriptionList = new ArrayList<>();
        while (isContinue) {
            waybillIiSearchRequest.setOffset(LIMIT*page);
            WxvideoshopElectronicBillSubscribesQueryResponse billSubscribesQueryResponse = wxvideoshopSDKService.execute(waybillIiSearchRequest, topSession, appName);

            response = new AyWaybillSearchResponse();
            response.init(billSubscribesQueryResponse);
            if (!response.isSuccess()) {
                //失败直接跳出循环
                LOGGER.logError("最后一次请求失败则跳出循环"+response.getErrorCode());
                break;
            }
            if (!CollectionUtils.isEmpty(billSubscribesQueryResponse.getAccountList())) {
                List<WxvideoshopElectronicBillSubscribesQueryResponse.AccountInfo> subscribesQueryList = billSubscribesQueryResponse.getAccountList();
                Map<String, List<WxvideoshopElectronicBillSubscribesQueryResponse.AccountInfo>> dataGroupByCompany = subscribesQueryList
                    .stream().collect(Collectors.groupingBy(g->g.getDeliveryId() + g.getCompanyType()));

                for (List<WxvideoshopElectronicBillSubscribesQueryResponse.AccountInfo> item : dataGroupByCompany.values()) {
                    if (CollectionUtils.isEmpty(item)) {
                        continue;
                    }

                    WxvideoshopElectronicBillSubscribesQueryResponse.AccountInfo accountInfo = item.get(0);
                    if (Objects.isNull(accountInfo) || Objects.isNull(accountInfo.getSenderAddress())) {
                        LOGGER.logError("缺少网点或发货信息，跳过");
                        continue;
                    }

                    waybillApplySubscriptionList.add(AyWaybillApplySubscriptionInfo.of(accountInfo.getShopId(), accountInfo.getDeliveryId(), accountInfo.getCompanyType(), item));
                }

                page++;
            } else {
                response.setWaybillApplySubscriptionCols(Collections.emptyList());
            }
            if (billSubscribesQueryResponse.getTotalNum()<=LIMIT*page){
                isContinue = false;
            }
        }
        response.setWaybillApplySubscriptionCols(waybillApplySubscriptionList);
        return response;
    }

    @Override
    public AyWaybillGetResponse waybillGet(AyWaybillGetRequest request, String topSession, String platformId, String appName) {
        AyWaybillGetResponse response = new AyWaybillGetResponse();
        WxvideoshopElectronicBillOrdersCreateRequest ordersCreateRequest = null;

        try {
            ordersCreateRequest = JSON.parseObject(request.getParamWaybillCloudPrintApplyNewRequest(),
                WxvideoshopElectronicBillOrdersCreateRequest.class);
        } catch (Exception e) {
            LOGGER.logError("非法的参数:paramWaybillCloudPrintApplyNewRequest",e);
            response.setErrorCode(ErrorCode.BaseCode.PARAMS_ERR.getCode().toString());
            response.setMsg(
                "非法的参数：paramWaybillCloudPrintApplyNewRequest = " + request.getParamWaybillCloudPrintApplyNewRequest());
            return response;
        }
        WxvideoshopElectronicBillOrdersCreateResponse ordersCreateResponse = wxvideoshopSDKService.execute(ordersCreateRequest, topSession, appName);
        response.init(ordersCreateResponse);
        if (!response.isSuccess()){
            if (ordersCreateResponse != null) {
                response.setMsg(ordersCreateResponse.getDeliveryErrorMsg());
            }
            return response;
        }
        List<WaybillCloudPrintInfo> ordersCreateResponseList = new ArrayList<>();
        WaybillCloudPrintInfo waybillCloudPrintInfo = WaybillCloudPrintInfo.of(ordersCreateResponse);
        ordersCreateResponseList.add(waybillCloudPrintInfo);
        response.setWaybillCloudPrintInfoList(ordersCreateResponseList);
        return response;
    }

    @Override
    public AyWaybillCancelResponse waybillCancel(AyWaybillCancelRequest request, String topSession, String platformId, String appName) {
        AyWaybillCancelResponse response = new AyWaybillCancelResponse();

        WxvideoshopElectronicBillOrdersCancelRequest cancelRequest = new WxvideoshopElectronicBillOrdersCancelRequest();
        cancelRequest.setWaybillId(request.getWaybillCode());
        cancelRequest.setDeliveryId(request.getCpCode());
        cancelRequest.setEwaybillOrderId(request.getWaybillOrderId());

        WxvideoshopElectronicBillOrdersCancelResponse cancelResponse = wxvideoshopSDKService.execute(cancelRequest, topSession, appName);
        response.init(cancelResponse);
        response.setCancelResult(response.isSuccess());

        return response;
    }

    /**
     * 获取平台id
     *
     * @return
     */
    @Override
    public String getPlatformId() {
        return CommonPlatformConstants.PLATFORM_WXVIDEOSHOP;
    }


}
