package cn.loveapp.print.service.utils;

import cn.loveapp.common.utils.LoggerHelper;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.springframework.beans.BeanUtils;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;

/**
 * 类型转换工具
 *
 * @program: orders-services-group
 * @description: ConvertUtil
 * @author: Jason
 * @create: 2020-04-24 11:44
 **/
public class ConvertUtil {
    private static final LoggerHelper LOGGER = LoggerHelper.getLogger(ConvertUtil.class);

    private static final ObjectMapper OBJECT_MAPPER = new ObjectMapper();


    /**
     * 通用转换器
     *
     * @param source
     * @param targetClass 目标类型的class, 需要有无参构造器
     * @param <T>
     * @param <C>
     * @return 转换失败时返回null
     */
    public static <T, C> C convert(T source, Class<C> targetClass) {
        if (source == null) {
            return null;
        }
        C target = null;
        try {
            target = targetClass.newInstance();
            BeanUtils.copyProperties(source, target);
        } catch (Exception e) {
            LOGGER.logError(e.getMessage(), e);
        }
        return target;
    }

    /**
     * 转换list
     *
     * @param source
     * @param targetClass
     * @param <T>
     * @param <C>
     * @return
     */
    public static <T, C> List<C> convertList(List<T> source, Class<C> targetClass) {
        if (source == null) {
            return null;
        }
        List<C> result = new ArrayList<>(source.size());
        if (source.isEmpty()) {
            return result;
        }
        for (T t : source) {
            C c = convert(t, targetClass);
            result.add(c);
        }
        return result;
    }

    /**
     * 添加对象进入Set
     *
     * @param set
     * @param value
     * @param <T>
     */
    public static <T> void add2Set(Set<T> set, T value) {
        if (value != null) {
            set.add(value);
        }
    }

    /**
     * 添加列表进入Set
     *
     * @param set
     * @param values
     * @param <T>
     */
    public static <T> void add2Set(Set<T> set, List<T> values) {
        if (values != null && values.size() > 0) {
            set.addAll(values);
        }
    }

    /**
     * 获取不为空的默认值
     *
     * @param args
     * @return
     */
    public static <T> T findFirstNotNull(T... args) {
        for (T arg : args) {
            if (arg != null) {
                return arg;
            }
        }
        return null;
    }

    /**
     * 类型转换
     *
     * @param value
     * @param defaultValue
     * @param <T>
     * @return
     */
    public static <T, V> T parseOrGetDefault(V value, T defaultValue, Class<T> type) {
        if (value != null) {
            try {
                return parseValue(value, type);
            } catch (Exception e) {
                // 转换异常，返回默认值
                LOGGER.logError("类型转换异常： value：" + value + "classType:" + type + "错误：" + e.getMessage(), e);
            }
        }
        return defaultValue;
    }

    private static <T, V> T parseValue(V value, Class<T> type) throws IOException {
        if (value.getClass() == type) {
            return (T) value;
        }
        T parsedValue = null;
        try {
            parsedValue = OBJECT_MAPPER.convertValue(value, type);
        } catch (IllegalArgumentException e) {
            // convertValue转换对象有问题
        }

        if (parsedValue == null && value instanceof String) {
            parsedValue = OBJECT_MAPPER.readValue((String) value, type);
        } else if (parsedValue == null) {
            String jsonString = OBJECT_MAPPER.writeValueAsString(value);
            parsedValue = parseValue(jsonString, type);
        }

        return parsedValue;
    }

    /**
     * Long类型转换
     *
     * @param value
     * @return
     */
    public static Integer parseInteger(Long value) {
        if (value == null) {
            return null;
        }
        try {
            return value.intValue();
        } catch (Exception e) {
            // 转换异常，返回默认值
            LOGGER.logError("类型转换异常： value：" + value + "错误：" + e.getMessage(), e);
        }
        return null;
    }
}
