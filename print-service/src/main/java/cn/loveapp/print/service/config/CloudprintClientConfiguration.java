package cn.loveapp.print.service.config;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import com.cainiao.link.consumer.LinkClient;

import cn.loveapp.common.utils.LoggerHelper;

/**
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2022/3/10 11:28
 * @Description: 云打印客户端配置
 */
@Configuration
public class CloudprintClientConfiguration {

    private static final LoggerHelper LOGGER = LoggerHelper.getLogger(CloudprintClientConfiguration.class);

    @Autowired
    private CaiNiaoCloudprintConfig caiNiaoCloudprintConfig;

    @Bean
    public LinkClient linkClient() {
        return LinkClient.builder(caiNiaoCloudprintConfig.getAppKey(), caiNiaoCloudprintConfig.getAppSecret()).build();
    }
}
