package cn.loveapp.print.service.controller;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

import cn.loveapp.print.api.dto.ElefaceSharingRelationDTO;
import cn.loveapp.print.api.response.SharingBatchCancelResponse;
import cn.loveapp.print.common.dto.UserMultiInfoDTO;
import cn.loveapp.print.service.annotation.UserAuth;
import cn.loveapp.print.service.bo.ElefaceSharingRelationQueryBo;
import cn.loveapp.print.service.constant.ElefaceSharingRelationOperateConstant;
import cn.loveapp.print.service.entity.ElefaceSharingRelation;
import cn.loveapp.print.service.response.MultiProxySharingToOthersTargetListResponse;
import cn.loveapp.print.service.response.MultiSharingToOthersListResponse;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import cn.loveapp.common.dto.UserSessionInfo;
import cn.loveapp.common.user.session.annotation.CheckUserSession;
import cn.loveapp.common.web.CommonApiResponse;
import cn.loveapp.common.web.CommonApiStatus;
import cn.loveapp.print.common.exception.CommonException;
import cn.loveapp.print.service.annotation.ShopsAuth;
import cn.loveapp.print.service.bo.ElefaceSharingCreateBo;
import cn.loveapp.print.service.bo.UserInfoBo;
import cn.loveapp.print.service.dto.TargetUserInfoDTO;
import cn.loveapp.print.service.dto.WaybillBranchShippAddressDTO;
import cn.loveapp.print.service.request.*;
import cn.loveapp.print.service.service.ElefaceAccountService;
import cn.loveapp.print.service.web.PrintResponseStatus;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import springfox.documentation.annotations.ApiIgnore;
import cn.loveapp.common.constant.HttpMethodsConstants;

/**
 * 面单账号相关服务接口 (网点信息、面单共享)
 *
 * <AUTHOR>
 */
@Api(tags = "面单账号相关服务接口")
@RestController
@RequestMapping("/account")
public class ElefaceAccountController {

    @Autowired
    private ElefaceAccountService elefaceAccountService;

    /**
     * 获取申请的面单列表（包含被分享的面单）
     *
     * @param sessionInfo
     * @return
     */
    @ApiOperation(value = "获取申请的面单列表（包含被分享的面单）", httpMethod = HttpMethodsConstants.POST)
    @ShopsAuth
    @RequestMapping(value = "/branch.shippaddress.getall", method = {RequestMethod.GET, RequestMethod.POST})
    @CheckUserSession(hasCheckPlatform = true)
    @Deprecated
    public CommonApiResponse<List<WaybillBranchShippAddressDTO>> getAllBranches(BranchShippaddressGetAllRequest request,
                                                                                @ApiIgnore UserSessionInfo sessionInfo,
                                                                                TargetUserInfoDTO targetUserInfoDTO) {
        UserInfoBo userInfoBo = UserInfoBo.of(sessionInfo, targetUserInfoDTO);
        try {
            return CommonApiResponse.success(elefaceAccountService.getAllBranchesShippAddress(userInfoBo, request.getIsIncludeUnAvailable()));
        } catch (CommonException e) {
            return new CommonApiResponse(CommonApiStatus.Success.code(), CommonApiStatus.Success.message(), e.getCode(),
                    e.getMessage(), null);
        }
    }

    /**
     * 获取面单账号列表（升级版，支持爱用多店）
     *
     * @param sessionInfo
     * @return
     */
    @ApiOperation(value = "获取面单账号列表", httpMethod = HttpMethodsConstants.POST)
    @ShopsAuth
    @RequestMapping(value = "/multi.branch.shippaddress.getall", method = {RequestMethod.GET, RequestMethod.POST})
    @CheckUserSession(hasCheckPlatform = true)
    public CommonApiResponse<List<WaybillBranchShippAddressDTO>> getAllBranchesV2(BranchShippaddressGetAllRequest request,
                                                                                @ApiIgnore UserSessionInfo sessionInfo,
                                                                                TargetUserInfoDTO targetUserInfoDTO) {
        UserInfoBo userInfoBo = UserInfoBo.of(sessionInfo, targetUserInfoDTO);
        try {
            return CommonApiResponse.success(elefaceAccountService.getMultiAllBranchesShippAddress(userInfoBo, request.getIsIncludeUnAvailable(), request.getGetType()));
        } catch (CommonException e) {
            return new CommonApiResponse(CommonApiStatus.Success.code(), CommonApiStatus.Success.message(), e.getCode(),
                    e.getMessage(), null);
        }
    }


    /**
     * 获取面单账号列表（只支持多店、爱用联盟版-面单代理模式）
     *
     * @param sessionInfo
     * @return
     */
    @ApiOperation(value = "获取面单账号列表", httpMethod = HttpMethodsConstants.POST)
    @ShopsAuth
    @RequestMapping(value = "/multi.branch.proxy.list.get", method = {RequestMethod.GET, RequestMethod.POST})
    @CheckUserSession(hasCheckPlatform = true)
    public CommonApiResponse<List<WaybillBranchShippAddressDTO>> multiBranchProxyListGet(ElefaceSharingProxyListRequest request,
                                                                                         @ApiIgnore UserSessionInfo sessionInfo,
                                                                                         TargetUserInfoDTO targetUserInfoDTO) {
        UserInfoBo userInfoBo = UserInfoBo.of(sessionInfo, targetUserInfoDTO);
        try {
            return CommonApiResponse.success(elefaceAccountService.multiBranchProxyListGet(request, userInfoBo));
        } catch (CommonException e) {
            return new CommonApiResponse(CommonApiStatus.Success.code(), CommonApiStatus.Success.message(), e.getCode(),
                    e.getMessage(), null);
        }
    }


    /**
     * 获取我共享出去的 面单共享关系
     *
     * @param sessionInfo
     * @return
     */
    @ApiOperation(value = "获取我共享出去的 面单共享关系", httpMethod = HttpMethodsConstants.POST)
    @RequestMapping("/sharing.toOthers.list")
    @CheckUserSession(hasCheckPlatform = true)
    @UserAuth
    @Deprecated
    public CommonApiResponse<List<ElefaceSharingRelation>> sharingToOthersList(@ApiIgnore UserSessionInfo sessionInfo, @ApiIgnore UserMultiInfoDTO userMultiInfoDTO) {
        List<UserInfoBo> userInfoBoList = UserInfoBo.of(sessionInfo, userMultiInfoDTO);
        List<ElefaceSharingRelation> relationList = new ArrayList<>();
        for (UserInfoBo userInfoBo : userInfoBoList) {
            List<ElefaceSharingRelation> sharingRelationsShareToOthersValid = elefaceAccountService.getSharingRelationsShareToOthersValid(userInfoBo);
            if (CollectionUtils.isNotEmpty(sharingRelationsShareToOthersValid)) {
                relationList.addAll(sharingRelationsShareToOthersValid);
            }
        }
        return CommonApiResponse.success(relationList);
    }

    /**
     * 获取我共享出去的 面单共享关系(支持爱用多店版)
     *
     * @param sessionInfo
     * @return
     */
    @ApiOperation(value = "获取我共享出去的 面单共享关系(爱用多店版)", httpMethod = HttpMethodsConstants.POST)
    @RequestMapping("/multi.sharing.toOthers.list")
    @CheckUserSession(hasCheckPlatform = true)
    @ShopsAuth
    public CommonApiResponse<List<ElefaceSharingRelationDTO>> multiSharingToOthersList(MultiSharingToOthersListRequest request, @ApiIgnore UserSessionInfo sessionInfo, @ApiIgnore TargetUserInfoDTO targetUserInfoDTO) {
        UserInfoBo userInfoBo = UserInfoBo.of(sessionInfo, targetUserInfoDTO);

        ElefaceSharingRelationQueryBo queryBo = ElefaceSharingRelationQueryBo.builder()
                .status(request.getStatus())
                .fuzzySearchStr(request.getFuzzySearchStr())
                .build();

        List<ElefaceSharingRelationDTO> relationList = elefaceAccountService.getMultiSharingRelationsShareToOthersValid(userInfoBo, queryBo);
        return CommonApiResponse.success(relationList);
    }

    /**
     * 获取我共享出去的 面单共享关系(支持爱用多店版) 分页-代理模式 不支持千牛端
     *
     * @param sessionInfo
     * @return
     */
    @ApiOperation(value = "获取我共享出去的 面单共享关系(爱用多店版)", httpMethod = HttpMethodsConstants.POST)
    @RequestMapping("/multi.proxy.sharing.toOthers.list")
    @CheckUserSession(hasCheckPlatform = true)
    @ShopsAuth
    public CommonApiResponse<MultiSharingToOthersListResponse> multiProxySharingToOthersList(MultiSharingToOthersListRequest request, @ApiIgnore UserSessionInfo sessionInfo, @ApiIgnore TargetUserInfoDTO targetUserInfoDTO) {
        UserInfoBo userInfoBo = UserInfoBo.of(sessionInfo, targetUserInfoDTO);

        if (Objects.nonNull(request.getPage()) && Objects.nonNull(request.getPageSize())) {
            Integer offset = request.getPage() - 1;
            Integer limit = request.getPageSize();
            request.setOffset(offset);
            request.setLimit(limit);
        }

        return CommonApiResponse.success(elefaceAccountService.multiProxySharingToOthersList(userInfoBo, request));
    }

    /**
     * 获取共享给我的 被取消的面单共享关系
     *
     * @param sessionInfo
     * @return
     */
    @ApiOperation(value = "获取共享给我的 被取消的面单共享关系", httpMethod = HttpMethodsConstants.POST)
    @RequestMapping("/sharing.toMe.canceled.list")
    @CheckUserSession(hasCheckPlatform = true)
    public CommonApiResponse<List<ElefaceSharingRelation>> sharingToMeCanceledList(@ApiIgnore UserSessionInfo sessionInfo) {
        UserInfoBo userInfoBo = UserInfoBo.of(sessionInfo);
        return CommonApiResponse.success(elefaceAccountService.getSharingRelationsShareToMeCanceled(userInfoBo));
    }

    /**
     * 获取共享给别人的 被取消的面单共享关系
     *
     * @param sessionInfo
     * @return
     */
    @ApiOperation(value = "获取共享给别人的 被取消的面单共享关系", httpMethod = HttpMethodsConstants.POST)
    @RequestMapping("/sharing.toOthers.canceled.list")
    @CheckUserSession(hasCheckPlatform = true)
    @UserAuth
    public CommonApiResponse<List<ElefaceSharingRelation>> sharingToOthersCanceledList(@ApiIgnore UserSessionInfo sessionInfo, @ApiIgnore UserMultiInfoDTO userMultiInfoDTO) {
        List<UserInfoBo> userInfoBoList = UserInfoBo.of(sessionInfo, userMultiInfoDTO);
        List<ElefaceSharingRelation> relationList = new ArrayList<>();
        for (UserInfoBo userInfoBo : userInfoBoList) {
            List<ElefaceSharingRelation> sharingRelationsShareToOthersValid = elefaceAccountService.getSharingRelationsShareToOtherCanceled(userInfoBo);
            if (CollectionUtils.isNotEmpty(sharingRelationsShareToOthersValid)) {
                relationList.addAll(sharingRelationsShareToOthersValid);
            }
        }
        return CommonApiResponse.success(relationList);
    }

    /**
     * 确认面单共享关系已取消
     *
     * @param request
     * @param sessionInfo
     * @return
     */
    @ApiOperation(value = "确认面单共享关系已取消", httpMethod = HttpMethodsConstants.POST)
    @RequestMapping("/sharing.toMe.canceled.confirm")
    @CheckUserSession(hasCheckPlatform = true)
    public CommonApiResponse sharingToMeCanceledConfirm(@Validated ElefaceSharingToMeCanceledConfirmRequest request,
                                                        @ApiIgnore UserSessionInfo sessionInfo) {
        UserInfoBo userInfoBo = UserInfoBo.of(sessionInfo);
        elefaceAccountService.confirmSharingRelationsShareToMeCanceled(request.getShareIds(), userInfoBo);
        return CommonApiResponse.success();
    }

    /**
     * 创建面单分享关系（分享指定店铺）
     *
     * @param sessionInfo
     * @return
     */
    @ApiOperation(value = "创建面单分享关系", httpMethod = HttpMethodsConstants.POST)
    @RequestMapping("/sharing.create")
    @CheckUserSession(hasCheckPlatform = true)
    public CommonApiResponse<ElefaceSharingRelation> sharingCreate(@Validated ElefaceSharingCreateRequest request,
                                                                   @ApiIgnore UserSessionInfo sessionInfo) {
        UserInfoBo userInfoBo = UserInfoBo.of(sessionInfo);
        ElefaceSharingCreateBo sharingCreateBo = ElefaceSharingCreateBo.of(request, userInfoBo);
        try {
            return CommonApiResponse.success(elefaceAccountService.createSharingRelation(sharingCreateBo));
        } catch (CommonException e) {
            return new CommonApiResponse(CommonApiStatus.Success.code(), CommonApiStatus.Success.message(), e.getCode(),
                    e.getMessage(), null);
        }
    }


    /**
     * 创建面单分享关系-支持多店版（多店版则分享对应店铺群）
     *
     * @param sessionInfo
     * @return
     */
    @ApiOperation(value = "创建面单分享关系", httpMethod = HttpMethodsConstants.POST)
    @RequestMapping("/multi.sharing.create")
    @CheckUserSession(hasCheckPlatform = true)
    public CommonApiResponse<ElefaceSharingRelation> multiSharingCreate(@Validated ElefaceSharingCreateRequest request,
                                                                   @ApiIgnore UserSessionInfo sessionInfo) {
        UserInfoBo userInfoBo = UserInfoBo.of(sessionInfo);
        ElefaceSharingCreateBo sharingCreateBo = ElefaceSharingCreateBo.of(request, userInfoBo);
        try {
            return CommonApiResponse.success(elefaceAccountService.multiCreateSharingRelation(sharingCreateBo));
        } catch (CommonException e) {
            return new CommonApiResponse(CommonApiStatus.Success.code(), CommonApiStatus.Success.message(), e.getCode(),
                    e.getMessage(), null);
        }
    }

    /**
     * 取消面单共享关系
     *
     * @param sessionInfo
     * @return
     */
    @ApiOperation(value = "取消面单共享关系", httpMethod = HttpMethodsConstants.POST)
    @RequestMapping("/sharing.cancel")
    @CheckUserSession(hasCheckPlatform = true)
    @ShopsAuth
    public CommonApiResponse sharingCancel(@Validated ElefaceSharingCancelRequest request,
                                           @ApiIgnore UserSessionInfo sessionInfo,
                                           @ApiIgnore TargetUserInfoDTO targetUserInfoDTO) {
        UserInfoBo userInfoBo = UserInfoBo.of(sessionInfo, targetUserInfoDTO);
        try {
            elefaceAccountService.cancelSharingRelation(request.getShareId(), userInfoBo, ElefaceSharingRelationOperateConstant.CANCEL);
            return CommonApiResponse.success();
        } catch (CommonException e) {
            return new CommonApiResponse(CommonApiStatus.Success.code(), CommonApiStatus.Success.message(), e.getCode(),
                    e.getMessage(), null);
        }
    }

    /**
     * 面单共享关系充值
     *
     * @param request
     * @param sessionInfo
     * @return
     */
    @ApiOperation(value = "面单共享关系充值", httpMethod = HttpMethodsConstants.POST)
    @RequestMapping("/sharing.topUp")
    @CheckUserSession(hasCheckPlatform = true)
    @ShopsAuth
    public CommonApiResponse topUpBranchShareNum(@Validated TopUpBranchShareNumRequest request,
                                                 @ApiIgnore UserSessionInfo sessionInfo,
                                                 @ApiIgnore TargetUserInfoDTO targetUserInfoDTO) {
        UserInfoBo userInfoBo = UserInfoBo.of(sessionInfo, targetUserInfoDTO);
        try {
            elefaceAccountService.topUpBranchShareNum(userInfoBo, request.getShareId(), request.getTopUpNum());
            return CommonApiResponse.success();
        } catch (CommonException e) {
            return new CommonApiResponse(CommonApiStatus.Success.code(), CommonApiStatus.Success.message(),
                    PrintResponseStatus.PlatformSdkError.code(), e.getMessage(), null);
        }
    }

    /**
     * 面单共享数量修改
     *
     * @param request
     * @param sessionInfo
     * @return
     */
    @ApiOperation(value = "面单共享数量修改", httpMethod = HttpMethodsConstants.POST)
    @RequestMapping("/sharing.quantityModify")
    @CheckUserSession(hasCheckPlatform = true)
    @ShopsAuth
    public CommonApiResponse sharingQuantityModify(@Validated TopUpBranchShareNumRequest request,
                                                   @ApiIgnore UserSessionInfo sessionInfo,
                                                   @ApiIgnore TargetUserInfoDTO targetUserInfoDTO) {
        UserInfoBo userInfoBo = UserInfoBo.of(sessionInfo, targetUserInfoDTO);
        try {
            elefaceAccountService.sharingQuantityModify(userInfoBo, request.getShareId(), request.getTopUpNum(), request.getShareNumUnlimited());
            return CommonApiResponse.success();
        } catch (CommonException e) {
            return new CommonApiResponse(CommonApiStatus.Success.code(), CommonApiStatus.Success.message(),
                    PrintResponseStatus.PlatformSdkError.code(), e.getMessage(), null);
        }
    }

    /**
     * 解除我代理的面单(包含我分享的)
     *
     * @param request
     * @param sessionInfo
     * @return
     */
    @ApiOperation(value = "解除我代理的面单", httpMethod = HttpMethodsConstants.POST)
    @RequestMapping(value = "/sharing.tome.remove", method = {RequestMethod.GET, RequestMethod.POST})
    @CheckUserSession(hasCheckPlatform = true)
    @ShopsAuth
    public CommonApiResponse<SharingBatchCancelResponse> sharingToMeRemove(@Validated SharingToMeRemoveRequest request,
                                                                           @ApiIgnore UserSessionInfo sessionInfo,
                                                                           @ApiIgnore TargetUserInfoDTO targetUserInfoDTO) {
        UserInfoBo userInfoBo = UserInfoBo.of(sessionInfo, targetUserInfoDTO);

        return CommonApiResponse.success(elefaceAccountService.removeSharingRelations(request.getOwnerSellerId(), request.getOwnerStoreId(),
                request.getOwnerAppName(), userInfoBo));
    }


    /**
     * 修改面单备注
     *
     * @param request
     * @param sessionInfo
     * @return
     */
    @ApiOperation(value = "修改面单备注", httpMethod = HttpMethodsConstants.POST)
    @RequestMapping(value = "/sharing.memo.update", method = {RequestMethod.GET, RequestMethod.POST})
    @CheckUserSession(hasCheckPlatform = true)
    @ShopsAuth
    public CommonApiResponse saveSharingMemo(@Validated SharingMemoSaveRequest request,
                                             @ApiIgnore UserSessionInfo sessionInfo,
                                             TargetUserInfoDTO targetUserInfoDTO) {
        UserInfoBo userInfoBo = UserInfoBo.of(sessionInfo, targetUserInfoDTO);

        try {
            elefaceAccountService.saveShareMemo(userInfoBo, request.getShareId(), request.getShareMemo(), request.getSalesman());
            return CommonApiResponse.success();
        } catch (CommonException e) {
            return new CommonApiResponse(CommonApiStatus.Success.code(), CommonApiStatus.Success.message(),
                    PrintResponseStatus.PlatformSdkError.code(), e.getMessage(), null);
        }
    }

    /**
     * 获取我共享过面单的 被分享者店铺列表
     *
     * @param sessionInfo
     * @return
     */
    @ApiOperation(value = "获取我共享出去的 面单共享关系(爱用多店版)", httpMethod = HttpMethodsConstants.POST)
    @RequestMapping("/multi.proxy.sharing.toOthers.target.list")
    @CheckUserSession(hasCheckPlatform = true)
    @ShopsAuth
    public CommonApiResponse<MultiProxySharingToOthersTargetListResponse> multiProxySharingToOthersTargetList(MultiProxySharingToOthersTargetListRequest request, @ApiIgnore UserSessionInfo sessionInfo, @ApiIgnore TargetUserInfoDTO targetUserInfoDTO) {
        UserInfoBo userInfoBo = UserInfoBo.of(sessionInfo, targetUserInfoDTO);
        return CommonApiResponse.success(elefaceAccountService.multiProxySharingToOthersTargetList(userInfoBo, request));
    }

}
