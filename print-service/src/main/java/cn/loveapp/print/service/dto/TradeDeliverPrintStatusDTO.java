package cn.loveapp.print.service.dto;

import java.io.Serializable;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 订单发货单打印状态
 *
 * <AUTHOR>
 */
@Data
@ApiModel(value = "订单发货单打印状态")
public class TradeDeliverPrintStatusDTO implements Serializable {
    private static final long serialVersionUID = -8008054462628998994L;
    /**
     * 订单号
     */
    @ApiModelProperty(value = "订单号")
    private String tid;

    /**
     * 是否打印
     */
    @ApiModelProperty(value = "是否打印")
    private Boolean isPrint;
}
