package cn.loveapp.print.service.dao.es;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.elasticsearch.client.RestHighLevelClient;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.search.aggregations.AggregationBuilders;
import org.elasticsearch.search.aggregations.Aggregations;
import org.elasticsearch.search.aggregations.bucket.terms.Terms;
import org.elasticsearch.search.aggregations.bucket.terms.TermsAggregationBuilder;
import org.springframework.data.elasticsearch.core.ElasticsearchOperations;
import org.springframework.data.elasticsearch.core.ResultsMapper;
import org.springframework.data.elasticsearch.core.query.NativeSearchQueryBuilder;
import org.springframework.stereotype.Repository;

import cn.loveapp.print.common.config.es.ElasticsearchConfiguration;
import cn.loveapp.print.common.constant.EsFields;
import cn.loveapp.print.common.entity.AyPrintLogSearchEs;
import cn.loveapp.print.service.bo.UserInfoBo;
import cn.loveapp.print.service.dto.PrintLogFieldListResultDTO;
import cn.loveapp.print.service.request.PrintLogFieldListStatisticRequest;

import static org.elasticsearch.index.query.QueryBuilders.*;

/**
 * <AUTHOR>
 * @date 2024-02-21 10:04
 * @description: ES-打印日志统计DAO
 */
@Repository
public class ElasticsearchAyPrintLogStatisticsQueryDao extends ElasticsearchAyPrintLogQueryDao {

    private static final String PRINTER_NAMES = "printerNames";
    private static final String PRINTER_USERS = "printerUsers";

    public ElasticsearchAyPrintLogStatisticsQueryDao(ElasticsearchConfiguration configuration,
        ElasticsearchOperations operations, RestHighLevelClient client, ResultsMapper mapper) {
        super(configuration, operations, client, mapper);
    }

    /**
     * 聚合搜索打印日志字段可选值列表
     *
     * @param request
     * @param userInfoBoList
     */
    public PrintLogFieldListResultDTO aggSearchPrintLogFieldList(PrintLogFieldListStatisticRequest request,
        List<UserInfoBo> userInfoBoList) {
        List<String> ayUserIds = new ArrayList<>();
        for (UserInfoBo userInfoBo : userInfoBoList) {
            ayUserIds.add(AyPrintLogSearchEs.createAyUserId(userInfoBo.getSellerId(), userInfoBo.getStoreId(),
                userInfoBo.getAppName()));
        }

        LocalDateTime startTime = request.getStartTime();
        LocalDateTime endTime = request.getEndTime();

        BoolQueryBuilder boolQueryBuilder = boolQuery();
        boolQueryBuilder.must(termsQuery(EsFields.ayUserId, ayUserIds));
        if (CollectionUtils.isEmpty(request.getPrintType())) {
            boolQueryBuilder.must(termsQuery(EsFields.printType, DEFAULT_PRINT_TYPES));
        } else {
            boolQueryBuilder.must(termsQuery(EsFields.printType, request.getPrintType()));
        }

        if (startTime != null) {
            boolQueryBuilder.must(rangeQuery(EsFields.printTime).gte(minuteSecondFormatter.format(startTime))
                .lte(minuteSecondFormatter.format(endTime)));
        }

        NativeSearchQueryBuilder builder = ayPrintLogListGetBuilder(userInfoBoList);
        builder.withQuery(boolQueryBuilder);

        // 打印机列表聚合
        TermsAggregationBuilder aggDistinctPrinterNumber = null;
        if (BooleanUtils.isTrue(request.getIsNeedStatisticsPrinterList())) {
            aggDistinctPrinterNumber = AggregationBuilders.terms(PRINTER_NAMES).field(EsFields.printerNumber)
                .size(printLogConfig.getPrintLogFieldListResultSize());
        }

        // 操作人列表聚合
        TermsAggregationBuilder aggDistinctPrinterUser = null;
        if (BooleanUtils.isTrue(request.getIsNeedStatisticsOperatorNameList())) {
            aggDistinctPrinterUser = AggregationBuilders.terms(PRINTER_USERS).field(EsFields.operatorNameKeyWord)
                .size(printLogConfig.getPrintLogFieldListResultSize());
        }

        if (request.getPrintLogTotal() < printLogConfig.getPrintLogFieldListStatisticsThreshold()) {
            if (aggDistinctPrinterNumber != null) {
                aggDistinctPrinterNumber.executionHint(EXECUTION_HINT_MAP);
            }

            if (aggDistinctPrinterUser != null) {
                aggDistinctPrinterUser.executionHint(EXECUTION_HINT_MAP);
            }
        }

        PrintLogFieldListResultDTO printLogFieldListResultDTO = new PrintLogFieldListResultDTO();
        if (aggDistinctPrinterNumber == null && aggDistinctPrinterUser == null) {
            return printLogFieldListResultDTO;
        }

        Aggregations aggregations = aggs(builder.build(), aggDistinctPrinterNumber, aggDistinctPrinterUser);
        Terms printerNameTerms = aggregations.get(PRINTER_NAMES);
        Terms printerUserTerms = aggregations.get(PRINTER_USERS);
        List<String> printerNameList = new ArrayList<>();
        List<String> printerUserList = new ArrayList<>();
        if (printerNameTerms != null) {
            for (Terms.Bucket printerNamebucket : printerNameTerms.getBuckets()) {
                String printerName = (String)printerNamebucket.getKey();
                printerNameList.add(printerName);
            }
        }

        if (printerUserTerms != null) {
            for (Terms.Bucket printerUserBucket : printerUserTerms.getBuckets()) {
                String printerUser = (String)printerUserBucket.getKey();
                printerUserList.add(printerUser);
            }
        }

        printLogFieldListResultDTO.setPrinterNameList(printerNameList);
        printLogFieldListResultDTO.setPrinterUserList(printerUserList);
        return printLogFieldListResultDTO;
    }
}
