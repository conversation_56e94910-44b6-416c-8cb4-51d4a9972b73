package cn.loveapp.print.service.constant;

/**
 * 面单共享关系相关常量
 *
 * <AUTHOR>
 */
public class ElefaceSharingRelationConstant {

    /**
     * 已取消共享
     */
    public static final Integer STATUS_CANCELED = 0;

    /**
     * 有效的共享
     */
    public static final Integer STATUS_VALID = 1;

    /**
     * 如果是-1则是无限制分享
     */
    public static final Long SHARE_NUM_UNLIMITED = -1L;

    /**
     * 取消回充面单开关（true,回充到被分享者，false or null 回充到分享者）
     */
    public static final String CANCEL_RECHARGE = "print.share.relation.cancel.recharge.to.target";
}
