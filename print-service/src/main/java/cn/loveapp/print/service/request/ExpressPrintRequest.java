package cn.loveapp.print.service.request;

import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

import cn.loveapp.print.common.dto.RecipientDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 快递单打印请求参数
 *
 * <AUTHOR>
 */
@Data
@ApiModel("快递单打印请求参数")
public class ExpressPrintRequest extends BasePrintRequest {

    /**
     * 物流公司
     */
    @ApiModelProperty(value = "物流公司", required = true)
    @NotEmpty
    private String logisticsCompany;

    /**
     * 收件人信息
     */
    @ApiModelProperty(value = "收件人信息", required = true)
    @NotNull
    @Valid
    private RecipientDTO recipient;

    /**
     * 发货人信息
     */
    @ApiModelProperty(value = "发货人信息")
    private RecipientDTO sender;

    /**
     * 打印模板
     */
    @ApiModelProperty(value = "打印模板")
    private String printModule;

    /**
     * 打印内容
     */
    @ApiModelProperty(value = "打印内容", required = true)
    @NotEmpty
    private String printData;

    /**
     * 快递单模板图片
     */
    @ApiModelProperty(value = "快递单模板图片", required = true)
    @NotEmpty
    private String expressImage;

}
