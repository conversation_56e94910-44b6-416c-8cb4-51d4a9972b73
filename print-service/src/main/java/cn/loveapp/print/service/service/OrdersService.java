package cn.loveapp.print.service.service;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

import javax.validation.constraints.NotEmpty;

import cn.loveapp.orders.dto.SavePrintLogDTO;
import cn.loveapp.print.service.bo.UserInfoBo;

/**
 * <AUTHOR>
 */
public interface OrdersService {
    /**
     * 同步订单打印记录
     *
     * @param userInfoBo
     * @param mergeTid
     * @param tids
     * @param printType
     * @param waybillCode
     * @param logisticsCompany
     * @param printTime
     */
    void syncPrintLog(UserInfoBo userInfoBo, String mergeTid, @NotEmpty List<String> tids,
        Map<String, List<String>> tidAndOids, String printType, String waybillCode, String logisticsCompany,
        LocalDateTime printTime);


    /**
     * 批量同步订单打印记录（异步消息）
     *
     * @param savePrintLogs
     */
    void asyncSavePrintLogBatch(List<SavePrintLogDTO> savePrintLogs);
}
