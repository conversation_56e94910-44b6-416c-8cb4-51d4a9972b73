package cn.loveapp.print.service.dao.print;

import java.util.List;

import cn.loveapp.print.api.dto.TargetPrintUserInfo;
import cn.loveapp.print.service.bo.ElefaceSharingRelationQueryBo;
import cn.loveapp.print.service.entity.ElefaceSharingRelation;
import org.apache.ibatis.annotations.Param;

/**
 * 电子面单共享关系(ElefaceSharingRelation)表数据库访问层
 *
 * <AUTHOR>
 * @since 2021-02-28 16:04:44
 */
public interface ElefaceSharingRelationDao {

    /**
     * 新增数据
     *
     * @param elefaceSharingRelation
     *            实例对象
     * @return 影响行数
     */
    int insert(ElefaceSharingRelation elefaceSharingRelation);

    /**
     * 通过shareId删除数据
     *
     * @param shareId
     *            分享id
     * @return 影响行数
     */
    int deleteByShareId(String shareId);

    /**
     * 批量删除
     *
     * @param shareIds
     * @return
     */
    int deleteByShareIds(List<String> shareIds);

    /**
     * 查询指定owner_user和target_user的分享记录
     *
     * @param ownerSellerId
     * @param ownerStoreId
     * @param ownerAppName
     * @param targetSellerId
     * @param targetStoreId
     * @param targetAppName
     * @return
     */
    List<ElefaceSharingRelation> queryByOwnerAndTargetUser(String ownerSellerId, String ownerStoreId,
        String ownerAppName, String targetSellerId, String targetStoreId, String targetAppName);

    /**
     * 增加面单已使用的数量
     *
     * @param shareId
     *            共享关系id
     * @param useNum
     *            需要增加的数量
     * @return
     */
    int addUsedNum(String shareId, Long useNum);

    /**
     * 添加共享面单的分享数量 （面单充值）
     *
     * @param shareId
     *            共享关系id
     * @param topUpNum
     *            需要增加的数量
     * @return
     */
    int addShareNum(String shareId, Long topUpNum);

    /**
     * 修改共享面单分享数量
     *
     * @param shareId
     *            共享关系id
     * @param shareNum
     *            面单分享数量
     * @return
     */
    int updateShareNum(String shareId, Long shareNum);

    /**
     * 修改status字段信息
     *
     * @param shareId
     * @param status
     * @return
     */
    int updateStatus(String shareId, Integer status);

    /**
     * 修改status和数量字段信息
     *
     * @param shareId
     * @param status
     * @return
     */
    int updateStatusAndShareNum(String shareId, Integer status, Long shareNum);

    /**
     * 通过share_id 查询数据
     *
     * @param shareId
     * @return
     */
    ElefaceSharingRelation queryByShareId(String shareId);

    /**
     * 通过share_id 批量查询数据
     *
     * @param shareIds
     * @return
     */
    List<ElefaceSharingRelation> queryByShareIds(List<String> shareIds);

    /**
     * 通过面单所有者的信息查询数据
     *
     * @param ownerSellerId
     * @param ownerStoreId
     * @param ownerAppName
     * @return
     */
    List<ElefaceSharingRelation> queryByOwnerUser(String ownerSellerId, String ownerStoreId, String ownerAppName);

    /**
     * 通过面单所有者的信息查询数据
     *
     * @param ownerSellerId
     * @param ownerStoreId
     * @param ownerAppName
     * @param status
     * @return
     */
    List<ElefaceSharingRelation> queryByOwnerUserAndStatus(String ownerSellerId, String ownerStoreId,
        String ownerAppName, Integer status);

    /**
     * 通过被分享用户的信息 查询数据
     *
     * @param targetSellerId
     * @param targetStoreId
     * @param targetAppName
     * @return
     */
    List<ElefaceSharingRelation> queryByTargetUser(String targetSellerId, String targetStoreId, String targetAppName);

    /**
     * 通过被分享用户的信息 查询数据
     *
     * @param targetSellerId
     * @param targetStoreId
     * @param targetAppName
     * @param status
     * @return
     */
    List<ElefaceSharingRelation> queryByTargetUserAndStatus(String targetSellerId, String targetStoreId,
        String targetAppName, Integer status);


    /**
     * 通过share_id 批量查询数据
     *
     * @param shareIds
     * @return
     */
    List<ElefaceSharingRelation> queryByShareIdsAndStatusList(@Param("shareIds") List<String> shareIds, @Param("statusList") List<String> statusList);


    /**
     * 通过share_id 修改备注
     *
     * @param shareId
     * @param shareMemo
     * @param salesman
     * @return
     */
    int updateShareMemoOrSalesmanByShareId(@Param("shareId")String shareId, @Param("shareMemo") String shareMemo, @Param("salesman") String salesman);


    /**
     * 通过被分享用户的信息 查询数据
     *
     * @param targetSellerId
     * @param targetStoreId
     * @param targetAppName
     * @param queryBo        查询条件
     * @return
     */
    List<ElefaceSharingRelation> searchByTargetUserAndQuery(@Param("targetSellerId") String targetSellerId, @Param("targetStoreId") String targetStoreId, @Param("targetAppName") String targetAppName, @Param("queryBo") ElefaceSharingRelationQueryBo queryBo);

    /**
     * 通过分享用户的信息 查询数据
     *
     * @param sellerId
     * @param storeId
     * @param appName
     * @param queryBo
     * @return
     */
    List<ElefaceSharingRelation> searchByOwnerUserAndQuery(@Param("sellerId") String sellerId, @Param("storeId") String storeId, @Param("appName") String appName, @Param("queryBo") ElefaceSharingRelationQueryBo queryBo);

    /**
     * 通过代理分享用户的信息 查询数据
     *
     * @param sellerId
     * @param storeId
     * @param appName
     * @param queryBo
     * @return
     */
    List<ElefaceSharingRelation> searchByProxyUserAndQuery(@Param("sellerId") String sellerId, @Param("storeId") String storeId, @Param("appName") String appName, @Param("queryBo") ElefaceSharingRelationQueryBo queryBo);

    /**
     * 通过shareId 更新代理标识
     * @param updateShareIdList
     * @param shareType
     */
    int updateShareTypeByShareId(@Param("updateShareIdList") List<String> updateShareIdList, @Param("shareType") Integer shareType);

    /**
     * 通过代理分享用户的信息 查询总数
     *
     * @param sellerId
     * @param storeId
     * @param appName
     * @param queryBo
     * @return
     */
    int countByProxyUserAndQuery(@Param("sellerId") String sellerId, @Param("storeId") String storeId, @Param("appName") String appName, @Param("queryBo") ElefaceSharingRelationQueryBo queryBo);


    /**
     * 查询代理分享的使用数量和取消数量
     *
     * @param sellerId
     * @param storeId
     * @param appName
     * @return
     */
    List<ElefaceSharingRelation> countByProxyRelation(@Param("sellerId") String sellerId, @Param("storeId") String storeId, @Param("appName") String appName);

    /**
     * 更新取消和分享数量
     * @param shareId
     * @param shareNum
     * @param cancelNum
     */
    void updateShareNumAndCancelNum(String shareId, Long shareNum, Long cancelNum);

    /**
     * 查询被分享的用户列表数量
     *
     * @param sellerId
     * @param storeId
     * @param appName
     * @param queryBo
     * @return
     */
    int countByProxyUserAndGroupByTarget(@Param("sellerId") String sellerId, @Param("storeId") String storeId, @Param("appName") String appName, @Param("queryBo") ElefaceSharingRelationQueryBo queryBo);

    /**
     * 查询被分享的用户列表列表
     *
     * @param sellerId
     * @param storeId
     * @param appName
     * @param queryBo
     * @return
     */
    List<TargetPrintUserInfo> searchByProxyUserAndGroupByTarget(@Param("sellerId") String sellerId, @Param("storeId") String storeId, @Param("appName") String appName, @Param("queryBo") ElefaceSharingRelationQueryBo queryBo);
}
