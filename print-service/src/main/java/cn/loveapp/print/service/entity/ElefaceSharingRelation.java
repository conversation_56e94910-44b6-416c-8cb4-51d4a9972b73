package cn.loveapp.print.service.entity;

import java.io.Serializable;
import java.util.Arrays;

import cn.loveapp.print.common.constant.ElefaceShareType;
import cn.loveapp.print.common.entity.ElefaceSharingRelationEntity;
import cn.loveapp.print.service.utils.ConvertUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;

import cn.loveapp.print.service.bo.ElefaceSharingCreateBo;
import cn.loveapp.print.service.constant.ElefaceSharingRelationConstant;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * (eleface_sharing_relation) 电子面单共享关系 实体类
 *
 * <AUTHOR>
 */
@ApiModel
@Data
public class ElefaceSharingRelation extends ElefaceSharingRelationEntity implements Serializable {
    private static final long serialVersionUID = -1578080710712539966L;
    private static final String DEFAULT_BRAND_CODE = "default";

    public static ElefaceSharingRelation of(ElefaceSharingCreateBo sharingCreateBo) {
        ElefaceSharingRelation sharingRelation = new ElefaceSharingRelation();
        BeanUtils.copyProperties(sharingCreateBo, sharingRelation);
        sharingRelation.setBrandCode(sharingCreateBo.getBrandCode());
        sharingRelation.setStatus(ElefaceSharingRelationConstant.STATUS_VALID);
        sharingRelation.setUsedNum(0L);
        sharingRelation.setSegmentCode(StringUtils.defaultIfBlank(sharingRelation.getSegmentCode(), StringUtils.EMPTY));
        sharingRelation.setShippAddressCity(
                StringUtils.defaultIfBlank(sharingRelation.getShippAddressCity(), StringUtils.EMPTY));
        sharingRelation.setShippAddressDistrict(
            StringUtils.defaultIfBlank(sharingRelation.getShippAddressDistrict(), StringUtils.EMPTY));
        sharingRelation.setShareType(ConvertUtil.findFirstNotNull(sharingRelation.getShareType(), ElefaceShareType.SHARE_TYPE_DEFAULT));
        return sharingRelation;
    }

}
