package cn.loveapp.print.service.dto;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024-01-23 18:04
 * @description: 打印日志高级搜索入参条件封装DTO
 */
@Data
public class AyPrintLogListSearchDTO {

    private Integer page;

    private Integer pageSize;

    /**
     * 批量订单号精确查询,单个和批量同时存在时，tidList优先级更高
     */
    private List<String> tidList;

    /**
     * 订单类型
     */
    private List<Integer> tradeType;

    /**
     * 收件人姓名
     */
    private String receiverName;

    /**
     * 收件人联系方式 电话/手机
     */
    private String receiverContact;

    /**
     * 收货地址关键字
     */
    private String receiverAddressKeyWord;

    /**
     * 发货人姓名
     */
    private String senderName;

    /**
     * 发货人联系方式 电话/手机
     */
    private String senderContact;

    /**
     * 收货地址关键字
     */
    private String senderAddressKeyWord;

    /**
     * 运单号列表，单个和批量同时存在时，waybillCodeList优先级更高
     */
    private List<String> waybillCodeList;

    /**
     * 子运单号
     */
    private String childWaybillCode;

    /**
     * 收件人地区列表(包含江浙沪、珠三角这些别名)
     */
    private List<String> receiverRegionList;

    /**
     * 收件人省份列表
     */
    private List<String> receiverProvinceList;

    /**
     * 收件人城市列表
     */
    private List<String> receiverCityList = new ArrayList<>();

    /**
     * 快递公司
     */
    private List<String> logisticsCompany;

    /**
     * 快递公司编号
     */
    private List<String> cpCodes;

    /**
     * 打印类型 express-快递单 eleface-电子面单 deliver-发货单
     */
    private String printType;

    /**
     * 打印类型集合 express-快递单 eleface-电子面单 deliver-发货单 优先级低于printType
     */
    private List<String> printTypeList;

    /**
     * 面单服务商
     * <p>
     * {@link cn.loveapp.print.common.constant.ElefaceProviderConstant}
     */
    private String elefaceProvider;

    /**
     * 开始时间
     */
    private LocalDateTime startTime;

    /**
     * 结束时间
     */
    private LocalDateTime endTime;

    /**
     * 是否需要运单号去重，去重后数据取最新的一条
     */
    private Boolean isDistinctWayBill;

    /**
     * 操作人
     */
    private String operatorName;

    /**
     * 操作人
     */
    private String operatorStoreId;

    /**
     * 回收单号
     * 1:查询已回收单号
     * 0:查询未回收单号
     * null:查询全部
     */
    private Integer valueOfCancelWayBill;

    /**
     * 是否需要聚合统计
     */
    private Boolean isNeedStatistics;

    /**
     * 是否统计订单总数
     */
    private Boolean isStatisticsTidCount;

    /**
     * 是否统计物流公司总数
     */
    private Boolean isStatisticsLogisticsCompanyCount;

    /**
     * 是否统计电子面单号总数
     */
    private Boolean isWaybillCodeCount;

    /**
     * 是否统计今天
     */
    private Boolean isStatisticsToday;

    /**
     * 打印机名称
     */
    private String printerName;

    /**
     * 爱用分销用户id列表
     */
    private List<String> distributeAyUserIdList;

    /**
     * 批次号
     */
    private List<String> batchIdList;

    /**
     * 批次序号
     */
    private List<Integer> numbersInBatch;
}
