package cn.loveapp.print.service.dao.redis;

import cn.loveapp.common.utils.LoggerHelper;
import cn.loveapp.print.service.entity.WaybillGetRedisEntity;
import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Repository;

import javax.validation.constraints.NotNull;
import java.util.Objects;
import java.util.TimeZone;
import java.util.concurrent.TimeUnit;

/**
 * 取号redis缓存DAO
 * @program: print-services-group
 * @description:
 * @author: zhangchunhui
 * @create: 2023/4/3 14:26
 **/
@Repository
public class WaybillRedisDao {

    private static LoggerHelper LOGGER = LoggerHelper.getLogger(WaybillRedisDao.class);

    /**
     * 面单获取接口RedisKey
     */
    public static final String PRINT_WAYBILL_GET_KEY = "print:waybill:get:";

    protected static final ObjectMapper OBJECT_MAPPER;

    @Autowired
    private StringRedisTemplate stringRedisTemplate;

    static {
        OBJECT_MAPPER = new ObjectMapper();
        OBJECT_MAPPER.setSerializationInclusion(JsonInclude.Include.NON_NULL);
        OBJECT_MAPPER.setTimeZone(TimeZone.getTimeZone("GMT+8"));
    }

    /**
     * 生成Redis面单获取接口幂等请求Id的key
     *
     * @param waybillGetRequestId
     * @return
     */
    private String initWaybillGetRedisKey(@NotNull String waybillGetRequestId, @NotNull String sellerId, @NotNull String appName, @NotNull String storeId) {
        return PRINT_WAYBILL_GET_KEY + sellerId + ":" + storeId + ":" + appName + ":" + waybillGetRequestId;
    }

    /**
     * 获取面单取号接口幂等缓存数据
     *
     * @param waybillGetRequestId
     * @return
     */
    public WaybillGetRedisEntity getWayBillGetRedisEntity(@NotNull String waybillGetRequestId, @NotNull String sellerId, @NotNull String appName, @NotNull String storeId) {
        String k = initWaybillGetRedisKey(waybillGetRequestId, sellerId, appName, storeId);
        if (hasExistCollection(k)) {
            return findObject(k);
        }
        return null;
    }

    /**
     * 保存面单取号接口幂等缓存数据
     *
     * @param waybillGetRequestId
     * @param waybillGetRedisEntity
     * @return
     */
    public boolean setWayBillGetRedisEntity(@NotNull String waybillGetRequestId, @NotNull WaybillGetRedisEntity waybillGetRedisEntity, @NotNull String sellerId, @NotNull String appName, @NotNull String storeId) {
        String k = initWaybillGetRedisKey(waybillGetRequestId, sellerId, appName, storeId);
        return put(k, waybillGetRedisEntity);
    }

    /**
     * 判断是否存在
     *
     * @param collection
     * @return
     */
    private Boolean hasExistCollection(String collection) {
        try {
            if (StringUtils.isEmpty(collection)) {
                return false;
            }
            return stringRedisTemplate.hasKey(collection);
        } catch (Exception e) {
            if (e.getMessage() == null) {
                LOGGER.logError("Entry '" + collection + "' does not exist in cache", e);
            } else {
                LOGGER.logError("Unable to find entry '" + collection + "' in cache collection '" + collection + "': " + e.getMessage() + "", e);
            }
            return false;
        }
    }

    private WaybillGetRedisEntity findObject(String collection) {
        if (StringUtils.isEmpty(collection)) {
            return null;
        }
        try {
            String valueAsString = stringRedisTemplate.opsForValue().get(collection);
            WaybillGetRedisEntity waybillGetRedis = OBJECT_MAPPER.readValue(valueAsString, WaybillGetRedisEntity.class);
            LOGGER.logInfo("获取取号接口缓存：" + JSON.toJSONString(waybillGetRedis));
            return waybillGetRedis;
        } catch (Exception e) {
            if (e.getMessage() == null) {
                LOGGER.logError("Entry '" + collection + "' does not exist in cache", e);
            } else {
                LOGGER.logError("Unable to find entry '" + collection + "' in cache collection '" + collection + "': " + e.getMessage() + "", e);
            }
            return null;
        }
    }

    private boolean put(String collection, WaybillGetRedisEntity waybillGetRedisEntity) {
        if (StringUtils.isEmpty(collection) || Objects.isNull(waybillGetRedisEntity)) {
            return false;
        }
        try {
            String valueAsString = OBJECT_MAPPER.writeValueAsString(waybillGetRedisEntity);
            stringRedisTemplate.opsForValue().set(collection, valueAsString, 5, TimeUnit.MINUTES);
            return true;
        } catch (Exception e) {
            LOGGER.logError("Unable to add object of key " + collection + " to cache collection '" + collection + "': " + e.getMessage() + "", e);
            return false;
        }
    }

}
