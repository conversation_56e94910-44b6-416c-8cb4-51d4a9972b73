package cn.loveapp.print.service.bo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 面单操作相关查询Bo
 *
 * <AUTHOR>
 * @Date 2024/7/25 5:19 PM
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ElefaceSharingRelationQueryBo {

    /**
     * 分享id
     */
    private List<String> shareIdList;

    /**
     * 模糊搜索店铺字段（分享者、面单账号、被分享者、备注）
     */
    private String fuzzySearchStr;

    /**
     * 面单所有者平台
     */
    private String ownerStoreId;

    /**
     * 面单所有者id
     */
    private String ownerSellerId;

    /**
     * 面单所有者应用
     */
    private String ownerAppName;

    /**
     * 面单被分享平台
     */
    private String elefaceTargetStoreId;

    /**
     * 搜索开始时间
     */
    private LocalDateTime operateTimeStart;

    /**
     * 搜索结束时间
     */
    private LocalDateTime operateTimeEnd;

    /**
     * 面单分享类型
     */
    private Integer shareType;

    /**
     * 面单状态
     */
    private Integer status;

    /**
     * 面单备注搜索
     */
    private String shareMemo;

    /**
     * 网点搜索
     */
    private String shippAddressMd5;

    /**
     * 网点搜索
     */
    private String branchName;

    /**
     * 业务员
     */
    private String salesman;

    /**
     * 快递公司
     */
    private String cpCode;

    /**
     * 快递公司
     */
    private List<String> cpCodeList;

    /**
     * 服务商
     */
    private String provider;

    /**
     * 面单账户模糊搜索字段
     */
    private String ownerSearchStr;

    /**
     * 面单被分享者模糊搜索字段
     */
    private String targetSearchStr;

    /**
     * 面单代理者模糊搜索字段
     */
    private String proxySearchStr;

    /**
     * 查询分页字段
     */
    private Integer limit;
    private Integer offset;

    /**
     * 操作类型列表，用于过滤特定类型的操作日志
     */
    private List<Integer> operateTypeList;

}
