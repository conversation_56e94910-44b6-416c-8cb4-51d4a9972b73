package cn.loveapp.print.service.sdk;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.alibaba.fastjson.JSON;
import com.cainiao.link.constant.Format;
import com.cainiao.link.consumer.LinkClient;
import com.cainiao.link.option.RequestOption;
import com.taobao.pac.sdk.cp.schema.CpBaseRequest;
import com.taobao.pac.sdk.cp.schema.CpBaseResponse;

import cn.loveapp.common.utils.LoggerHelper;
import cn.loveapp.print.service.config.CaiNiaoCloudprintConfig;

/**
 * @Author: zhongzijie
 * @Date: 2022/3/9 14:03
 * @Description: 菜鸟云打印SDK
 */
@Component
public class CaiNiaoSDKService {

    private static final LoggerHelper LOGGER = LoggerHelper.getLogger(CaiNiaoSDKService.class);

    @Autowired
    private CaiNiaoCloudprintConfig caiNiaoCloudprintConfig;

    @Autowired
    private LinkClient linkClient;

    public <R extends CpBaseResponse> R execute(CpBaseRequest<R> request) {
        RequestOption option = new RequestOption();
        option.setFormat(Format.XML);
        option.setFromCode(caiNiaoCloudprintConfig.getToken());
        String apiName = request.getApi();
        LOGGER.logInfo("CaiNiao api <" + apiName + "> 接口请求参数 " + JSON.toJSONString(request));
        R response = linkClient.execute(request, option);
        LOGGER.logInfo("CaiNiao api <" + apiName + "> 接口响应参数 " + JSON.toJSONString(response));
        return response;
    }
}
