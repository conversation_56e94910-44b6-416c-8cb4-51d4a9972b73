package cn.loveapp.print.service.dao.newprint;

import java.util.List;

import cn.loveapp.print.common.entity.AySerialLog;

/**
 * <AUTHOR>
 */
public interface AyTradeSerialLogDao {

    /**
     * 插入一条新数据
     *
     * @param aySerialLog
     * @return
     */
    int insert(AySerialLog aySerialLog);

    /**
     * 批量插入
     *
     * @param aySerialLogList
     * @return
     */
    int batchInsert(List<AySerialLog> aySerialLogList);

    /**
     * 查询订单的打印流水号列表
     *
     * @param tid
     * @param tradeType
     * @param storeId
     * @param sellerId
     * @param appName
     * @return
     */
    List<AySerialLog> queryByTid(String tid, Integer tradeType, String storeId, String sellerId, String appName);

    /**
     * 查询订单的打印流水号列表
     *
     * @param tidList
     * @param tradeType
     * @param storeId
     * @param sellerId
     * @param appName
     * @return
     */
    List<AySerialLog> queryByTidList(List<String> tidList, Integer tradeType, long maxId, int pageSize, String storeId, String sellerId, String appName);

    /**
     * 更新作废标识
     *
     * @param isInvalid
     * @param tids
     * @param tradeType
     * @param storeId
     * @param sellerId
     * @param appName
     * @return
     */
    int updateIsInvalidByTids(Boolean isInvalid, List<String> tids, Integer tradeType, String storeId, String sellerId,
        String appName);

}
