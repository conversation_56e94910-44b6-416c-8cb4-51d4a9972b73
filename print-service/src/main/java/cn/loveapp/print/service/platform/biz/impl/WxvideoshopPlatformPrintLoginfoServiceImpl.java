package cn.loveapp.print.service.platform.biz.impl;

import cn.loveapp.common.constant.CommonPlatformConstants;
import cn.loveapp.print.common.entity.AyElefaceOperatelog;
import cn.loveapp.print.service.platform.biz.PlatformPrintLogInfoService;
import cn.loveapp.print.api.dto.TradeLogisticsBindingHistoryResponseDTO;
import org.springframework.stereotype.Service;

/**
 * 打印日志响应平台视频号服务实现类
 * @date 2023/09/26
 * <AUTHOR>
 *
 */
@Service
public class WxvideoshopPlatformPrintLoginfoServiceImpl implements PlatformPrintLogInfoService {


    /**
     * 视频号,将面单操作日志中的objectId取出放到waybillOrderId
     * @param logisticsBindingHistoryDTO
     * @param operatelog
     * @param platformId
     * @param appName
     */
    @Override
    public void appendInfo(TradeLogisticsBindingHistoryResponseDTO.LogisticsBindingHistoryDTO logisticsBindingHistoryDTO, AyElefaceOperatelog operatelog, String platformId, String appName) {
        //waybillOrderId放在ObjectId中保存
        logisticsBindingHistoryDTO.setWaybillOrderId(operatelog.getObjectId());
    }

    /**
     * 获取平台id
     *
     * @return
     */
    @Override
    public String getPlatformId() {
        return CommonPlatformConstants.PLATFORM_WXVIDEOSHOP;
    }
}
