package cn.loveapp.print.service.bo;

import cn.loveapp.print.service.dto.DistributeOrderInfoDTO;
import cn.loveapp.print.service.request.DistributeElefacePrintRequest;
import lombok.Data;

import java.util.List;

/**
 * 分销代发面单打印传输Bo
 *
 * <AUTHOR>
 * @Date 2023/11/6 3:02 PM
 */
@Data
public class DistributeElefacePrintBo extends ElefacePrintBo {

    /**
     * 分销单信息
     */
    @Deprecated
    private List<DistributeOrderInfoDTO> distributeOrderInfoList;


    public static DistributeElefacePrintBo of(DistributeElefacePrintRequest request) {
        DistributeElefacePrintBo elefacePrintBo = new DistributeElefacePrintBo();
        elefacePrintBo.setBasePrintInfo(request);
        elefacePrintBo.setProvider(request.getProvider());
        elefacePrintBo.setCpCode(request.getCpCode());
        elefacePrintBo.setLogisticsCompany(request.getLogisticsCompany());
        elefacePrintBo.setWaybillCode(request.getWaybillCode());
        elefacePrintBo.setChildWaybillCode(request.getChildWaybillCode());
        elefacePrintBo.setRecipient(request.getRecipient());
        elefacePrintBo.setSender(request.getSender());
        elefacePrintBo.setPrintData(request.getPrintData());
        elefacePrintBo.setCustomData(request.getCustomData());
        elefacePrintBo.setBrandCode(request.getBrandCode());
        elefacePrintBo.setExternalInfo(request.getExternalInfo());
        elefacePrintBo.setRealCpCode(request.getRealCpCode());
        elefacePrintBo.setDistributeOrderInfoList(request.getDistributeOrderInfoList());
        return elefacePrintBo;
    }

}
