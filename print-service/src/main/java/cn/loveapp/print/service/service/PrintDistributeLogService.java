package cn.loveapp.print.service.service;

import cn.loveapp.print.common.exception.CommonException;
import cn.loveapp.print.service.bo.DistributeElefacePrintBo;
import cn.loveapp.print.service.bo.UserInfoBo;
import cn.loveapp.print.service.dto.DistributeOrderInfoDTO;
/**
 * 分销代发打印相关处理接口
 *
 * <AUTHOR>
 * @Date 2023/11/6 3:00 PM
 */
public interface PrintDistributeLogService {

    /**
     * 面单打印
     *
     * @param elefacePrintBo
     * @param userInfoBo
     * @throws Exception 入库异常
     */
    void elefacePrint(DistributeElefacePrintBo elefacePrintBo, UserInfoBo userInfoBo);

    /**
     * 取消电子面单
     *
     * @param cpCode
     * @param waybillCode
     * @param isCancelFromApi
     * @param userInfoBo
     * @param distributeInfo
     */
    void waybillCancel(String cpCode, String waybillCode, Integer billVersion, Boolean isCancelFromApi, UserInfoBo userInfoBo, DistributeOrderInfoDTO distributeInfo) throws CommonException;
}
