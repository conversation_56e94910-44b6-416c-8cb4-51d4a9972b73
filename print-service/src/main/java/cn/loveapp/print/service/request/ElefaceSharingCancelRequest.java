package cn.loveapp.print.service.request;

import javax.validation.constraints.NotEmpty;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 面单共享关系取消 request
 *
 * <AUTHOR>
 */
@Data
@ApiModel(value = "面单共享关系取消")
public class ElefaceSharingCancelRequest {
    /**
     * 共享关系id
     */
    @ApiModelProperty(value = "共享关系id", required = true)
    @NotEmpty
    private String shareId;
}
