package cn.loveapp.print.service.request;

import javax.validation.constraints.NotEmpty;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 */
@ApiModel("取消电子面单请求体")
@Data
public class ElefaceWaybillCancelRequest {

    /**
     * 物流公司code
     */
    @ApiModelProperty(value = "物流公司code", required = true)
    @NotEmpty
    private String cpCode;

    /**
     * 面单号
     */
    @ApiModelProperty(value = "面单号", required = true)
    @NotEmpty
    private String waybillCode;

    /**
     * 是否调用api取消
     */
    @ApiModelProperty(value = "是否调用api取消")
    private Boolean isCancelFromApi;

    /**
     * 电子面单订单id,全局唯一id (微信视频号专用)
     */
    @ApiModelProperty(value = "电子面单订单id,全局唯一id (微信视频号专用)")
    private String ewaybillOrderId;

    /**
     * 电子面单版本号，1-默认值旧版电子面单 2-新版电子面单 (XHS使用)
     */
    @ApiModelProperty(value = "电子面单版本号")
    private Integer billVersion;
}
