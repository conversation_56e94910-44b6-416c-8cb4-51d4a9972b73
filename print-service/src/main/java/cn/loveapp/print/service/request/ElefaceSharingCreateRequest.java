package cn.loveapp.print.service.request;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 创建面单共享关系请求 request
 *
 * <AUTHOR>
 */
@ApiModel(value = "创建面单共享关系请求")
@Data
public class ElefaceSharingCreateRequest {

    /**
     * 面单被分享者的店铺名
     */
    @ApiModelProperty(value = "面单被分享者的店铺名")
    private String targetMallName;

    /**
     * 面单所有者的sellerNick
     */
    @ApiModelProperty(value = "面单所有者的sellerNick")
    private String ownerSellerNick;

    /**
     * 面单所有者的sellerId
     */
    @ApiModelProperty(value = "面单所有者的sellerId")
    private String ownerSellerId;

    /**
     * 面单所有者的平台id
     */
    @ApiModelProperty(value = "面单所有者的平台id")
    private String ownerStoreId;

    /**
     * 面单所有者的应用名称
     */
    @ApiModelProperty(value = "面单所有者的应用名称")
    private String ownerAppName;

    /**
     * 被分享用户sellerNick
     */
    @ApiModelProperty(value = "被分享用户sellerNick", required = true)
    private String targetSellerNick;

    /**
     * 被分享用户平台id
     */
    @ApiModelProperty(value = "被分享用户平台id", required = true)
    @NotEmpty
    private String targetStoreId;

    /**
     * 被分享用户应用名称
     */
    @ApiModelProperty(value = "被分享用户应用名称", required = true)
    @NotEmpty
    private String targetAppName;

    /**
     * 面单服务商 CN PDD
     */
    @ApiModelProperty(value = "面单服务商 CN PDD", required = true)
    @NotEmpty
    private String provider;

    /**
     * 快递公司code
     */
    @ApiModelProperty(value = "快递公司code", required = true)
    @NotEmpty
    private String cpCode;

    /**
     * 物流服务商 业务类型
     */
    @ApiModelProperty(value = "物流服务商 业务类型", required = true)
    @NotNull
    private Long cpType;

    /**
     * 网点code
     */
    @ApiModelProperty("网点code")
    private String branchCode;

    /**
     * 号段信息
     */
    @ApiModelProperty("号段信息")
    private String segmentCode;

    /**
     * 发货地址 省
     */
    @ApiModelProperty(value = "发货地址 省", required = true)
    private String shippAddressProvince;

    /**
     * 发货地址 市
     */
    @ApiModelProperty(value = "发货地址 市")
    private String shippAddressCity;

    /**
     * 发货地址 区
     */
    @ApiModelProperty(value = "发货地址 区")
    private String shippAddressDistrict;

    /**
     * 发货地址 详细
     */
    @ApiModelProperty(value = "发货地址 详细", required = true)
    @NotEmpty
    private String shippAddressDetail;

    /**
     * 分享的面单数量
     */
    @ApiModelProperty(value = "分享的面单数量", required = true)
    @NotNull
    private Long shareNum;

    /**
     * 子品牌
     */
    @ApiModelProperty("子品牌")
    private String brandCode;

    /**
     * 分享类型， 1: 标志代理使用
     */
    @ApiModelProperty("分享类型， 1: 标志代理使用")
    private Integer shareType;

    /**
     * 分享备注
     */
    @ApiModelProperty("分享备注")
    private String shareMemo;

    /**
     * 面单记录操作人
     */
    @ApiModelProperty("面单记录操作人")
    private String operator;

    /**
     * 面单记录操作终端
     */
    @ApiModelProperty("面单记录操作终端")
    private String operateTerminal;

    /**
     * 代理用户id
     */
    @ApiModelProperty("代理用户id")
    private String proxySellerId;

    /**
     * 代理用户nick
     */
    @ApiModelProperty("代理用户nick")
    private String proxySellerNick;

    /**
     * 代理用户平台
     */
    @ApiModelProperty("代理用户平台")
    private String proxyStoreId;

    /**
     * 代理用户应用
     */
    @ApiModelProperty("代理用户应用")
    private String proxyAppName;

    /**
     * 网点名称
     */
    @ApiModelProperty("网点名称")
    private String branchName;

    /**
     * 是否面单可用
     */
    @ApiModelProperty("是否面单可用")
    private boolean isCheckOwnerUsable;

    /**
     * 业务员备注
     */
    @ApiModelProperty("业务员备注")
    private String salesman;

    /**
     * 电子面单版本号，1-默认值旧版电子面单 2-新版电子面单 (XHS使用)
     */
    @ApiModelProperty("电子面单版本号")
    private Integer billVersion;

}
