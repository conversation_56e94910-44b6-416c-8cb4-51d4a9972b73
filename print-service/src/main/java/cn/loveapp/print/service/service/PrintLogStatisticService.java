package cn.loveapp.print.service.service;

import java.util.List;

import cn.loveapp.print.service.bo.UserInfoBo;
import cn.loveapp.print.service.request.PrintLogFieldListStatisticRequest;
import cn.loveapp.print.service.response.PrintLogFieldListStatisticResponse;

/**
 * <AUTHOR>
 * @date 2024-02-21 09:59
 * @description: 打印日志统计服务接口
 */
public interface PrintLogStatisticService {

    /**
     * 获取聚合搜索打印日志信息结果（打印机列表、操作用户列表）
     *
     * @param request
     * @param userInfoBoList
     */
    PrintLogFieldListStatisticResponse statisticsPrintLogFieldList(PrintLogFieldListStatisticRequest request,
        List<UserInfoBo> userInfoBoList);
}
