package cn.loveapp.print.service.response;

import cn.loveapp.print.service.bo.SerialGetBo;
import cn.loveapp.print.service.request.PrintTradeInfoDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 批量获取流水号获取响应体
 *
 * <AUTHOR>
 * @Date 2023/12/7 2:04 PM
 */
@Data
@ApiModel("批量获取流水号获取响应体")
public class BatchSerialGetResponse {

    /**
     * 流水号信息列表
     */
    @ApiModelProperty(value = "流水号信息列表")
    private List<SerialGetResponseDTO> serialList;


    public void addSerialGetResponse(String serial, SerialGetBo serialGetBo) {
        if (Objects.isNull(serialGetBo) || StringUtils.isEmpty(serial)) {
            return;
        }
        if (serialList == null) {
            serialList = new ArrayList<>();
        }
        SerialGetResponseDTO serialGetResponseDTO = new SerialGetResponseDTO(serial);
        List<PrintTradeInfoDTO> tradeInfoList = serialGetBo.getTradeInfoList();
        if (tradeInfoList != null) {
            List<String> tidList = tradeInfoList.stream().map(PrintTradeInfoDTO::getTid).collect(Collectors.toList());
            serialGetResponseDTO.setTidList(tidList);
        }
        serialGetResponseDTO.setMergeTid(serialGetBo.getMergeTid());
        serialList.add(serialGetResponseDTO);
    }

}
