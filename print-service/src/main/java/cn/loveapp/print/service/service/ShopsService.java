package cn.loveapp.print.service.service;

import cn.loveapp.print.common.dto.TargetSellerInfo;
import cn.loveapp.shops.api.response.ShopsResponse;

import java.util.List;

/**
 * 多店服务
 *
 * <AUTHOR>
 */
public interface ShopsService {
    /**
     * 多店鉴权
     *
     * @param sellerNick
     * @param storeId
     * @param appName
     * @param targetNick
     * @param targetStoreId
     * @param targetAppName
     * @return
     */
    boolean shopsAuth(String sellerNick, String storeId, String appName, String targetNick, String targetStoreId, String targetAppName);

    /**
     * 多个绑定店铺鉴权
     *
     * @param sellerNick
     * @param storeId
     * @param appName
     * @param targetSellerList
     * @return
     */
    boolean multiShopsAuth(String sellerNick, String storeId, String appName, List<TargetSellerInfo> targetSellerList);


    /**
     * 获取店铺群
     *
     * @param sellerNick
     * @param storeId
     * @param appName
     * @return
     */
    ShopsResponse shopGet(String sellerId, String sellerNick, String storeId, String appName, boolean includeMembers);
}
