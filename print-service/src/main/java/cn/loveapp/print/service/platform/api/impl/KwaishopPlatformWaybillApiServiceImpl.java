package cn.loveapp.print.service.platform.api.impl;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import com.kuaishou.merchant.open.api.domain.express.GetEbillOrderResponse;
import com.kuaishou.merchant.open.api.domain.express.KwaishopScmEbillBaseResponse;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;

import com.alibaba.fastjson.JSON;
import com.kuaishou.merchant.open.api.domain.express.SubscribeDTO;
import com.kuaishou.merchant.open.api.request.express.OpenExpressEbillCancelRequest;
import com.kuaishou.merchant.open.api.request.express.OpenExpressEbillGetRequest;
import com.kuaishou.merchant.open.api.request.express.OpenExpressSubscribeQueryRequest;
import com.kuaishou.merchant.open.api.response.express.OpenExpressEbillCancelResponse;
import com.kuaishou.merchant.open.api.response.express.OpenExpressEbillGetResponse;
import com.kuaishou.merchant.open.api.response.express.OpenExpressSubscribeQueryResponse;

import cn.loveapp.common.constant.CommonPlatformConstants;
import cn.loveapp.common.platformsdk.kwaishop.KwaishopSDKService;
import cn.loveapp.common.utils.LoggerHelper;
import cn.loveapp.print.service.api.entity.AyWaybillApplySubscriptionInfo;
import cn.loveapp.print.service.api.entity.WaybillCloudPrintInfo;
import cn.loveapp.print.service.api.request.AyWaybillCancelRequest;
import cn.loveapp.print.service.api.request.AyWaybillGetRequest;
import cn.loveapp.print.service.api.request.AyWaybillSearchRequest;
import cn.loveapp.print.service.api.response.AyWaybillCancelResponse;
import cn.loveapp.print.service.api.response.AyWaybillGetResponse;
import cn.loveapp.print.service.api.response.AyWaybillSearchResponse;
import cn.loveapp.print.service.code.ErrorCode;
import cn.loveapp.print.service.platform.api.PlatformWaybillApiService;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2022-08-29 10:54
 * @Description: 电子面单api service 快手实现类
 */
@Service
public class KwaishopPlatformWaybillApiServiceImpl implements PlatformWaybillApiService {

    public static final LoggerHelper LOGGER = LoggerHelper.getLogger(KwaishopPlatformWaybillApiServiceImpl.class);

    /**
     * 电子面单取消成功返回确认字段
     */
    public static final Integer CANCEL_SUCCESS_RESULT = 1;

    @Autowired
    private KwaishopSDKService kwaishopSDKService;

    @Override
    public AyWaybillSearchResponse waybillSearch(AyWaybillSearchRequest request, String topSession, String platformId,
        String appName) {
        OpenExpressSubscribeQueryRequest openExpressSubscribeQueryRequest = new OpenExpressSubscribeQueryRequest();
        openExpressSubscribeQueryRequest.setAccessToken(topSession);
        openExpressSubscribeQueryRequest.setExpressCompanyCode(StringUtils.trimToEmpty(request.getCpCode()));
        OpenExpressSubscribeQueryResponse openExpressSubscribeQueryResponse =
            kwaishopSDKService.execute(openExpressSubscribeQueryRequest, appName);
        AyWaybillSearchResponse response = new AyWaybillSearchResponse();
        response.init(openExpressSubscribeQueryResponse);
        if (!response.isSuccess()) {
            return response;
        }

        if (CollectionUtils.isNotEmpty(openExpressSubscribeQueryResponse.getData())) {
            Map<String, List<SubscribeDTO>> dataGroupByCompany =
                openExpressSubscribeQueryResponse.getData().stream().collect(
                    Collectors.groupingBy(item -> item.getExpressCompanyCode() + ":" + item.getExpressCompanyType()));
            response.setWaybillApplySubscriptionCols(dataGroupByCompany
                .values().stream().map(item -> AyWaybillApplySubscriptionInfo.of(item,
                    item.get(0).getExpressCompanyCode(), item.get(0).getExpressCompanyType()))
                .collect(Collectors.toList()));
        } else {
            response.setWaybillApplySubscriptionCols(Collections.emptyList());
        }

        return response;
    }

    @Override
    public AyWaybillGetResponse waybillGet(AyWaybillGetRequest request, String topSession, String platformId,
        String appName) {
        AyWaybillGetResponse response = new AyWaybillGetResponse();
        OpenExpressEbillGetRequest openExpressEbillGetRequest = null;
        try {
            openExpressEbillGetRequest =
                JSON.parseObject(request.getParamWaybillCloudPrintApplyNewRequest(), OpenExpressEbillGetRequest.class);
        } catch (Exception e) {
            LOGGER.logError("非法的参数：paramWaybillCloudPrintApplyNewRequest", e);
            response.setErrorCode(ErrorCode.BaseCode.PARAMS_ERR.getCode().toString());
            response.setMsg(
                "非法的参数：paramWaybillCloudPrintApplyNewRequest = " + request.getParamWaybillCloudPrintApplyNewRequest());
            return response;
        }

        openExpressEbillGetRequest.setAccessToken(topSession);
        OpenExpressEbillGetResponse openExpressEbillGetResponse =
            kwaishopSDKService.execute(openExpressEbillGetRequest, appName);

        response.init(openExpressEbillGetResponse);
        if (!response.isSuccess()) {
            return response;
        }

        if (CollectionUtils.isEmpty(openExpressEbillGetResponse.getData())) {
            response.setErrorCode(String.valueOf(openExpressEbillGetResponse.getResult()));
            response.setMsg(openExpressEbillGetResponse.getErrorMsg());
            return response;
        }
        List<WaybillCloudPrintInfo> waybillCloudPrintInfoList = new ArrayList<>();
        List<GetEbillOrderResponse> responseData = openExpressEbillGetResponse.getData();
        for (GetEbillOrderResponse orderResponse : responseData) {
            if (CollectionUtils.isEmpty(orderResponse.getData()) && orderResponse.getBaseResponse() != null) {
                KwaishopScmEbillBaseResponse baseResponse = orderResponse.getBaseResponse();
                response.setErrorCode(String.valueOf(baseResponse.getResult()));
                response.setMsg(baseResponse.getMessage());
                response.setSubMsg(orderResponse.getRequestId());
                return response;
            }
            List<WaybillCloudPrintInfo> collect = orderResponse.getData().stream().map(WaybillCloudPrintInfo::of).collect(Collectors.toList());
            waybillCloudPrintInfoList.addAll(collect);
        }

        response.setWaybillCloudPrintInfoList(waybillCloudPrintInfoList);

        return response;
    }

    @Override
    public AyWaybillCancelResponse waybillCancel(AyWaybillCancelRequest request, String topSession, String platformId,
        String appName) {
        AyWaybillCancelResponse response = new AyWaybillCancelResponse();
        OpenExpressEbillCancelRequest openExpressEbillCancelRequest = new OpenExpressEbillCancelRequest();
        openExpressEbillCancelRequest.setAccessToken(topSession);
        openExpressEbillCancelRequest.setExpressCompanyCode(request.getCpCode());
        openExpressEbillCancelRequest.setWaybillCode(request.getWaybillCode());

        OpenExpressEbillCancelResponse openExpressEbillCancelResponse =
            kwaishopSDKService.execute(openExpressEbillCancelRequest, appName);

        response.init(openExpressEbillCancelResponse);
        if (!response.isSuccess()) {
            return response;
        }
        response.setCancelResult(
            CANCEL_SUCCESS_RESULT.equals(openExpressEbillCancelResponse.getResult()) ? Boolean.TRUE : Boolean.FALSE);
        return response;
    }

    @Override
    public String getPlatformId() {
        return CommonPlatformConstants.PLATFORM_KWAISHOP;
    }


}
