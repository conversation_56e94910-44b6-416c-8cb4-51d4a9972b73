package cn.loveapp.print.service.api.entity;



import com.doudian.api.domain.Netsite;
import com.google.common.collect.Lists;
import com.jd.open.api.sdk.domain.wujiemiandan.QueryContractApi.response.query.QueryContractResponse;
import com.jd.open.api.sdk.domain.wujiemiandan.QueryContractApi.response.query.ValueAddedServiceDTO;
import com.jd.open.api.sdk.domain.wujiemiandan.QueryContractApi.response.query.WaybillAddress;
import com.kuaishou.merchant.open.api.domain.express.AddressDTO;
import com.kuaishou.merchant.open.api.domain.express.SubscribeDTO;
import com.pdd.pop.sdk.http.api.pop.response.PddWaybillSearchResponse;
import com.taobao.api.response.CainiaoWaybillIiSearchResponse;
import com.wxvideoshop.api.response.WxvideoshopElectronicBillSubscribesQueryResponse;
import com.xiaohongshu.fls.opensdk.entity.express.response.ElectronicBillSubscribesQueryResponse;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 面单网点信息及对应的商家的发货信息 淘宝 https://open.taobao.com/api.htm?docId=27125&docType=2&source=search 拼多多
 * https://open.pinduoduo.com/application/document/api?id=pdd.waybill.search
 *
 * <AUTHOR>
 */
@Data
public class AyWaybillApplySubscriptionInfo implements Serializable {

    private static final long serialVersionUID = -8049295484617183564L;


    private static final String JD_SPECIAL_ADDRESS = "北京市大兴区亦庄经济开发区京麦京东物流电子面单自动开通";

    /**
     * 微信视频号, 物流服务商 业务类型暂仅支持 2:直营
     */
    private static final long ALLIANCE_BUSINESS = 2L;
    /**
     * 快递公司code
     */
    private String cpCode;

    /**
     * 物流服务商 业务类型 淘宝 1：直营 2：加盟 3：落地配 4：直营带网点 拼多多 1, "加盟型" 2, "直营且月结账号必填" 3, "直营且月结账号非必填" 4, "直营且无月结账号"
     */
    private Long cpType;

    /**
     * 面单网点账号信息
     */
    private List<WaybillBranchAccount> branchAccountCols;

    /**
     * 电子面单账号id，每绑定一个网点分配一个acctId
     */
    private String acctId;

    /**
     * 店铺id，全局唯一，一个店铺分配一个shop_id
     */
    private String shopId;

    /**
     * 电子面单版本号，1-默认值旧版电子面单 2-新版电子面单 (XHS使用)
     */
    private Integer billVersion;

    /**
     * 淘宝（菜鸟）的结构转换成爱用的结构
     *
     * @param subscriptionInfo
     * @return
     */
    public static AyWaybillApplySubscriptionInfo
        of(CainiaoWaybillIiSearchResponse.WaybillApplySubscriptionInfo subscriptionInfo) {
        AyWaybillApplySubscriptionInfo ayWaybillApplySubscriptionInfo = new AyWaybillApplySubscriptionInfo();
        ayWaybillApplySubscriptionInfo.setCpCode(subscriptionInfo.getCpCode());
        ayWaybillApplySubscriptionInfo.setCpType(subscriptionInfo.getCpType());
        ayWaybillApplySubscriptionInfo.setBranchAccountCols(subscriptionInfo.getBranchAccountCols().stream()
            .map(WaybillBranchAccount::of).collect(Collectors.toList()));
        return ayWaybillApplySubscriptionInfo;
    }

    /**
     * 拼多多的结构转换成爱用的结构
     *
     * @param subscriptionInfo
     * @return
     */
    public static AyWaybillApplySubscriptionInfo
        of(PddWaybillSearchResponse.InnerPddWaybillSearchResponseWaybillApplySubscriptionColsItem subscriptionInfo) {
        AyWaybillApplySubscriptionInfo ayWaybillApplySubscriptionInfo = new AyWaybillApplySubscriptionInfo();
        ayWaybillApplySubscriptionInfo.setCpCode(subscriptionInfo.getWpCode());
        ayWaybillApplySubscriptionInfo.setCpType(Long.valueOf(subscriptionInfo.getWpType()));
        ayWaybillApplySubscriptionInfo.setBranchAccountCols(subscriptionInfo.getBranchAccountCols().stream()
            .map(WaybillBranchAccount::of).collect(Collectors.toList()));
        return ayWaybillApplySubscriptionInfo;
    }

    /**
     * 抖店的结构转换成爱用的结构
     *
     * @param netsites
     * @param cpCode
     * @param cpType
     * @return
     */
    public static AyWaybillApplySubscriptionInfo of(List<Netsite> netsites, String cpCode, Long cpType) {
        AyWaybillApplySubscriptionInfo ayWaybillApplySubscriptionInfo = new AyWaybillApplySubscriptionInfo();
        ayWaybillApplySubscriptionInfo.setCpCode(cpCode);
        ayWaybillApplySubscriptionInfo.setCpType(cpType);
        ayWaybillApplySubscriptionInfo
            .setBranchAccountCols(netsites.stream().map(WaybillBranchAccount::of).collect(Collectors.toList()));
        return ayWaybillApplySubscriptionInfo;
    }

    /**
     * 快手的结构转换为爱用的结构
     *
     * @param subscribeDTOS
     * @param expressCompanyCode
     * @param expressCompanyType
     * @return
     */
    public static AyWaybillApplySubscriptionInfo of(List<SubscribeDTO> subscribeDTOS, String expressCompanyCode,
        Integer expressCompanyType) {
        AyWaybillApplySubscriptionInfo ayWaybillApplySubscriptionInfo = new AyWaybillApplySubscriptionInfo();
        ayWaybillApplySubscriptionInfo
            .setBranchAccountCols(subscribeDTOS.stream().map(WaybillBranchAccount::of).collect(Collectors.toList()));
        ayWaybillApplySubscriptionInfo.setCpCode(expressCompanyCode);
        ayWaybillApplySubscriptionInfo.setCpType(Long.valueOf(expressCompanyType));
        return ayWaybillApplySubscriptionInfo;
    }

    /**
     * 小红书的结构转换为爱用的结构
     * @param cpCode
     * @param cpType
     * @param subscribe
     * @return
     */
    public static AyWaybillApplySubscriptionInfo of(String cpCode, Integer cpType, Integer billVersion,
        List<ElectronicBillSubscribesQueryResponse.Subscribe> subscribe) {
        AyWaybillApplySubscriptionInfo ayWaybillApplySubscriptionInfo = new AyWaybillApplySubscriptionInfo();
        ayWaybillApplySubscriptionInfo.setCpCode(cpCode);
        ayWaybillApplySubscriptionInfo.setCpType(Long.valueOf(cpType));
        ayWaybillApplySubscriptionInfo.setBillVersion(billVersion);
        ayWaybillApplySubscriptionInfo
            .setBranchAccountCols(subscribe.stream().map(WaybillBranchAccount::of).collect(Collectors.toList()));
        return ayWaybillApplySubscriptionInfo;
    }

    public static AyWaybillApplySubscriptionInfo of(String shopId,String cpCode, Integer cpType, List<WxvideoshopElectronicBillSubscribesQueryResponse.AccountInfo> AccountInfo){
        AyWaybillApplySubscriptionInfo ayWaybillApplySubscriptionInfo = new AyWaybillApplySubscriptionInfo();
        ayWaybillApplySubscriptionInfo.setBranchAccountCols(AccountInfo.stream().map(WaybillBranchAccount::of).collect(Collectors.toList()));
        ayWaybillApplySubscriptionInfo.setCpCode(cpCode);
        ayWaybillApplySubscriptionInfo.setCpType(Long.valueOf(cpType));
        ayWaybillApplySubscriptionInfo.setShopId(shopId);
        return ayWaybillApplySubscriptionInfo;
    }

    public static AyWaybillApplySubscriptionInfo of(List<QueryContractResponse> item, QueryContractResponse firstItem) {
        AyWaybillApplySubscriptionInfo ayWaybillApplySubscriptionInfo = new AyWaybillApplySubscriptionInfo();
        ayWaybillApplySubscriptionInfo.setCpCode(firstItem.getProviderCode());
        ayWaybillApplySubscriptionInfo.setCpType(Long.valueOf(firstItem.getOperationType()));
        ayWaybillApplySubscriptionInfo.setAcctId(String.valueOf(firstItem.getProviderId()));
        ayWaybillApplySubscriptionInfo
                .setBranchAccountCols(item.stream().map(WaybillBranchAccount::of).collect(Collectors.toList()));
        return ayWaybillApplySubscriptionInfo;
    }

    /**
     * 面单网点账号信息
     */
    @Data
    public static class WaybillBranchAccount implements Serializable {
        private static final long serialVersionUID = 5139623202028524676L;
        /**
         * 已使用面单数量
         */
        private Long allocatedQuantity;

        /**
         * 网点Code
         */
        private String branchCode;

        /**
         * 网点名称
         */
        private String branchName;

        /**
         * 网点状态
         */
        private Long branchStatus;

        /**
         * 取消的面单总数
         */
        private Long cancelQuantity;

        /**
         * 已回收用面单数量
         */
        private Long recycledQuantity;

        /**
         * 打印的面单总数
         */
        private Long printQuantity;

        /**
         * 电子面单余额数量
         */
        private Long quantity;

        /**
         * 号段信息 （菜鸟面单专有字段）
         */
        private String segmentCode;

        /**
         * 可用服务信息列表
         */
        private List<ServiceInfoDto> serviceInfoCols;

        /**
         * 当前网点下的发货地址
         */
        private List<AddressDto> shippAddressCols;

        /**
         * 品牌code （菜鸟面单专有字段）
         */
        private String brandCode;

        /**
         * 月结卡号列表 （菜鸟面单专有字段）
         */
        private List<String> customerCodeList;

        /**
         * 客户编码（直营物流公司的月结卡号,快手电子面单专有字段）
         */
        private String settleAccount;

        /**
         * 电子面单账号id，每绑定一个网点分配一个acct_id(微信视频号专有字段)
         */
        private String acctId;

        /**
         * 店铺id，全局唯一，一个店铺分配一个shop_id(微信视频号专有字段)
         */
        private String shopId;

        /**
         * 是否支持货到付款（京东字段）
         */
        private Boolean supportCod;

        public String getSegmentCode() {
            return StringUtils.trimToEmpty(segmentCode);
        }

        public static WaybillBranchAccount
            of(CainiaoWaybillIiSearchResponse.WaybillBranchAccount cainiaoWaybillBranchAccount) {
            WaybillBranchAccount waybillBranchAccount = new WaybillBranchAccount();
            BeanUtils.copyProperties(cainiaoWaybillBranchAccount, waybillBranchAccount);
            if (CollectionUtils.isNotEmpty(cainiaoWaybillBranchAccount.getServiceInfoCols())) {
                waybillBranchAccount.setServiceInfoCols(cainiaoWaybillBranchAccount.getServiceInfoCols().stream()
                    .map(ServiceInfoDto::of).collect(Collectors.toList()));
            }
            if (CollectionUtils.isNotEmpty(cainiaoWaybillBranchAccount.getShippAddressCols())) {
                waybillBranchAccount.setShippAddressCols(cainiaoWaybillBranchAccount.getShippAddressCols().stream()
                    .map(AddressDto::of).collect(Collectors.toList()));
            }
            return waybillBranchAccount;
        }

        public static WaybillBranchAccount of(
            PddWaybillSearchResponse.InnerPddWaybillSearchResponseWaybillApplySubscriptionColsItemBranchAccountColsItem pddWaybillBranchAccount) {
            WaybillBranchAccount waybillBranchAccount = new WaybillBranchAccount();
            BeanUtils.copyProperties(pddWaybillBranchAccount, waybillBranchAccount);
            if (CollectionUtils.isNotEmpty(pddWaybillBranchAccount.getServiceInfoCols())) {
                waybillBranchAccount.setServiceInfoCols(pddWaybillBranchAccount.getServiceInfoCols().stream()
                    .map(ServiceInfoDto::of).collect(Collectors.toList()));
            }
            if (CollectionUtils.isNotEmpty(pddWaybillBranchAccount.getShippAddressCols())) {
                waybillBranchAccount.setShippAddressCols(pddWaybillBranchAccount.getShippAddressCols().stream()
                    .map(AddressDto::of).collect(Collectors.toList()));
            }
            return waybillBranchAccount;
        }

        public static WaybillBranchAccount of(Netsite netsite) {
            WaybillBranchAccount waybillBranchAccount = new WaybillBranchAccount();
            waybillBranchAccount.setAllocatedQuantity(netsite.getAllocatedQuantity());
            waybillBranchAccount.setBranchCode(netsite.getNetsiteCode());
            waybillBranchAccount.setBranchName(netsite.getNetsiteName());
            waybillBranchAccount.setCancelQuantity(netsite.getCancelledQuantity());
            waybillBranchAccount.setRecycledQuantity(netsite.getRecycledQuantity());
            waybillBranchAccount.setQuantity(netsite.getAmount());
            if (CollectionUtils.isNotEmpty(netsite.getSenderAddress())) {
                waybillBranchAccount.setShippAddressCols(
                    netsite.getSenderAddress().stream().map(AddressDto::of).collect(Collectors.toList()));
            }
            return waybillBranchAccount;
        }

        /**
         * 快手
         *
         * @param subscribeDTO
         * @return
         */
        public static WaybillBranchAccount of(SubscribeDTO subscribeDTO) {
            WaybillBranchAccount waybillBranchAccount = new WaybillBranchAccount();
            waybillBranchAccount.setAllocatedQuantity(subscribeDTO.getAllocatedQuantity());
            waybillBranchAccount.setBranchCode(subscribeDTO.getNetSiteCode());
            waybillBranchAccount.setBranchName(subscribeDTO.getNetSiteName());
            waybillBranchAccount.setCancelQuantity(subscribeDTO.getCancelQuantity());
            waybillBranchAccount.setRecycledQuantity(subscribeDTO.getRecycledQuantity());
            waybillBranchAccount.setQuantity(subscribeDTO.getAvailableQuantity());
            waybillBranchAccount.setSettleAccount(subscribeDTO.getSettleAccount());
            if (CollectionUtils.isNotEmpty(subscribeDTO.getSenderAddress())) {
                waybillBranchAccount.setShippAddressCols(
                    subscribeDTO.getSenderAddress().stream().map(AddressDto::of).collect(Collectors.toList()));
            }
            return waybillBranchAccount;
        }

        /**
         * 小红书
         * @param subscribe
         * @return
         */
        public static WaybillBranchAccount of(ElectronicBillSubscribesQueryResponse.Subscribe subscribe) {
            WaybillBranchAccount waybillBranchAccount = new WaybillBranchAccount();
            ElectronicBillSubscribesQueryResponse.Usage usage = subscribe.getUsage();
            waybillBranchAccount.setAllocatedQuantity(Long.valueOf(usage.getAllocatedQuantity()));
            waybillBranchAccount.setBranchCode(subscribe.getBranchCode());
            waybillBranchAccount.setBranchName(subscribe.getBranchName());
            waybillBranchAccount.setCancelQuantity(Long.valueOf(usage.getCancelQuantity()));
            waybillBranchAccount.setRecycledQuantity(Long.valueOf(usage.getRecycledQuantity()));
            waybillBranchAccount.setQuantity(Long.valueOf(usage.getQuantity()));
            if (CollectionUtils.isNotEmpty(subscribe.getSenderAddressList())) {
                waybillBranchAccount.setShippAddressCols(
                        subscribe.getSenderAddressList().stream().map(AddressDto::of).collect(Collectors.toList()));
            }
            if (StringUtils.isNotEmpty(subscribe.getCustomerCode())) {
                waybillBranchAccount.setCustomerCodeList(Lists.newArrayList(subscribe.getCustomerCode()));
            }
            return waybillBranchAccount;
        }

        /**
         * 微信视频号
         * @param accountInfo
         * @return
         */
        public static WaybillBranchAccount of(WxvideoshopElectronicBillSubscribesQueryResponse.AccountInfo accountInfo){
            WaybillBranchAccount waybillBranchAccount = new WaybillBranchAccount();
            waybillBranchAccount.setAllocatedQuantity(accountInfo.getAllocated());
            waybillBranchAccount.setBranchStatus((long) accountInfo.getStatus().getCode());
            waybillBranchAccount.setCancelQuantity(accountInfo.getCancel());
            waybillBranchAccount.setRecycledQuantity(accountInfo.getRecycled());
            waybillBranchAccount.setQuantity(accountInfo.getAvailable());
            WxvideoshopElectronicBillSubscribesQueryResponse.SiteInfo siteInfo = accountInfo.getSiteInfo();
            if (Objects.nonNull(siteInfo)) {
                waybillBranchAccount.setBranchName(siteInfo.getSiteName());
                waybillBranchAccount.setBranchCode(siteInfo.getSiteCode());
            }
            waybillBranchAccount.setShippAddressCols(Collections.singletonList(AddressDto.of(accountInfo.getSenderAddress())));
            waybillBranchAccount.setShopId(accountInfo.getShopId());
            waybillBranchAccount.setAcctId(accountInfo.getAcctId());
            return waybillBranchAccount;
        }

        public static WaybillBranchAccount of(QueryContractResponse queryContractResponse) {
            WaybillBranchAccount waybillBranchAccount = new WaybillBranchAccount();
            waybillBranchAccount.setQuantity((long)queryContractResponse.getAmount());
            waybillBranchAccount.setBranchCode(queryContractResponse.getBranchCode());
            waybillBranchAccount.setBranchName(queryContractResponse.getBranchName());
            waybillBranchAccount.setServiceInfoCols(queryContractResponse.getValueAddedServices().stream()
                    .map(ServiceInfoDto::of).collect(Collectors.toList()));
            waybillBranchAccount.setShippAddressCols(Lists.newArrayList(AddressDto.of(queryContractResponse.getAddress())));
            waybillBranchAccount.setSettleAccount(queryContractResponse.getSettlementCode());
            waybillBranchAccount.setSupportCod(queryContractResponse.getSupportCod());
            return waybillBranchAccount;
        }
    }

    /**
     * 网点服务信息
     */
    @ApiModel
    @Data
    public static class ServiceInfoDto implements Serializable, Cloneable {

        private static final long serialVersionUID = -5103360340961502982L;
        /**
         * 改服务是否为必选服务
         */
        @ApiModelProperty("改服务是否为必选服务")
        private Boolean required;

        /**
         * 服务编码
         */
        @ApiModelProperty("服务编码")
        private String serviceCode;

        /**
         * 服务的官方描述
         */
        @ApiModelProperty("服务的官方描述")
        private String serviceDesc;

        /**
         * 服务名称
         */
        @ApiModelProperty("服务名称")
        private String serviceName;

        /**
         * 服务属性定义
         */
        @ApiModelProperty("服务属性定义")
        private List<ServiceAttributeDto> serviceAttributes;

        @Override
        public ServiceInfoDto clone() {
            try {
                ServiceInfoDto clone = (ServiceInfoDto)super.clone();
                if (this.serviceAttributes != null) {
                    clone.setServiceAttributes(
                        this.serviceAttributes.stream().map(ServiceAttributeDto::clone).collect(Collectors.toList()));
                }
                return clone;
            } catch (Exception ignore) {
            }
            return null;
        }

        public static ServiceInfoDto of(CainiaoWaybillIiSearchResponse.ServiceInfoDto cainiaoServiceInfo) {
            ServiceInfoDto serviceInfo = new ServiceInfoDto();
            BeanUtils.copyProperties(cainiaoServiceInfo, serviceInfo);
            if (CollectionUtils.isNotEmpty(cainiaoServiceInfo.getServiceAttributes())) {
                serviceInfo.setServiceAttributes(cainiaoServiceInfo.getServiceAttributes().stream()
                    .map(ServiceAttributeDto::of).collect(Collectors.toList()));
            }
            return serviceInfo;
        }

        public static ServiceInfoDto of(
            PddWaybillSearchResponse.InnerPddWaybillSearchResponseWaybillApplySubscriptionColsItemBranchAccountColsItemServiceInfoColsItem pddServiceInfo) {
            ServiceInfoDto serviceInfo = new ServiceInfoDto();
            BeanUtils.copyProperties(pddServiceInfo, serviceInfo);
            if (CollectionUtils.isNotEmpty(pddServiceInfo.getServiceAttributes())) {
                serviceInfo.setServiceAttributes(pddServiceInfo.getServiceAttributes().stream()
                    .map(ServiceAttributeDto::of).collect(Collectors.toList()));
            }
            return serviceInfo;
        }

        public static ServiceInfoDto of(ValueAddedServiceDTO valueAddedServiceDTO) {
            ServiceInfoDto serviceInfo = new ServiceInfoDto();
            serviceInfo.setServiceCode(valueAddedServiceDTO.getServiceCode());
            serviceInfo.setServiceName(valueAddedServiceDTO.getServiceName());
            serviceInfo.setServiceDesc(valueAddedServiceDTO.getServiceName());
            return serviceInfo;
        }
    }

    /**
     * 网点服务属性定义
     */
    @ApiModel
    @Data
    public static class ServiceAttributeDto implements Serializable, Cloneable {
        private static final long serialVersionUID = 4183191084957221178L;
        /**
         * 属性的值，用户实际传入的值
         */
        @ApiModelProperty("属性的值，用户实际传入的值")
        private String attributeCode;

        /**
         * 属性的名称，可以用于前端的展示
         */
        @ApiModelProperty("属性的名称，可以用于前端的展示")
        private String attributeName;

        /**
         * 属性的类型
         */
        @ApiModelProperty("属性的类型")
        private String attributeType;

        /**
         * 枚举类型的枚举值
         */
        @ApiModelProperty("枚举类型的枚举值")
        private String typeDesc;

        @Override
        public ServiceAttributeDto clone() {
            try {
                return (ServiceAttributeDto)super.clone();
            } catch (Exception ignore) {
            }
            return null;
        }

        public static ServiceAttributeDto
            of(CainiaoWaybillIiSearchResponse.ServiceAttributeDto cainiaoServiceAttribute) {
            ServiceAttributeDto serviceAttribute = new ServiceAttributeDto();
            BeanUtils.copyProperties(cainiaoServiceAttribute, serviceAttribute);
            return serviceAttribute;
        }

        public static ServiceAttributeDto of(
            PddWaybillSearchResponse.InnerPddWaybillSearchResponseWaybillApplySubscriptionColsItemBranchAccountColsItemServiceInfoColsItemServiceAttributesItem pddServiceAttribute) {
            ServiceAttributeDto serviceAttribute = new ServiceAttributeDto();
            BeanUtils.copyProperties(pddServiceAttribute, serviceAttribute);
            return serviceAttribute;
        }
    }

    /**
     * 网点地址
     */
    @ApiModel
    @Data
    public static class AddressDto implements Serializable {
        private static final long serialVersionUID = 1793799503434082320L;
        /**
         * 市名称
         */
        @ApiModelProperty("市名称")
        private String city;

        /**
         * 详细地址
         */
        @ApiModelProperty("详细地址")
        private String detail;

        /**
         * 区名称
         */
        @ApiModelProperty("区名称")
        private String district;

        /**
         * 省名称
         */
        @ApiModelProperty("省名称")
        private String province;

        /**
         * 街道/镇名称
         */
        @ApiModelProperty("街道/镇名称")
        private String town;

        /**
         * 市id
         */
        @ApiModelProperty("市id")
        private String cityId;

        /**
         * 区id
         */
        @ApiModelProperty("区id")
        private String districtId;

        /**
         * 省id
         */
        @ApiModelProperty("省id")
        private String provinceId;

        /**
         * 街道/镇id
         */
        @ApiModelProperty("街道/镇id")
        private String townId;


        /**
         * 手机号
         */
        @ApiModelProperty("手机号")
        private String mobile;

        /**
         * 姓名
         */
        @ApiModelProperty("姓名")
        private String name;

        /**
         * 电话号码
         */
        @ApiModelProperty("电话号码")
        private String phone;

        public static AddressDto of(CainiaoWaybillIiSearchResponse.AddressDto cainiaoAddress) {
            AddressDto address = new AddressDto();
            BeanUtils.copyProperties(cainiaoAddress, address);
            return address;
        }

        public static AddressDto of(
            PddWaybillSearchResponse.InnerPddWaybillSearchResponseWaybillApplySubscriptionColsItemBranchAccountColsItemShippAddressColsItem pddAddress) {
            AddressDto address = new AddressDto();
            BeanUtils.copyProperties(pddAddress, address);
            return address;
        }

        public static AddressDto of(com.doudian.api.domain.Address address) {
            AddressDto addressDto = new AddressDto();
            addressDto.setProvince(address.getProvinceName());
            addressDto.setCity(address.getCityName());
            addressDto.setDistrict(address.getDistrictName());
            addressDto.setTown(address.getStreetName());
            addressDto.setDetail(address.getDetailAddress());
            return addressDto;
        }

        /**
         * 快手地址格式转换为爱用地址格式
         *
         * @param address
         * @return
         */
        public static AddressDto of(AddressDTO address) {
            AddressDto addressDto = new AddressDto();
            addressDto.setProvince(address.getProvinceName());
            addressDto.setCity(address.getCityName());
            addressDto.setDistrict(address.getDistrictName());
            addressDto.setTown(address.getStreetName());
            addressDto.setDetail(address.getDetailAddress());
            return addressDto;
        }

        /**
         * 小红书地址转换爱用地址格式
         * @param senderAddress
         * @return
         */
        public static AddressDto of(ElectronicBillSubscribesQueryResponse.SenderAddress senderAddress) {
            AddressDto addressDto = new AddressDto();
            ElectronicBillSubscribesQueryResponse.Address address = senderAddress.getAddress();
            addressDto.setProvince(address.getProvince());
            addressDto.setCity(address.getCity());
            addressDto.setDistrict(address.getDistrict());
            addressDto.setTown(address.getTown());
            addressDto.setDetail(address.getDetail());
            addressDto.setPhone(senderAddress.getPhone());
            addressDto.setName(senderAddress.getName());
            addressDto.setMobile(senderAddress.getMobile());
            return addressDto;
        }

        /**
         * 微信视频号地址转换爱用地址格式
         * @param senderAddress
         * @return
         */
        public static AddressDto of(WxvideoshopElectronicBillSubscribesQueryResponse.SenderAddress senderAddress){
            AddressDto addressDto = new AddressDto();
            addressDto.setProvince(senderAddress.getProvince());
            addressDto.setCity(senderAddress.getCity());
            addressDto.setDistrict(senderAddress.getCounty());
            addressDto.setTown(senderAddress.getStreet());
            addressDto.setDetail(senderAddress.getAddress());
            return addressDto;
        }

        public static AddressDto of(WaybillAddress address) {
            if (JD_SPECIAL_ADDRESS.equals(address.getAddress()) && StringUtils.isAllEmpty(address.getCityName(), address.getProvinceName())) {
                // 京东特殊地址，未返回省市区特殊处理
                // "北京市大兴区亦庄经济开发区京麦京东物流电子面单自动开通"
                AddressDto addressDto = new AddressDto();
                addressDto.setProvince("北京市");
                addressDto.setCity("北京市");
                addressDto.setDistrict("大兴区");
                addressDto.setTown("亦庄镇");
                addressDto.setDetail(address.getAddress());
                return addressDto;
            }

            AddressDto addressDto = new AddressDto();
            addressDto.setProvince(address.getProvinceName());
            addressDto.setProvinceId(String.valueOf(address.getProvinceId()));
            addressDto.setCity(address.getCityName());
            addressDto.setCityId(String.valueOf(address.getCityId()));
            addressDto.setDistrict(address.getCountryName());
            addressDto.setDistrictId(String.valueOf(address.getCountryId()));
            addressDto.setTown(address.getCountrysideName());
            addressDto.setTownId(String.valueOf(address.getCountrysideId()));
            addressDto.setDetail(address.getAddress());
            return addressDto;
        }
    }
}
