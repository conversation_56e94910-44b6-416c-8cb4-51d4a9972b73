package cn.loveapp.print.service.request;

import cn.loveapp.print.service.dto.DistributeOrderInfoDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 分销代发面单打印请求request
 *
 * <AUTHOR>
 * @Date 2023/11/6 3:27 PM
 */
@Data
@ApiModel
public class DistributeElefacePrintRequest extends ElefacePrintRequest {

    /**
     * 分销单信息
     */
    @ApiModelProperty("分销单信息")
    private List<DistributeOrderInfoDTO> distributeOrderInfoList;
}
