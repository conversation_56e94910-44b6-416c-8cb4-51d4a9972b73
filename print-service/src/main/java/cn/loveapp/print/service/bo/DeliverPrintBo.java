package cn.loveapp.print.service.bo;

import cn.loveapp.print.service.request.DeliverPrintRequest;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class DeliverPrintBo extends BasePrintBo {
    /**
     * 打印模板
     */
    private String printModule;
    /**
     * 打印类容
     */
    private String printData;

    public static DeliverPrintBo of(DeliverPrintRequest request) {
        DeliverPrintBo deliverPrintBo = new DeliverPrintBo();
        deliverPrintBo.setBasePrintInfo(request);
        deliverPrintBo.setRecipient(request.getRecipient());
        deliverPrintBo.setPrintModule(request.getPrintModule());
        deliverPrintBo.setPrintData(request.getPrintData());
        deliverPrintBo.setOperatorName(request.getOperatorName());
        deliverPrintBo.setSender(request.getSender());
        return deliverPrintBo;
    }
}
