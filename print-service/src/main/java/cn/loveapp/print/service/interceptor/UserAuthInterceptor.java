package cn.loveapp.print.service.interceptor;

import cn.loveapp.common.web.CommonApiResponse;
import cn.loveapp.print.common.dto.UserMultiInfoDTO;
import cn.loveapp.print.common.exception.TargetUserNotExistException;
import cn.loveapp.print.service.annotation.UserAuth;
import cn.loveapp.print.service.service.UserAuthService;
import com.alibaba.fastjson.JSON;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.MethodParameter;
import org.springframework.web.bind.support.WebDataBinderFactory;
import org.springframework.web.context.request.NativeWebRequest;
import org.springframework.web.method.HandlerMethod;
import org.springframework.web.method.support.HandlerMethodArgumentResolver;
import org.springframework.web.method.support.ModelAndViewContainer;
import org.springframework.web.servlet.handler.HandlerInterceptorAdapter;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * 用户鉴权拦截器
 *
 * <AUTHOR>
 * @date 2020-02-08 16:07:13
 */
public class UserAuthInterceptor extends HandlerInterceptorAdapter implements HandlerMethodArgumentResolver {
    public static final String COMMON_ATTRIBUTE_TARGETUSERINFO = "COMMON_ATTRIBUTE_TARGETUSERINFOS";

    @Autowired
    private UserAuthService userAuthService;

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler)
        throws Exception {
        MDC.remove("sellerNick");
        if (request.getAttribute(COMMON_ATTRIBUTE_TARGETUSERINFO) == null) {
            if (handler instanceof HandlerMethod) {
                HandlerMethod handlerMethod = (HandlerMethod)handler;
                if (!handlerMethod.hasMethodAnnotation(UserAuth.class)) {
                    return true;
                }
                try {
                    UserMultiInfoDTO userMultiInfoDTO = userAuthService.auth(request);
                    if (userMultiInfoDTO.getNick() != null) {
                        MDC.put("sellerNick", userMultiInfoDTO.getNick());
                    }
                    request.setAttribute(COMMON_ATTRIBUTE_TARGETUSERINFO, userMultiInfoDTO);
                } catch (TargetUserNotExistException e) {
                    // 多店鉴权失败
                    CommonApiResponse errorResponse = CommonApiResponse.of(403, e.getMessage());
                    response.setContentType("application/json; charset=utf-8");
                    response.getWriter().append(JSON.toJSONString(errorResponse)).flush();
                    return false;
                }
            }
        }
        return true;
    }

    @Override
    public boolean supportsParameter(MethodParameter methodParameter) {
        return methodParameter.getParameterType().isAssignableFrom(UserMultiInfoDTO.class);
    }

    @Override
    public Object resolveArgument(MethodParameter methodParameter, ModelAndViewContainer modelAndViewContainer,
        NativeWebRequest nativeWebRequest, WebDataBinderFactory webDataBinderFactory) throws Exception {
        return (UserMultiInfoDTO)nativeWebRequest.getAttribute(COMMON_ATTRIBUTE_TARGETUSERINFO, 0);
    }
}
