package cn.loveapp.print.service.request;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;

import javax.validation.constraints.NotNull;

import cn.loveapp.common.utils.DateUtil;
import cn.loveapp.print.service.annotation.TradeTypeValidation;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

/**
 * <AUTHOR>
 */
@Data
@ApiModel(value = "电子面单操作日志获取请求体")
public class WaybillOperatelogListGetRequest {
    private static final DateTimeFormatter DF = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    /**
     * 页
     */
    @ApiModelProperty(value = "页", required = true)
    @NotNull
    private Integer page = 1;

    /**
     * 每页数量
     */
    @ApiModelProperty(value = "每页数量", required = true)
    @NotNull
    private Integer pageSize = 20;

    /**
     * 订单类型
     */
    @ApiModelProperty(value = "订单类型")
    @TradeTypeValidation
    private Integer tradeType;

    /**
     * 面单是否被取消
     */
    @ApiModelProperty(value = "面单是否被取消")
    private Boolean isCancel;

    /**
     * 面单服务商
     */
    @ApiModelProperty(value = "面单服务商")
    private String provider;

    /**
     * 物流公司编码
     */
    @ApiModelProperty(value = "物流公司编码")
    private String cpCode;

    /**
     * 物流公司编码
     */
    @ApiModelProperty(value = "物流公司编码(多个)")
    private List<String> cpCodeList;

    /**
     * 订单号
     */
    @ApiModelProperty(value = "订单号")
    private String tid;

    /**
     * 订单号列表
     */
    @ApiModelProperty(value = "订单号列表")
    private List<String> tidList;

    /**
     * 运单号
     */
    @ApiModelProperty(value = "运单号")
    private String waybillCode;

    /**
     * 运单号列表
     */
    @ApiModelProperty(value = "运单号列表")
    private List<String> waybillCodeList;

    /**
     * 开始时间
     */
    @ApiModelProperty(value = "开始时间")
    private LocalDateTime startTime;

    /**
     * 结束时间
     */
    @ApiModelProperty(value = "结束时间")
    private LocalDateTime endTime;

    /**
     * 面单取消开始时间
     */
    @ApiModelProperty(value = "面单取消开始时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime cancelStartTime;

    /**
     * 面单取消结束时间
     */
    @ApiModelProperty(value = "面单取消结束时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime cancelEndTime;

    /**
     * 面单所有者商家id
     */
    @ApiModelProperty(value = "面单所有者商家id")
    private String ownerSellerId;

    /**
     * 面单所有者Nick
     */
    @ApiModelProperty(value = "面单所有者Nick")
    private String ownerSellerNick;

    /**
     * 面单所有者 targetId
     */
    @ApiModelProperty(value = "面单所有者 targetId")
    private String ownerStoreId;

    /**
     * 面单所有者 appName
     */
    @ApiModelProperty(value = "面单所有者 appName")
    private String ownerAppName;

    /**
     * 电子面单打印数量
     */
    @ApiModelProperty(value = "电子面单打印数量")
    private Integer printCount;

    public void setStartTime(String startTime) {
        this.startTime = DateUtil.parseString(startTime);
    }

    public void setEndTime(String endTime) {
        this.endTime = DateUtil.parseString(endTime);
    }

}
