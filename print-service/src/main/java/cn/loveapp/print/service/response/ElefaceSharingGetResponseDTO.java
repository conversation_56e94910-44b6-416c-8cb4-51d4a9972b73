package cn.loveapp.print.service.response;

import cn.loveapp.print.service.entity.ElefaceSharingRelation;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @program: print-services-group
 * @description: 共享面单关系查询response
 * @author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @create: 2022/12/20 10:43
 **/
@Data
@ApiModel("共享面单关系查询响应体")
public class ElefaceSharingGetResponseDTO {

    /**
     * 面单共享关系
     */
    @ApiModelProperty(value = "面单共享关系")
    private ElefaceSharingRelation sharingRelation;

}
