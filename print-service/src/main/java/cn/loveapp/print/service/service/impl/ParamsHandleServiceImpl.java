package cn.loveapp.print.service.service.impl;

import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.Objects;

import cn.loveapp.common.constant.CommonAppConstants;
import cn.loveapp.print.common.dto.WaybillOperatelogQueryDTO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import cn.loveapp.common.dto.UserSessionInfo;
import cn.loveapp.print.service.config.PrintLogConfig;
import cn.loveapp.print.service.dto.LogisticsPrintlogQueryDTO;
import cn.loveapp.print.service.service.ParamsHandleService;

/**
 * <AUTHOR>
 * @date 2024-09-18 14:30
 * @description: 参数校验处理服务接口实现类
 */
@Service
public class ParamsHandleServiceImpl implements ParamsHandleService {

    @Autowired
    private PrintLogConfig printLogConfig;

    @Override
    public void handlePrintLogSearchListParams(LogisticsPrintlogQueryDTO queryDTO, UserSessionInfo sessionInfo) {
        LocalDateTime now = LocalDateTime.now();
        boolean isProfessionalVip = printLogConfig.getProfessionalVipList().contains(sessionInfo.getVipflag());
        if (Objects.equals(sessionInfo.getAppName(), CommonAppConstants.APP_TRADE_ERP)) {
            isProfessionalVip = true;
        }

        LocalDateTime defaultDatsSearchTime = now.minusDays(printLogConfig.getPrintLogListSearchDefaultDays());

        boolean isStartTimeWithNull = false;
        if (queryDTO.getStartTime() == null) {
            queryDTO.setStartTime(defaultDatsSearchTime);
            isStartTimeWithNull = true;
        }

        if (isProfessionalVip) {
            // 专业版校验最大查询时间
            LocalDateTime professionalVipMaxSearchTime =
                now.minusDays(printLogConfig.getPrintLogListProfessionalVipSearchMaxDays());
            if (!isStartTimeWithNull && queryDTO.getStartTime().isBefore(professionalVipMaxSearchTime)) {
                queryDTO.setStartTime(professionalVipMaxSearchTime);
            }
        } else {
            // 非专业版校验最大查询时间
            if (!isStartTimeWithNull && queryDTO.getStartTime().isBefore(defaultDatsSearchTime)) {
                queryDTO.setStartTime(defaultDatsSearchTime);
            }

            // 防止前端没有限制, 将专业版查询条件置null
            queryDTO.setPrinterName(null);
            queryDTO.setOperatorName(null);
            queryDTO.setIsNeedStatistics(false);
        }
    }

    @Override
    public void handleWaybillOperateLogLisParams(WaybillOperatelogQueryDTO queryDTO, UserSessionInfo sessionInfo) {
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime defaultDatsSearchTime = now.minusDays(printLogConfig.getPrintLogListSearchDefaultDays());

        boolean isProfessionalVip = printLogConfig.getProfessionalVipList().contains(sessionInfo.getVipflag());
        if (Objects.equals(sessionInfo.getAppName(), CommonAppConstants.APP_TRADE_ERP)) {
            isProfessionalVip = true;
        }

        boolean isStartTimeWithNull = false;
        if (queryDTO.getStartTime() == null) {
            queryDTO.setStartTime(defaultDatsSearchTime);
            isStartTimeWithNull = true;
        }

        if (queryDTO.getEndTime() == null) {
            queryDTO.setEndTime(now.plusDays(1).with(LocalTime.MIDNIGHT));
        }

        if (isProfessionalVip) {
            // 专业版校验最大查询时间
            LocalDateTime professionalVipMaxSearchTime =
                now.minusDays(printLogConfig.getPrintLogListProfessionalVipSearchMaxDays());
            if (!isStartTimeWithNull && queryDTO.getStartTime().isBefore(professionalVipMaxSearchTime)) {
                queryDTO.setStartTime(professionalVipMaxSearchTime);
            }
        } else {
            if (!isStartTimeWithNull && queryDTO.getStartTime().isBefore(defaultDatsSearchTime)) {
                queryDTO.setStartTime(defaultDatsSearchTime);
            }
        }
    }
}
