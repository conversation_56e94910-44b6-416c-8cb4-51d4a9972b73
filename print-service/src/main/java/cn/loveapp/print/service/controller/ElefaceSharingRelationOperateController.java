package cn.loveapp.print.service.controller;

import cn.loveapp.common.constant.HttpMethodsConstants;
import cn.loveapp.common.dto.UserSessionInfo;
import cn.loveapp.common.user.session.annotation.CheckUserSession;
import cn.loveapp.common.web.CommonApiResponse;
import cn.loveapp.common.web.CommonApiStatus;
import cn.loveapp.print.common.exception.CommonException;
import cn.loveapp.print.service.annotation.ShopsAuth;
import cn.loveapp.print.service.bo.ElefaceSharingRelationQueryBo;
import cn.loveapp.print.service.bo.UserInfoBo;
import cn.loveapp.print.service.convert.CommonConvertMapper;
import cn.loveapp.print.service.dto.ElefaceSharingRelationOperateDTO;
import cn.loveapp.print.service.dto.TargetUserInfoDTO;
import cn.loveapp.print.service.request.ElefaceSharingRelationOperateQueryRequest;
import cn.loveapp.print.service.response.ElefaceSharingRelationOperateListResponse;
import cn.loveapp.print.service.service.ElefaceSharingRelationOperateService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;
import springfox.documentation.annotations.ApiIgnore;

import java.util.List;
import java.util.Objects;

/**
 * 面单分享日志相关服务接口
 *
 * <AUTHOR>
 * @Date 2024/7/25 4:55 PM
 */
@Api(tags = "面单分享日志相关服务接口")
@RestController
@RequestMapping("/print/sharinglog")
public class ElefaceSharingRelationOperateController {

    @Autowired
    private ElefaceSharingRelationOperateService elefaceSharingRelationOperateService;

    /**
     * 获取面单操作日志列表
     *
     * @param sessionInfo
     * @return
     */
    @ApiOperation(value = "获取面单操作日志列表", httpMethod = HttpMethodsConstants.POST)
    @ShopsAuth
    @RequestMapping(value = "/list.get", method = {RequestMethod.POST})
    @CheckUserSession(hasCheckPlatform = true)
    public CommonApiResponse<List<ElefaceSharingRelationOperateDTO>> getAllBranches(ElefaceSharingRelationOperateQueryRequest request,
                                                                                    @ApiIgnore UserSessionInfo sessionInfo,
                                                                                    TargetUserInfoDTO targetUserInfoDTO) {
        UserInfoBo userInfoBo = UserInfoBo.of(sessionInfo, targetUserInfoDTO);
        try {
            ElefaceSharingRelationQueryBo queryBo = CommonConvertMapper.INSTANCE.toElefaceSharingRelationOperateQueryBo(request);

            return CommonApiResponse.success(elefaceSharingRelationOperateService.searchOperateListGet(queryBo, userInfoBo));
        } catch (CommonException e) {
            return new CommonApiResponse(CommonApiStatus.Success.code(), CommonApiStatus.Success.message(), e.getCode(),
                    e.getMessage(), null);
        }
    }

    /**
     * 获取面单操作日志列表
     *
     * @param sessionInfo
     * @return
     */
    @ApiOperation(value = "获取面单操作日志列表(分页)", httpMethod = HttpMethodsConstants.POST)
    @ShopsAuth
    @RequestMapping(value = "/page.list.get", method = {RequestMethod.POST})
    @CheckUserSession(hasCheckPlatform = true)
    public CommonApiResponse<ElefaceSharingRelationOperateListResponse> getSharingRelationOperate(ElefaceSharingRelationOperateQueryRequest request,
                                                                                                  @ApiIgnore UserSessionInfo sessionInfo,
                                                                                                  TargetUserInfoDTO targetUserInfoDTO) {
        UserInfoBo userInfoBo = UserInfoBo.of(sessionInfo, targetUserInfoDTO);
        try {
            ElefaceSharingRelationQueryBo queryBo = CommonConvertMapper.INSTANCE.toElefaceSharingRelationOperateQueryBo(request);
            if (Objects.nonNull(request.getPage()) && Objects.nonNull(request.getPageSize())) {
                queryBo.setOffset((request.getPage() - 1) * request.getPageSize());
                queryBo.setLimit(request.getPageSize());
            }
            return CommonApiResponse.success(elefaceSharingRelationOperateService.searchOperateListGetByPage(queryBo, userInfoBo));
        } catch (CommonException e) {
            return new CommonApiResponse(CommonApiStatus.Success.code(), CommonApiStatus.Success.message(), e.getCode(),
                    e.getMessage(), null);
        }
    }
}
