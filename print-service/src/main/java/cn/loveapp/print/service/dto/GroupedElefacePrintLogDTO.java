package cn.loveapp.print.service.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 面单打印统计DTO
 *
 * <AUTHOR>
 */
@Data
@ApiModel(value = "面单打印统计DTO")
public class GroupedElefacePrintLogDTO {
    /**
     * 物流公司code
     */
    @ApiModelProperty(value = "物流公司code")
    private String cpCode;

    /**
     * 物流公司名称
     */
    @ApiModelProperty(value = "物流公司名称")
    private String logisticsCompany;

    /**
     * 收件人省份
     */
    @ApiModelProperty(value = "收件人省份")
    private String receiverProvince;

    /**
     * 总打印面单数
     */
    @ApiModelProperty(value = "总打印面单数")
    private Integer totalPrint;

    /**
     * 总取消面单数
     */
    @ApiModelProperty(value = "总取消面单数")
    private Integer totalCancel;

    /**
     * 面单服务商
     */
    @ApiModelProperty(value = "面单服务商")
    private String elefaceProvider;
}
