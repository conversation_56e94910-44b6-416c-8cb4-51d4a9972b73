package cn.loveapp.print.service.dao.newprint;

import java.util.List;

import cn.loveapp.print.common.entity.AyElefacePrintlog;

/**
 * <AUTHOR>
 */
public interface AyElefacePrintlogDao {

    /**
     * 新增一条数据
     *
     * @param ayElefacePrintlog
     * @return
     */
    int insert(AyElefacePrintlog ayElefacePrintlog);

    /**
     * 通过id批量查询打印信息
     *
     * @param ids
     * @param sellerId
     * @return
     */
    List<AyElefacePrintlog> queryPrintData(List<Long> ids, String sellerId);
}
