package cn.loveapp.print.service.request;

import java.util.List;

import javax.validation.constraints.NotEmpty;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 确认面单共享关系取消 request
 *
 * <AUTHOR>
 */
@Data
@ApiModel
public class ElefaceSharingToMeCanceledConfirmRequest {
    /**
     * 共享id
     */
    @NotEmpty
    @ApiModelProperty(value = "共享id列表", required = true)
    private List<String> shareIds;
}
