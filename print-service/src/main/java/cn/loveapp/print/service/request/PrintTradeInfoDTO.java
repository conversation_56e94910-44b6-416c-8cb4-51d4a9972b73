package cn.loveapp.print.service.request;

import java.util.List;

import javax.validation.constraints.NotEmpty;

import cn.loveapp.print.service.dto.DistributeOrderInfoDTO;
import cn.loveapp.print.common.dto.WayBillOperateLogExtDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 */
@ApiModel
@Data
public class PrintTradeInfoDTO {
    /**
     * 订单号
     */
    @ApiModelProperty(value = "订单号", required = true)
    @NotEmpty
    String tid;

    /**
     * 购物车子单号列表
     */
    @ApiModelProperty(value = "购物车子单号列表, 多个使用英文逗号分割", required = true)
    @NotEmpty
    List<String> oids;

    /**
     * 分销单信息
     */
    @ApiModelProperty(value = "分销单信息")
    private DistributeOrderInfoDTO ayDistributeInfo;

    /**
     * 电子面单云打印接口中收件人信息
     */
    @ApiModelProperty(value = "电子面单云打印接口中收/发件人信息")
    private WayBillOperateLogExtDTO wayBillOperateLogExtDTO;
}
