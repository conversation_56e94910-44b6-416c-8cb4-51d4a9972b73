package cn.loveapp.print.service.controller;

import cn.loveapp.common.annotation.RequestParamConvert;
import cn.loveapp.common.constant.HttpMethodsConstants;
import cn.loveapp.common.dto.UserSessionInfo;
import cn.loveapp.common.user.session.annotation.CheckUserSession;
import cn.loveapp.common.utils.LoggerHelper;
import cn.loveapp.common.web.CommonApiResponse;
import cn.loveapp.common.web.CommonApiStatus;
import cn.loveapp.print.common.exception.CommonException;
import cn.loveapp.print.service.annotation.ShopsAuth;
import cn.loveapp.print.service.bo.DistributeElefacePrintBo;
import cn.loveapp.print.service.bo.UserInfoBo;
import cn.loveapp.print.service.dto.DistributeOrderInfoDTO;
import cn.loveapp.print.service.dto.TargetUserInfoDTO;
import cn.loveapp.print.service.request.DistributeElefacePrintRequest;
import cn.loveapp.print.service.request.DistributeElefaceWaybillCancelRequest;
import cn.loveapp.print.service.service.PrintDistributeLogService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;
import springfox.documentation.annotations.ApiIgnore;

/**
 * 分销代发打印日志相关操作
 *
 * <AUTHOR>
 * @Date 2023/11/6 2:58 PM
 */
@Api(tags = "分销代发打印日志相关操作")
@RestController
@RequestMapping("/print/aydistribute/printlog")
public class PrintDistributeLogController {

    private static final LoggerHelper LOGGER = LoggerHelper.getLogger(PrintDistributeLogController.class);


    @Autowired
    private PrintDistributeLogService printDistributeLogService;

    /**
     * 电子面单打印
     *
     * @param request
     * @param sessionInfo
     * @return
     */
    @ApiOperation(value = "电子面单打印", httpMethod = HttpMethodsConstants.POST)
    @RequestMapping("/eleface.print")
    @CheckUserSession(hasCheckPlatform = true)
    @ShopsAuth
    @RequestParamConvert(underscore = false)
    public CommonApiResponse elefacePrint(@Validated DistributeElefacePrintRequest request, @ApiIgnore UserSessionInfo sessionInfo,
                                          TargetUserInfoDTO targetUserInfoDTO) throws Exception {
        DistributeElefacePrintBo elefacePrintBo = DistributeElefacePrintBo.of(request);
        UserInfoBo userInfoBo = UserInfoBo.of(sessionInfo, targetUserInfoDTO);
        printDistributeLogService.elefacePrint(elefacePrintBo, userInfoBo);
        return CommonApiResponse.success();
    }

    /**
     * 取消电子面单号
     *
     * @param request
     * @param sessionInfo
     * @return
     */
    @ApiOperation(value = "取消电子面单号", httpMethod = HttpMethodsConstants.POST)
    @RequestMapping(value = "/waybill.cancel", method = {RequestMethod.GET, RequestMethod.POST})
    @CheckUserSession(hasCheckPlatform = true)
    @ShopsAuth
    public CommonApiResponse waybillCancel(@Validated DistributeElefaceWaybillCancelRequest request,
                                           @ApiIgnore UserSessionInfo sessionInfo, TargetUserInfoDTO targetUserInfoDTO) {
        String cpCode = request.getCpCode();
        String waybillCode = request.getWaybillCode();
        Integer billVersion = request.getBillVersion();
        DistributeOrderInfoDTO distributeOrderInfo = request.getDistributeInfo();

        Boolean isCancelFromApi = request.getIsCancelFromApi();
        UserInfoBo userInfoBo = UserInfoBo.of(sessionInfo, targetUserInfoDTO);
        try {
            printDistributeLogService.waybillCancel(cpCode, waybillCode, billVersion, isCancelFromApi, userInfoBo,
                distributeOrderInfo);
        } catch (CommonException e) {
            return new CommonApiResponse(CommonApiStatus.Success.code(), CommonApiStatus.Success.message(), e.getCode(),
                    e.getMessage(), null);
        }
        return CommonApiResponse.success();
    }

}
