package cn.loveapp.print.service.service.es;

import cn.loveapp.common.utils.RedisUtil;
import cn.loveapp.print.api.dto.MultiWaybillOperateLogListGetDTO;
import cn.loveapp.print.service.bo.UserInfoBo;
import cn.loveapp.print.service.config.PrintLogConfig;
import cn.loveapp.print.service.dao.es.ElasticsearchAyOperateLogQueryDao;
import cn.loveapp.print.service.dto.AyPrintLogSearchListAndAggDTO;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024-09-19 17:13
 * @description: 面单获取日志高级搜索服务接口
 */
@Component
public class AyElefaceOperateLogSearchService {

    /**
     * es搜索文档返回的最大文档数
     */
    public static final Integer MAX_ES_TOTAL = 10000;

    private static final String OPERATE_LOG_SEARCH_COUNT_PREFIX = "operate:logList:count";

    @Autowired
    private ElasticsearchAyOperateLogQueryDao elasticsearchAyOperateLogQueryDao;

    @Autowired
    private PrintLogConfig printLogConfig;

    @Autowired
    private StringRedisTemplate redisTemplate;


    public AyPrintLogSearchListAndAggDTO ayPrintLogListGetQueryByLimit(MultiWaybillOperateLogListGetDTO operateLogListGetDTO,
                                                                       List<UserInfoBo> userInfoBoList) {
        AyPrintLogSearchListAndAggDTO ayPrintLogSearchListAndAggDTO =
                elasticsearchAyOperateLogQueryDao.ayOperateLogListGetQueryByLimit(operateLogListGetDTO, userInfoBoList);

        if (ayPrintLogSearchListAndAggDTO.getTotalResults() >= MAX_ES_TOTAL) {
            ayPrintLogSearchListAndAggDTO.setTotalResults(queryPrintLogCountFromCache(operateLogListGetDTO, userInfoBoList).longValue());
        }

        return ayPrintLogSearchListAndAggDTO;
    }

    private Integer queryPrintLogCountFromCache(MultiWaybillOperateLogListGetDTO operateLogListGetDTO, List<UserInfoBo> userInfoBoList) {
        Integer countCacheTimeout = printLogConfig.getCountCacheTimeout();
        Integer totalResults = 0;
        if (countCacheTimeout <= 0) {
            totalResults = queryPrintLogCountFromDb(operateLogListGetDTO, userInfoBoList);
        } else {
            MultiWaybillOperateLogListGetDTO cacheKey = new MultiWaybillOperateLogListGetDTO();
            // 忽略与搜索结果数量无关的字段
            BeanUtils.copyProperties(operateLogListGetDTO, cacheKey, "page", "pageSize");
            totalResults = RedisUtil.getCacheWithShortKey(redisTemplate,
                    () -> queryPrintLogCountFromDb(operateLogListGetDTO, userInfoBoList), Integer.class, countCacheTimeout,
                    OPERATE_LOG_SEARCH_COUNT_PREFIX, cacheKey);
        }

        return totalResults;
    }

    protected Integer queryPrintLogCountFromDb(MultiWaybillOperateLogListGetDTO operateLogListGetDTO, List<UserInfoBo> userInfoBoList) {
        return elasticsearchAyOperateLogQueryDao.queryTotalCount(operateLogListGetDTO, userInfoBoList);
    }

}
