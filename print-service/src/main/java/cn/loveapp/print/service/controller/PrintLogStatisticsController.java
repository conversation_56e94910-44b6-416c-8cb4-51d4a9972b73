package cn.loveapp.print.service.controller;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import cn.loveapp.common.constant.HttpMethodsConstants;
import cn.loveapp.common.dto.UserSessionInfo;
import cn.loveapp.common.user.session.annotation.CheckUserSession;
import cn.loveapp.common.utils.LoggerHelper;
import cn.loveapp.common.web.CommonApiResponse;
import cn.loveapp.print.common.dto.UserMultiInfoDTO;
import cn.loveapp.print.service.annotation.ShopsAuth;
import cn.loveapp.print.service.bo.UserInfoBo;
import cn.loveapp.print.service.request.PrintLogFieldListStatisticRequest;
import cn.loveapp.print.service.response.PrintLogFieldListStatisticResponse;
import cn.loveapp.print.service.service.PrintLogStatisticService;
import io.swagger.annotations.ApiOperation;
import springfox.documentation.annotations.ApiIgnore;

/**
 * <AUTHOR>
 * @date 2024-02-20 09:48
 * @description: 打印日志统计控制层
 */
@RestController
@RequestMapping("/print/statistic")
public class PrintLogStatisticsController {
    private static final LoggerHelper LOGGER = LoggerHelper.getLogger(PrintLogStatisticsController.class);

    @Autowired
    private PrintLogStatisticService printLogStatisticService;

    @ApiOperation(value = "打印日志字段列表聚合", httpMethod = HttpMethodsConstants.POST)
    @RequestMapping(value = "printlog.fieldlist.statistic", method = {RequestMethod.GET, RequestMethod.POST})
    @CheckUserSession(hasCheckPlatform = true)
    @ShopsAuth
    public CommonApiResponse<PrintLogFieldListStatisticResponse> printLogFieldListStatistic(
        PrintLogFieldListStatisticRequest request, @ApiIgnore UserSessionInfo sessionInfo,
        UserMultiInfoDTO userMultiInfoDTO) {

        List<UserInfoBo> userInfoBoList = UserInfoBo.of(sessionInfo, userMultiInfoDTO);
        return CommonApiResponse.success(printLogStatisticService.statisticsPrintLogFieldList(request, userInfoBoList));
    }

}
