package cn.loveapp.print.service.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * 电子面单批量取号request
 *
 * @program: print-services-group
 * @description: 电子面单批量取号请求体
 * @author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @create: 2022/12/22 18:32
 **/
@Data
@ApiModel("电子面单批量取号请求体")
public class ElefaceWaybillBatchGetRequest {

    /**
     * 业务Id（每次请求唯一，接口重试传相同值时幂等）
     */
    @ApiModelProperty(value = "业务Id（每次请求唯一，接口重试传相同值时幂等）", required = true)
    private String waybillGetRequestId;

    /**
     * 物流公司code
     */
    @ApiModelProperty(value = "物流公司code", required = true)
    @NotEmpty
    private String cpCode;

    /**
     * 物流公司
     */
    @ApiModelProperty(value = "物流公司name", required = true)
    @NotEmpty
    private String logisticsCompany;

    /**
     * 分享关系的id
     */
    @ApiModelProperty("分享关系的id")
    private String shareId;

    /**
     * 物流子品牌
     */
    @ApiModelProperty("物流子品牌")
    private String brandCode;

    /**
     * 面单获取准备数据
     */
    @ApiModelProperty("面单获取准备数据")
    private List<ElefaceGetPrepareInfoDTO> elefaceGetPrepareInfos;

    /**
     * 操作人(由前端传入当前操作的子账号名)
     */
    @ApiModelProperty("操作人")
    private String operator;

}
