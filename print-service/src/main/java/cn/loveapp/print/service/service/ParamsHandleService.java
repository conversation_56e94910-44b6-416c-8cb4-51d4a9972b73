package cn.loveapp.print.service.service;

import cn.loveapp.common.dto.UserSessionInfo;
import cn.loveapp.print.service.dto.LogisticsPrintlogQueryDTO;
import cn.loveapp.print.common.dto.WaybillOperatelogQueryDTO;

/**
 * <AUTHOR>
 * @date 2024-09-18 14:24
 * @description: 参数校验处理服务
 */
public interface ParamsHandleService {

    /**
     * 处理打印日志高搜参数
     *
     * @param queryDTO
     * @param sessionInfo
     */
    void handlePrintLogSearchListParams(LogisticsPrintlogQueryDTO queryDTO, UserSessionInfo sessionInfo);


    /**
     * 处理打印面单操作日志列表搜索参数
     *
     * @param queryDTO
     * @param sessionInfo
     */
    void handleWaybillOperateLogLisParams(WaybillOperatelogQueryDTO queryDTO, UserSessionInfo sessionInfo);

}
