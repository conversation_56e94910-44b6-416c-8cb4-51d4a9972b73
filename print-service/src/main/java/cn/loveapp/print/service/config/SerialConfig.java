package cn.loveapp.print.service.config;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

import lombok.Data;

/**
 * 流水号相关配置
 *
 * <AUTHOR>
 */
@Data
@Configuration
public class SerialConfig {

    /**
     * 订单流水号redis缓存时间（小时）
     */
    @Value("${serial.redis.timeout:24}")
    private Integer redisTimeout;

    /**
     * 流水号绑定获取时 分页每页查询条数
     */
    @Value("${serial.bound.get.pageSize:200}")
    private Integer serialBoundGetPageSize;

}
