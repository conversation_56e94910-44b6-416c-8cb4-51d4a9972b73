package cn.loveapp.print.service.service.impl;

import java.util.List;

import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import cn.loveapp.common.utils.RedisUtil;
import cn.loveapp.print.service.bo.UserInfoBo;
import cn.loveapp.print.service.config.PrintLogConfig;
import cn.loveapp.print.service.dao.es.ElasticsearchAyPrintLogStatisticsQueryDao;
import cn.loveapp.print.service.dto.PrintLogFieldListResultDTO;
import cn.loveapp.print.service.request.PrintLogFieldListStatisticRequest;
import cn.loveapp.print.service.response.PrintLogFieldListStatisticResponse;
import cn.loveapp.print.service.service.PrintLogStatisticService;

/**
 * <AUTHOR>
 * @date 2024-02-21 09:59
 * @description: 打印日志统计服务接口实现类
 */
@Service
public class PrintLogStatisticServiceImpl implements PrintLogStatisticService {

    /**
     * 日志聚合redis缓存key
     */
    private static final String PRINT_LOG_FIELD_LIST_RESULT_PREFIX = "print:logList:fieldList";

    @Autowired
    private ElasticsearchAyPrintLogStatisticsQueryDao elasticsearchAyPrintLogStatisticsQueryDao;

    @Autowired
    private StringRedisTemplate redisTemplate;

    @Autowired
    private PrintLogConfig printLogConfig;

    @Override
    public PrintLogFieldListStatisticResponse statisticsPrintLogFieldList(PrintLogFieldListStatisticRequest request,
        List<UserInfoBo> userInfoBoList) {

        PrintLogFieldListResultDTO printLogFieldListResultDTO = RedisUtil.getCacheWithShortKey(redisTemplate,
            () -> elasticsearchAyPrintLogStatisticsQueryDao.aggSearchPrintLogFieldList(request, userInfoBoList),
            PrintLogFieldListResultDTO.class, printLogConfig.getPrintLogFieldListStatisticsCacheTimeout(),
            PRINT_LOG_FIELD_LIST_RESULT_PREFIX, request, userInfoBoList);

        PrintLogFieldListStatisticResponse printLogFieldListStatisticResponse =
            new PrintLogFieldListStatisticResponse();
        BeanUtils.copyProperties(printLogFieldListResultDTO, printLogFieldListStatisticResponse);
        return printLogFieldListStatisticResponse;
    }
}
