package cn.loveapp.print.service.request;

import cn.loveapp.print.api.contant.ElefaceGetType;
import lombok.Data;

/**
 * 获取面单网点信息请求request
 * 
 * <AUTHOR>
 * @Date 2023/12/19 10:35 AM
 */
@Data
public class BranchShippaddressGetAllRequest {

    /**
     * 是否返回不可用面单信息
     */
    private Boolean isIncludeUnAvailable = false;

    /**
     * 获取的面单类型-默认全部
     */
    private String getType = ElefaceGetType.GET_ALL;

}
