package cn.loveapp.print.service.request;

import java.util.List;

import javax.validation.Valid;

import cn.loveapp.print.common.dto.BaseUserInfoDTO;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024-05-07 14:47
 * @description: 内部打印日志高搜请求体
 */
@Data
public class PrintLogRequest<P> {

    /**
     * 当前用户信息
     */
    private BaseUserInfoDTO userInfo;

    /**
     * 多店用户请求信息参数
     */
    @Valid
    private List<BaseUserInfoDTO> targetUserList;

    /**
     * 其他请求参数
     */
    @Valid
    private P parameters;

}
