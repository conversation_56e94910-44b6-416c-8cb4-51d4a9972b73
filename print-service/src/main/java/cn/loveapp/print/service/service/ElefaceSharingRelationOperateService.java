package cn.loveapp.print.service.service;

import cn.loveapp.print.common.exception.CommonException;
import cn.loveapp.print.service.bo.ElefaceSharingRelationQueryBo;
import cn.loveapp.print.service.bo.UserInfoBo;
import cn.loveapp.print.service.dto.ElefaceSharingRelationOperateDTO;
import cn.loveapp.print.service.entity.ElefaceSharingRelation;
import cn.loveapp.print.service.response.ElefaceSharingRelationOperateListResponse;

import java.util.List;

/**
 * 面单分享记录操作日志接口
 *
 * <AUTHOR>
 * @Date 2024/7/25 5:12 PM
 */
public interface ElefaceSharingRelationOperateService {

    /**
     * 查询面单分享记录操作日志列表
     *
     * @param queryBo
     * @param userInfoBo
     * @return
     * @throws CommonException
     */
    List<ElefaceSharingRelationOperateDTO> searchOperateListGet(ElefaceSharingRelationQueryBo queryBo, UserInfoBo userInfoBo) throws CommonException;

    /**
     * 更新面单分享记录操作日志
     * @param elefaceSharingRelation
     * @param lastShareNum
     * @param operateType
     * @param operator
     * @param operateTerminal
     */
    void saveElefaceSharingRelationOperateLog(ElefaceSharingRelation elefaceSharingRelation, Long lastShareNum, Integer operateType, String operator, String operateTerminal);

    /**
     * 查询面单分享记录操作日志列表(分页)
     * @param queryBo
     * @param userInfoBo
     * @return
     */
    ElefaceSharingRelationOperateListResponse searchOperateListGetByPage(ElefaceSharingRelationQueryBo queryBo, UserInfoBo userInfoBo) throws CommonException;
}
