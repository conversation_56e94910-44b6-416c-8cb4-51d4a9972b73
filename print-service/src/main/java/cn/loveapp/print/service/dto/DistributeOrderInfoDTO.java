package cn.loveapp.print.service.dto;

import cn.loveapp.print.service.annotation.TradeTypeValidation;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 分销代发-分销单信息
 *
 * <AUTHOR>
 * @Date 2023/11/6 3:29 PM
 */
@Data
@ApiModel(value = "分销单信息")
public class DistributeOrderInfoDTO {

    /**
     * 店铺id
     */
    @ApiModelProperty("店铺id")
    private String sellerId;

    /**
     * 店铺nick
     */
    @ApiModelProperty("店铺nick")
    private String sellerNick;

    /**
     * 店铺平台
     */
    @ApiModelProperty("店铺平台")
    private String storeId;

    /**
     * 店铺应用
     */
    @ApiModelProperty("店铺应用")
    private String appName;

    /**
     * 关联分销单订单号
     */
    @ApiModelProperty("关联分销单订单号")
    private String tid;

    /**
     * 关联分销单子单号
     */
    @ApiModelProperty("关联分销单子单号")
    private List<String> oidList;

    /**
     * 关联分销用户爱用账号id
     */
    @ApiModelProperty("关联分销用户爱用账号id")
    private String distributeSellerId;

    /**
     * 关联分销用户爱用账号nick
     */
    @ApiModelProperty("关联分销用户爱用账号nick")
    private String distributeSellerNick;

    /**
     * 关联分销用户爱用平台
     */
    @ApiModelProperty("关联分销用户爱用平台")
    private String distributeStoreId;

    /**
     * 关联分销用户爱用应用
     */
    @ApiModelProperty("关联分销用户爱用应用")
    private String distributeAppName;

    /**
     * 来源订单类型
     */
    @ApiModelProperty(value = "来源订单类型")
    @TradeTypeValidation
    private Integer sourceTradeType;


}
