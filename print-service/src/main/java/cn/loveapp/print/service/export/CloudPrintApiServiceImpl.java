package cn.loveapp.print.service.export;

import cn.loveapp.print.api.request.AyCloudprintCmdRenderInnerRequest;
import cn.loveapp.print.api.response.AyCloudprintCmdRenderInnerResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;

import cn.loveapp.common.utils.LoggerHelper;
import cn.loveapp.common.web.CommonApiResponse;
import cn.loveapp.common.web.CommonApiStatus;
import cn.loveapp.print.api.request.AyCloudprintBindPrinterInnerRequest;
import cn.loveapp.print.api.request.AyCloudprintGetVerifyCodeInnerRequest;
import cn.loveapp.print.api.request.AyCloudprintSendPrintTaskInnerRequest;
import cn.loveapp.print.api.response.AyCloudprintBindPrinterInnerResponse;
import cn.loveapp.print.api.response.AyCloudprintPublicResponse;
import cn.loveapp.print.api.service.CloudPrintApiService;
import cn.loveapp.print.service.service.CloudprintApiTransferService;

/**
 * @Author: zhongzijie
 * @Date: 2022/3/8 15:44
 * @Description: 云打印服务api
 */
@RestController
public class CloudPrintApiServiceImpl implements CloudPrintApiService {

    private static final LoggerHelper LOGGER = LoggerHelper.getLogger(CloudPrintApiServiceImpl.class);

    @Autowired
    private CloudprintApiTransferService cloudprintApiTransferService;

    @Override
    public CommonApiResponse<AyCloudprintCmdRenderInnerResponse> cmdRender(AyCloudprintCmdRenderInnerRequest request) {
        String platformId = request.getStoreId();
        String printerPlatformId = request.getPrinterPlatformId();
        String appName = request.getAppName();
        AyCloudprintCmdRenderInnerResponse ayCloudprintCmdRenderInnerResponse =
            cloudprintApiTransferService.cmdRender(request, platformId, printerPlatformId, appName);
        if (ayCloudprintCmdRenderInnerResponse.isSuccess()) {
            return CommonApiResponse.success(ayCloudprintCmdRenderInnerResponse);
        }
        return CommonApiResponse.failed(CommonApiStatus.Failed.code(),
            ayCloudprintCmdRenderInnerResponse.getErrorMsg(), ayCloudprintCmdRenderInnerResponse);
    }

    @Override
    public CommonApiResponse<AyCloudprintPublicResponse> getVerifyCode(AyCloudprintGetVerifyCodeInnerRequest request) {
        String platformId = request.getStoreId();
        String printerPlatformId = request.getPrinterPlatformId();
        String appName = request.getAppName();
        AyCloudprintPublicResponse ayCloudprintPublicResponse =
            cloudprintApiTransferService.getVerifyCode(request, platformId, printerPlatformId, appName);
        if (ayCloudprintPublicResponse.isSuccess()) {
            return CommonApiResponse.success();
        }
        return CommonApiResponse.failed(CommonApiStatus.Failed.code(), ayCloudprintPublicResponse.getErrorMsg(),
            ayCloudprintPublicResponse);
    }

    @Override
    public CommonApiResponse<AyCloudprintBindPrinterInnerResponse>
        bindPrinter(AyCloudprintBindPrinterInnerRequest request) {
        String platformId = request.getStoreId();
        String printerPlatformId = request.getPrinterPlatformId();
        String appName = request.getAppName();
        AyCloudprintBindPrinterInnerResponse ayCloudprintBindPrinterInnerResponse =
            cloudprintApiTransferService.bindPrinter(request, platformId, printerPlatformId, appName);
        if (ayCloudprintBindPrinterInnerResponse.isSuccess()) {
            return CommonApiResponse.success(ayCloudprintBindPrinterInnerResponse);
        }
        return CommonApiResponse.failed(CommonApiStatus.Failed.code(),
            ayCloudprintBindPrinterInnerResponse.getErrorMsg(), ayCloudprintBindPrinterInnerResponse);
    }

    @Override
    public CommonApiResponse<AyCloudprintPublicResponse> sendPrintTask(AyCloudprintSendPrintTaskInnerRequest request) {
        String platformId = request.getStoreId();
        String printerPlatformId = request.getPrinterPlatformId();
        String appName = request.getAppName();
        AyCloudprintPublicResponse ayCloudprintPublicResponse =
            cloudprintApiTransferService.sendPrintTask(request, platformId, printerPlatformId, appName);
        if (ayCloudprintPublicResponse.isSuccess()) {
            return CommonApiResponse.success();
        }
        return CommonApiResponse.failed(CommonApiStatus.Failed.code(), ayCloudprintPublicResponse.getErrorMsg(),
            ayCloudprintPublicResponse);
    }
}
