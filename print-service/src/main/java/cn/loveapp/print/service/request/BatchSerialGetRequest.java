package cn.loveapp.print.service.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * 批量获取流水号接口request
 *
 * <AUTHOR>
 * @Date 2023/12/7 2:10 PM
 */
@Data
public class BatchSerialGetRequest {

    /**
     * 取号请求信息
     */
    @ApiModelProperty(value = "取号请求信息", required = true)
    @NotEmpty
    private List<SerialGetRequest> serialGetRequests;

}
