package cn.loveapp.print.service.bo;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;

import cn.loveapp.print.api.request.ElefaceSharingCreateInnerRequest;
import cn.loveapp.print.service.request.ElefaceSharingCreateRequest;
import lombok.Data;

/**
 * 面单共享关系创建 bo
 *
 * <AUTHOR>
 */
@Data
public class ElefaceSharingCreateBo {

    /**
     * 面单被分享者的店铺名
     */
    private String targetMallName;

    /**
     * 面单所有者的sellerNick
     */
    private String ownerSellerNick;

    /**
     * 面单所有者的sellerId
     */
    private String ownerSellerId;

    /**
     * 面单所有者的平台id
     */
    private String ownerStoreId;

    /**
     * 面单所有者的应用名称
     */
    private String ownerAppName;

    /**
     * 被分享用户sellerNick
     */
    private String targetSellerNick;

    /**
     * 被分享用户sellerId
     */
    private String targetSellerId;

    /**
     * 被分享用户平台id
     */
    private String targetStoreId;

    /**
     * 被分享用户应用名称
     */
    private String targetAppName;

    /**
     * 面单服务商 CN PDD
     */
    private String provider;

    /**
     * 快递公司code
     */
    private String cpCode;

    /**
     * 物流服务商 业务类型
     */
    private Long cpType;

    /**
     * 网点code
     */
    private String branchCode;

    /**
     * 号段信息
     */
    private String segmentCode;

    /**
     * 发货地址 省
     */
    private String shippAddressProvince;

    /**
     * 发货地址 市
     */
    private String shippAddressCity;

    /**
     * 发货地址 区
     */
    private String shippAddressDistrict;

    /**
     * 发货地址 详细
     */
    private String shippAddressDetail;

    /**
     * 分享的面单数量
     */
    private Long shareNum;

    /**
     * 子品牌
     */
    private String brandCode;

    /**
     * 分享类型， 1: 标志代理使用
     */
    private Integer shareType;

    /**
     * 分享备注
     */
    private String shareMemo;

    /**
     * 面单记录操作人
     */
    private String operator;

    /**
     * 面单记录操作终端
     */
    private String operateTerminal;

    /**
     * 代理用户id
     */
    private String proxySellerId;
    /**
     * 代理用户nick
     */
    private String proxySellerNick;
    /**
     * 代理用户平台
     */
    private String proxyStoreId;
    /**
     * 代理用户应用
     */
    private String proxyAppName;
    /**
     * 网点名称
     */
    private String branchName;

    /**
     * 是否面单可用
     */
    private boolean isCheckOwnerUsable;

    /**
     * 业务员备注
     */
    private String salesman;

    /**
     * 面单账户名
     */
    private String ownerMallName;

    /**
     * 面单代理账户名
     */
    private String proxyMallName;

    /**
     * 电子面单版本号，1-默认值旧版电子面单 2-新版电子面单 (XHS使用)
     */
    private Integer billVersion;

    public String getSegmentCode() {
        return StringUtils.trimToEmpty(segmentCode);
    }

    public static ElefaceSharingCreateBo of(ElefaceSharingCreateRequest request, UserInfoBo userInfoBo) {
        ElefaceSharingCreateBo createBo = new ElefaceSharingCreateBo();
        BeanUtils.copyProperties(request, createBo);

        // 未指定
        if (StringUtils.isAnyEmpty(request.getOwnerSellerId(), request.getOwnerStoreId(), request.getOwnerAppName())) {
            createBo.setOwnerSellerNick(userInfoBo.getSessionNick());
            createBo.setOwnerSellerId(userInfoBo.getSessionSellerId());
            createBo.setOwnerStoreId(userInfoBo.getSessionStoreId());
            createBo.setOwnerAppName(userInfoBo.getSessionAppName());
        }
        return createBo;
    }

    public static ElefaceSharingCreateBo of(ElefaceSharingCreateInnerRequest request) {
        ElefaceSharingCreateBo createBo = new ElefaceSharingCreateBo();
        BeanUtils.copyProperties(request, createBo);
        return createBo;
    }
}
