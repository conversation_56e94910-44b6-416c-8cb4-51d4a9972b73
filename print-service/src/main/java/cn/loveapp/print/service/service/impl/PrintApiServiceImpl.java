package cn.loveapp.print.service.service.impl;

import cn.loveapp.common.utils.LoggerHelper;
import cn.loveapp.common.web.CommonApiResponse;
import cn.loveapp.common.web.CommonApiStatus;
import cn.loveapp.print.api.dto.ElefaceSharingRelationDTO;
import cn.loveapp.print.api.dto.ShareInfo;
import cn.loveapp.print.api.dto.WaybillOperateLogSaveDTO;
import cn.loveapp.print.api.dto.WaybillOperatelogResponseDTO;
import cn.loveapp.print.api.request.*;
import cn.loveapp.print.api.response.CountPrintLogResponse;
import cn.loveapp.print.api.response.ElefaceIsCancelGetInnerResponse;
import cn.loveapp.print.api.response.SharingBatchCancelResponse;
import cn.loveapp.print.common.constant.PrintTypeConstant;
import cn.loveapp.print.common.constant.TradeTypeConstant;
import cn.loveapp.print.common.entity.AyPrintlog;
import cn.loveapp.print.common.exception.CommonException;
import cn.loveapp.print.service.bo.ElefaceSharingCreateBo;
import cn.loveapp.print.service.bo.UserInfoBo;
import cn.loveapp.print.service.config.PrintLogConfig;
import cn.loveapp.print.service.constant.ElefaceSharingRelationOperateConstant;
import cn.loveapp.print.common.dto.WaybillOperatelogQueryDTO;
import cn.loveapp.print.service.entity.ElefaceSharingRelation;
import cn.loveapp.print.service.service.ElefaceAccountService;
import cn.loveapp.print.service.service.ElefaceWaybillService;
import cn.loveapp.print.service.service.PrintApiService;
import cn.loveapp.print.service.service.PrintlogService;
import cn.loveapp.print.service.utils.ConvertUtil;
import com.alibaba.fastjson.JSONObject;
import com.doudian.open.utils.StringUtil;
import org.apache.commons.lang3.BooleanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;

/**
 * @Author: zhongzijie
 * @Date: 2022/8/1 15:53
 * @Description: 打印服务实现类
 */
@Service
public class PrintApiServiceImpl implements PrintApiService {

	private static final LoggerHelper LOGGER = LoggerHelper.getLogger(PrintApiServiceImpl.class);

	@Autowired
	private ElefaceAccountService elefaceAccountService;

	@Autowired
	private PrintlogService printlogService;

	@Autowired
	private ElefaceWaybillService elefaceWaybillService;

	@Autowired
	private PrintLogConfig printLogConfig;

	@Override
	public CommonApiResponse<ElefaceSharingRelationDTO> elefaceSharingCreate(ElefaceSharingCreateInnerRequest request) {
		ElefaceSharingCreateBo createBo = ElefaceSharingCreateBo.of(request);
		try {
			ElefaceSharingRelation sharingRelation = elefaceAccountService.createSharingRelation(createBo);
			return CommonApiResponse.success(ConvertUtil.convert(sharingRelation, ElefaceSharingRelationDTO.class));
		} catch (CommonException e) {
			return new CommonApiResponse(CommonApiStatus.Success.code(), CommonApiStatus.Success.message(),
				e.getCode(), e.getMessage(), null);
		}
	}

	@Override
	public CommonApiResponse<ElefaceSharingRelationDTO> multiElefaceSharingCreate(ElefaceSharingCreateInnerRequest request) {
		ElefaceSharingCreateBo createBo = ElefaceSharingCreateBo.of(request);
		try {
			ElefaceSharingRelation sharingRelation = elefaceAccountService.multiCreateSharingRelation(createBo);
			return CommonApiResponse.success(ConvertUtil.convert(sharingRelation, ElefaceSharingRelationDTO.class));
		} catch (CommonException e) {
			return new CommonApiResponse(CommonApiStatus.Success.code(), CommonApiStatus.Success.message(),
					e.getCode(), e.getMessage(), null);
		}
	}


	@Override
	public CommonApiResponse elefaceShippaddressGetall(ElefaceShippaddressGetallInnerRequest request) {
		UserInfoBo userInfoBo = new UserInfoBo();
		userInfoBo.setSessionNick(request.getSellerNick());
		userInfoBo.setSessionSellerId(request.getSellerId());
		userInfoBo.setSessionStoreId(request.getStoreId());
		userInfoBo.setSessionAppName(request.getAppName());
		try {
			return CommonApiResponse.success(elefaceAccountService.getAllBranchesShippAddress(userInfoBo, request.getIsIncludeUnAvailable()));
		} catch (CommonException e) {
			return new CommonApiResponse(CommonApiStatus.Success.code(), CommonApiStatus.Success.message(),
				e.getCode(), e.getMessage(), null);
		}
	}

	@Override
	public CommonApiResponse multiElefaceShippaddressGetall(ElefaceShippaddressGetallInnerRequest request) {
		UserInfoBo userInfoBo = new UserInfoBo();
		userInfoBo.setSessionNick(request.getSellerNick());
		userInfoBo.setSessionSellerId(request.getSellerId());
		userInfoBo.setSessionStoreId(request.getStoreId());
		userInfoBo.setSessionAppName(request.getAppName());
		try {
			return CommonApiResponse.success(elefaceAccountService.getMultiAllBranchesShippAddress(userInfoBo, request.getIsIncludeUnAvailable(), request.getGetType()));
		} catch (CommonException e) {
			return new CommonApiResponse(CommonApiStatus.Success.code(), CommonApiStatus.Success.message(),
					e.getCode(), e.getMessage(), null);
		}
	}

	@Override
	public CommonApiResponse<Boolean> queryOrderHasPrintHistory(OrderHasprintGetInnerRequest request) {
		try {
			LOGGER.logInfo("查询订单是否打印过单子入参：" + JSONObject.toJSONString(request));
			//面单 > 快递单 > 发货单
            List<String> printTypes = request.getPrintTypes();
            if (CollectionUtils.isEmpty(printTypes)) {
                printTypes = Arrays.asList(PrintTypeConstant.DELIVER, PrintTypeConstant.ELEFACE, PrintTypeConstant.EXPRESS);
            }
			AyPrintlog printlog = printlogService.queryHasPrintHistoryByTid(request.getTid(), TradeTypeConstant.NORMAL_TRADE, printTypes,request.getSellerId(), request.getStoreId(), request.getAppName());
            boolean hasPrintLog = printlog != null;

            if (!hasPrintLog && BooleanUtils.isTrue(request.getIncludeElefaceOperateLog())) {
                //如果没打印过面单或快递单，并且需要查询面单获取记录
                WaybillOperatelogQueryDTO queryDTO = new WaybillOperatelogQueryDTO();
                queryDTO.setTid(request.getTid());
                //订单类型 0-普通订单 1-自由打印订单
                queryDTO.setTradeType(0);
                Long elefaceLogCount = elefaceWaybillService.queryCount(queryDTO, request.getStoreId(), request.getSellerId(), request.getAppName());
                if (elefaceLogCount > 0L) {
                    hasPrintLog = true;
                }
            }
            return CommonApiResponse.success(hasPrintLog);
		} catch (Exception e) {
			return new CommonApiResponse(CommonApiStatus.Failed.code(), CommonApiStatus.Failed.message(),
				CommonApiStatus.Failed.code(), e.getMessage(), null);
		}
	}

	@Override
	public CommonApiResponse waybillOperatelogSave(WaybillOperateLogSaveRequest request) {
		try {
			LOGGER.logInfo("批量插入面单操作日志：" + JSONObject.toJSONString(request));
			List<WaybillOperateLogSaveDTO> waybillOperatelogSaves = request.getWaybillOperateLogSaves();
			if (CollectionUtils.isEmpty(waybillOperatelogSaves)){
				return new CommonApiResponse(CommonApiStatus.Failed.code(), CommonApiStatus.Failed.message(),
						CommonApiStatus.Failed.code(), "参数错误", null);
			}
			elefaceWaybillService.saveWaybillOperateLogBatch(waybillOperatelogSaves);
			return CommonApiResponse.success();
		} catch (Exception e) {
			return new CommonApiResponse(CommonApiStatus.Failed.code(), CommonApiStatus.Failed.message(),
					CommonApiStatus.Failed.code(), e.getMessage(), null);
		}
	}

    @Override
    public CommonApiResponse<CountPrintLogResponse> countPrintLogByPrintType(CountPrintLogRequest request) {
        try {
            LOGGER.logInfo("打印日志统计入参：" + JSONObject.toJSONString(request));
            return CommonApiResponse.success(printlogService.countPrintLogByPrintType(request));
        } catch (Exception e) {
            return new CommonApiResponse(CommonApiStatus.Failed.code(), CommonApiStatus.Failed.message(),
                CommonApiStatus.Failed.code(), e.getMessage(), null);
        }
    }

    @Override
    public CommonApiResponse<List<ElefaceIsCancelGetInnerResponse>> elefaceIsCancelGet(ElefaceIsCancelGetInnerRequest request) {
        try {
            LOGGER.logInfo("批量查询面单是否被回收日志：" + JSONObject.toJSONString(request));
            return CommonApiResponse.success(elefaceWaybillService.elefaceIsCancelGet(request));
        } catch (Exception e) {
            return new CommonApiResponse(CommonApiStatus.Failed.code(), CommonApiStatus.Failed.message(),
                CommonApiStatus.Failed.code(), e.getMessage(), null);
        }
    }


	@Override
	public CommonApiResponse<List<ElefaceSharingRelationDTO>> sharingBatchGet(SharingBatchGetRequest request) {
		try {
			LOGGER.logInfo("查询面单共享列表：" + JSONObject.toJSONString(request));
			List<ElefaceSharingRelation> relationList = elefaceAccountService.sharingBatchGet(request);
			return CommonApiResponse.success(ConvertUtil.convertList(relationList, ElefaceSharingRelationDTO.class));
		} catch (Exception e) {
			return new CommonApiResponse(CommonApiStatus.Failed.code(), CommonApiStatus.Failed.message(),
					CommonApiStatus.Failed.code(), e.getMessage(), null);
		}
	}

	@Override
	public CommonApiResponse<SharingBatchCancelResponse> sharingBatchCancel(SharingBatchCancelRequest request) {
		try {
			LOGGER.logInfo("取消面单共享：" + JSONObject.toJSONString(request));
			List<ShareInfo> shareInfoList = request.getShareInfoList();
			SharingBatchCancelResponse response = new SharingBatchCancelResponse();

			if (CollectionUtils.isEmpty(shareInfoList)) {
				return new CommonApiResponse(CommonApiStatus.Failed.code(), CommonApiStatus.Failed.message(),
						CommonApiStatus.Failed.code(), "参数为空，取消失败", null);
			}

			for (ShareInfo shareInfo : shareInfoList) {
				String shareId = shareInfo.getShareId();
				try {
					UserInfoBo userInfoBo = new UserInfoBo();
					userInfoBo.setSellerId(shareInfo.getSellerId());
					userInfoBo.setAppName(shareInfo.getAppName());
					userInfoBo.setStoreId(shareInfo.getStoreId());
					userInfoBo.setOperator(request.getOperator());
					userInfoBo.setOperatorStoreId(request.getOperatorStoreId());
					userInfoBo.setOperateTerminal(request.getOperateTerminal());
					elefaceAccountService.cancelSharingRelation(shareId, userInfoBo, ElefaceSharingRelationOperateConstant.CANCEL);
					response.setSuccess(shareId);
				} catch (CommonException e) {
					response.setError(shareId);
				}
			}

			return CommonApiResponse.success(response);
		} catch (Exception e) {
			return new CommonApiResponse(CommonApiStatus.Failed.code(), CommonApiStatus.Failed.message(),
					CommonApiStatus.Failed.code(), e.getMessage(), null);
		}
	}

	@Override
	public CommonApiResponse<String> sharingTopUpNum(ShareTopUpNumRequest request) {
		try {
			LOGGER.logInfo("面单共享充值：" + JSONObject.toJSONString(request));

			if (Objects.isNull(request)) {
				return new CommonApiResponse(CommonApiStatus.Failed.code(), CommonApiStatus.Failed.message(),
						CommonApiStatus.Failed.code(), "参数为空，取消失败", null);
			}
			UserInfoBo userInfoBo = new UserInfoBo();
			userInfoBo.setSellerId(request.getOwnerSellerId());
			userInfoBo.setAppName(request.getOwnerAppName());
			userInfoBo.setStoreId(request.getOwnerStoreId());
			userInfoBo.setOperator(request.getOperator());
			userInfoBo.setOperatorStoreId(request.getOperatorStoreId());
			userInfoBo.setOperateTerminal(request.getOperateTerminal());

			elefaceAccountService.topUpBranchShareNum(userInfoBo, request.getShareId(), request.getTopUpNum());
			return CommonApiResponse.success();
		} catch (Exception e) {
			return new CommonApiResponse(CommonApiStatus.Failed.code(), CommonApiStatus.Failed.message(),
					CommonApiStatus.Failed.code(), e.getMessage(), null);
		}
	}

	@Override
	public CommonApiResponse<String> sharingQuantityModify(ShareTopUpNumRequest request) {
		try {
			LOGGER.logInfo("面单数量修改：" + JSONObject.toJSONString(request));

			if (Objects.isNull(request)) {
				return new CommonApiResponse(CommonApiStatus.Failed.code(), CommonApiStatus.Failed.message(),
						CommonApiStatus.Failed.code(), "参数为空，修改失败", null);
			}
			UserInfoBo userInfoBo = new UserInfoBo();
			userInfoBo.setSellerId(request.getOwnerSellerId());
			userInfoBo.setAppName(request.getOwnerAppName());
			userInfoBo.setStoreId(request.getOwnerStoreId());
			userInfoBo.setOperator(request.getOperator());
			userInfoBo.setOperatorStoreId(request.getOperatorStoreId());
			userInfoBo.setOperateTerminal(request.getOperateTerminal());

			elefaceAccountService.sharingQuantityModify(userInfoBo, request.getShareId(), request.getTopUpNum(), request.getShareNumUnlimited());
			return CommonApiResponse.success();
		} catch (Exception e) {
			return new CommonApiResponse(CommonApiStatus.Failed.code(), CommonApiStatus.Failed.message(),
					CommonApiStatus.Failed.code(), e.getMessage(), null);
		}
	}

	@Override
	public CommonApiResponse<String> sharingRecovery(ShareRecoveryRequest request) {
		try {
			LOGGER.logInfo("面单恢复修改：" + JSONObject.toJSONString(request));

			if (Objects.isNull(request)) {
				return new CommonApiResponse(CommonApiStatus.Failed.code(), CommonApiStatus.Failed.message(),
						CommonApiStatus.Failed.code(), "参数为空，修改失败", null);
			}
			UserInfoBo userInfoBo = new UserInfoBo();
			userInfoBo.setSellerId(request.getSellerId());
			userInfoBo.setAppName(request.getAppName());
			userInfoBo.setStoreId(request.getStoreId());
			userInfoBo.setOperator(request.getOperator());
			userInfoBo.setOperateTerminal(request.getOperateTerminal());

			elefaceAccountService.sharingRecovery(userInfoBo, request.getShareId(), request.getShareRecoveryNum(), request.getShareNumUnlimited());
			return CommonApiResponse.success();
		} catch (Exception e) {
			return new CommonApiResponse(CommonApiStatus.Failed.code(), CommonApiStatus.Failed.message(),
					CommonApiStatus.Failed.code(), e.getMessage(), null);
		}
	}

	@Override
	public CommonApiResponse<WaybillOperatelogResponseDTO> waybillShareOperateLogListGet(WaybillShareOperateLogListGetRpcRequest request) {

		String ownerSellerId = request.getOwnerSellerId();
		String targetSellerId = request.getTargetSellerId();
		List<String> ignoreUserIdList = printLogConfig.getIgnoreUserIdList();
		if (!CollectionUtils.isEmpty(ignoreUserIdList) && (ignoreUserIdList.contains(ownerSellerId) || ignoreUserIdList.contains(targetSellerId))) {
			LOGGER.logWarn("限制查询用户:" + ownerSellerId + "," + targetSellerId);
			return new CommonApiResponse<>(CommonApiStatus.Failed.code(), CommonApiStatus.Failed.message(),
					CommonApiStatus.Failed.code(), "查询限流中", null);
		}

		Integer page = request.getPage();
		Integer pageSize = request.getPageSize();
		Integer tradeType = request.getTradeType();
		Boolean isCancel = request.getIsCancel();
		String provider = request.getProvider();
		String cpCode = request.getCpCode();
		String tid = request.getTid();
		List<String> tidList = request.getTidList();
		String waybillCode = request.getWaybillCode();
		LocalDateTime startTime = request.getStartTime();
		LocalDateTime endTime = request.getEndTime();

		// 组装查询DTO
		WaybillOperatelogQueryDTO queryDTO = new WaybillOperatelogQueryDTO();
		queryDTO.setOffset((page - 1) * pageSize);
		queryDTO.setLimit(pageSize);
		queryDTO.setTradeType(tradeType);
		queryDTO.setIsCancel(isCancel);
		queryDTO.setProvider(provider);
		queryDTO.addCpCodes(cpCode);
		// 如果传了tid，默认忽略tidList
		if (StringUtil.isEmpty(tid) && org.apache.commons.collections.CollectionUtils.isNotEmpty(tidList)) {
			queryDTO.setTidList(tidList);
		} else {
			queryDTO.setTid(tid);
		}
		queryDTO.setWaybillCode(waybillCode);
		queryDTO.setStartTime(startTime);
		queryDTO.setEndTime(endTime);

		queryDTO.setOwnerSellerId(request.getOwnerSellerId());
		queryDTO.setOwnerSellerNick(request.getOwnerSellerNick());
		queryDTO.setOwnerStoreId(request.getOwnerStoreId());
		queryDTO.setOwnerAppName(request.getOwnerAppName());

		// 设置面单使用者（被分享的）
		UserInfoBo userInfoBo = new UserInfoBo();
		userInfoBo.setStoreId(request.getTargetStoreId());
		userInfoBo.setSellerId(request.getTargetSellerId());
		userInfoBo.setSellerNick(request.getTargetSellerNick());
		userInfoBo.setAppName(request.getTargetAppName());

		return CommonApiResponse.success(elefaceWaybillService.operatelogListGet(queryDTO, userInfoBo));
	}
}
