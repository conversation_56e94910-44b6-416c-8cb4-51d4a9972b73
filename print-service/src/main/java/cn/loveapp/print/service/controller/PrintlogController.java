package cn.loveapp.print.service.controller;

import java.util.ArrayList;
import java.util.List;

import cn.loveapp.print.service.service.ParamsHandleService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import com.google.common.collect.Lists;

import cn.loveapp.common.annotation.RequestParamConvert;
import cn.loveapp.common.constant.HttpMethodsConstants;
import cn.loveapp.common.dto.UserSessionInfo;
import cn.loveapp.common.user.session.annotation.CheckUserSession;
import cn.loveapp.common.utils.LoggerHelper;
import cn.loveapp.common.web.CommonApiResponse;
import cn.loveapp.common.web.CommonApiStatus;
import cn.loveapp.print.api.dto.TradeLogisticsBindingHistoryResponseDTO;
import cn.loveapp.print.api.dto.WaybillOperateLogSaveDTO;
import cn.loveapp.print.api.request.WaybillOperateLogSaveRequest;
import cn.loveapp.print.api.response.LogisticsPrintlogResponseDTO;
import cn.loveapp.print.common.dto.UserMultiInfoDTO;
import cn.loveapp.print.service.annotation.ShopsAuth;
import cn.loveapp.print.service.annotation.UserAuth;
import cn.loveapp.print.service.bo.*;
import cn.loveapp.print.service.config.PrintLogConfig;
import cn.loveapp.print.service.dto.*;
import cn.loveapp.print.service.request.*;
import cn.loveapp.print.service.service.PrintlogService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import springfox.documentation.annotations.ApiIgnore;

/**
 * 打印日志相关操作
 *
 * <AUTHOR>
 */
@Api(tags = "打印日志相关操作")
@RestController
@RequestMapping("/print/printlog")
public class PrintlogController {

    private static final LoggerHelper LOGGER = LoggerHelper.getLogger(PrintlogController.class);

    @Autowired
    private PrintlogService printlogService;

    @Autowired
    private PrintLogConfig printLogConfig;

    @Autowired
    private ParamsHandleService paramsHandleService;

    /**
     * 电子面单打印
     *
     * @param request
     * @param sessionInfo
     *
     * @return
     */
    @ApiOperation(value = "电子面单打印", httpMethod = HttpMethodsConstants.POST)
    @RequestMapping("/eleface.print")
    @CheckUserSession(hasCheckPlatform = true)
    @ShopsAuth
    @RequestParamConvert(underscore = false)
    public CommonApiResponse elefacePrint(@Validated ElefacePrintRequest request, @ApiIgnore UserSessionInfo sessionInfo,
        TargetUserInfoDTO targetUserInfoDTO) throws Exception {
        ElefacePrintBo elefacePrintBo = ElefacePrintBo.of(request);
        UserInfoBo userInfoBo = UserInfoBo.of(sessionInfo, targetUserInfoDTO);
        printlogService.elefacePrint(elefacePrintBo, userInfoBo);
        return CommonApiResponse.success();
    }

    /**
     * 快递单打印
     *
     * @param sessionInfo
     *
     * @return
     */
    @ApiOperation(value = "快递单打印", httpMethod = HttpMethodsConstants.POST)
    @RequestMapping("/express.print")
    @CheckUserSession(hasCheckPlatform = true)
    @ShopsAuth
    @RequestParamConvert(underscore = false)
    public CommonApiResponse expressPrint(@Validated ExpressPrintRequest request, @ApiIgnore UserSessionInfo sessionInfo,
        TargetUserInfoDTO targetUserInfoDTO) {
        ExpressPrintBo expressPrintBo = ExpressPrintBo.of(request);
        UserInfoBo userInfoBo = UserInfoBo.of(sessionInfo, targetUserInfoDTO);
        printlogService.expressPrint(expressPrintBo, userInfoBo);
        return CommonApiResponse.success();
    }

    /**
     * 发货单打印
     *
     * @param request
     * @param sessionInfo
     *
     * @return
     */
    @ApiOperation(value = "发货单打印", httpMethod = HttpMethodsConstants.POST)
    @RequestMapping("/deliver.print")
    @CheckUserSession(hasCheckPlatform = true)
    @ShopsAuth
    @RequestParamConvert(underscore = false)
    public CommonApiResponse deliverPrint(@Validated DeliverPrintRequest request, @ApiIgnore UserSessionInfo sessionInfo,
        TargetUserInfoDTO targetUserInfoDTO) {
        DeliverPrintBo deliverPrintBo = DeliverPrintBo.of(request);
        UserInfoBo userInfoBo = UserInfoBo.of(sessionInfo, targetUserInfoDTO);
        printlogService.deliverPrint(deliverPrintBo, userInfoBo);
        return CommonApiResponse.success();

    }

    /**
     * 获取指定订单的物流单绑定记录
     * @param request
     * @param sessionInfo
     * @param userMultiInfoDTO
     * @return TradeLogisticsBindingHistoryResponseDTO
     */
    @ApiOperation(value = "获取指定订单的物流单绑定记录", httpMethod = HttpMethodsConstants.POST)
    @RequestMapping("/logistics.binding.history.get.batch")
    @CheckUserSession(hasCheckPlatform = true)
    @UserAuth
    public CommonApiResponse<TradeLogisticsBindingHistoryResponseDTO> logisticsBindingHistoryGetBatch(
        @Validated LogisticsBindHistoryGetRequest request, @ApiIgnore UserSessionInfo sessionInfo,
        UserMultiInfoDTO userMultiInfoDTO) {
        TradeLogisticsBindingHistoryResponseDTO responseDTO = new TradeLogisticsBindingHistoryResponseDTO();
        List<TradeLogisticsBindingHistoryResponseDTO.TradeLogisticsBindingHistoryDTO> tradeLogisticsBindingHistoryList = new ArrayList<>();
        responseDTO.setTradeLogisticsBindingHistoryList(tradeLogisticsBindingHistoryList);
        if (printLogConfig.getPrintLogListDegradePlatforms().contains(sessionInfo.getStoreId())) {
            //降级开关
            return CommonApiResponse.success(responseDTO);
        }

        Integer tradeType = request.getTradeType();
        Boolean needPrintData = request.getNeedPrintData();

        List<UserInfoBo> userInfoBoList = UserInfoBo.of(sessionInfo, userMultiInfoDTO);

        if (CollectionUtils.isEmpty(request.getTargetPrintInfoList())) {
            List<String> tids = request.getTids();
            for (UserInfoBo userInfoBo : userInfoBoList) {
                TradeLogisticsBindingHistoryResponseDTO targetResponse = printlogService.queryTradeLogisticsBindingHistoryBatch(tids, tradeType, needPrintData, userInfoBo);
                if (CollectionUtils.isEmpty(targetResponse.getTradeLogisticsBindingHistoryList())) {
                    continue;
                }
                tradeLogisticsBindingHistoryList.addAll(targetResponse.getTradeLogisticsBindingHistoryList());
            }
        } else {
            for (TargetPrintInfoDTO targetPrintInfo : request.getTargetPrintInfoList()) {
                UserInfoBo targetUser = UserInfoBo.of(userInfoBoList, targetPrintInfo);
                if (targetUser == null) {
                    LOGGER.logError("鉴权失败，找不到该用户");
                    continue;
                } else if (CollectionUtils.isEmpty(targetPrintInfo.getTidList())) {
                    LOGGER.logError("缺少参数tidList");
                    continue;
                }
                TradeLogisticsBindingHistoryResponseDTO targetResponse = printlogService.queryTradeLogisticsBindingHistoryBatch(targetPrintInfo.getTidList(), tradeType, needPrintData, targetUser);
                if (CollectionUtils.isEmpty(targetResponse.getTradeLogisticsBindingHistoryList())) {
                    continue;
                }
                tradeLogisticsBindingHistoryList.addAll(targetResponse.getTradeLogisticsBindingHistoryList());
            }
        }
        return CommonApiResponse.success(responseDTO);
    }

    /**
     * 获取指定订单的发货单打印状态
     * @param request
     * @param sessionInfo
     * @param userMultiInfoDTO
     * @return TradeDeliverPrintStatusDTO
     */
    @ApiOperation(value = "获取指定订单的发货单打印状态", httpMethod = HttpMethodsConstants.POST)
    @RequestMapping("/deliver.print.status.get.batch")
    @CheckUserSession(hasCheckPlatform = true)
    @UserAuth
    public CommonApiResponse<List<TradeDeliverPrintStatusDTO>> deliverPrintStatusGetBatch(@Validated DeliverPrintStatusGetRequest request,
                                                                                          @ApiIgnore UserSessionInfo sessionInfo,  UserMultiInfoDTO userMultiInfoDTO) {

        List<TradeDeliverPrintStatusDTO> tradeDeliverPrintStatusList = new ArrayList<>();
        if (printLogConfig.getPrintLogListDegradePlatforms().contains(sessionInfo.getStoreId())) {
            //降级开关
            return CommonApiResponse.success(tradeDeliverPrintStatusList);
        }

        Integer tradeType = request.getTradeType();

        List<UserInfoBo> userInfoBoList = UserInfoBo.of(sessionInfo, userMultiInfoDTO);

        if (CollectionUtils.isEmpty(request.getTargetPrintInfoList())) {
            // 未指定分组
            List<String> tids = request.getTids();
            for (UserInfoBo userInfoBo : userInfoBoList) {
                List<TradeDeliverPrintStatusDTO> targetStatusList = printlogService.queryTradeDeliverPrintStatus(tids, tradeType, userInfoBo);
                if (CollectionUtils.isEmpty(targetStatusList)) {
                    continue;
                }
                tradeDeliverPrintStatusList.addAll(targetStatusList);
            }

        } else {
            for (TargetPrintInfoDTO targetPrintInfo : request.getTargetPrintInfoList()) {
                UserInfoBo targetUser = UserInfoBo.of(userInfoBoList, targetPrintInfo);
                if (targetUser == null) {
                    LOGGER.logError("鉴权失败，找不到该用户");
                    continue;
                } else if (CollectionUtils.isEmpty(targetPrintInfo.getTidList())) {
                    LOGGER.logError("缺少参数tidList");
                    continue;
                }
                List<TradeDeliverPrintStatusDTO> targetStatusList = printlogService.queryTradeDeliverPrintStatus(targetPrintInfo.getTidList(), tradeType, targetUser);
                if (CollectionUtils.isEmpty(targetStatusList)) {
                    continue;
                }
                tradeDeliverPrintStatusList.addAll(targetStatusList);
            }
        }
        return CommonApiResponse.success(tradeDeliverPrintStatusList);
    }

    /**
     * 查询物流单打印日志
     *
     * @param request
     * @param sessionInfo
     * @param targetUserInfoDTO
     *
     * @return
     */
    @ApiOperation(value = "查询物流单打印日志", httpMethod = HttpMethodsConstants.POST)
    @RequestMapping(value = "/logistics.printlog.list.get", method = {RequestMethod.GET, RequestMethod.POST})
    @CheckUserSession(hasCheckPlatform = true)
    @ShopsAuth
    public CommonApiResponse<LogisticsPrintlogResponseDTO> logisticsPrintlogListGet(
        LogisticsPrintlogListGetRequest request, @ApiIgnore UserSessionInfo sessionInfo,
        TargetUserInfoDTO targetUserInfoDTO, UserMultiInfoDTO userMultiInfoDTO) {

        LogisticsPrintlogQueryDTO queryDTO = LogisticsPrintlogQueryDTO.of(request);
        paramsHandleService.handlePrintLogSearchListParams(queryDTO, sessionInfo);
        List<UserInfoBo> userInfoBoList = null;
        if (userMultiInfoDTO == null) {
            // 老逻辑
            UserInfoBo userInfoBo = UserInfoBo.of(sessionInfo, targetUserInfoDTO);
            userInfoBoList = Lists.newArrayList(userInfoBo);
        } else {
            userInfoBoList = UserInfoBo.of(sessionInfo, userMultiInfoDTO);
        }

        // 新逻辑查es
        return CommonApiResponse.success(printlogService.logisticsPrintLogListGetByESSearch(queryDTO, userInfoBoList));
    }

    /**
     * 聚合面单打印记录
     *
     * @param request
     * @param sessionInfo
     * @param targetUserInfoDTO
     * @return
     */
    @ApiOperation(value = "聚合面单打印记录", httpMethod = HttpMethodsConstants.POST)
    @RequestMapping("/group.eleface.printlog")
    @CheckUserSession(hasCheckPlatform = true)
    @ShopsAuth
    public CommonApiResponse<List<GroupedElefacePrintLogDTO>> groupElefacePrintlog(@Validated GroupElefacePrintLogRequest request,
                                                                                   @ApiIgnore UserSessionInfo sessionInfo, TargetUserInfoDTO targetUserInfoDTO) {
        UserInfoBo userInfoBo = UserInfoBo.of(sessionInfo, targetUserInfoDTO);

        GroupElefacePrintLogQueryBo queryBo = GroupElefacePrintLogQueryBo.of(request);

        return CommonApiResponse.success(printlogService.groupElefacePrintLog(queryBo, userInfoBo));
    }

    /**
     * 面单操作日志批量保存
     * @param request
     * @return
     */
    @ApiOperation(value = "面单操作日志批量保存", httpMethod = HttpMethodsConstants.POST)
    @RequestParamConvert
    @CheckUserSession(hasCheckPlatform = true)
    @RequestMapping(value = "/waybill.operatelog.save", method = {RequestMethod.POST})
    public CommonApiResponse waybillOperatelogSave(@Validated WaybillOperateLogSaveRequest request) {
        try {
            List<WaybillOperateLogSaveDTO> waybillOperatelogSaves = request.getWaybillOperateLogSaves();
            if (CollectionUtils.isEmpty(waybillOperatelogSaves)){
                return new CommonApiResponse(CommonApiStatus.Failed.code(), CommonApiStatus.Failed.message(),
                        CommonApiStatus.Failed.code(), "参数错误", null);
            }
            printlogService.waybillOperatelogSave(waybillOperatelogSaves);
            return CommonApiResponse.success();
        } catch (Exception e) {
            return new CommonApiResponse(CommonApiStatus.Failed.code(), CommonApiStatus.Failed.message(),
                    CommonApiStatus.Failed.code(), e.getMessage(), null);
        }
    }
}
