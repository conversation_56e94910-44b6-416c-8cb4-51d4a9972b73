package cn.loveapp.print.service.service;

import java.util.List;

import cn.loveapp.print.api.dto.WaybillOperateLogSaveDTO;
import cn.loveapp.print.api.request.CountPrintLogRequest;
import cn.loveapp.print.api.response.CountPrintLogResponse;
import cn.loveapp.print.common.entity.AyPrintlog;
import cn.loveapp.print.service.bo.*;
import cn.loveapp.print.service.dto.*;
import cn.loveapp.print.api.response.LogisticsPrintlogResponseDTO;
import cn.loveapp.print.api.dto.TradeLogisticsBindingHistoryResponseDTO;

/**
 * <AUTHOR>
 */
public interface PrintlogService {

    /**
     * 面单打印
     *
     * @param elefacePrintBo
     * @param userInfoBo
     *
     * @throws Exception
     *             入库异常
     */
    void elefacePrint(ElefacePrintBo elefacePrintBo, UserInfoBo userInfoBo);

    /**
     * 快递单打印
     *
     * @param expressPrintBo
     * @param userInfoBo
     *
     * @throws Exception
     *             入库异常
     */
    void expressPrint(ExpressPrintBo expressPrintBo, UserInfoBo userInfoBo);

    /**
     * 发货单打印
     *
     * @param deliverPrintBo
     * @param userInfoBo
     *
     * @throws Exception
     *             入库异常
     */
    void deliverPrint(DeliverPrintBo deliverPrintBo, UserInfoBo userInfoBo);

    /**
     * 保存面单已回收
     *
     * @param cpCode
     * @param waybillCode
     * @param userInfoBo
     */
    void saveWaybillIsCancel(String cpCode, String waybillCode, UserInfoBo userInfoBo);

    /**
     * 批量查询订单的绑定关系
     *
     * @param tids
     * @param tradeType
     * @param needPrintData
     * @param userInfoBo
     *
     * @return
     */
    TradeLogisticsBindingHistoryResponseDTO queryTradeLogisticsBindingHistoryBatch(List<String> tids, Integer tradeType,
        Boolean needPrintData, UserInfoBo userInfoBo);

    /**
     * 查询指定订单的发货单打印状态
     *
     * @param tids
     * @param tradeType
     * @param userInfoBo
     * @return
     */
    List<TradeDeliverPrintStatusDTO> queryTradeDeliverPrintStatus(List<String> tids, Integer tradeType,
        UserInfoBo userInfoBo);

    /**
     * 获取物流单打印历史(通过ES搜索)
     *
     * @param queryDTO
     * @param userInfoBo
     *
     * @return
     */
    LogisticsPrintlogResponseDTO logisticsPrintLogListGetByESSearch(LogisticsPrintlogQueryDTO queryDTO,
        List<UserInfoBo> userInfoBo);

    /**
     * 面单打印记录统计
     *
     * @param queryBo
     * @param userInfoBo
     * @return
     */
    List<GroupedElefacePrintLogDTO> groupElefacePrintLog(GroupElefacePrintLogQueryBo queryBo, UserInfoBo userInfoBo);

    /**
     * 根据订单id查询是否打印过单子
     *
     * @param tid
     * @param tradeType
     * @param storeId
     * @param appName
     * @return
     */
    AyPrintlog queryHasPrintHistoryByTid(String tid, Integer tradeType, List<String> printTypes, String sellerId,
        String storeId, String appName);

    /**
     * 保存运单号操作日志
     * @param waybillOperatelogSaves
     * @return
     */
    void waybillOperatelogSave(List<WaybillOperateLogSaveDTO> waybillOperatelogSaves);

    /**
     * 统计不同打印类型的打印日志记录数
     * @param request
     * @return
     */
    CountPrintLogResponse countPrintLogByPrintType(CountPrintLogRequest request);

    /**
     * 获取搜索结果
     *
     * @param queryDTO
     * @param userInfoBoList
     * @return
     */
    AyPrintLogListSearchResultDTO getSearchResult(LogisticsPrintlogQueryDTO queryDTO, List<UserInfoBo> userInfoBoList);

    /**
     * 查询打印日志总数缓存
     *
     * @param ayPrintLogListSearchDTO
     * @param userInfoBoList
     * @return
     */
    Integer queryPrintLogCountFromCache(AyPrintLogListSearchDTO ayPrintLogListSearchDTO,
        List<UserInfoBo> userInfoBoList);

    /**
     * 打印日志列表获取计数聚合
     *
     * @param ayPrintLogSearchListAndAggDTO
     * @param ayPrintLogListSearchDTO
     * @param userInfoBoList
     */
    void ayPrintLogListGetCountStatisticsFromCache(AyPrintLogSearchListAndAggDTO ayPrintLogSearchListAndAggDTO,
        AyPrintLogListSearchDTO ayPrintLogListSearchDTO, List<UserInfoBo> userInfoBoList);
}
