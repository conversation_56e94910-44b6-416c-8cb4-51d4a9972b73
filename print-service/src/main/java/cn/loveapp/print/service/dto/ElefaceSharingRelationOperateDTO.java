package cn.loveapp.print.service.dto;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * 面单操作日志
 *
 * <AUTHOR>
 * @Date 2024/7/25 5:13 PM
 */
@Data
public class ElefaceSharingRelationOperateDTO {


    /**
     * 面单分享Id
     */
    private String shareId;

    /**
     * 面单所有者的sellerNick
     */
    private String ownerSellerNick;

    /**
     * 面单所有者的sellerId
     */
    private String ownerSellerId;

    /**
     * 面单所有者的平台id
     */
    private String ownerStoreId;

    /**
     * 面单所有者的应用名称
     */
    private String ownerAppName;

    /**
     * 目标用户id
     */
    private String targetSellerId;
    /**
     * 目标用户nick
     */
    private String targetSellerNick;
    /**
     * 目标用户平台
     */
    private String targetStoreId;
    /**
     * 目标用户信息应用
     */
    private String targetAppName;

    /**
     * 面单分享备注（操作时的备注）
     */
    private String shareMemo;

    /**
     * 操作类型
     */
    private Integer operateType;

    /**
     * 更新前分享数量
     */
    private Long lastShareNum;

    /**
     * 更新后分享数量
     */
    private Long newShareNum;

    /**
     * 操作终端
     */
    private String operateTerminal;

    /**
     * 操作用户
     */
    private String operator;

    /**
     * 操作时间
     */
    private LocalDateTime operatorTime;

    /**
     * 面单服务商 CN PDD
     */
    private String provider;

    /**
     * 快递公司code
     */
    private String cpCode;

    /**
     * 物流服务商 业务类型
     */
    private Long cpType;

    /**
     * 物流网点code
     */
    private String branchCode;

    /**
     * 网点名称
     */
    protected String branchName;

    /**
     * 号段信息 菜鸟专有字段 其他平台为空字符串
     */
    private String segmentCode;

    /**
     * 发货地址 省
     */
    private String shippAddressProvince;

    /**
     * 发货地址 市
     */
    private String shippAddressCity;

    /**
     * 发货地址 区
     */
    private String shippAddressDistrict;

    /**
     * 发货地址 详细
     */
    private String shippAddressDetail;

    /**
     * 代理用户id
     */
    private String proxySellerId;
    /**
     * 代理用户id
     */
    private String proxySellerNick;
    /**
     * 代理用户平台
     */
    private String proxyStoreId;
    /**
     * 代理用户应用
     */
    private String proxyAppName;

    /**
     * 所有者店铺名
     */
    private String ownerMallName;

    /**
     * 被分享店铺名
     */
    private String targetMallName;

    /**
     * 代理店铺名
     */
    private String proxyMallName;

    /**
     * 业务员
     */
    private String salesman;

}
