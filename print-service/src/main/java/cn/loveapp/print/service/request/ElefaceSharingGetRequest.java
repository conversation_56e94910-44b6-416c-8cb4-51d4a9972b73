package cn.loveapp.print.service.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * @program: print-services-group
 * @description: 共享面单关系查询request
 * @author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @create: 2022/12/20 10:43
 **/
@Data
@ApiModel("共享面单关系查询请求体")
public class ElefaceSharingGetRequest {

    /**
     * 面单分享Id
     */
    @ApiModelProperty(value = "面单分享Id", required = true)
    @NotNull
    private String shardId;

}
