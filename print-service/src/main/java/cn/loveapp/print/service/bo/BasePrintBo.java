package cn.loveapp.print.service.bo;

import java.time.LocalDateTime;
import java.util.List;

import cn.loveapp.print.common.dto.RecipientDTO;
import cn.loveapp.print.service.request.BasePrintRequest;
import cn.loveapp.print.service.request.PrintTradeInfoDTO;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class BasePrintBo {
    /**
     * 订单信息
     */
    private List<PrintTradeInfoDTO> tradeInfoList;
    /**
     * 合单tid
     */
    private String mergeTid;
    /**
     * 是否拆单打印
     */
    private Boolean isSplit;
    /**
     * 订单类型
     */
    private Integer tradeType;
    /**
     * 打印流水号
     */
    private String serial;
    /**
     * 打印机
     */
    private String printer;
    /**
     * 打印数量
     */
    private Integer printCount;
    /**
     * 买家Nick
     */
    private String buyerNick;

    /**
     * 操作人
     */
    private String operatorName;

    /**
     * 打印时间
     */
    private LocalDateTime printTime;

    /**
     * 批次号
     */
    private String batchId;

    /**
     * 当前批次序号
     */
    private List<Integer> numbersInBatch;

    /**
     * 当前批次总数
     */
    private Integer batchTotals;

    /**
     * 收件人信息
     */
    private RecipientDTO recipient;

    /**
     * 发件人信息
     */
    private RecipientDTO sender;

    public void setBasePrintInfo(BasePrintRequest request) {
        this.tradeInfoList = request.getTradeInfoList();
        this.mergeTid = request.getMergeTid();
        this.isSplit = request.getIsSplit();
        this.tradeType = request.getTradeType();
        this.serial = request.getSerial();
        this.printer = request.getPrinter();
        this.printCount = request.getPrintCount();
        this.buyerNick = request.getBuyerNick();
        this.printTime = request.getPrintTime();
        this.numbersInBatch = request.getNumbersInBatch();
        this.batchId = request.getBatchId();
        this.batchTotals = request.getBatchTotals();

    }
}
