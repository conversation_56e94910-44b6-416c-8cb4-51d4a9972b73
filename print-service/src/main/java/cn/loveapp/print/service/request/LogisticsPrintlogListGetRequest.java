package cn.loveapp.print.service.request;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;

import javax.validation.constraints.NotNull;

import cn.loveapp.common.utils.DateUtil;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 */
@ApiModel
@Data
public class LogisticsPrintlogListGetRequest {
    private static final DateTimeFormatter DF = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    @ApiModelProperty(value = "页", required = true)
    @NotNull
    private Integer page = 1;

    @ApiModelProperty(value = "每页数量", required = true)
    @NotNull
    private Integer pageSize = 20;

    @ApiModelProperty(value = "下次查询时最大id,where id < maxId", required = true)
    @NotNull
    private Long maxId;

    /**
     * 订单号
     */
    @ApiModelProperty(value = "订单号")
    private String tid;

    /**
     * 批量订单号精确查询,单个和批量同时存在时，tidList优先级更高
     */
    @ApiModelProperty(value = "批量订单号精确查询")
    private List<String> tidList;

    @ApiModelProperty(value = "订单类型")
    private List<Integer> tradeType;

    /**
     * 收件人姓名
     */
    @ApiModelProperty(value = "收件人姓名")
    private String receiverName;

    /**
     * 收件人联系方式 电话/手机
     */
    @ApiModelProperty(value = "收件人联系方式 电话/手机")
    private String receiverContact;

    /**
     * 收货地址关键字
     */
    @ApiModelProperty(value = "收货地址关键字")
    private String receiverAddressKeyWord;

    /**
     * 发货人姓名
     */
    @ApiModelProperty(value = "发货人姓名")
    private String senderName;

    /**
     * 发货人联系方式 电话/手机
     */
    @ApiModelProperty(value = "发货人联系方式 电话/手机")
    private String senderContact;

    /**
     * 收货地址关键字
     */
    @ApiModelProperty(value = "收货地址关键字")
    private String senderAddressKeyWord;

    /**
     * 运单号
     */
    @ApiModelProperty(value = "运单号")
    private String waybillCode;

    /**
     * 运单号列表，单个和批量同时存在时，waybillCodeList优先级更高
     */
    private List<String> waybillCodeList;

    /**
     * 收件人地区列表
     */
    @ApiModelProperty(value = "收件人地区列表")
    private List<String> receiverRegionList;

    /**
     * 快递公司
     */
    @ApiModelProperty(value = "快递公司")
    private List<String> logisticsCompany;

    /**
     * 快递公司编号
     */
    @ApiModelProperty(value = "快递公司编号")
    private String cpCode;

    /**
     * 快递公司编号
     */
    @ApiModelProperty(value = "快递公司编号(多个)")
    private List<String> cpCodeList;

    /**
     * 打印类型 express-快递单 eleface-电子面单 deliver-发货单
     */
    @ApiModelProperty(value = "打印类型 express-快递单 eleface-电子面单 deliver-发货单")
    private String printType;

    /**
     * 面单服务商
     */
    @ApiModelProperty(value = "面单服务商")
    private String elefaceProvider;

    /**
     * 开始时间
     */
    @ApiModelProperty(value = "开始时间")
    private LocalDateTime startTime;

    /**
     * 结束时间
     */
    @ApiModelProperty(value = "结束时间")
    private LocalDateTime endTime;


    /**
     * 收件人城市列表
     */
    @ApiModelProperty(value = "收件人城市列表")
    private List<String> receiverCityList = new ArrayList<>();

    /**
     * 是否需要运单号去重，去重后数据取最新的一条
     */
    @ApiModelProperty(value = "是否需要运单号去重")
    private Boolean isDistinctWayBill;

    /**
     * 是否回收单号
     * true  :查询已回收单号
     * false :查询未回收单号
     * null(不传)：查询全部
     */
    @ApiModelProperty(value = "是否回收单号")
    private Boolean isCancelWayBill;

    /**
     * 订单集合和手机号查询是否是或的关系
     * true  :tid or mobile 两个字段或查询
     * false/null :tid and mobile 两个字段且查询
     */
    @ApiModelProperty(value = "订单集合和手机号查询是否是或的关系")
    private Boolean isTidListAndMobileQueryWithOR;

    /**
     * 操作人
     */
    private String operatorName;

    /**
     * 操作人
     */
    private String operatorStoreId;

    /**
     * 是否使用es进行查询
     */
    private Boolean isUseEsSearch;

    /**
     * 是否需要聚合统计
     */
    private Boolean isNeedStatistics;

    /**
     * 是否统计订单总数
     */
    private Boolean isStatisticsTidCount;

    /**
     * 是否统计物流公司总数
     */
    private Boolean isStatisticsLogisticsCompanyCount;

    /**
     * 是否统计电子面单号总数
     */
    private Boolean isWaybillCodeCount;

    /**
     * 是否统计一天
     */
    private Boolean isStatisticsToday;

    /**
     * 打印机名称
     */
    private String printerName;

    /**
     * 分销商爱用用户id列表
     */
    private List<String> ayDistributeSellerIdList;

    /**
     * 批次号
     */
    private List<String> batchIdList;

    /**
     * 批次序号
     */
    private List<Integer> numbersInBatch;


    public void setStartTime(String startTime) {
        this.startTime = DateUtil.parseString(startTime);
    }

    public void setEndTime(String endTime) {
        this.endTime = DateUtil.parseString(endTime);
    }
}
