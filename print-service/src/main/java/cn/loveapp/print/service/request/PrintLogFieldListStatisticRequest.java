package cn.loveapp.print.service.request;

import cn.loveapp.common.utils.DateUtil;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024-02-20 09:53
 * @description: 打印日志字段列表统计请求体
 */
@Data
public class PrintLogFieldListStatisticRequest {

    /**
     * 打印日志列表总数
     */
    private Integer printLogTotal;

    /**
     * 是否需要统计打印机列表
     */
    private Boolean isNeedStatisticsPrinterList;

    /**
     * 是否需要统计操作人列表
     */
    private Boolean isNeedStatisticsOperatorNameList;

    /**
     * 统计开始时间
     */
    private LocalDateTime startTime;

    /**
     * 统计结束时间
     */
    private LocalDateTime endTime;

    /**
     * 日志类型
     */
    private List<String> printType;

    public void setStartTime(String startTime) {
        this.startTime = DateUtil.parseString(startTime);
    }

    public void setEndTime(String endTime) {
        this.endTime = DateUtil.parseString(endTime);
    }

}
