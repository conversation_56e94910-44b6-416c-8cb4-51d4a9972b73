package cn.loveapp.print.service.request;
import cn.loveapp.print.service.annotation.TradeTypeValidation;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/***
 * <AUTHOR>
 * @Description 批量获取订单流水号绑定记录请求体
 * @Date 10:38 2023/9/12
 **/
@Data
@ApiModel(value = "批量获取订单流水号绑定记录请求体")
public class SerialBoundGetRequest {

    @ApiModelProperty(value = "批量流水号")
    @NotNull
    private List<SerialBound> serialBoundGetList;

    /**
     * 订单类型 0-普通订单 1-自由打印订单
     */
    @ApiModelProperty(value = "订单类型 0-普通订单 1-自由打印订单")
    @TradeTypeValidation
    private Integer tradeType;


    @Data
    public static class SerialBound {
        /**
         * 打印的订单信息
         */
        @ApiModelProperty(value = "打印的订单信息", required = true)
        @NotEmpty
        @Valid
        private List<PrintTradeInfoDTO> tradeInfoList;

        /**
         * 合单订单的主单tid
         */
        @ApiModelProperty(value = "合单订单的主单tid")
        private String mergeTid;
    }



}
