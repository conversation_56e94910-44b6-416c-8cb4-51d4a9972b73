package cn.loveapp.print.service.rpc;

import java.util.List;

import cn.loveapp.print.common.dto.TargetSellerInfo;
import cn.loveapp.print.common.dto.response.MultiShopsSessionCheckResponse;
import cn.loveapp.print.service.service.ParamsHandleService;
import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import cn.loveapp.common.constant.HttpMethodsConstants;
import cn.loveapp.common.dto.UserSessionInfo;
import cn.loveapp.common.web.CommonApiResponse;
import cn.loveapp.common.web.CommonApiStatus;
import cn.loveapp.print.api.response.LogisticsPrintlogResponseDTO;
import cn.loveapp.print.common.dto.UserMultiInfoDTO;
import cn.loveapp.print.common.service.UserCenterService;
import cn.loveapp.print.service.bo.UserInfoBo;
import cn.loveapp.print.service.config.PrintLogConfig;
import cn.loveapp.print.service.dto.LogisticsPrintlogQueryDTO;
import cn.loveapp.print.service.request.LogisticsPrintlogListGetRequest;
import cn.loveapp.print.service.request.PrintLogRequest;
import cn.loveapp.print.common.dto.BaseUserInfoDTO;
import cn.loveapp.print.service.request.SerialInvalidateRequest;
import cn.loveapp.print.service.service.PrintlogService;
import cn.loveapp.print.service.service.SerialService;
import cn.loveapp.uac.response.UserInfoResponse;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * 打印服务的RPC接口
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/printRpc")
@Api(tags = "流水号操作接口")
public class PrintRpcController {

    @Autowired
    private SerialService serialService;

    @Autowired
    private UserCenterService userCenterService;

    @Autowired
    private PrintlogService printlogService;

    @Autowired
    private PrintLogConfig printLogConfig;

    @Autowired
    private ParamsHandleService paramsHandleService;

    /**
     * 作废打印流水号（手动拆合单需要作废之前的流水号）
     *
     * @param request
     * @return
     */
    @RequestMapping("/serial.invalidate")
    @ApiOperation(value = "作废打印流水号（手动拆合单需要作废之前的流水号）", httpMethod = HttpMethodsConstants.POST)
    public CommonApiResponse serialInvalidate(@Validated SerialInvalidateRequest request) {
        List<String> subTids = request.getSubTids();
        Integer tradeType = request.getTradeType();

        String storeId = request.getStoreId();
        String sellerId = request.getSellerId();
        String appName = request.getAppName();

        UserInfoBo userInfoBo = new UserInfoBo();
        userInfoBo.setStoreId(storeId);
        userInfoBo.setSellerId(sellerId);
        userInfoBo.setAppName(appName);

        serialService.serialInvalidate(subTids, tradeType, userInfoBo);
        return CommonApiResponse.success();
    }

    /**
     * 查询物流单打印日志
     * @param request
     * @return
     */
    @ApiOperation(value = "查询物流单打印日志", httpMethod = HttpMethodsConstants.POST)
    @RequestMapping(value = "/logistics.printlog.list.get", method = {RequestMethod.GET, RequestMethod.POST})
    public CommonApiResponse<LogisticsPrintlogResponseDTO>
        logisticsPrintlogListGet(@RequestBody PrintLogRequest<LogisticsPrintlogListGetRequest> request) {

        BaseUserInfoDTO baseUserInfoDTO = request.getUserInfo();
        List<BaseUserInfoDTO> targetUserList = request.getTargetUserList();

        // 1. 先检查当前登录账号是否有效
        String sellerNick = baseUserInfoDTO.getSellerNick();
        String sellerId = baseUserInfoDTO.getSellerId();
        String storeId = baseUserInfoDTO.getStoreId();
        String appName = baseUserInfoDTO.getAppName();
        UserInfoResponse userInfo = userCenterService.getUserInfo(storeId, appName, sellerNick, sellerId);
        if (userInfo == null) {
            return CommonApiResponse.failed(CommonApiStatus.ForbiddenError.code(),
                CommonApiStatus.ForbiddenError.message());
        }

        UserSessionInfo sessionInfo = new UserSessionInfo();
        sessionInfo.setSellerId(userInfo.getSellerId());
        sessionInfo.setNick(userInfo.getSellerNick());
        sessionInfo.setStoreId(userInfo.getPlatformId());
        sessionInfo.setAppName(userInfo.getAppName());

        // 2. 存在多店信息则进行多店鉴权
        UserMultiInfoDTO userMultiInfoDTO = null;
        if (CollectionUtils.isNotEmpty(targetUserList)) {
            MultiShopsSessionCheckResponse multiShopsSessionCheckResponse =
                userCenterService.checkMultiShopsTopSession(targetUserList);
            if (multiShopsSessionCheckResponse == null) {
                return CommonApiResponse.failed(CommonApiStatus.ForbiddenError.code(),
                    CommonApiStatus.ForbiddenError.message());
            }

            List<BaseUserInfoDTO> tagerUserInfoList = multiShopsSessionCheckResponse.getTagerUserInfoList();
            userMultiInfoDTO = new UserMultiInfoDTO();
            List<TargetSellerInfo> targetSellerInfoList = Lists.newArrayList();
            for (BaseUserInfoDTO userInfoDTO : tagerUserInfoList) {
                TargetSellerInfo targetSellerInfo = new TargetSellerInfo();
                targetSellerInfo.setTargetSellerId(userInfoDTO.getSellerId());
                targetSellerInfo.setTargetNick(userInfoDTO.getSellerNick());
                targetSellerInfo.setTargetStoreId(userInfoDTO.getStoreId());
                targetSellerInfo.setTargetAppName(userInfoDTO.getAppName());
                targetSellerInfoList.add(targetSellerInfo);
            }

            userMultiInfoDTO.setTargetSellerList(targetSellerInfoList);
        }

        // 3. 处理入参
        LogisticsPrintlogListGetRequest parameters = request.getParameters();
        LogisticsPrintlogQueryDTO queryDTO = LogisticsPrintlogQueryDTO.of(parameters);
        paramsHandleService.handlePrintLogSearchListParams(queryDTO, sessionInfo);

        // 4. 若存在userMultiInfoDTO 则只搜索目标用户
        List<UserInfoBo> userInfoBoList = UserInfoBo.of(sessionInfo, userMultiInfoDTO);
        // 新逻辑查es
        return CommonApiResponse.success(printlogService.logisticsPrintLogListGetByESSearch(queryDTO, userInfoBoList));
    }
}
