package cn.loveapp.print.service.request;

import cn.loveapp.print.service.bo.ElefaceSharingRelationQueryBo;
import lombok.Data;

/**
 * 获取分享面单记录列表
 *
 * <AUTHOR>
 * @Date 2024/7/25 9:33 PM
 */
@Data
public class MultiSharingToOthersListRequest extends ElefaceSharingRelationQueryBo {

    /**
     * 分享状态
     */
    private Integer status;

    /**
     * 模糊搜索店铺字段-分单小程序单搜索框使用（分享者、面单账号、被分享者、备注）
     */
    private String fuzzySearchStr;

    /**
     * 页
     */
    private Integer page;

    /**
     * 每页数量
     */
    private Integer pageSize;

}
