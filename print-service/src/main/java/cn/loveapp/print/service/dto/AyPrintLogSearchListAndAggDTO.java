package cn.loveapp.print.service.dto;

import java.util.List;

import cn.loveapp.print.common.entity.AyPrintLogSearchEs;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024-01-23 17:14
 * @description: ES搜索结果和聚合结果DTO
 */
@Data
public class AyPrintLogSearchListAndAggDTO {

    /**
     * 结果总数
     */
    private Long totalResults;

    /**
     * 从ES查询出来的结果
     */
    private List<AyPrintLogSearchEs> itemSearchESList;

    /**
     * tid总数
     */
    private Long tidTotal;

    /**
     * 物流公司总数
     */
    private Long logisticsCompanyTotal;

    /**
     * 电子面单号总数
     */
    private Long waybillCodeTotal;

    /**
     * 面单取号后打印计数
     */
    private Long printAfterGetWaybillCount;

    /**
     * 面单取号后未打印计数
     */
    private Long notPrintAfterGetWaybillCount;

    /**
     * 面单取号后发货计数
     */
    private Long sendGoodAfterGetWaybillCount;

    /**
     * 面单取号后未发货计数
     */
    private Long notSendGoodAfterGetWaybillCount;

    /**
     * 已回收单号
     */
    private Long cancelAfterGetWaybillCount;
}
