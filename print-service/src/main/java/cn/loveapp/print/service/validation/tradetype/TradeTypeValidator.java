package cn.loveapp.print.service.validation.tradetype;

import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;

import cn.loveapp.print.common.constant.TradeTypeConstant;
import cn.loveapp.print.service.annotation.TradeTypeValidation;

/**
 * 订单类型判断的注解
 *
 * <AUTHOR>
 */
public class TradeTypeValidator implements ConstraintValidator<TradeTypeValidation, Integer> {
    @Override
    public void initialize(TradeTypeValidation constraintAnnotation) {

    }

    @Override
    public boolean isValid(Integer integer, ConstraintValidatorContext constraintValidatorContext) {
        // 判断tradeType是否有效
        if (null != integer) {
            return TradeTypeConstant.getAll().contains(integer);
        }
        return true;
    }
}
