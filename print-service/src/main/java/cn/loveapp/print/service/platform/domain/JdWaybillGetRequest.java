package cn.loveapp.print.service.platform.domain;

import com.jd.open.api.sdk.domain.wujiemiandan.WaybillReceiveOpenApi.request.create.WaybillReceiveRequest;
import lombok.Data;

/**
 * <AUTHOR>
 * @Date 2025/2/27 14:23
 */
@Data
public class JdWaybillGetRequest {

    /**
     * 操作id
     */
    private String objectId;

    /**
     * 取号请求体
     */
    private WaybillReceiveRequest request;

    /**
     * 是否京东商城订单（pop订单）。枚举值：1—京东商城订单；0—其他平台订单。
     * 不填或者填写0则认为非京东商城订单。长度1
     */
    private Integer popFlag;
}
