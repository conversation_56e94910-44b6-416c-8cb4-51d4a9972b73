package cn.loveapp.print.service.export;

import cn.loveapp.common.utils.LoggerHelper;
import cn.loveapp.common.web.CommonApiResponse;
import cn.loveapp.common.web.CommonApiStatus;
import cn.loveapp.print.api.dto.ElefaceSharingRelationDTO;
import cn.loveapp.print.api.dto.TargetPrintUserInfo;
import cn.loveapp.print.api.dto.TradeLogisticsBindingHistoryResponseDTO;
import cn.loveapp.print.api.dto.WaybillOperatelogResponseDTO;
import cn.loveapp.print.api.request.*;
import cn.loveapp.print.api.response.CountPrintLogResponse;
import cn.loveapp.print.api.response.ElefaceIsCancelGetInnerResponse;
import cn.loveapp.print.api.response.LogisticsPrintlogResponseDTO;
import cn.loveapp.print.api.response.SharingBatchCancelResponse;
import cn.loveapp.print.api.service.PrintInnerApiService;
import cn.loveapp.print.service.bo.UserInfoBo;
import cn.loveapp.print.service.config.PrintLogConfig;
import cn.loveapp.print.service.convert.CommonConvertMapper;
import cn.loveapp.print.service.dto.LogisticsPrintlogQueryDTO;
import cn.loveapp.print.service.service.ElefaceWaybillService;
import cn.loveapp.print.service.service.PrintApiService;
import cn.loveapp.print.service.service.PrintlogService;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.List;

/**
 * print 服务 内部接口服务 实现类
 *
 * <AUTHOR>
 */
@RestController
public class PrintInnerApiServiceImpl implements PrintInnerApiService {

	private static final LoggerHelper LOGGER = LoggerHelper.getLogger(PrintInnerApiServiceImpl.class);

	@Autowired
	private PrintApiService printApiService;

    @Autowired
    private PrintLogConfig printLogConfig;

    @Autowired
    private PrintlogService printlogService;

    @Autowired
    private ElefaceWaybillService elefaceWaybillService;

	@Override
	public CommonApiResponse<ElefaceSharingRelationDTO> elefaceSharingCreate(@RequestBody @Validated ElefaceSharingCreateInnerRequest request) {
		return printApiService.elefaceSharingCreate(request);
	}

    @Override
    public CommonApiResponse<ElefaceSharingRelationDTO> multiElefaceSharingCreate(@RequestBody @Validated ElefaceSharingCreateInnerRequest request) {
        return printApiService.multiElefaceSharingCreate(request);
    }

	@Override
	public CommonApiResponse elefaceShippaddressGetall(@RequestBody @Validated ElefaceShippaddressGetallInnerRequest request) {
		return printApiService.elefaceShippaddressGetall(request);
	}

    @Override
    public CommonApiResponse multiElefaceShippaddressGetall(@RequestBody @Validated ElefaceShippaddressGetallInnerRequest request) {
        return printApiService.multiElefaceShippaddressGetall(request);
    }

	@Override
	public CommonApiResponse<Boolean> queryOrderHasPrintHistory(OrderHasprintGetInnerRequest request) {
		return printApiService.queryOrderHasPrintHistory(request);
	}

	@Override
	public CommonApiResponse waybillOperateLogSave(WaybillOperateLogSaveRequest request) {
		return printApiService.waybillOperatelogSave(request);
	}

    @Override
    public CommonApiResponse<CountPrintLogResponse> countPrintLogByPrintType(CountPrintLogRequest request) {
        return printApiService.countPrintLogByPrintType(request);
    }

    @Override
    public CommonApiResponse<LogisticsPrintlogResponseDTO> printLogListGet(PrintLogListGetInnerRequest request) {
        LOGGER.logInfo("获取打印日志列表入参：" + JSONObject.toJSONString(request));
        try {
            if (request.getPageSize() != null && request.getPageSize() > printLogConfig.getPrintLogListMaxSize()) {
                throw new IllegalArgumentException("每页请求数量不能大于：" + printLogConfig.getPrintLogListMaxSize());
            }

            LogisticsPrintlogQueryDTO queryDTO = LogisticsPrintlogQueryDTO.of(request);
            queryDTO.setFields(printLogConfig.getPrintLogListFields());

            UserInfoBo userInfoBo = new UserInfoBo();
            userInfoBo.setSellerId(request.getSellerId());
            userInfoBo.setStoreId(request.getStoreId());
            userInfoBo.setAppName(request.getAppName());
            return CommonApiResponse
                .success(printlogService.logisticsPrintLogListGetByESSearch(queryDTO, Lists.newArrayList(userInfoBo)));
        } catch (Exception e) {
            LOGGER.logError("接口请求异常: " + e.getMessage(), e);
            return new CommonApiResponse(CommonApiStatus.Failed.code(), CommonApiStatus.Failed.message(),
                CommonApiStatus.Failed.code(), e.getMessage(), null);
        }
    }


    @Override
    public CommonApiResponse<List<ElefaceIsCancelGetInnerResponse>> elefaceIsCancelGet(ElefaceIsCancelGetInnerRequest request) {
        return printApiService.elefaceIsCancelGet(request);
    }

    @Override
    public CommonApiResponse<List<ElefaceSharingRelationDTO>> sharingBatchGet(SharingBatchGetRequest request) {
        return printApiService.sharingBatchGet(request);
    }

    @Override
    public CommonApiResponse<SharingBatchCancelResponse> sharingBatchCancel(SharingBatchCancelRequest request) {
        return printApiService.sharingBatchCancel(request);
    }

    @Override
    public CommonApiResponse<String> sharingTopUpNum(ShareTopUpNumRequest request) {
        return printApiService.sharingTopUpNum(request);
    }

    @Override
    public CommonApiResponse<String> sharingQuantityModify(ShareTopUpNumRequest request) {
        return printApiService.sharingQuantityModify(request);
    }

    @Override
    public CommonApiResponse<String> sharingRecovery(ShareRecoveryRequest request) {
        return printApiService.sharingRecovery(request);
    }

    @Override
    public CommonApiResponse<WaybillOperatelogResponseDTO> waybillShareOperateLogListGet(WaybillShareOperateLogListGetRpcRequest request) {
        return printApiService.waybillShareOperateLogListGet(request);
    }

    @Override
    public CommonApiResponse<TradeLogisticsBindingHistoryResponseDTO> logisticsBindingHistoryGetBatch(LogisticsBindHistoryGetApiRequest request) {
        TradeLogisticsBindingHistoryResponseDTO responseDTO = new TradeLogisticsBindingHistoryResponseDTO();
        List<TradeLogisticsBindingHistoryResponseDTO.TradeLogisticsBindingHistoryDTO> tradeLogisticsBindingHistoryList = new ArrayList<>();
        Integer tradeType = request.getTradeType();
        Boolean needPrintData = request.getNeedPrintData();
        for (LogisticsBindHistoryGetApiRequest.TargetPrintInfoDTO targetPrintInfo : request.getTargetPrintInfoList()) {
            if (printLogConfig.getPrintLogListDegradePlatforms().contains(targetPrintInfo.getStoreId())) {
                //降级开关
                continue;
            }
            UserInfoBo targetUser = new UserInfoBo();
            targetUser.setSellerNick(targetPrintInfo.getSellerNick());
            targetUser.setSellerId(targetPrintInfo.getSellerId());
            targetUser.setStoreId(targetPrintInfo.getStoreId());
            targetUser.setAppName(targetPrintInfo.getAppName());
            TradeLogisticsBindingHistoryResponseDTO targetResponse = printlogService.queryTradeLogisticsBindingHistoryBatch(targetPrintInfo.getTidList(), tradeType, needPrintData, targetUser);
            if (CollectionUtils.isEmpty(targetResponse.getTradeLogisticsBindingHistoryList())) {
                continue;
            }
            tradeLogisticsBindingHistoryList.addAll(targetResponse.getTradeLogisticsBindingHistoryList());
        }
        responseDTO.setTradeLogisticsBindingHistoryList(tradeLogisticsBindingHistoryList);
        return CommonApiResponse.success(responseDTO);
    }

    @Override
    public CommonApiResponse<WaybillOperatelogResponseDTO> multiWaybillOperateLogListGet(MultiWaybillOperateLogListGetInnerRequest request) {
        List<TargetPrintUserInfo> userInfoList = request.getUserInfoList();
        if (request.getPage() <= 0L || request.getPageSize() < 0L) {
            LOGGER.logError("参数异常: pageNo和pageSize必须为正数");
            return CommonApiResponse.failed(CommonApiStatus.RequestParamError.code(), "参数异常: pageNo和pageSize必须为正数", null);
        }

        if (CollectionUtils.isEmpty(userInfoList)) {
            LOGGER.logError("参数异常: 用户信息参数缺失");
            return CommonApiResponse.failed(CommonApiStatus.RequestParamError.code(), "参数异常: 用户信息参数缺失", null);
        }

        List<UserInfoBo> userInfoBoList = CommonConvertMapper.INSTANCE.toUserInfoBo(userInfoList);

        try {
            WaybillOperatelogResponseDTO responseDTO = elefaceWaybillService.multiWaybillOperateLogListGet(request, userInfoBoList);
            return CommonApiResponse.success(responseDTO);
        } catch (Exception e) {
            LOGGER.logError("查询异常：" + e.getMessage(), e);
            return CommonApiResponse.failed(CommonApiStatus.ServerError, null);
        }
    }
}
