package cn.loveapp.print.service.bo;

import cn.loveapp.print.service.request.ExpressPrintRequest;
import lombok.Data;


/**
 * <AUTHOR>
 */
@Data
public class ExpressPrintBo extends BasePrintBo {
    /**
     * 物流公司
     */
    private String logisticsCompany;
    /**
     * 打印模板
     */
    private String printModule;
    /**
     * 快递单打印信息
     */
    private String printData;
    /**
     * 快递单模板图片
     */
    private String expressImage;

    /**
     * 操作人
     */
    private String operatorName;

    public static ExpressPrintBo of(ExpressPrintRequest request) {
        ExpressPrintBo expressPrintBo = new ExpressPrintBo();
        expressPrintBo.setBasePrintInfo(request);
        expressPrintBo.setLogisticsCompany(request.getLogisticsCompany());
        expressPrintBo.setRecipient(request.getRecipient());
        expressPrintBo.setPrintModule(request.getPrintModule());
        expressPrintBo.setPrintData(request.getPrintData());
        expressPrintBo.setExpressImage(request.getExpressImage());
        expressPrintBo.setOperatorName(request.getOperatorName());
        expressPrintBo.setSender(request.getSender());
        return expressPrintBo;
    }
}
