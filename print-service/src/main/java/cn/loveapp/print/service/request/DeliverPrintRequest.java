package cn.loveapp.print.service.request;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;

import cn.loveapp.print.common.dto.RecipientDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
@ApiModel(value = "发货单打印请求")
public class DeliverPrintRequest extends BasePrintRequest {

    /**
     * 收件人信息
     */
    @ApiModelProperty(value = "收件人信息", required = true)
    @NotNull
    @Valid
    private RecipientDTO recipient;

    /**
     * 发货人信息
     */
    @ApiModelProperty(value = "发货人信息")
    private RecipientDTO sender;

    /**
     * 打印模板
     */
    @ApiModelProperty(value = "打印模板")
    private String printModule;

    /**
     * 打印内容
     */
    @ApiModelProperty(value = "打印内容")
    private String printData;
}
