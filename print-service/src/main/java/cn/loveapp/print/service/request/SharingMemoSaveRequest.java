package cn.loveapp.print.service.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;

/**
 * 解除分享给我的面单共享关系 request
 *
 * <AUTHOR>
 */
@ApiModel
@Data
public class SharingMemoSaveRequest {
    /**
     * 面单分享Id
     */
    @ApiModelProperty(value = "面单分享Id", required = true)
    @NotEmpty
    private String shareId;

    /**
     * 面单分享备注
     */
    @ApiModelProperty(value = "面单分享备注")
    private String shareMemo;

    /**
     * 业务员
     */
    @ApiModelProperty(value = "业务员")
    private String salesman;

}
