package cn.loveapp.print.service.request;

import java.util.List;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

import cn.loveapp.print.service.annotation.TradeTypeValidation;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 作废流水号操作的Request
 *
 * <AUTHOR>
 */
@Data
@ApiModel
public class SerialInvalidateRequest {

    /**
     * 要作废的流水号
     */
    @NotEmpty
    @ApiModelProperty(value = "要作废的流水号", required = true)
    private List<String> subTids;

    /**
     * 订单类型 0-普通订单 1-自由打印订单
     */
    @TradeTypeValidation
    @NotNull
    @ApiModelProperty(value = "订单类型 0-普通订单 1-自由打印订单", required = true)
    private Integer tradeType;

    /**
     * 商家id
     */
    @NotEmpty
    @ApiModelProperty(value = "商家id", required = true)
    private String sellerId;

    /**
     * 商家平台
     */
    @NotEmpty
    @ApiModelProperty(value = "商家平台", required = true)
    private String storeId;

    /**
     * 插件名称
     */
    @NotEmpty
    private String appName;
}
