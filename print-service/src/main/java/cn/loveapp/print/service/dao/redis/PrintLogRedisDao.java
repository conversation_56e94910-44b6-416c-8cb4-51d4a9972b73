package cn.loveapp.print.service.dao.redis;
import cn.loveapp.common.utils.LoggerHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Repository;
import org.springframework.util.StringUtils;
import javax.validation.constraints.NotNull;
import java.util.Arrays;
import java.util.List;

/***
 * <AUTHOR>
 * @Description 打印日志Redis 数据操作层
 * @Date 17:31 2023/10/23
 **/
@Repository
public class PrintLogRedisDao {
    private static final LoggerHelper LOGGER = LoggerHelper.getLogger(PrintLogRedisDao.class);
    /**
     * 订单打印记录的redis缓存前缀key
     */
    private static final String TRADE_PRINTLOG_KEY = "trade:printlog:";

    private static final String TRADE_PRINT_TYPE = "printType";

    private static final String TRADE_PRINT_TYPE_SEPARATOR = ",";

    @Autowired
    private StringRedisTemplate stringRedisTemplate;

    /**
     * 生成Redis订单打印记录key
     *
     * @param tradeType
     * @param storeId
     * @param sellerId
     * @param appName
     * @return
     */
    private String initPrintLogKey(@NotNull String tradeType, @NotNull String storeId, @NotNull String sellerId, @NotNull String appName) {
        return TRADE_PRINTLOG_KEY + tradeType + ":" + sellerId + ":" + storeId + ":" + appName;
    }

    /**
     * 获取用户的打印类型
     * @param sellerId
     * @param appName
     * @param storeId
     * @return
     */
    public List<String> getUserPrintType(String sellerId, String appName, String storeId) {
        try {
            String key = initPrintLogKey(TRADE_PRINT_TYPE, storeId, sellerId, appName);
            String printTypeListStr = stringRedisTemplate.opsForValue().get(key);
            if (StringUtils.isEmpty(printTypeListStr)) {
                return null;
            }
            String[] printTypeArr = printTypeListStr.split(TRADE_PRINT_TYPE_SEPARATOR);
            return Arrays.asList(printTypeArr);
        } catch (Exception e) {
            LOGGER.logError("缓存获取失败：" + e.getMessage(), e);
        }
        return null;
    }

    /**
     * 设置用户的打印类型
     * @param printTypeDTOList
     * @param sellerId
     * @param appName
     * @param storeId
     */
    public void setUserPrintType(List<String> printTypeDTOList, String sellerId, String appName, String storeId) {
        try {
            String key = initPrintLogKey(TRADE_PRINT_TYPE, storeId, sellerId, appName);
            stringRedisTemplate.opsForValue().set(key, String.join(TRADE_PRINT_TYPE_SEPARATOR, printTypeDTOList));
        } catch (Exception e) {
            LOGGER.logError("缓存写入失败：" + e.getMessage(), e);
        }
    }

    /**
     * 删除用户的打印类型
     * @param sellerId
     * @param appName
     * @param storeId
     */
    public void delUserPrintType(String sellerId, String appName, String storeId) {
        try {
            String key = initPrintLogKey(TRADE_PRINT_TYPE, storeId, sellerId, appName);
            stringRedisTemplate.delete(key);
        } catch (Exception e) {
            LOGGER.logError("缓存删除失败：" + e.getMessage(), e);
        }
    }
}
