package cn.loveapp.print.service.response;

import cn.loveapp.print.api.dto.TargetPrintUserInfo;

import lombok.Data;

import java.util.List;

/**
 * 获取我共享过面单的被分享者店铺列表 响应体
 *
 * <AUTHOR>
 * @Date 2024/9/25 2:23 PM
 */
@Data
public class MultiProxySharingToOthersTargetListResponse {


    /**
     * 总数
     */
    private Integer totalResult;

    /**
     * 用户数据
     */
    private List<TargetPrintUserInfo> targetSellerList;

}
