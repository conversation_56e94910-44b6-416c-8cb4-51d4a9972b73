package cn.loveapp.print.service.dao.es;

import static org.elasticsearch.index.query.QueryBuilders.*;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

import cn.loveapp.print.common.constant.PrintTypeConstant;
import cn.loveapp.print.common.service.AesEncryptionService;
import cn.loveapp.print.common.utils.ElasticsearchUtil;
import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.elasticsearch.action.search.SearchType;
import org.elasticsearch.client.RestHighLevelClient;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.search.aggregations.Aggregation;
import org.elasticsearch.search.aggregations.AggregationBuilders;
import org.elasticsearch.search.aggregations.Aggregations;
import org.elasticsearch.search.aggregations.metrics.CardinalityAggregationBuilder;
import org.elasticsearch.search.aggregations.metrics.NumericMetricsAggregation;
import org.elasticsearch.search.sort.SortBuilders;
import org.elasticsearch.search.sort.SortOrder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Primary;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.elasticsearch.core.ElasticsearchOperations;
import org.springframework.data.elasticsearch.core.ResultsMapper;
import org.springframework.data.elasticsearch.core.query.NativeSearchQueryBuilder;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Repository;

import cn.loveapp.print.common.config.es.ElasticsearchConfiguration;
import cn.loveapp.print.common.constant.EsFields;
import cn.loveapp.print.common.dao.es.CommonAyPrintLogSearchESDao;
import cn.loveapp.print.common.entity.AyPrintLogSearchEs;
import cn.loveapp.print.service.bo.UserInfoBo;
import cn.loveapp.print.service.config.PrintLogConfig;
import cn.loveapp.print.service.dto.AyPrintLogListSearchDTO;
import cn.loveapp.print.service.dto.AyPrintLogSearchListAndAggDTO;

/**
 * <AUTHOR>
 * @date 2024-01-21 15:09
 * @description: 打印日志搜索
 */
@Primary
@Repository
public class ElasticsearchAyPrintLogQueryDao extends CommonAyPrintLogSearchESDao {

    /**
     * 打印日志默认全部类型
     */
    protected static final List<String> DEFAULT_PRINT_TYPES =
        Lists.newArrayList(PrintTypeConstant.EXPRESS, PrintTypeConstant.ELEFACE);

    /**
     * 聚合查询在内存中映射处理参数
     */
    protected static final String EXECUTION_HINT_MAP = "map";
    /**
     * tid去ES聚合字段名n
     */
    public static final String AGG_TID = "distinctTid";
    /**
     * 物流公司去ES聚合字段名n
     */
    public static final String AGG_LOGISTICS_COMPANY = "distinctLogisticsCompany";
    /**
     * 运单号去ES聚合字段名n
     */
    public static final String AGG_DISTINCT_WAYBILL_CODE = "distinctWaybillCode";

    @Autowired
    private AesEncryptionService aesEncryptionService;

    @Autowired
    protected PrintLogConfig printLogConfig;

    @Autowired
    private StringRedisTemplate redisTemplate;

    public ElasticsearchAyPrintLogQueryDao(ElasticsearchConfiguration configuration, ElasticsearchOperations operations,
        RestHighLevelClient client, ResultsMapper mapper) {
        super(configuration, operations, client, mapper);
    }

    /**
     * 打印日志列表分页查询
     *
     * @param ayPrintLogListSearchDTO
     * @param userInfoBoList
     * @return
     */
    public AyPrintLogSearchListAndAggDTO ayPrintLogListGetQueryByLimit(AyPrintLogListSearchDTO ayPrintLogListSearchDTO,
        List<UserInfoBo> userInfoBoList) {
        AyPrintLogSearchListAndAggDTO ayPrintLogSearchListAndAggDTO = new AyPrintLogSearchListAndAggDTO();
        NativeSearchQueryBuilder builder = ayPrintLogListGetBuilder(userInfoBoList);
        BoolQueryBuilder boolQuerySearchList = createBoolQuerySearchList(ayPrintLogListSearchDTO, userInfoBoList);
        builder.withQuery(filterQuery(boolQuerySearchList));
        builder.withSort(SortBuilders.fieldSort(EsFields.printTime).order(SortOrder.DESC));
        builder
            .withPageable(PageRequest.of(ayPrintLogListSearchDTO.getPage() - 1, ayPrintLogListSearchDTO.getPageSize()));
        Page<AyPrintLogSearchEs> ayPrintLogSearchEs = pageQuery(builder);

        ayPrintLogSearchListAndAggDTO.setTotalResults(ayPrintLogSearchEs.getTotalElements());
        ayPrintLogSearchListAndAggDTO.setItemSearchESList(ayPrintLogSearchEs.getContent());
        return ayPrintLogSearchListAndAggDTO;
    }

    /**
     * 打印日志列表计数统计
     *
     * @param ayPrintLogListSearchDTO
     * @param userInfoBoList
     */
    public AyPrintLogSearchListAndAggDTO printLogListCountStatistics(AyPrintLogListSearchDTO ayPrintLogListSearchDTO,
        List<UserInfoBo> userInfoBoList) {

        AyPrintLogSearchListAndAggDTO ayPrintLogSearchListAndAggDTO = new AyPrintLogSearchListAndAggDTO();
        NativeSearchQueryBuilder builder = ayPrintLogListGetBuilder(userInfoBoList);
        BoolQueryBuilder boolQuerySearchList = createBoolQuerySearchList(ayPrintLogListSearchDTO, userInfoBoList);
        builder.withQuery(filterQuery(boolQuerySearchList));

        if (BooleanUtils.isTrue(ayPrintLogListSearchDTO.getIsStatisticsToday())) {
            LocalDateTime startTime = LocalDateTime.now().withHour(0).withMinute(0).withSecond(0);
            ayPrintLogListSearchDTO.setStartTime(startTime);
            ayPrintLogListSearchDTO.setEndTime(startTime.plusDays(1));
            BoolQueryBuilder aggBoolQuerySearchList =
                createBoolQuerySearchList(ayPrintLogListSearchDTO, userInfoBoList);
            builder.withQuery(filterQuery(aggBoolQuerySearchList));
        }

        // 订单数聚合
        CardinalityAggregationBuilder cardinalityTid = null;
        if (BooleanUtils.isTrue(ayPrintLogListSearchDTO.getIsStatisticsTidCount())) {
            cardinalityTid = AggregationBuilders.cardinality(AGG_TID).field(EsFields.tid);
        }

        // 物流公司数量聚合
        CardinalityAggregationBuilder cardinalityLogisticsCompany = null;
        if (BooleanUtils.isTrue(ayPrintLogListSearchDTO.getIsStatisticsLogisticsCompanyCount())) {
            cardinalityLogisticsCompany =
                AggregationBuilders.cardinality(AGG_LOGISTICS_COMPANY).field(EsFields.logisticsCompany);
        }

        // 物流单号数量聚合
        CardinalityAggregationBuilder cardinalityWaybillCode = null;
        if (BooleanUtils.isTrue(ayPrintLogListSearchDTO.getIsWaybillCodeCount())) {
            cardinalityWaybillCode =
                AggregationBuilders.cardinality(AGG_DISTINCT_WAYBILL_CODE).field(EsFields.waybillNumber);
        }

        Aggregations aggregations =
            aggs(builder.build(), cardinalityTid, cardinalityLogisticsCompany, cardinalityWaybillCode);

        for (Aggregation aggregation : aggregations.asList()) {
            if (AGG_TID.equals(aggregation.getName())) {
                NumericMetricsAggregation.SingleValue singleValue = (NumericMetricsAggregation.SingleValue) aggregation;
                ayPrintLogSearchListAndAggDTO.setTidTotal((long) singleValue.value());
            } else if (AGG_LOGISTICS_COMPANY.equals(aggregation.getName())) {
                NumericMetricsAggregation.SingleValue singleValue = (NumericMetricsAggregation.SingleValue) aggregation;
                ayPrintLogSearchListAndAggDTO.setLogisticsCompanyTotal((long) singleValue.value());
            } else if (AGG_DISTINCT_WAYBILL_CODE.equals(aggregation.getName())) {
                NumericMetricsAggregation.SingleValue singleValue = (NumericMetricsAggregation.SingleValue) aggregation;
                ayPrintLogSearchListAndAggDTO.setWaybillCodeTotal((long) singleValue.value());
            }
        }
        return ayPrintLogSearchListAndAggDTO;
    }

    /**
     * 打印日志计数查询
     *
     * @param ayPrintLogListSearchDTO
     * @param userInfoBoList
     * @return
     */
    public Integer queryTotalCount(AyPrintLogListSearchDTO ayPrintLogListSearchDTO, List<UserInfoBo> userInfoBoList) {
        BoolQueryBuilder boolQueryBuilder = createBoolQuerySearchList(ayPrintLogListSearchDTO, userInfoBoList);
        NativeSearchQueryBuilder builder = ayPrintLogListGetBuilder(userInfoBoList);
        builder.withQuery(filterQuery(boolQueryBuilder));
        return (int)count(builder.build());
    }

    /**
     * 生成打印日志搜索索引条件
     *
     * @param userInfoDTO
     * @return
     */
    private NativeSearchQueryBuilder ayPrintLogListGetBuilder(UserInfoBo userInfoDTO) {
        NativeSearchQueryBuilder builder = new NativeSearchQueryBuilder().withSearchType(SearchType.QUERY_THEN_FETCH)
            .withFields(EsFields.sellerId, EsFields.ayPrintLogId);
        builder.withIndices(getIndexName());
        builder.withRoute(userInfoDTO.getSellerId());
        return builder;
    }

    /**
     * 生成打印日志多店查询Builder
     *
     * @param userInfoBoList
     * @return
     */
    protected NativeSearchQueryBuilder ayPrintLogListGetBuilder(List<UserInfoBo> userInfoBoList) {
        List<String> routerArr = new ArrayList<>(userInfoBoList.size());

        for (UserInfoBo userInfoBo : userInfoBoList) {
            routerArr.add(userInfoBo.getSellerId());
        }
        String routerArrStr = StringUtils.join(routerArr, ",");

        NativeSearchQueryBuilder builder = new NativeSearchQueryBuilder().withSearchType(SearchType.QUERY_THEN_FETCH)
            .withFields(EsFields.sellerId, EsFields.ayPrintLogId, EsFields.mergeSubPrintLogId);
        builder.withIndices(getIndexName());
        builder.withRoute(routerArrStr);
        return builder;
    }

    /**
     * 打印日志分页查询
     *
     * @param builder
     * @return
     */
    private Page<AyPrintLogSearchEs> pageQuery(NativeSearchQueryBuilder builder) {
        if (CollectionUtils.isEmpty(builder.build().getIndices())) {
            builder.withIndices(AyPrintLogSearchEs.INDEX_NAME_PREFIX + "*");
        }
        return operations.queryForPage(builder.build(), AyPrintLogSearchEs.class);
    }


    /**
     * 日志列表搜索条件
     *
     * @param ayPrintLogListSearchDTO
     * @param userInfoBoList
     * @return
     */
    private BoolQueryBuilder createBoolQuerySearchList(AyPrintLogListSearchDTO ayPrintLogListSearchDTO,
        List<UserInfoBo> userInfoBoList) {
        BoolQueryBuilder queryCondition = boolQuery();

        queryCondition.mustNot(termsQuery(EsFields.isDeleted, true));

        if (ayPrintLogListSearchDTO == null) {
            return queryCondition;
        }

        List<String> ayUserIds = Lists.newArrayList();
        for (UserInfoBo userInfoBo : userInfoBoList) {
            ayUserIds.add(AyPrintLogSearchEs.createAyUserId(userInfoBo.getSellerId(), userInfoBo.getStoreId(),
                userInfoBo.getAppName()));
        }

        if (!CollectionUtils.isEmpty(ayUserIds)) {
            queryCondition.must(termsQuery(EsFields.ayUserId, ayUserIds));
        }

        if (StringUtils.isNotBlank(ayPrintLogListSearchDTO.getPrintType())) {
            queryCondition.must(termsQuery(EsFields.printType, ayPrintLogListSearchDTO.getPrintType()));
        } else {
            queryCondition.must(termsQuery(EsFields.printType, DEFAULT_PRINT_TYPES));
        }

        if (!CollectionUtils.isEmpty(ayPrintLogListSearchDTO.getTidList())) {
            queryCondition.must(termsQuery(EsFields.tid, ayPrintLogListSearchDTO.getTidList()));
        }

        if (CollectionUtils.isNotEmpty(ayPrintLogListSearchDTO.getTradeType())) {
            queryCondition.must(termsQuery(EsFields.tradeType, ayPrintLogListSearchDTO.getTradeType()));
        }

        if (StringUtils.isNotBlank(ayPrintLogListSearchDTO.getReceiverName())) {
            queryCondition.must(
                boolQuery().should(matchPhraseQuery(EsFields.receiverName, ayPrintLogListSearchDTO.getReceiverName()))
                    .should(termsQuery(EsFields.encryptReceiverName,
                        aesEncryptionService.encryptForTrade(ayPrintLogListSearchDTO.getReceiverName()))));
        }

        if (StringUtils.isNotBlank(ayPrintLogListSearchDTO.getReceiverContact())) {
            queryCondition.must(boolQuery()
                .should(matchPhraseQuery(EsFields.receiverPhone, ayPrintLogListSearchDTO.getReceiverContact()))
                .should(matchPhraseQuery(EsFields.receiverMobile, ayPrintLogListSearchDTO.getReceiverContact())));
        }

        if (StringUtils.isNotBlank(ayPrintLogListSearchDTO.getReceiverAddressKeyWord())) {
            queryCondition
                .must(matchPhraseQuery(EsFields.receiverAddress,
                    ElasticsearchUtil.splitAlphanumeric(ayPrintLogListSearchDTO.getReceiverAddressKeyWord())));
        }

        if (StringUtils.isNotBlank(ayPrintLogListSearchDTO.getSenderName())) {
            queryCondition.must(matchPhraseQuery(EsFields.senderName, ayPrintLogListSearchDTO.getSenderName()));
        }

        if (StringUtils.isNotBlank(ayPrintLogListSearchDTO.getSenderContact())) {
            queryCondition.must(boolQuery()
                .should(matchPhraseQuery(EsFields.senderPhone, ayPrintLogListSearchDTO.getSenderContact()))
                .should(matchPhraseQuery(EsFields.senderMobile, ayPrintLogListSearchDTO.getSenderContact())));
        }

        if (StringUtils.isNotBlank(ayPrintLogListSearchDTO.getSenderAddressKeyWord())) {
            queryCondition.must(matchPhraseQuery(EsFields.senderAddress,
                ElasticsearchUtil.splitAlphanumeric(ayPrintLogListSearchDTO.getSenderAddressKeyWord())));
        }

        if (!CollectionUtils.isEmpty(ayPrintLogListSearchDTO.getWaybillCodeList())) {
            queryCondition.must(termsQuery(EsFields.waybillNumber, ayPrintLogListSearchDTO.getWaybillCodeList()));
        }

        if (BooleanUtils.isTrue(ayPrintLogListSearchDTO.getIsDistinctWayBill())) {
            queryCondition.mustNot(termQuery(EsFields.isRepetitionPrint, Boolean.TRUE));
        }

        if (CollectionUtils.isNotEmpty(ayPrintLogListSearchDTO.getLogisticsCompany())) {
            queryCondition
                .must(termsQuery(EsFields.logisticsCompany, ayPrintLogListSearchDTO.getLogisticsCompany()));
        }

        if (!CollectionUtils.isEmpty(ayPrintLogListSearchDTO.getCpCodes())) {
            queryCondition.must(termsQuery(EsFields.logisticsCompanyCode, ayPrintLogListSearchDTO.getCpCodes()));
        }

        if (ayPrintLogListSearchDTO.getStartTime() != null) {
            queryCondition.must(rangeQuery(EsFields.printTime)
                .gte(minuteSecondFormatter.format(ayPrintLogListSearchDTO.getStartTime())));
        }

        if (ayPrintLogListSearchDTO.getEndTime() != null) {
            queryCondition.must(
                rangeQuery(EsFields.printTime).lte(minuteSecondFormatter.format(ayPrintLogListSearchDTO.getEndTime())));

        }

        if (StringUtils.isNotBlank(ayPrintLogListSearchDTO.getElefaceProvider())) {
            queryCondition.must(termsQuery(EsFields.elefaceProvider, ayPrintLogListSearchDTO.getElefaceProvider()));
        }

        if (StringUtils.isNotBlank(ayPrintLogListSearchDTO.getOperatorName())) {
            queryCondition.must(
                boolQuery().must(termsQuery(EsFields.operatorNameKeyWord, ayPrintLogListSearchDTO.getOperatorName()))
                    .must(termsQuery(EsFields.operatorStoreId, ayPrintLogListSearchDTO.getOperatorStoreId())));
        }

        if (StringUtils.isNotBlank(ayPrintLogListSearchDTO.getPrinterName())) {
            queryCondition.must(
                boolQuery().must(matchPhraseQuery(EsFields.printerNumber, ayPrintLogListSearchDTO.getPrinterName())));
        }

        if (ayPrintLogListSearchDTO.getValueOfCancelWayBill() != null) {
            if (BooleanUtils.isTrue(BooleanUtils.toBoolean(ayPrintLogListSearchDTO.getValueOfCancelWayBill()))) {
                queryCondition.must(termQuery(EsFields.isCancel, Boolean.TRUE));
            } else {
                queryCondition.mustNot(termQuery(EsFields.isCancel, Boolean.TRUE));
            }
        }

        if (CollectionUtils.isNotEmpty(ayPrintLogListSearchDTO.getReceiverProvinceList())) {
            queryCondition
                .must(termsQuery(EsFields.receiverProvince, ayPrintLogListSearchDTO.getReceiverProvinceList()));
        }

        if (CollectionUtils.isNotEmpty(ayPrintLogListSearchDTO.getReceiverCityList())) {
            queryCondition.must(termsQuery(EsFields.receiverCity, ayPrintLogListSearchDTO.getReceiverCityList()));
        }

        if (CollectionUtils.isNotEmpty(ayPrintLogListSearchDTO.getDistributeAyUserIdList())) {
            queryCondition.must(termsQuery(EsFields.ayDistributeAyUserId, ayPrintLogListSearchDTO.getDistributeAyUserIdList()));
        }

        if (CollectionUtils.isNotEmpty(ayPrintLogListSearchDTO.getNumbersInBatch())) {
            queryCondition.must(termsQuery(EsFields.numbersInBatch, ayPrintLogListSearchDTO.getNumbersInBatch()));
        }

        if (CollectionUtils.isNotEmpty(ayPrintLogListSearchDTO.getBatchIdList())) {
            queryCondition.must(termsQuery(EsFields.batchId, ayPrintLogListSearchDTO.getBatchIdList()));
        }

        return queryCondition;
    }
}
