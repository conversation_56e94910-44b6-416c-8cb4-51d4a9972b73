package cn.loveapp.print.service.dto;

import java.io.Serializable;
import java.util.List;
import java.util.stream.Collectors;

import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.BeanUtils;

import cn.loveapp.print.service.api.entity.AyWaybillApplySubscriptionInfo;
import cn.loveapp.print.service.entity.ElefaceSharingRelation;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 面单网点发货地址DTO
 *
 * <AUTHOR>
 */
@ApiModel
@Data
public class WaybillBranchShippAddressDTO implements Serializable {
    private static final long serialVersionUID = -6108653064078467490L;

    /**
     * 面单平台服务商 {@link cn.loveapp.print.common.constant.ElefaceProviderConstant}
     */
    @ApiModelProperty("面单平台服务商")
    private String provider;

    /**
     * 物流公司Code
     */
    @ApiModelProperty("物流公司code")
    private String cpCode;

    /**
     * 物流服务商 业务类型
     */
    @ApiModelProperty("物流服务商 业务类型")
    private Long cpType;

    /**
     * 电子面单版本号，1-默认值旧版电子面单 2-新版电子面单 (XHS使用)
     */
    private Integer billVersion;

    /**
     * 发货地址
     */
    @ApiModelProperty("发货地址")
    private AyWaybillApplySubscriptionInfo.AddressDto shippAddress;

    /**
     * 面单共享关系
     * <p>
     * 非共享面单为null值
     */
    @ApiModelProperty("面单共享关系, 非共享面单为null值")
    private ElefaceSharingRelation sharingRelation;

    /*--------------------------------------以下都是网点的信息---------------------------------------------*/

    /**
     * 已使用面单数量
     */
    @ApiModelProperty("已使用面单数量")
    private Long allocatedQuantity;

    /**
     * 网点Code
     */
    @ApiModelProperty("网点Code")
    private String branchCode;

    /**
     * 网点名称
     */
    @ApiModelProperty("网点名称")
    private String branchName;

    /**
     * 网点状态
     */
    @ApiModelProperty("网点状态")
    private Long branchStatus;

    /**
     * 取消的面单总数
     */
    @ApiModelProperty("取消的面单总数")
    private Long cancelQuantity;

    /**
     * 已回收用面单数量 (拼多多特有字段)
     */
    @ApiModelProperty("已回收用面单数量")
    private Long recycledQuantity;

    /**
     * 打印的面单总数
     */
    @ApiModelProperty("打印的面单总数")
    private Long printQuantity;

    /**
     * 电子面单余额数量
     */
    @ApiModelProperty("电子面单余额数量")
    private Long quantity;

    /**
     * 号段信息 （菜鸟面单专有字段）
     */
    @ApiModelProperty("号段信息")
    private String segmentCode;

    /**
     * 可用服务信息列表
     */
    @ApiModelProperty("可用服务信息列表")
    private List<AyWaybillApplySubscriptionInfo.ServiceInfoDto> serviceInfoCols;

    /**
     * 品牌code （菜鸟面单专有字段）
     */
    @ApiModelProperty("品牌code")
    private String brandCode;

    /**
     * 月结卡号列表 （菜鸟面单专有字段）
     */
    @ApiModelProperty("月结卡号列表")
    private List<String> customerCodeList;

    /**
     * 客户编码（直营物流公司的月结卡号,快手电子面单专有字段）
     */
    @ApiModelProperty("客户编码")
    private String settleAccount;

    /**
     * 电子面单账号id，每绑定一个网点分配一个acctId(微信视频号专有字段)
     */
    private String acctId;

    /**
     * 店铺id，全局唯一，一个店铺分配一个shopId(微信视频号专有字段)
     */
    private String shopId;

    /**
     * 面单网点可用标识（授权过期、api调用异常该值为false）
     */
    private Boolean isAvailable = true;

    /**
     * 不可用原因
     */
    private String unAvailableReason;

    /**
     * 构建 WaybillBranchShippAddressDTO 实例
     *
     * @param cpCode
     *            快递公司code
     * @param cpType
     *            物流服务商 业务类型
     * @param branchAccount
     *            网点账号信息
     * @param shippAddressDto
     *            发货地址信息
     * @return
     */
    public static WaybillBranchShippAddressDTO of(String cpCode, Long cpType, Integer billVersion,
        AyWaybillApplySubscriptionInfo.WaybillBranchAccount branchAccount,
        AyWaybillApplySubscriptionInfo.AddressDto shippAddressDto) {
        WaybillBranchShippAddressDTO waybillBranchShippAddressDTO = new WaybillBranchShippAddressDTO();
        waybillBranchShippAddressDTO.setCpCode(cpCode);
        waybillBranchShippAddressDTO.setCpType(cpType);
        waybillBranchShippAddressDTO.setBillVersion(billVersion);
        BeanUtils.copyProperties(branchAccount, waybillBranchShippAddressDTO, "serviceInfoCols");
        if (CollectionUtils.isNotEmpty(branchAccount.getServiceInfoCols())) {
            waybillBranchShippAddressDTO.setServiceInfoCols(branchAccount.getServiceInfoCols().stream()
                .map(AyWaybillApplySubscriptionInfo.ServiceInfoDto::clone).collect(Collectors.toList()));
        }
        waybillBranchShippAddressDTO.setShippAddress(shippAddressDto);
        return waybillBranchShippAddressDTO;
    }

    /**
     * 构建异常 WaybillBranchShippAddressDTO 实例
     *
     * @param relation
     * @param unAvailableReason
     * @return
     */
    public static WaybillBranchShippAddressDTO ofUnAvailable(ElefaceSharingRelation relation, String unAvailableReason) {
        WaybillBranchShippAddressDTO waybillBranchShippAddressDTO = new WaybillBranchShippAddressDTO();
        waybillBranchShippAddressDTO.setCpCode(relation.getCpCode());
        waybillBranchShippAddressDTO.setCpType(relation.getCpType());
        waybillBranchShippAddressDTO.setBillVersion(relation.getBillVersion());
        waybillBranchShippAddressDTO.setProvider(relation.getProvider());
        waybillBranchShippAddressDTO.setBranchCode(relation.getBranchCode());
        waybillBranchShippAddressDTO.setBranchName(relation.getBranchName());
        waybillBranchShippAddressDTO.setBrandCode(relation.getBrandCode());
        waybillBranchShippAddressDTO.setIsAvailable(false);
        waybillBranchShippAddressDTO.setUnAvailableReason(unAvailableReason);
        waybillBranchShippAddressDTO.setSharingRelation(relation);
        return waybillBranchShippAddressDTO;
    }
}
