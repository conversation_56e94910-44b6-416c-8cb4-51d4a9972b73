package cn.loveapp.print.service.controller;

import java.time.LocalDateTime;
import java.util.List;

import cn.loveapp.common.utils.LoggerHelper;
import cn.loveapp.print.common.dto.UserMultiInfoDTO;
import cn.loveapp.print.service.request.*;
import cn.loveapp.print.service.response.ElefaceSharingGetResponseDTO;
import cn.loveapp.print.api.dto.WaybillOperatelogResponseDTO;
import cn.loveapp.print.service.service.ParamsHandleService;
import cn.loveapp.print.service.response.WaybillOperateLogDetailUpdateResponse;
import com.doudian.open.utils.StringUtil;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import cn.loveapp.common.annotation.RequestParamConvert;
import cn.loveapp.common.dto.UserSessionInfo;
import cn.loveapp.common.user.session.annotation.CheckUserSession;
import cn.loveapp.common.web.CommonApiResponse;
import cn.loveapp.common.web.CommonApiStatus;
import cn.loveapp.print.common.exception.CommonException;
import cn.loveapp.print.service.annotation.ShopsAuth;
import cn.loveapp.print.service.bo.UserInfoBo;
import cn.loveapp.print.service.bo.WaybillGetResponseBo;
import cn.loveapp.print.service.dto.TargetUserInfoDTO;
import cn.loveapp.print.common.dto.WaybillOperatelogQueryDTO;
import cn.loveapp.print.service.response.WaybillGetResponseDTO;
import cn.loveapp.print.service.service.ElefaceWaybillService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import springfox.documentation.annotations.ApiIgnore;
import cn.loveapp.common.constant.HttpMethodsConstants;

/**
 * 电子面单相关操作接口
 *
 * <AUTHOR>
 */
@Api(tags = "电子面单相关操作接口")
@RestController
@RequestMapping("/print/eleface")
public class PrintElefaceController {

    private static final LoggerHelper LOGGER = LoggerHelper.getLogger(PrintElefaceController.class);

    @Autowired
    private ElefaceWaybillService elefaceWaybillService;

    @Autowired
    private ParamsHandleService paramsHandleService;

    /**
     * 淘宝电子面单云打印接口
     *
     * @param request
     * @param sessionInfo
     * @param targetUserInfoDTO
     * @return
     */
    @Deprecated
    @ApiOperation(value = "淘宝电子面单云打印接口(废弃)", httpMethod = HttpMethodsConstants.POST)
    @RequestMapping("/cainiao.waybill.get")
    @CheckUserSession(hasCheckPlatform = true)
    @ShopsAuth
    @RequestParamConvert(underscore = false)
    public CommonApiResponse<WaybillGetResponseDTO> cainiaoWaybillGet(@Validated ElefaceWaybillGetRequest request, @ApiIgnore UserSessionInfo sessionInfo,
                                                                      TargetUserInfoDTO targetUserInfoDTO) {
        String cpCode = request.getCpCode();
        List<WaybillGetResponseBo> responseList;
        try {
            responseList =
                    elefaceWaybillService.waybillGetBatch(request, UserInfoBo.of(sessionInfo, targetUserInfoDTO));
        } catch (CommonException e) {
            return new CommonApiResponse(CommonApiStatus.Success.code(), CommonApiStatus.Success.message(), e.getCode(),
                    e.getMessage(), null);
        }

        return CommonApiResponse.success(WaybillGetResponseDTO.of(cpCode, responseList));
    }

    /**
     * 拼多多打印接口
     *
     * @param request
     * @param sessionInfo
     * @param targetUserInfoDTO
     * @return
     */
    @Deprecated
    @ApiOperation(value = "拼多多打印接口(废弃)", httpMethod = HttpMethodsConstants.POST)
    @RequestMapping("/pdd.waybill.get")
    @CheckUserSession(hasCheckPlatform = true)
    @ShopsAuth
    @RequestParamConvert(underscore = false)
    public CommonApiResponse<WaybillGetResponseDTO> pddWaybillGet(@Validated ElefaceWaybillGetRequest request, @ApiIgnore UserSessionInfo sessionInfo,
                                                                  TargetUserInfoDTO targetUserInfoDTO) {

        String cpCode = request.getCpCode();
        List<WaybillGetResponseBo> responseList;
        try {
            responseList =
                    elefaceWaybillService.waybillGetBatch(request, UserInfoBo.of(sessionInfo, targetUserInfoDTO));
        } catch (CommonException e) {
            return new CommonApiResponse(CommonApiStatus.Success.code(), CommonApiStatus.Success.message(), e.getCode(),
                    e.getMessage(), null);
        }

        return CommonApiResponse.success(WaybillGetResponseDTO.of(cpCode, responseList));
    }

    /**
     * 取消电子面单号
     *
     * @param request
     * @param sessionInfo
     * @return
     */
    @Deprecated
    @ApiOperation(value = "淘宝电子面单取消电子面单号(废弃)", httpMethod = HttpMethodsConstants.POST)
    @RequestMapping("/cainiao.waybill.cancel")
    @CheckUserSession(hasCheckPlatform = true)
    @ShopsAuth
    public CommonApiResponse cainiaoWaybillCancel(@Validated ElefaceWaybillCancelRequest request,
                                                  @ApiIgnore UserSessionInfo sessionInfo, TargetUserInfoDTO targetUserInfoDTO) {

        String cpCode = request.getCpCode();
        String waybillCode = request.getWaybillCode();
        Boolean isCancelFromApi = request.getIsCancelFromApi();
        Integer billVersion = request.getBillVersion();

        UserInfoBo userInfoBo = UserInfoBo.of(sessionInfo, targetUserInfoDTO);

        try {
            elefaceWaybillService.waybillCancel(cpCode, waybillCode, billVersion, isCancelFromApi, userInfoBo);
        } catch (CommonException e) {
            return new CommonApiResponse(CommonApiStatus.Success.code(), CommonApiStatus.Success.message(), e.getCode(),
                    e.getMessage(), null);
        }
        return CommonApiResponse.success();
    }

    /**
     * 取消电子面单号
     *
     * @param request
     * @param sessionInfo
     * @return
     */
    @Deprecated
    @RequestMapping("/pdd.waybill.cancel")
    @ApiOperation(value = "拼多多取消电子面单号(废弃)", httpMethod = HttpMethodsConstants.POST)
    @CheckUserSession(hasCheckPlatform = true)
    @ShopsAuth
    public CommonApiResponse pddWaybillCancel(@Validated ElefaceWaybillCancelRequest request,
                                              @ApiIgnore UserSessionInfo sessionInfo, TargetUserInfoDTO targetUserInfoDTO) {

        String cpCode = request.getCpCode();
        String waybillCode = request.getWaybillCode();
        Boolean isCancelFromApi = request.getIsCancelFromApi();
        Integer billVersion = request.getBillVersion();

        UserInfoBo userInfoBo = UserInfoBo.of(sessionInfo, targetUserInfoDTO);

        try {
            elefaceWaybillService.waybillCancel(cpCode, waybillCode, billVersion, isCancelFromApi, userInfoBo);
        } catch (CommonException e) {
            return new CommonApiResponse(CommonApiStatus.Success.code(), CommonApiStatus.Success.message(), e.getCode(),
                    e.getMessage(), null);
        }
        return CommonApiResponse.success();
    }

    /**
     * 电子面单取号
     *
     * @param request
     * @param sessionInfo
     * @param targetUserInfoDTO
     * @return
     */
    @ApiOperation(value = "电子面单取号", httpMethod = HttpMethodsConstants.POST)
    @RequestMapping(value = "/waybill.get", method = {RequestMethod.GET, RequestMethod.POST})
    @CheckUserSession(hasCheckPlatform = true)
    @ShopsAuth
    @RequestParamConvert(underscore = false)
    public CommonApiResponse<WaybillGetResponseDTO> waybillGet(@Validated ElefaceWaybillGetRequest request, @ApiIgnore UserSessionInfo sessionInfo, TargetUserInfoDTO targetUserInfoDTO) {
        String cpCode = request.getCpCode();
        List<WaybillGetResponseBo> responseList;

        try {
            responseList = elefaceWaybillService.waybillGetBatch(request, UserInfoBo.of(sessionInfo, targetUserInfoDTO));
        } catch (CommonException e) {
            return new CommonApiResponse(CommonApiStatus.Success.code(), CommonApiStatus.Success.message(),
                    e.getCode(), e.getMessage(), null);
        }

        return CommonApiResponse.success(WaybillGetResponseDTO.of(cpCode, responseList));
    }

    /**
     * 电子面单批量取号
     *
     * @param request
     * @param sessionInfo
     * @param targetUserInfoDTO
     * @return
     */
    @ApiOperation(value = "电子面单批量取号", httpMethod = HttpMethodsConstants.POST)
    @RequestMapping(value = "/waybill.batch.get", method = {RequestMethod.GET, RequestMethod.POST})
    @CheckUserSession(hasCheckPlatform = true)
    @ShopsAuth
    @RequestParamConvert(underscore = false)
    public CommonApiResponse<WaybillGetResponseDTO> waybillBatchGet(@Validated ElefaceWaybillBatchGetRequest request, @ApiIgnore UserSessionInfo sessionInfo, TargetUserInfoDTO targetUserInfoDTO) {
        String cpCode = request.getCpCode();
        List<WaybillGetResponseBo> responseList;
        try {
            responseList = elefaceWaybillService.waybillGetBatchV2(request, UserInfoBo.of(sessionInfo, targetUserInfoDTO));
        } catch (CommonException e) {
            return new CommonApiResponse(CommonApiStatus.Success.code(), CommonApiStatus.Success.message(),
                    e.getCode(), e.getMessage(), null);
        }

        return CommonApiResponse.success(WaybillGetResponseDTO.of(cpCode, responseList));

    }

    /**
     * 取消电子面单号
     *
     * @param request
     * @param sessionInfo
     * @return
     */
    @ApiOperation(value = "取消电子面单号", httpMethod = HttpMethodsConstants.POST)
    @RequestMapping(value = "/waybill.cancel", method = {RequestMethod.GET, RequestMethod.POST})
    @CheckUserSession(hasCheckPlatform = true)
    @ShopsAuth
    public CommonApiResponse waybillCancel(@Validated ElefaceWaybillCancelRequest request,
                                           @ApiIgnore UserSessionInfo sessionInfo, TargetUserInfoDTO targetUserInfoDTO) {
        String cpCode = request.getCpCode();
        String waybillCode = request.getWaybillCode();
        Boolean isCancelFromApi = request.getIsCancelFromApi();
        Integer billVersion = request.getBillVersion();
        UserInfoBo userInfoBo = UserInfoBo.of(sessionInfo, targetUserInfoDTO);
        try {
            elefaceWaybillService.waybillCancel(cpCode, waybillCode, billVersion, isCancelFromApi, userInfoBo);
        } catch (CommonException e) {
            return new CommonApiResponse(CommonApiStatus.Success.code(), CommonApiStatus.Success.message(), e.getCode(),
                    e.getMessage(), null);
        }
        return CommonApiResponse.success();
    }

    /**
     * 获取电子面单操作列表
     *
     * @param request
     * @param sessionInfo
     * @return
     */
    @ApiOperation(value = "获取电子面单操作列表", httpMethod = HttpMethodsConstants.POST)
    @RequestMapping("/waybill.operatelog.list.get")
    @CheckUserSession(hasCheckPlatform = true)
    @ShopsAuth
    public CommonApiResponse<WaybillOperatelogResponseDTO> waybillOperatelogListGet(@Validated WaybillOperatelogListGetRequest request,
                                                                                    @ApiIgnore UserSessionInfo sessionInfo, TargetUserInfoDTO targetUserInfoDTO) {
        Integer page = request.getPage();
        Integer pageSize = request.getPageSize();
        Integer tradeType = request.getTradeType();
        Boolean isCancel = request.getIsCancel();
        String provider = request.getProvider();
        String cpCode = request.getCpCode();
        List<String> cpCodeList = request.getCpCodeList();
        String tid = request.getTid();
        List<String> tidList = request.getTidList();
        String waybillCode = request.getWaybillCode();
        List<String> waybillCodeList = request.getWaybillCodeList();
        LocalDateTime startTime = request.getStartTime();
        LocalDateTime endTime = request.getEndTime();

        UserInfoBo userInfoBo = UserInfoBo.of(sessionInfo, targetUserInfoDTO);

        // 组装查询DTO
        WaybillOperatelogQueryDTO queryDTO = new WaybillOperatelogQueryDTO();
        queryDTO.setOffset((page - 1) * pageSize);
        queryDTO.setLimit(pageSize);
        queryDTO.setTradeType(tradeType);
        queryDTO.setIsCancel(isCancel);
        queryDTO.setProvider(provider);
        queryDTO.addCpCodes(cpCode);
        queryDTO.addCpCodes(cpCodeList);
        // 如果传了tid，默认忽略tidList
        if (StringUtil.isEmpty(tid) && CollectionUtils.isNotEmpty(tidList)) {
            queryDTO.setTidList(tidList);
        } else {
            queryDTO.setTid(tid);
        }
        // 如果传了waybillCode，默认忽略waybillCodeList
        if (StringUtil.isEmpty(waybillCode) && CollectionUtils.isNotEmpty(waybillCodeList)) {
            queryDTO.setWaybillCodeList(waybillCodeList);
        } else {
            queryDTO.setWaybillCode(waybillCode);
        }
        queryDTO.setStartTime(startTime);
        queryDTO.setEndTime(endTime);
        queryDTO.setCancelStartTime(request.getCancelStartTime());
        queryDTO.setCancelEndTime(request.getCancelEndTime());
        queryDTO.setOwnerSellerId(request.getOwnerSellerId());
        queryDTO.setOwnerSellerNick(request.getOwnerSellerNick());
        queryDTO.setOwnerStoreId(request.getOwnerStoreId());
        queryDTO.setOwnerAppName(request.getOwnerAppName());
        queryDTO.setPrintCount(request.getPrintCount());
        paramsHandleService.handleWaybillOperateLogLisParams(queryDTO, sessionInfo);
        return CommonApiResponse.success(elefaceWaybillService.operatelogListGet(queryDTO, userInfoBo));
    }

    /**
     * 获取共享的电子面单操作列表
     *
     * @param request
     * @param sessionInfo
     * @return
     */
    @ApiOperation(value = "获取共享的电子面单操作列表", httpMethod = HttpMethodsConstants.POST)
    @RequestMapping("/waybill.shareoperatelog.list.get")
    @CheckUserSession(hasCheckPlatform = true)
    @ShopsAuth
    public CommonApiResponse<WaybillOperatelogResponseDTO> waybillShareOperatelogListGet(@Validated WaybillShareOperatelogListGetRequest request,
                                                                                         @ApiIgnore UserSessionInfo sessionInfo, TargetUserInfoDTO targetUserInfoDTO) {
        Integer page = request.getPage();
        Integer pageSize = request.getPageSize();
        Integer tradeType = request.getTradeType();
        Boolean isCancel = request.getIsCancel();
        String provider = request.getProvider();
        String cpCode = request.getCpCode();
        String tid = request.getTid();
        List<String> tidList = request.getTidList();
        String waybillCode = request.getWaybillCode();
        LocalDateTime startTime = request.getStartTime();
        LocalDateTime endTime = request.getEndTime();

        // 查询分享面单，当前登陆为面单所有者
        UserInfoBo ownerUserInfo = UserInfoBo.of(sessionInfo, targetUserInfoDTO);

        // 组装查询DTO
        WaybillOperatelogQueryDTO queryDTO = new WaybillOperatelogQueryDTO();
        queryDTO.setOffset((page - 1) * pageSize);
        queryDTO.setLimit(pageSize);
        queryDTO.setTradeType(tradeType);
        queryDTO.setIsCancel(isCancel);
        queryDTO.setProvider(provider);
        queryDTO.addCpCodes(cpCode);
        // 如果传了tid，默认忽略tidList
        if (StringUtil.isEmpty(tid) && CollectionUtils.isNotEmpty(tidList)) {
            queryDTO.setTidList(tidList);
        } else {
            queryDTO.setTid(tid);
        }
        queryDTO.setWaybillCode(waybillCode);
        queryDTO.setStartTime(startTime);
        queryDTO.setEndTime(endTime);

        queryDTO.setOwnerSellerId(ownerUserInfo.getSellerId());
        queryDTO.setOwnerSellerNick(ownerUserInfo.getSellerNick());
        queryDTO.setOwnerStoreId(ownerUserInfo.getStoreId());
        queryDTO.setOwnerAppName(ownerUserInfo.getAppName());

        // 设置面单使用者（被分享的）
        UserInfoBo userInfoBo = new UserInfoBo();
        userInfoBo.setStoreId(request.getStoreId());
        userInfoBo.setSellerId(request.getSellerId());
        userInfoBo.setSellerNick(request.getSellerNick());
        userInfoBo.setAppName(request.getAppName());

        return CommonApiResponse.success(elefaceWaybillService.operatelogListGet(queryDTO, userInfoBo));
    }

    /**
     * 获取面单共享关系
     *
     * @param request
     * @param sessionInfo
     * @return
     */
    @ApiOperation(value = "获取面单共享关系", httpMethod = HttpMethodsConstants.POST)
    @RequestMapping(value = "/waybill.share.get", method = {RequestMethod.GET, RequestMethod.POST})
    @CheckUserSession(hasCheckPlatform = true)
    @ShopsAuth
    public CommonApiResponse<ElefaceSharingGetResponseDTO> waybillShareGet(@Validated ElefaceSharingGetRequest request,
                                                                           @ApiIgnore UserSessionInfo sessionInfo, TargetUserInfoDTO targetUserInfoDTO) {
        if (StringUtil.isEmpty(request.getShardId())) {
            return CommonApiResponse.failed(CommonApiStatus.RequestParamError.code(), "缺少入参shardId");
        }
        UserInfoBo userInfoBo = UserInfoBo.of(sessionInfo, targetUserInfoDTO);
        return CommonApiResponse.success(elefaceWaybillService.waybillShareGet(request, userInfoBo));
    }


    /**
     * 获取面单操作列表
     *
     * @param request
     * @param sessionInfo
     * @return
     */
    @ApiOperation(value = "获取共享的电子面单操作列表", httpMethod = HttpMethodsConstants.POST)
    @RequestMapping("/multi/waybill.operate.log.list.get")
    @CheckUserSession(hasCheckPlatform = true)
    @ShopsAuth
    public CommonApiResponse<WaybillOperatelogResponseDTO> multiWaybillOperateLogListGet(@Validated MultiWaybillOperateLogListGetRequest request,
                                                                                         @ApiIgnore UserSessionInfo sessionInfo, @ApiIgnore UserMultiInfoDTO userMultiInfoDTO) {

        if (request.getPage() <= 0L || request.getPageSize() < 0L) {
            LOGGER.logError(sessionInfo.getNick(), "-", "参数异常: pageNo和pageSize必须为正数");
        }

        List<UserInfoBo> userInfoBoList = UserInfoBo.of(sessionInfo, userMultiInfoDTO);

        try {
            WaybillOperatelogResponseDTO responseDTO = elefaceWaybillService.multiWaybillOperateLogListGet(request, userInfoBoList);
            return CommonApiResponse.success(responseDTO);
        } catch (Exception e) {
            LOGGER.logError(sessionInfo.getNick(), "-", "查询异常：" + e.getMessage(), e);
            return CommonApiResponse.failed(CommonApiStatus.ServerError, null);
        }
    }


    /**
     * 面单操作列表详情更新
     *
     * @param request
     * @param sessionInfo
     * @return
     */
    @ApiOperation(value = "面单操作列表详情更新", httpMethod = HttpMethodsConstants.POST)
    @RequestMapping("/multi/waybill.operate.log.detail.update")
    @CheckUserSession(hasCheckPlatform = true)
    @ShopsAuth
    public CommonApiResponse<WaybillOperateLogDetailUpdateResponse> waybillOperateLogDetailUpdate(@Validated WaybillOperateLogDetailUpdateRequest request,
                                                                                                  @ApiIgnore UserSessionInfo sessionInfo, @ApiIgnore UserMultiInfoDTO userMultiInfoDTO) {

        List<UserInfoBo> userInfoBoList = UserInfoBo.of(sessionInfo, userMultiInfoDTO);

        try {
            WaybillOperateLogDetailUpdateResponse response = new WaybillOperateLogDetailUpdateResponse();
            response.setOperateLogDetail(elefaceWaybillService.waybillOperateLogDetailUpdate(request.getOperateLogDetail(), request.getElefaceLogUserRole(), userInfoBoList));
            return CommonApiResponse.success(response);
        } catch (Exception e) {
            LOGGER.logError(sessionInfo.getNick(), "-", "查询异常：" + e.getMessage(), e);
            return CommonApiResponse.failed(CommonApiStatus.ServerError, null);
        }
    }
}
