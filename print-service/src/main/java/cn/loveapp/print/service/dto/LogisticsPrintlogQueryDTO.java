package cn.loveapp.print.service.dto;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

import cn.loveapp.common.constant.CommonAppConstants;
import cn.loveapp.common.constant.CommonPlatformConstants;
import cn.loveapp.print.api.request.PrintLogListGetInnerRequest;
import cn.loveapp.print.common.entity.AyPrintLogSearchEs;
import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import cn.loveapp.print.service.request.LogisticsPrintlogListGetRequest;
import lombok.Data;

/**
 * 物流单打印日志查询
 *
 * <AUTHOR>
 */
@Data
public class LogisticsPrintlogQueryDTO {

    /**
     * 查询获取打印日志最大pagesize
     */
    private static final int MAX_GET_PRINT_LOG_LIMIT = 1000;

    private Integer offset;

    private Integer limit;

    private Integer page;

    private Integer pageSize;

    /**
     * 批量订单号精确查询,单个和批量同时存在时，tidList优先级更高
     */
    private List<String> tidList;

    /**
     * 订单类型
     */
    private List<Integer> tradeType;

    /**
     * 收件人姓名
     */
    private String receiverName;

    /**
     * 收件人联系方式 电话/手机
     */
    private String receiverContact;

    /**
     * 收货地址关键字
     */
    private String receiverAddressKeyWord;

    /**
     * 发货人姓名
     */
    private String senderName;

    /**
     * 发货人联系方式 电话/手机
     */
    private String senderContact;

    /**
     * 收货地址关键字
     */
    private String senderAddressKeyWord;

    /**
     * 运单号列表，单个和批量同时存在时，waybillCodeList优先级更高
     */
    private List<String> waybillCodeList;

    /**
     * 子运单号
     */
    private String childWaybillCode;

    /**
     * 收件人地区列表(包含江浙沪、珠三角这些别名)
     */
    private List<String> receiverRegionList;

    /**
     * 收件人省份列表
     */
    private List<String> receiverProvinceList;

    /**
     * 收件人城市列表
     */
    private List<String> receiverCityList = new ArrayList<>();

    /**
     * 快递公司
     */
    private List<String> logisticsCompany;

    /**
     * 快递公司编号
     */
    private List<String> cpCodes;

    /**
     * 打印类型 express-快递单 eleface-电子面单 deliver-发货单
     */
    private String printType;

    /**
     * 打印类型集合 express-快递单 eleface-电子面单 deliver-发货单
     * 优先级低于printType
     */
    private List<String> printTypeList;

    /**
     * 面单服务商
     * <p>
     * {@link cn.loveapp.print.common.constant.ElefaceProviderConstant}
     */
    private String elefaceProvider;

    /**
     * 开始时间
     */
    private LocalDateTime startTime;

    /**
     * 结束时间
     */
    private LocalDateTime endTime;

    /**
     * 下次查询时最大id,where id < maxId(上次查询列表响应的最小id)
     */
    private Long maxId;

    /**
     * 是否需要运单号去重，去重后数据取最新的一条
     */
    private Boolean isDistinctWayBill;

    /**
     * 回收单号
     * 1:查询已回收单号
     * 0:查询未回收单号
     * null:查询全部
     */
    private Integer valueOfCancelWayBill;

    /**
     * 订单集合和手机号查询是否是或的关系
     * true  :tid or mobile 两个字段或查询
     * false/null :tid and mobile 两个字段且查询
     */
    private Boolean isTidListAndMobileQueryWithOR;

    /**
     * 是否需要查询面单获取记录和快递单打印日志
     */
    private boolean needQueryElefaceAndExpressLog = true;

    /**
     * 数据库需要查询响应的字段
     */
    private List<String> fields;

    /**
     * 操作人
     */
    private String operatorName;

    /**
     * 操作人
     */
    private String operatorStoreId;

    /**
     * 是否使用es进行查询
     */
    private Boolean isUseEsSearch;

    /**
     * 是否需要聚合统计
     */
    private Boolean isNeedStatistics;

    /**
     * 是否统计订单总数
     */
    private Boolean isStatisticsTidCount;

    /**
     * 是否统计物流公司总数
     */
    private Boolean isStatisticsLogisticsCompanyCount;

    /**
     * 是否统计电子面单号总数
     */
    private Boolean isWaybillCodeCount;

    /**
     * 是否统计一天
     */
    private Boolean isStatisticsToday;

    /**
     * 打印机名称
     */
    private String printerName;

    /**
     * 爱用分销用户id列表
     */
    private List<String> distributeAyUserIdList;

    /**
     * 批次号
     */
    private List<String> batchIdList;

    /**
     * 批次序号
     */
    private List<Integer> numbersInBatch;


    public static LogisticsPrintlogQueryDTO of(LogisticsPrintlogListGetRequest request) {
        Integer page = request.getPage();
        Integer pageSize = request.getPageSize();

        LogisticsPrintlogQueryDTO queryDTO = new LogisticsPrintlogQueryDTO();
        BeanUtils.copyProperties(request, queryDTO);

        if (request.getIsCancelWayBill() != null) {
            queryDTO.setValueOfCancelWayBill(BooleanUtils.toInteger(request.getIsCancelWayBill()));
        }
        //tidList优先级更高,只查询单个时将值塞入list中进行查询
        if (StringUtils.isNotEmpty(request.getTid()) && CollectionUtils.isEmpty(queryDTO.getTidList())) {
            queryDTO.setTidList(Lists.newArrayList(request.getTid()));
        }
        //waybillCodeList优先级更高,只查询单个时将值塞入list中进行查询
        if (StringUtils.isNotEmpty(request.getWaybillCode()) && CollectionUtils.isEmpty(queryDTO.getWaybillCodeList())) {
            queryDTO.setWaybillCodeList(Lists.newArrayList(request.getWaybillCode()));
        }
        queryDTO.setOffset((page - 1) * pageSize);
        if (request.getCpCode() != null || request.getCpCodeList() != null) {
            if (queryDTO.cpCodes == null) {
                queryDTO.cpCodes = Lists.newArrayList();
            }

            if (StringUtils.isNotBlank(request.getCpCode())) {
                queryDTO.cpCodes.add(request.getCpCode());
            }

            if (CollectionUtils.isNotEmpty(request.getCpCodeList())) {
                queryDTO.cpCodes.addAll(request.getCpCodeList());
            }
        }

        // 校验pageSize最大值为1000
        if (pageSize > MAX_GET_PRINT_LOG_LIMIT) {
            pageSize = MAX_GET_PRINT_LOG_LIMIT;
        }

        queryDTO.setLimit(pageSize);

        // 分销信息查询
        if (CollectionUtils.isNotEmpty(request.getAyDistributeSellerIdList())) {
            List<String> distributeAyUserIdList = new ArrayList<>();
            request.getAyDistributeSellerIdList().forEach(sellerId -> {
                distributeAyUserIdList.add(AyPrintLogSearchEs.createAyUserId(sellerId, CommonPlatformConstants.PLATFORM_AIYONG, CommonAppConstants.APP_AIYONG));
            });
            queryDTO.setDistributeAyUserIdList(distributeAyUserIdList);
        }

        return queryDTO;
    }


    public static LogisticsPrintlogQueryDTO of(PrintLogListGetInnerRequest request) {
        LogisticsPrintlogQueryDTO queryDTO = new LogisticsPrintlogQueryDTO();
        Integer pageSize = request.getPageSize();
        Integer page = request.getPage();
        //maxId不为空时，使用maxId查询
        queryDTO.setLimit(pageSize);
        queryDTO.setPageSize(pageSize);
        queryDTO.setPage(page);
        if (request.getMaxId() != null) {
            queryDTO.setMaxId(request.getMaxId());
            queryDTO.setOffset(0);
        } else {
            //maxId为空则page不为空，使用offset查询
            queryDTO.setOffset((request.getPage() - 1) * pageSize);
        }

        queryDTO.setPrintType(request.getPrintType());
        queryDTO.setWaybillCodeList(request.getWaybillCodeList());
        queryDTO.setNeedQueryElefaceAndExpressLog(request.isNeedQueryElefaceAndExpressLog());
        return queryDTO;
    }
}
