package cn.loveapp.print.service.request;

import java.util.List;

import cn.loveapp.print.service.annotation.TradeTypeValidation;
import cn.loveapp.print.service.dto.TargetPrintInfoDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 物流单绑定记录获取
 *
 * <AUTHOR>
 */
@Data
@ApiModel("物流单绑定记录获取")
public class LogisticsBindHistoryGetRequest {
    /**
     * 订单列表
     */
    @ApiModelProperty(value = "订单列表")
    private List<String> tids;

    /**
     * 订单类型 0-普通订单 1-自由打印订单
     */
    @ApiModelProperty(value = "订单类型 0-普通订单 1-自由打印订单", required = true)
    @TradeTypeValidation
    private Integer tradeType;

    /**
     * 是否需要返回printData
     */
    @ApiModelProperty(value = "是否需要返回printData")
    private Boolean needPrintData;

    /**
     * 订单多店列表
     */
    @ApiModelProperty(value = "订单列表")
    private List<TargetPrintInfoDTO> targetPrintInfoList;
}
