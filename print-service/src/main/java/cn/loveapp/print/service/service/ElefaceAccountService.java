package cn.loveapp.print.service.service;

import java.util.List;

import cn.loveapp.print.api.dto.ElefaceSharingRelationDTO;
import cn.loveapp.print.api.request.SharingBatchGetRequest;
import cn.loveapp.print.api.response.SharingBatchCancelResponse;
import cn.loveapp.print.common.exception.CommonException;
import cn.loveapp.print.service.bo.ElefaceSharingCreateBo;
import cn.loveapp.print.service.bo.ElefaceSharingRelationQueryBo;
import cn.loveapp.print.service.bo.UserInfoBo;
import cn.loveapp.print.service.dto.WaybillBranchShippAddressDTO;
import cn.loveapp.print.service.entity.ElefaceSharingRelation;
import cn.loveapp.print.service.request.ElefaceSharingProxyListRequest;
import cn.loveapp.print.service.request.MultiProxySharingToOthersTargetListRequest;
import cn.loveapp.print.service.response.MultiProxySharingToOthersTargetListResponse;
import cn.loveapp.print.service.response.MultiSharingToOthersListResponse;

/**
 * 面单账号相关服务 service
 *
 * <AUTHOR>
 */
public interface ElefaceAccountService {
    /**
     * 获取用户所有网点的发货地址
     * <p>
     * 包括被共享的面单
     *
     * @param userInfo
     * @return
     * @throws CommonException
     */
    List<WaybillBranchShippAddressDTO> getAllBranchesShippAddress(UserInfoBo userInfo, Boolean isIncludeUnAvailable) throws CommonException;

    /**
     * 获取面单网点信息-支持多店版
     *
     * @param userInfoBo
     * @param isIncludeUnAvailable
     * @param getType
     * @return
     * @throws CommonException
     */
    List<WaybillBranchShippAddressDTO> getMultiAllBranchesShippAddress(UserInfoBo userInfoBo, Boolean isIncludeUnAvailable, String getType) throws CommonException;

    /**
     * 充值共享面单使用数量
     *
     * @param userInfoBo
     * @param shareId
     * @param topUpNum
     * @throws CommonException
     */
    void topUpBranchShareNum(UserInfoBo userInfoBo, String shareId, Long topUpNum) throws CommonException;

    /**
     * 获取用户共享出去的面单共享关系
     *
     * @param userInfoBo
     * @return
     */
    List<ElefaceSharingRelation> getSharingRelationsShareToOthersValid(UserInfoBo userInfoBo);

    /**
     * 获取用户共享出去的面单共享关系(爱用多店版)
     *
     * @param userInfoBo
     * @return
     */
    List<ElefaceSharingRelationDTO> getMultiSharingRelationsShareToOthersValid(UserInfoBo userInfoBo, ElefaceSharingRelationQueryBo queryBo);

    /**
     * 获取用户共享出去的面单共享关系(代理模式)
     *
     * @param userInfoBo
     * @return
     */
    MultiSharingToOthersListResponse multiProxySharingToOthersList(UserInfoBo userInfoBo, ElefaceSharingRelationQueryBo queryBo);

    /**
     * 获取用户被共享的 面单共享关系
     *
     * @param userInfoBo
     * @return
     */
    List<ElefaceSharingRelation> getSharingRelationsShareToMeValid(UserInfoBo userInfoBo);

    /**
     * 获取用户被共享的 当前被取消的面单共享关系
     *
     * @param userInfoBo
     * @return
     */
    List<ElefaceSharingRelation> getSharingRelationsShareToMeCanceled(UserInfoBo userInfoBo);

    /**
     * 确认 面单共享关系已被取消
     *
     * @param shareIds
     * @param userInfoBo
     */
    void confirmSharingRelationsShareToMeCanceled(List<String> shareIds, UserInfoBo userInfoBo);

    /**
     * 获取指定id的共享关系
     *
     * @param shareId
     * @return
     */
    ElefaceSharingRelation getSharingRelationByShareId(String shareId);

    /**
     * 创建面单共享关系
     *
     * @param sharingCreateBo
     * @return
     * @throws CommonException
     */
    ElefaceSharingRelation createSharingRelation(ElefaceSharingCreateBo sharingCreateBo) throws CommonException;

    /**
     * 创建面单共享关系（多店版-支持店铺群创建）
     *
     * @param sharingCreateBo
     * @return
     * @throws CommonException
     */
    ElefaceSharingRelation multiCreateSharingRelation(ElefaceSharingCreateBo sharingCreateBo) throws CommonException;

    /**
     * 取消面单共享
     *
     * @param shareId
     * @param userInfoBo
     * @param cancel
     * @return
     * @throws CommonException
     */
    void cancelSharingRelation(String shareId, UserInfoBo userInfoBo, Integer cancel) throws CommonException;

    /**
     * 记录共享关系面单使用数量
     *
     * @param shareId
     * @param useNum
     * @param userInfo
     * @throws CommonException
     */
    void addRelationUsedNum(String shareId, Long useNum, UserInfoBo userInfo);

    /**
     * 判断共享关系是否有效 owner(分享者)
     *
     * @param sharingRelation
     * @param userInfoBo
     * @return
     */
    boolean checkOwnerUserSharingRelation(ElefaceSharingRelation sharingRelation, UserInfoBo userInfoBo);

    /**
     * 判断共享关系是否有效 target(被分享者)
     *
     * @param sharingRelation
     * @param userInfoBo
     * @return
     */
    boolean checkTargetUserSharingRelation(ElefaceSharingRelation sharingRelation, UserInfoBo userInfoBo);

    /**
     * 解除面单共享关系（被分享用户主动解除指定分享者所有共享关系）
     *
     * @param ownerSellerId
     * @param ownerStoreId
     * @param ownerAppName
     * @param userInfoBo
     * @return
     */
    SharingBatchCancelResponse removeSharingRelations(String ownerSellerId, String ownerStoreId, String ownerAppName, UserInfoBo userInfoBo);

    /**
     * 获取用户共享给别人的 取消的面单共享关系
     *
     * @param userInfoBo
     * @return
     */
    List<ElefaceSharingRelation> getSharingRelationsShareToOtherCanceled(UserInfoBo userInfoBo);

    /**
     * 查询面单共享关系列表
     * @param request
     * @return
     */
    List<ElefaceSharingRelation> sharingBatchGet(SharingBatchGetRequest request);

    /**
     * 面单共享数量修改
     *
     * @param userInfoBo
     * @param shareId
     * @param surplusNum        面单剩余使用数量
     * @param shareNumUnlimited
     * @throws CommonException
     */
    void sharingQuantityModify(UserInfoBo userInfoBo, String shareId, Long surplusNum, Boolean shareNumUnlimited) throws CommonException;


    /**
     * 面单共享恢复
     *
     * @param userInfoBo
     * @param shareId
     * @param recoveryNum
     * @param shareNumUnlimited
     * @throws CommonException
     */
    void sharingRecovery(UserInfoBo userInfoBo, String shareId, Long recoveryNum, Boolean shareNumUnlimited) throws CommonException;

    /**
     * 保存面单分享记录备注
     *
     * @param userInfoBo
     * @param shareId
     * @param shareMemo
     * @param salesman
     */
    void saveShareMemo(UserInfoBo userInfoBo, String shareId, String shareMemo, String salesman) throws CommonException;


    /**
     * 获取面单网点信息-支持多店代理模式
     *
     * @param request
     * @param userInfoBo
     * @return
     * @throws CommonException
     */
    List<WaybillBranchShippAddressDTO> multiBranchProxyListGet(ElefaceSharingProxyListRequest request, UserInfoBo userInfoBo) throws CommonException;

    /**
     * 获取我共享过面单的 被分享者店铺列表
     * @param userInfoBo
     * @param request
     * @return
     */
    MultiProxySharingToOthersTargetListResponse multiProxySharingToOthersTargetList(UserInfoBo userInfoBo, MultiProxySharingToOthersTargetListRequest request);
}
