package cn.loveapp.print.service.api.response;

import cn.loveapp.print.service.code.ErrorCode;
import com.doudian.api.BaseDoudianResponse;
import com.google.common.collect.Lists;
import com.jd.open.api.sdk.response.AbstractResponse;
import com.kuaishou.merchant.open.api.KsMerchantResponse;
import com.pdd.pop.sdk.http.PopBaseHttpResponse;
import com.taobao.api.TaobaoResponse;
import com.wxvideoshop.api.BaseWxvideoshopResponse;
import com.xhs.api.BaseXhsResponse;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;

/**
 * Api返回基础信息
 *
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class AyBaseResponse extends TaobaoResponse {

    public <T extends PopBaseHttpResponse> void init(T response) {
        if (null == response) {
            this.setErrorCode(ErrorCode.BaseCode.REQUEST_ERR.getCode().toString());
            return;
        }
        if (null != response.getErrorResponse()) {
            this.setErrorCode(response.getErrorResponse().getErrorCode().toString());
            this.setSubCode(response.getErrorResponse().getSubCode());
            this.setSubMsg(response.getErrorResponse().getSubMsg());
        }
    }

    public <T extends TaobaoResponse> void init(T response) {
        if (null == response) {
            this.setErrorCode(ErrorCode.BaseCode.REQUEST_ERR.getCode().toString());
            return;
        }
        BeanUtils.copyProperties(response, this);
    }

    public <T extends BaseDoudianResponse> void init(T response) {
        if (null == response) {
            this.setErrorCode(ErrorCode.BaseCode.REQUEST_ERR.getCode().toString());
            return;
        }
        if (!response.isSuccess()) {
            this.setBody(response.getBody());
            this.setErrorCode(response.getCode());
            this.setSubCode(response.getErrNo());
            this.setSubMsg(response.getMessage());
        }
    }

    public <T extends KsMerchantResponse> void init(T response) {
        if (null == response) {
            this.setErrorCode(ErrorCode.BaseCode.REQUEST_ERR.getCode().toString());
            return;
        }
        if (!response.isSuccess()) {
            this.setErrorCode(String.valueOf(response.getResult()));
            this.setSubMsg(response.getErrorMsg());
        }
    }

    public <T extends BaseXhsResponse> void init(T response) {
        if (null == response) {
            this.setErrorCode(ErrorCode.BaseCode.REQUEST_ERR.getCode().toString());
            return;
        }
        if (!response.isSuccess()) {
            this.setErrorCode(String.valueOf(response.getErrorCode()));
            this.setSubMsg(response.getErrorMessage());
        }
    }

    public <T extends BaseWxvideoshopResponse> void init(T response){
        if (null == response){
            this.setErrorCode(ErrorCode.BaseCode.REQUEST_ERR.getCode().toString());
            return;
        }
        if (!response.isSuccess()){
            this.setErrorCode(String.valueOf(response.getErrorCode()));
            this.setSubMsg(response.getErrorMessage());
        }
    }

    public <T extends AbstractResponse> void init(T response) {
        if (null == response) {
            this.setErrorCode(ErrorCode.BaseCode.REQUEST_ERR.getCode().toString());
            return;
        }
        this.setMsg(response.getZhDesc());
    }
    
    public String getAllErrorMassage() {
        String message = this.getMessage();
        String msg = this.getMsg();
        String subMsg = this.getSubMsg();
        String subMessage = this.getSubMessage();

        return StringUtils.join(Lists.newArrayList(message, msg, subMsg, subMessage), ",");
    }
}
