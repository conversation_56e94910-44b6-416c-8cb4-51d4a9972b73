package cn.loveapp.print.service.controller;

import cn.loveapp.common.utils.LoggerHelper;
import cn.loveapp.common.web.CommonApiStatus;
import cn.loveapp.print.service.request.BatchSerialGetRequest;
import cn.loveapp.print.service.request.SerialBoundGetRequest;
import cn.loveapp.print.service.response.BatchSerialGetResponse;
import cn.loveapp.print.service.response.SerialBoundGetResponseDTO;
import cn.loveapp.shops.api.annotaion.UserShopsAuth;
import cn.loveapp.shops.api.dto.UserMultiInfoDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import cn.loveapp.common.annotation.RequestParamConvert;
import cn.loveapp.common.dto.UserSessionInfo;
import cn.loveapp.common.user.session.annotation.CheckUserSession;
import cn.loveapp.common.web.CommonApiResponse;
import cn.loveapp.print.service.annotation.ShopsAuth;
import cn.loveapp.print.service.bo.SerialGetBo;
import cn.loveapp.print.service.bo.UserInfoBo;
import cn.loveapp.print.service.dto.TargetUserInfoDTO;
import cn.loveapp.print.service.request.SerialGetRequest;
import cn.loveapp.print.service.response.SerialGetResponseDTO;
import cn.loveapp.print.service.service.SerialService;
import springfox.documentation.annotations.ApiIgnore;
import cn.loveapp.common.constant.HttpMethodsConstants;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 打印流水号相关接口 （这个其实并不是流水号，产品只是把它叫做流水号，只是根据打印时间生成了简短的订单号）
 *
 * <AUTHOR>
 */
@Api(tags = "打印流水号相关接口")
@RestController
@RequestMapping("/print/serial")
public class PrintSerialController {

    private static final LoggerHelper LOGGER = LoggerHelper.getLogger(PrintSerialController.class);

    @Autowired
    private SerialService serialService;

    /**
     * 获取打印流水号
     *
     * @param request
     * @param sessionInfo
     * @return
     */
    @ApiOperation(value = "获取打印流水号", httpMethod = HttpMethodsConstants.POST)
    @RequestMapping("/serial.get")
    @CheckUserSession(hasCheckPlatform = true)
    @ShopsAuth
    @RequestParamConvert(underscore = false)
    public CommonApiResponse<SerialGetResponseDTO> serialGet(@Validated SerialGetRequest request, @ApiIgnore UserSessionInfo sessionInfo,
                                                             TargetUserInfoDTO targetUserInfoDTO) {
        SerialGetBo serialGetBo = SerialGetBo.of(request);
        UserInfoBo userInfoBo = UserInfoBo.of(sessionInfo, targetUserInfoDTO);

        String serial = serialService.serialGet(serialGetBo, userInfoBo);

        // todo 多店流水号加上店铺标识
        return CommonApiResponse.success(new SerialGetResponseDTO(serial));
    }

    /**
     * 批量获取打印流水号
     *
     * @param request
     * @param sessionInfo
     * @return
     */
    @ApiOperation(value = "批量获取打印流水号", httpMethod = HttpMethodsConstants.POST)
    @RequestMapping("/batch.serial.get")
    @CheckUserSession(hasCheckPlatform = true)
    @UserShopsAuth
    @RequestParamConvert(underscore = false)
    public CommonApiResponse<BatchSerialGetResponse> batchSerialGet(@Validated BatchSerialGetRequest request, @ApiIgnore UserSessionInfo sessionInfo,
                                                                    UserMultiInfoDTO userMultiInfoDTO) {

        if (Objects.isNull(request) || CollectionUtils.isEmpty(request.getSerialGetRequests())) {
            return CommonApiResponse.failed(CommonApiStatus.RequestParamError.code(), "缺少入参");
        }

        if (userMultiInfoDTO.getTargetSellerList() == null) {
            return CommonApiResponse.failed(CommonApiStatus.ForbiddenError, null);
        }

        List<SerialGetRequest> serialGetRequests = request.getSerialGetRequests();

        List<String> sellerInfoList = userMultiInfoDTO.getTargetSellerList().stream().map(m -> m.getTargetStoreId() + m.getTargetSellerId()).collect(Collectors.toList());

        for (SerialGetRequest r : serialGetRequests) {
            if (StringUtils.isNoneEmpty(r.getStoreId(), r.getSellerId()) && !sellerInfoList.contains(r.getStoreId() + r.getSellerId())) {
                LOGGER.logError("鉴权异常，目标用户不匹配");
                return CommonApiResponse.failed(CommonApiStatus.ForbiddenError, null);
            }
        }

        BatchSerialGetResponse batchSerialGetResponse = new BatchSerialGetResponse();

        for (SerialGetRequest serialGetRequest : serialGetRequests) {
            SerialGetBo serialGetBo = SerialGetBo.of(serialGetRequest);
            UserInfoBo userInfoBo = UserInfoBo.of(sessionInfo);
            if (StringUtils.isNoneEmpty(serialGetRequest.getStoreId(), serialGetRequest.getSellerId())) {
                userInfoBo.setStoreId(serialGetRequest.getStoreId());
                userInfoBo.setAppName(serialGetRequest.getAppName());
                userInfoBo.setSellerId(serialGetRequest.getSellerId());
                userInfoBo.setSellerNick(serialGetRequest.getSellerNick());
            }

            String serial = serialService.serialGet(serialGetBo, userInfoBo);
            batchSerialGetResponse.addSerialGetResponse(serial, serialGetBo);
        }
        return CommonApiResponse.success(batchSerialGetResponse);
    }

    /**
     * 查询订单绑定过的流水号，未绑定过的订单不做获取流水号操作，返回前端当前最大流水号
     * @param request
     * @param sessionInfo
     * @return
     */
    @ApiOperation(value = "批量获取绑定过的打印流水号以及当前最大流水号", httpMethod = HttpMethodsConstants.POST)
    @RequestMapping("/serial.bound.get")
    @CheckUserSession(hasCheckPlatform = true)
    @ShopsAuth
    @RequestParamConvert(underscore = false)
    public CommonApiResponse<SerialBoundGetResponseDTO> serialBoundGet(@Validated SerialBoundGetRequest request, @ApiIgnore UserSessionInfo sessionInfo,
                                                                       TargetUserInfoDTO targetUserInfoDTO) {
        UserInfoBo userInfoBo = UserInfoBo.of(sessionInfo, targetUserInfoDTO);
        return CommonApiResponse.success(serialService.serialBoundGet(request, userInfoBo));
    }
}
