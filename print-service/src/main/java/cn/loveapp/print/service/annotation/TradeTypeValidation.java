package cn.loveapp.print.service.annotation;

import java.lang.annotation.*;

import javax.validation.Constraint;
import javax.validation.Payload;

import cn.loveapp.print.service.validation.tradetype.TradeTypeValidator;

/**
 * <AUTHOR>
 */
@Documented
@Constraint(validatedBy = TradeTypeValidator.class)
@Target({ElementType.FIELD})
@Retention(RetentionPolicy.RUNTIME)
public @interface TradeTypeValidation {

    String message() default "非法的订单类型";

    Class<?>[] groups() default {};

    Class<? extends Payload>[] payload() default {};

    @Documented
    @Target({ElementType.FIELD})
    @Retention(RetentionPolicy.RUNTIME)
    @interface List {
        TradeTypeValidation[] value();
    }

}
