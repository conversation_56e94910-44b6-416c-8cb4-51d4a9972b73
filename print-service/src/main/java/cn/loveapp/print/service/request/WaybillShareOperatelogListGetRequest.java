package cn.loveapp.print.service.request;

import cn.loveapp.common.utils.DateUtil;
import cn.loveapp.print.service.annotation.TradeTypeValidation;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;

/**
 * 共享面单日志查询request
 *
 * @program: print-services-group
 * @description: 共享面单日志查询request
 * @author: z<PERSON><PERSON><PERSON><PERSON>
 * @create: 2022/12/1 10:43
 **/
@Data
@ApiModel(value = "共享面单日志查询请求")
public class WaybillShareOperatelogListGetRequest {
    private static final DateTimeFormatter DF = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    /**
     * 页
     */
    @ApiModelProperty(value = "页", required = true)
    @NotNull
    private Integer page = 1;

    /**
     * 每页数量
     */
    @ApiModelProperty(value = "每页数量", required = true)
    @NotNull
    private Integer pageSize = 20;

    /**
     * 订单类型
     */
    @ApiModelProperty(value = "订单类型")
    @TradeTypeValidation
    private Integer tradeType;

    /**
     * 面单是否被取消
     */
    @ApiModelProperty(value = "面单是否被取消")
    private Boolean isCancel;

    /**
     * 面单服务商
     */
    @ApiModelProperty(value = "面单服务商")
    private String provider;

    /**
     * 物流公司编码
     */
    @ApiModelProperty(value = "物流公司编码")
    private String cpCode;

    /**
     * 订单号
     */
    @ApiModelProperty(value = "订单号")
    private String tid;

    /**
     * 订单号列表
     */
    @ApiModelProperty(value = "订单号列表")
    private List<String> tidList;

    /**
     * 运单号
     */
    @ApiModelProperty(value = "运单号")
    private String waybillCode;

    /**
     * 开始时间
     */
    @ApiModelProperty(value = "开始时间")
    private LocalDateTime startTime;

    /**
     * 结束时间
     */
    @ApiModelProperty(value = "结束时间")
    private LocalDateTime endTime;

    /**
     * 目标用户商家id
     */
    @ApiModelProperty(value = "目标用户商家id")
    private String sellerId;

    /**
     * 目标用户Nick
     */
    @ApiModelProperty(value = "目标用户Nick")
    private String sellerNick;

    /**
     * 目标用户 targetId
     */
    @ApiModelProperty(value = "目标用户 targetId")
    private String storeId;

    /**
     * 目标用户 appName
     */
    @ApiModelProperty(value = "目标用户 appName")
    private String appName;

    public void setStartTime(String startTime) {
        this.startTime = DateUtil.parseString(startTime);
    }

    public void setEndTime(String endTime) {
        this.endTime = DateUtil.parseString(endTime);
    }

}
