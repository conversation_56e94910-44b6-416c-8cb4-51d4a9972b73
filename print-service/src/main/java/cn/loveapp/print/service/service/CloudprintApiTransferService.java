package cn.loveapp.print.service.service;

import cn.loveapp.print.api.request.AyCloudprintBindPrinterInnerRequest;
import cn.loveapp.print.api.request.AyCloudprintCmdRenderInnerRequest;
import cn.loveapp.print.api.request.AyCloudprintGetVerifyCodeInnerRequest;
import cn.loveapp.print.api.request.AyCloudprintSendPrintTaskInnerRequest;
import cn.loveapp.print.api.response.AyCloudprintBindPrinterInnerResponse;
import cn.loveapp.print.api.response.AyCloudprintCmdRenderInnerResponse;
import cn.loveapp.print.api.response.AyCloudprintPublicResponse;

/**
 * @Author: zhongzijie
 * @Date: 2022/3/9 11:04
 * @Description: 云打印api转发服务
 */
public interface CloudprintApiTransferService {

    AyCloudprintCmdRenderInnerResponse cmdRender(AyCloudprintCmdRenderInnerRequest request, String platformId,
        String printerPlatformId, String appName);

    /**
     * 获取验证码
     *
     * @param request
     * @param platformId
     * @param printerPlatformId
     * @param appName
     * @return
     */
    AyCloudprintPublicResponse getVerifyCode(AyCloudprintGetVerifyCodeInnerRequest request, String platformId,
        String printerPlatformId, String appName);

    /**
     * 绑定打印机
     *
     * @param request
     * @param platformId
     * @param printerPlatformId
     * @param appName
     * @return
     */
    AyCloudprintBindPrinterInnerResponse bindPrinter(AyCloudprintBindPrinterInnerRequest request, String platformId,
        String printerPlatformId, String appName);

    /**
     * 发送打印任务
     *
     * @param request
     * @param platformId
     * @param printerPlatformId
     * @param appName
     * @return
     */
    AyCloudprintPublicResponse sendPrintTask(AyCloudprintSendPrintTaskInnerRequest request, String platformId,
        String printerPlatformId, String appName);
}
