package cn.loveapp.print.service.config;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

import lombok.Data;

/**
 * @Author: z<PERSON><PERSON><PERSON><PERSON>
 * @Date: 2022/3/9 13:21
 * @Description: 菜鸟云打印api相关配置
 */
@Configuration
@Data
public class CaiNiaoCloudprintConfig {
    /**
     * appkey
     */
    @Value("${cloud.print.cainiao.appkey}")
    private String appKey;

    /**
     * appSecret
     */
    @Value("${cloud.print.cainiao.appSecret}")
    private String appSecret;

    /**
     * 接口调用授权凭证（菜鸟云颁发，固定值）
     */
    @Value("${cloud.print.cainiao.token}")
    private String token;

    /**
     * 接口调用地址
     */
    @Value("${cloud.print.cainiao.url}")
    private String url;

}
