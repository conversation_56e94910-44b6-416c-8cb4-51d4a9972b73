package cn.loveapp.print.service.web;

import cn.loveapp.common.web.CommonApiStatusInterface;

/**
 * 打印service的返回值
 *
 * <AUTHOR>
 */
public enum PrintResponseStatus implements CommonApiStatusInterface {

    /**
     * 平台SDK接口异常
     */
    PlatformSdkError(50001, "平台SDK接口异常"),

    /**
     * 面单共享关系不存在
     */
    ElefaceSharingNotExistError(50002, "面单共享关系不存在或失效");

    private int code;
    private String message;

    PrintResponseStatus(int code, String message) {
        this.code = code;
        this.message = message;
    }

    @Override
    public int code() {
        return this.code;
    }

    @Override
    public String message() {
        return this.message;
    }
}
