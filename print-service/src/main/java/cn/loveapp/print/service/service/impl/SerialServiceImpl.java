package cn.loveapp.print.service.service.impl;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import cn.loveapp.print.service.config.SerialConfig;
import cn.loveapp.print.service.request.SerialBoundGetRequest;
import cn.loveapp.print.service.response.SerialBoundGetResponseDTO;
import org.apache.commons.lang3.BooleanUtils;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import com.alibaba.fastjson.JSON;

import cn.loveapp.common.constant.CommonConstants;
import cn.loveapp.common.utils.LoggerHelper;
import cn.loveapp.print.common.entity.AySerialLog;
import cn.loveapp.print.service.bo.SerialGetBo;
import cn.loveapp.print.service.bo.UserInfoBo;
import cn.loveapp.print.service.dao.newprint.AyTradeSerialLogDao;
import cn.loveapp.print.service.dao.redis.SerialRedisDao;
import cn.loveapp.print.service.request.PrintTradeInfoDTO;
import cn.loveapp.print.service.service.SerialService;

/**
 * <AUTHOR>
 */
@Service
public class SerialServiceImpl implements SerialService {
    private final static LoggerHelper LOGGER = LoggerHelper.getLogger(SerialServiceImpl.class);

    @Resource
    private SerialRedisDao serialRedisDao;

    @Resource
    private AyTradeSerialLogDao ayTradeSerialLogDao;

    @Autowired
    private SerialConfig serialConfig;

    /**
     * 获取订单流水号
     *
     * @param serialGetBo
     * @param userInfoBo
     * @return
     */
    @Override
    public String serialGet(SerialGetBo serialGetBo, UserInfoBo userInfoBo) {
        String mergeTid = serialGetBo.getMergeTid();
        List<PrintTradeInfoDTO> tradeInfoList = serialGetBo.getTradeInfoList();
        Integer tradeType = serialGetBo.getTradeType();
        Boolean isFetchNewSerial = serialGetBo.getIsFetchNewSerial();
        if (!StringUtils.isEmpty(mergeTid)) {
            // 合单
            return getSerial(mergeTid, tradeInfoList, tradeType, userInfoBo, isFetchNewSerial);
        } else {
            return getSerial(tradeInfoList.get(0), tradeType, userInfoBo, isFetchNewSerial);
        }
    }

    @Override
    public SerialBoundGetResponseDTO serialBoundGet(SerialBoundGetRequest request, UserInfoBo userInfoBo) {
        List<SerialBoundGetRequest.SerialBound> serialBoundGetList = request.getSerialBoundGetList();
        SerialBoundGetResponseDTO result = new SerialBoundGetResponseDTO();
        //1:循环查询订单、合单只需要取任意子单的流水号即可（因为流水号都一样）
        List<String> tidList = serialBoundGetList.stream().map(m -> m.getTradeInfoList().get(0).getTid()).collect(Collectors.toList());

        long maxId = 0;
        List<AySerialLog> dbQueryResultList = new LinkedList<>();
        List<AySerialLog> aySerialLogList = new ArrayList<>();

        //分页查询
        do {
            aySerialLogList = ayTradeSerialLogDao.queryByTidList(tidList, request.getTradeType(), maxId, serialConfig.getSerialBoundGetPageSize(), userInfoBo.getStoreId(), userInfoBo.getSellerId(), userInfoBo.getAppName());
            if (!CollectionUtils.isEmpty(aySerialLogList)) {
                maxId = aySerialLogList.get(aySerialLogList.size() - 1).getId();
            }
            dbQueryResultList.addAll(aySerialLogList);
        } while (aySerialLogList.size() == serialConfig.getSerialBoundGetPageSize());

        Map<String, List<AySerialLog>> tidAndSerialListMap = dbQueryResultList.stream().collect(Collectors.groupingBy(k -> k.getTid()));

        //2:判断oid组合，保留匹配成功的数据,组装响应数据
        List<SerialBoundGetResponseDTO.Serial> serialBoundList = new LinkedList<>();
        for (SerialBoundGetRequest.SerialBound serialBound : serialBoundGetList) {
            SerialBoundGetResponseDTO.Serial resultItem = new SerialBoundGetResponseDTO.Serial();
            PrintTradeInfoDTO printTradeInfoDTO = serialBound.getTradeInfoList().get(0);
            String tid = printTradeInfoDTO.getTid();
            if (StringUtils.isEmpty(serialBound.getMergeTid())) {
                resultItem.setBusinessId(tid);
            } else {
                resultItem.setBusinessId(serialBound.getMergeTid());
            }

            //存在则说明有绑定历史，否则说明未获取过流水号
            if (tidAndSerialListMap.containsKey(tid)) {
                List<AySerialLog> aySerialLogs = tidAndSerialListMap.get(tid);
                for (AySerialLog aySerialLog : aySerialLogs) {
                    List<String> oidList = Arrays.asList(aySerialLog.getOids().split(","));
                    if (printTradeInfoDTO.getOids().containsAll(oidList)) {
                        //oid全匹配，才算作匹配
                        resultItem.setSerial(aySerialLog.getSerial());
                        break;
                    }
                }
            }
            serialBoundList.add(resultItem);
        }

        //3：查询最大流水号
        String currentMaxSerial = getCurrentMaxSerial(userInfoBo);
        result.setCurrentSerial(currentMaxSerial);
        result.setSerialBoundList(serialBoundList);
        return result;
    }

    /**
     * 获取流水号（普通订单）
     *
     * @param tradeInfoDTO
     * @param tradeType
     * @param userInfoBo
     * @param isFetchNewSerial
     * @return
     */
    private String getSerial(PrintTradeInfoDTO tradeInfoDTO, Integer tradeType, UserInfoBo userInfoBo, Boolean isFetchNewSerial) {
        String serial = gerSerialFromCache(tradeInfoDTO, tradeType, userInfoBo);

        if (null == serial) {
            return generateSerial(tradeInfoDTO, tradeType, userInfoBo);
        }

        // 判断是否取新流水号
        if (BooleanUtils.isTrue(isFetchNewSerial)) {
            // 作废之前的流水号
            serialInvalidate(Collections.singletonList(tradeInfoDTO.getTid()), tradeType, userInfoBo);
            return generateSerial(tradeInfoDTO, tradeType, userInfoBo);
        } else {
            return serial;
        }
    }

    /**
     * 获取流水号（合单）
     *
     * @param mergeTid
     * @param tradeInfoList
     * @param tradeType
     * @param userInfoBo
     * @param isFetchNewSerial
     * @return
     */
    private String getSerial(String mergeTid, List<PrintTradeInfoDTO> tradeInfoList, Integer tradeType,
                             UserInfoBo userInfoBo, Boolean isFetchNewSerial) {
        List<String> serialList = new ArrayList<>();

        for (PrintTradeInfoDTO tradeInfoDTO : tradeInfoList) {
            // 逐个查询子单流水号
            String serial = gerSerialFromCache(tradeInfoDTO, tradeType, userInfoBo);
            if (null == serial) {
                break;
            }
            serialList.add(serial);
        }
        // 判断是否所有的子单都有流水号并且相同
        if (serialList.size() != tradeInfoList.size()
                || serialList.stream().anyMatch(item -> !item.equals(serialList.get(0))) || BooleanUtils.isTrue(isFetchNewSerial)) {
            // 作废合单之前的流水号
            serialInvalidate(tradeInfoList.stream().map(PrintTradeInfoDTO::getTid).collect(Collectors.toList()),
                    tradeType, userInfoBo);
            // 重新生成流水号
            return generateSerial(mergeTid, tradeInfoList, tradeType, userInfoBo);
        } else {
            return serialList.get(0);
        }
    }

    private String gerSerialFromCache(PrintTradeInfoDTO tradeInfoDTO, Integer tradeType, UserInfoBo userInfoBo) {
        String tid = tradeInfoDTO.getTid();
        List<String> oids = tradeInfoDTO.getOids();

        // 从redis查询订单的流水号
        Map<String, List<String>> serialToOidsMap = serialRedisDao.getTradeSerial(tid, tradeType, userInfoBo);
        if (CollectionUtils.isEmpty(serialToOidsMap)) {
            // redis没有，尝试用db获取
            serialToOidsMap = getSerialFromDB(tid, tradeType, userInfoBo);
        }
        if (!CollectionUtils.isEmpty(serialToOidsMap)) {
            for (String serial : serialToOidsMap.keySet()) {
                if (oids.size() == serialToOidsMap.get(serial).size()
                    && oids.containsAll(serialToOidsMap.get(serial))) {
                    // 有相同组合oids则返回该流水号
                    return serial;
                }
            }
        }
        return null;
    }

    /**
     * 从数据库查询订单流水号
     *
     * @param tid
     * @param tradeType
     * @param userInfoBo
     * @return
     */
    private Map<String, List<String>> getSerialFromDB(String tid, Integer tradeType, UserInfoBo userInfoBo) {
        // 先查询该订单相关的所有流水号
        List<AySerialLog> aySerialLogList = ayTradeSerialLogDao.queryByTid(tid, tradeType, userInfoBo.getStoreId(),
            userInfoBo.getSellerId(), userInfoBo.getAppName());
        if (!CollectionUtils.isEmpty(aySerialLogList)) {
            Map<String, List<String>> serialToOidsMap = new HashMap<>(aySerialLogList.size());
            for (AySerialLog ayPrintlog : aySerialLogList) {
                serialToOidsMap.put(ayPrintlog.getSerial(), Arrays.asList(ayPrintlog.getOids().split(",")));
            }
            // 更新到redis
            serialRedisDao.flushTradeSerial(serialToOidsMap, tid, tradeType, userInfoBo);
            return serialToOidsMap;
        }
        return null;
    }

    /**
     * 生成流水号
     *
     * @param createTime
     * @param indexNumber
     * @return
     */
    private String generateSerial(LocalDateTime createTime, Long indexNumber) {
        String numberStr = String.format("%04d", indexNumber);
        if (indexNumber > 9999) {
            // 1w以上超过4位，直接返回
            numberStr = String.valueOf(indexNumber);
        }
        return createTime.format(DateTimeFormatter.ofPattern("MMdd")) + numberStr;
    }

    /**
     * 生成新的流水号
     *
     * @param tradeInfoDTO
     * @param tradeType
     * @param userInfoBo
     * @return
     */
    private String generateSerial(PrintTradeInfoDTO tradeInfoDTO, Integer tradeType, UserInfoBo userInfoBo) {

        Long indexNumber = serialRedisDao.generateUserSerialIndex(userInfoBo);
        // 生成流水号
        LocalDateTime createTime = LocalDateTime.now();
        String serial = generateSerial(createTime, indexNumber);

        String tid = tradeInfoDTO.getTid();
        List<String> oids = tradeInfoDTO.getOids();

        LOGGER.logInfo(MDC.get(CommonConstants.nick), tid, "生成新的流水号：" + serial);

        // 流水号落库
        AySerialLog aySerialLog = new AySerialLog();
        aySerialLog.setTid(tid);
        aySerialLog.setTradeType(tradeType);
        aySerialLog.setOids(oids);
        aySerialLog.setAppName(userInfoBo.getAppName());
        aySerialLog.setSellerId(userInfoBo.getSellerId());
        aySerialLog.setSellerNick(userInfoBo.getSellerNick());
        aySerialLog.setStoreId(userInfoBo.getStoreId());
        aySerialLog.setCreateTime(createTime);
        aySerialLog.setIndexNumber(indexNumber);
        aySerialLog.setSerial(serial);
        aySerialLog.setOperator(userInfoBo.getOperator());
        aySerialLog.setOperatorStoreId(userInfoBo.getOperatorStoreId());
        aySerialLog.setOperateTerminal(userInfoBo.getOperateTerminal());
        ayTradeSerialLogDao.insert(aySerialLog);

        // 保存到redis
        serialRedisDao.saveTradeSerial(serial, tid, oids, tradeType, userInfoBo);

        return serial;
    }

    /**
     * 获取当前最大流水号
     * @param userInfoBo
     * @return
     */
    private String getCurrentMaxSerial(UserInfoBo userInfoBo){
        Long indexNumber = serialRedisDao.getUserCurrentSerialIndex(userInfoBo);
        // 生成流水号
        LocalDateTime createTime = LocalDateTime.now();
        String serial = generateSerial(createTime, indexNumber);

        LOGGER.logInfo(MDC.get(CommonConstants.nick), "-", "当前最大流水号为：" + serial);
        return serial;
    }

    private String generateSerial(String mergeTid, List<PrintTradeInfoDTO> tradeInfoList, Integer tradeType,
        UserInfoBo userInfoBo) {

        Long indexNumber = serialRedisDao.generateUserSerialIndex(userInfoBo);
        // 生成流水号
        LocalDateTime createTime = LocalDateTime.now();
        String serial = generateSerial(createTime, indexNumber);

        LOGGER.logInfo(MDC.get(CommonConstants.nick), "-",
            "合单订单生成新的流水号：" + serial + ",tradeInfoDTOList=" + JSON.toJSONString(tradeInfoList));

        List<AySerialLog> aySerialLogList = new ArrayList<>();
        for (PrintTradeInfoDTO tradeInfoDTO : tradeInfoList) {
            String tid = tradeInfoDTO.getTid();
            List<String> oids = tradeInfoDTO.getOids();

            AySerialLog aySerialLog = new AySerialLog();
            aySerialLog.setTid(tid);
            aySerialLog.setTradeType(tradeType);
            aySerialLog.setMergeTid(mergeTid);
            aySerialLog.setOids(oids);
            aySerialLog.setAppName(userInfoBo.getAppName());
            aySerialLog.setSellerId(userInfoBo.getSellerId());
            aySerialLog.setSellerNick(userInfoBo.getSellerNick());
            aySerialLog.setStoreId(userInfoBo.getStoreId());
            aySerialLog.setCreateTime(createTime);
            aySerialLog.setIndexNumber(indexNumber);
            aySerialLog.setSerial(serial);
            aySerialLog.setOperator(userInfoBo.getOperator());
            aySerialLog.setOperatorStoreId(userInfoBo.getOperatorStoreId());
            aySerialLog.setOperateTerminal(userInfoBo.getOperateTerminal());
            aySerialLogList.add(aySerialLog);

            // 保存到redis
            serialRedisDao.saveTradeSerial(serial, tid, oids, tradeType, userInfoBo);
        }
        ayTradeSerialLogDao.batchInsert(aySerialLogList);

        return serial;
    }

    @Override
    public void serialInvalidate(List<String> tids, Integer tradeType, UserInfoBo userInfoBo) {
        // 数据库标记作废
        ayTradeSerialLogDao.updateIsInvalidByTids(true, tids, tradeType, userInfoBo.getStoreId(),
            userInfoBo.getSellerId(), userInfoBo.getAppName());
        // 删除Redis中的流水号缓存
        serialRedisDao.deleteTradeSerial(tids, tradeType, userInfoBo);
    }
}
