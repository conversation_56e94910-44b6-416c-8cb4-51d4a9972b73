package cn.loveapp.print.service.config;

import java.util.List;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

import com.google.common.collect.Lists;

import lombok.Data;

/**
 * 版本灰度控制相关配置
 *
 * <AUTHOR>
 */
@Configuration
@Data
public class VersionGrayConfig {

    public static final String CONFIGURATION_PREFIX = "print.version.gray";

    /**
     * 是否开启灰度
     */
    @Value("${print.version.gray.enable:false}")
    private boolean enable = false;



    /**
     * 灰度版本内的用户Nick
     */
    @Value("${print.version.gray.users:}")
    private List<String> users = Lists.newArrayList();

    /**
     * 灰度的平台
     */
    @Value("${print.version.gray.platforms:}")
    private List<String> platforms = Lists.newArrayList();

    /**
     * 灰度服务的host
     */
    @Value("${print.version.gray.serviceHost:}")
    private String serviceHost;
}
