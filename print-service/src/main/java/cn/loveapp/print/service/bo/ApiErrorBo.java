package cn.loveapp.print.service.bo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @program: print-services-group
 * @description: api异常返回信息Bo
 * @author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @create: 2023/1/3 17:56
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ApiErrorBo {

    /**
     * 面单请求Id
     */
    private String objectId;

    /**
     * 异常状态码
     */
    private String code;

    /**
     * 异常信息
     */
    private String msg;

    /**
     * 异常状态详情码
     */
    private String subCode;

    /**
     * 异常状态详情信息
     */
    private String subMsg;

    /**
     * 淘宝请求Id
     */
    private String requestId;

    public ApiErrorBo(String objectId) {
        this.objectId = objectId;
    }
}
