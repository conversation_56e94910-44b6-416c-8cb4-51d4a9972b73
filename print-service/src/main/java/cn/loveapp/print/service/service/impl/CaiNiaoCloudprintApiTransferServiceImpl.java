package cn.loveapp.print.service.service.impl;

import cn.loveapp.print.api.request.AyCloudprintCmdRenderInnerRequest;
import cn.loveapp.print.api.response.AyCloudprintCmdRenderInnerResponse;
import com.google.common.collect.Lists;
import com.taobao.pac.sdk.cp.dataobject.request.CLOUDPRINT_CMD_RENDER.CloudprintCmdRenderRequest;
import com.taobao.pac.sdk.cp.dataobject.request.CLOUDPRINT_CMD_RENDER.Document;
import com.taobao.pac.sdk.cp.dataobject.request.CLOUDPRINT_CMD_RENDER.RenderConfig;
import com.taobao.pac.sdk.cp.dataobject.request.CLOUDPRINT_CMD_RENDER.RenderContent;
import com.taobao.pac.sdk.cp.dataobject.response.CLOUDPRINT_CMD_RENDER.CloudprintCmdRenderResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.taobao.pac.sdk.cp.dataobject.request.CLOUDPRINT_PRINTER_BIND.CloudprintPrinterBindRequest;
import com.taobao.pac.sdk.cp.dataobject.request.CLOUDPRINT_PRINTER_GET_VERIFY_CODE.CloudprintPrinterGetVerifyCodeRequest;
import com.taobao.pac.sdk.cp.dataobject.request.CLOUDPRINT_PRINTER_PRINT.CloudprintPrinterPrintRequest;
import com.taobao.pac.sdk.cp.dataobject.request.CLOUDPRINT_PRINTER_PRINT.CustomData;
import com.taobao.pac.sdk.cp.dataobject.request.CLOUDPRINT_PRINTER_PRINT.PrintData;
import com.taobao.pac.sdk.cp.dataobject.response.CLOUDPRINT_PRINTER_BIND.CloudprintPrinterBindResponse;
import com.taobao.pac.sdk.cp.dataobject.response.CLOUDPRINT_PRINTER_GET_VERIFY_CODE.CloudprintPrinterGetVerifyCodeResponse;
import com.taobao.pac.sdk.cp.dataobject.response.CLOUDPRINT_PRINTER_PRINT.CloudprintPrinterPrintResponse;

import cn.loveapp.common.web.CommonApiStatus;
import cn.loveapp.print.api.request.AyCloudprintBindPrinterInnerRequest;
import cn.loveapp.print.api.request.AyCloudprintGetVerifyCodeInnerRequest;
import cn.loveapp.print.api.request.AyCloudprintSendPrintTaskInnerRequest;
import cn.loveapp.print.api.response.AyCloudprintBindPrinterInnerResponse;
import cn.loveapp.print.api.response.AyCloudprintPublicResponse;
import cn.loveapp.print.service.sdk.CaiNiaoSDKService;
import cn.loveapp.print.service.service.CloudprintApiTransferService;

import java.util.List;

/**
 * @Author: zhongzijie
 * @Date: 2022/3/9 12:55
 * @Description: 菜鸟云打印api转发服务
 */
@Service
public class CaiNiaoCloudprintApiTransferServiceImpl implements CloudprintApiTransferService {

    @Autowired
    private CaiNiaoSDKService caiNiaoSDKService;

    @Override
    public AyCloudprintCmdRenderInnerResponse cmdRender(AyCloudprintCmdRenderInnerRequest request, String platformId,
        String printerPlatformId, String appName) {
        CloudprintCmdRenderRequest cloudprintCmdRenderRequest = new CloudprintCmdRenderRequest();
        cloudprintCmdRenderRequest.setClientId(request.getClientId());
        cloudprintCmdRenderRequest.setRequestId(request.getRequestId());
        cloudprintCmdRenderRequest.setClientType(request.getClientType());
        AyCloudprintCmdRenderInnerRequest.RenderConfig reqConfig = request.getConfig();
        RenderConfig renderConfig = new RenderConfig();
        renderConfig.setNeedTopLogo(reqConfig.getNeedTopLogo());
        renderConfig.setNeedMiddleLogo(reqConfig.getNeedMiddleLogo());
        renderConfig.setNeedBottomLogo(reqConfig.getNeedBottomLogo());
        renderConfig.setOrientation(reqConfig.getOrientation());
        renderConfig.setExtra(reqConfig.getExtra());
        cloudprintCmdRenderRequest.setConfig(renderConfig);
        AyCloudprintCmdRenderInnerRequest.Document reqDocument = request.getDocument();
        Document document = new Document();
        List<AyCloudprintCmdRenderInnerRequest.RenderContent> reqRenderContents = reqDocument.getContents();
        List<RenderContent> renderContents = Lists.newArrayList();
        for (AyCloudprintCmdRenderInnerRequest.RenderContent reqRenderContent : reqRenderContents) {
            RenderContent renderContent = new RenderContent();
            renderContent.setAddData(reqRenderContent.getAddData());
            renderContent.setEncrypted(reqRenderContent.getEncrypted());
            renderContent.setSignature(reqRenderContent.getSignature());
            renderContent.setPrintData(reqRenderContent.getPrintData());
            renderContent.setTemplateUrl(reqRenderContent.getTemplateUrl());
            renderContent.setVer(reqRenderContent.getVer());
            renderContents.add(renderContent);
        }
        document.setContents(renderContents);
        cloudprintCmdRenderRequest.setDocument(document);
        cloudprintCmdRenderRequest.setPrinterName(request.getPrinterName());
        CloudprintCmdRenderResponse cloudprintCmdRenderResponse = caiNiaoSDKService.execute(cloudprintCmdRenderRequest);
        AyCloudprintCmdRenderInnerResponse response = new AyCloudprintCmdRenderInnerResponse();
        if (cloudprintCmdRenderResponse == null) {
            response.setSuccess(Boolean.FALSE);
            response.setErrorCode(String.valueOf(CommonApiStatus.Failed.code()));
            response.setErrorMsg(CommonApiStatus.Failed.message());
            return response;
        }
        response.setCmdContent(cloudprintCmdRenderResponse.getCmdContent());
        response.setCmdEncoding(cloudprintCmdRenderResponse.getCmdEncoding());
        response.setSuccess(cloudprintCmdRenderResponse.isSuccess());
        response.setErrorCode(cloudprintCmdRenderResponse.getErrorCode());
        response.setErrorMsg(cloudprintCmdRenderResponse.getErrorMsg());
        return response;
    }

    @Override
    public AyCloudprintPublicResponse getVerifyCode(AyCloudprintGetVerifyCodeInnerRequest request, String platformId,
        String printerPlatformId, String appName) {
        CloudprintPrinterGetVerifyCodeRequest cloudprintPrinterGetVerifyCodeRequest =
            new CloudprintPrinterGetVerifyCodeRequest();
        cloudprintPrinterGetVerifyCodeRequest.setPrinterId(request.getPrinterId());
        CloudprintPrinterGetVerifyCodeResponse cloudprintPrinterGetVerifyCodeResponse =
            caiNiaoSDKService.execute(cloudprintPrinterGetVerifyCodeRequest);
        AyCloudprintPublicResponse response = new AyCloudprintPublicResponse();
        if (cloudprintPrinterGetVerifyCodeResponse == null) {
            response.setSuccess(Boolean.FALSE);
            response.setErrorCode(CommonApiStatus.Failed.code() + "");
            response.setErrorMsg(CommonApiStatus.Failed.message());
            return response;
        }
        response.setSuccess(cloudprintPrinterGetVerifyCodeResponse.isSuccess());
        response.setErrorCode(cloudprintPrinterGetVerifyCodeResponse.getErrorCode());
        response.setErrorMsg(cloudprintPrinterGetVerifyCodeResponse.getErrorMsg());
        return response;
    }

    @Override
    public AyCloudprintBindPrinterInnerResponse bindPrinter(AyCloudprintBindPrinterInnerRequest request,
        String platformId, String printerPlatformId, String appName) {
        CloudprintPrinterBindRequest cloudprintPrinterBindRequest = new CloudprintPrinterBindRequest();
        cloudprintPrinterBindRequest.setPrinterId(request.getPrinterId());
        cloudprintPrinterBindRequest.setUserId(request.getSellerNick());
        cloudprintPrinterBindRequest.setVerifyCode(request.getVerifyCode());
        CloudprintPrinterBindResponse cloudprintPrinterBindResponse =
            caiNiaoSDKService.execute(cloudprintPrinterBindRequest);
        AyCloudprintBindPrinterInnerResponse response = new AyCloudprintBindPrinterInnerResponse();
        if (cloudprintPrinterBindResponse == null) {
            response.setSuccess(Boolean.FALSE);
            response.setErrorCode(CommonApiStatus.Failed.code() + "");
            response.setErrorMsg(CommonApiStatus.Failed.message());
            return response;
        }
        if (cloudprintPrinterBindResponse.getCloudPrintSharedCodeResponse() != null) {
            response.setShardCode(cloudprintPrinterBindResponse.getCloudPrintSharedCodeResponse().getShardCode());
        }
        response.setSuccess(cloudprintPrinterBindResponse.isSuccess());
        response.setErrorCode(cloudprintPrinterBindResponse.getErrorCode());
        response.setErrorMsg(cloudprintPrinterBindResponse.getErrorMsg());
        return response;
    }

    @Override
    public AyCloudprintPublicResponse sendPrintTask(AyCloudprintSendPrintTaskInnerRequest request, String platformId,
        String printerPlatformId, String appName) {
        CloudprintPrinterPrintRequest cloudprintPrinterPrintRequest = new CloudprintPrinterPrintRequest();
        cloudprintPrinterPrintRequest.setPrinterId(request.getPrinterId());
        cloudprintPrinterPrintRequest.setUserId(request.getSellerNick());
        cloudprintPrinterPrintRequest.setSharedCode(request.getShardCode());
        PrintData printData = new PrintData();
        printData.setData(request.getPrintData());
        printData.setAddData(request.getPrintAddData());
        printData.setEncrypted(request.getPrintEncrypted());
        printData.setSignature(request.getPrintSignature());
        printData.setTemplateUrl(request.getPrintTemplateUrl());
        cloudprintPrinterPrintRequest.setPrintData(printData);
        CustomData customData = new CustomData();
        customData.setData(request.getCustomData());
        customData.setTemplateUrl(request.getCustomTemplateUrl());
        cloudprintPrinterPrintRequest.setCustomData(customData);
        cloudprintPrinterPrintRequest.setCurrentPageCount(request.getCurrentPageCount());
        cloudprintPrinterPrintRequest.setTotalPageCount(request.getTotalPageCount());
        CloudprintPrinterPrintResponse cloudprintPrinterPrintResponse =
            caiNiaoSDKService.execute(cloudprintPrinterPrintRequest);
        AyCloudprintPublicResponse response = new AyCloudprintPublicResponse();
        if (cloudprintPrinterPrintResponse == null) {
            response.setSuccess(Boolean.FALSE);
            response.setErrorCode(CommonApiStatus.Failed.code() + "");
            response.setErrorMsg(CommonApiStatus.Failed.message());
            return response;
        }
        response.setSuccess(cloudprintPrinterPrintResponse.isSuccess());
        response.setErrorCode(cloudprintPrinterPrintResponse.getErrorCode());
        response.setErrorMsg(cloudprintPrinterPrintResponse.getErrorMsg());
        return response;
    }
}
