package cn.loveapp.print.service.web;

import java.io.IOException;
import java.lang.reflect.Type;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import cn.loveapp.common.dto.UserSessionInfo;
import cn.loveapp.common.user.session.constant.SessionConstants;
import cn.loveapp.print.common.utils.RoutingDelegateUtils;
import cn.loveapp.print.service.config.VersionGrayConfig;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.core.MethodParameter;
import org.springframework.http.HttpInputMessage;
import org.springframework.http.ResponseEntity;
import org.springframework.http.converter.HttpMessageConverter;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.servlet.handler.HandlerInterceptorAdapter;
import org.springframework.web.servlet.mvc.method.annotation.RequestBodyAdvice;

import cn.loveapp.common.utils.LoggerHelper;
import cn.loveapp.uac.request.BaseHttpRequest;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.SneakyThrows;

/**
 * 版本灰度 处理器
 *
 * <AUTHOR>
 */
@ConditionalOnProperty(prefix = VersionGrayConfig.CONFIGURATION_PREFIX, name = "enable", havingValue = "true")
@ControllerAdvice
public class VersionGrayHandler extends HandlerInterceptorAdapter implements RequestBodyAdvice {

    private static final LoggerHelper LOGGER = LoggerHelper.getLogger(VersionGrayHandler.class);

    private static final String SELLER_NICK_REQUEST_PARAM = "sellerNick";

    private static final String PLATFORM_ID_REQUEST_PARAM = "platformId";

    @Autowired
    private VersionGrayConfig versionGrayConfig;

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        String sellerNick = request.getParameter(SELLER_NICK_REQUEST_PARAM);
        String platformId = request.getParameter(PLATFORM_ID_REQUEST_PARAM);
        if(StringUtils.isEmpty(sellerNick)) {
            UserSessionInfo userSessionInfo = (UserSessionInfo)request.getAttribute(SessionConstants.REQUEST_ATTRIBUTE_SESSIONINFO);
            if(userSessionInfo != null){
                sellerNick = userSessionInfo.getNick();
                platformId = StringUtils.defaultString(platformId, userSessionInfo.getStoreId());
            }
        }
        if (checkGrayConditions(sellerNick, platformId)) {
            LOGGER.logInfo(request.getParameter(SELLER_NICK_REQUEST_PARAM), platformId, "灰度名单中用户, 准备进行灰度转发");
            throw new VersionGrayHitException();
        }
        return true;
    }

    @Override
    public boolean supports(MethodParameter methodParameter, Type targetType, Class<? extends HttpMessageConverter<?>> converterType) {
        return versionGrayConfig.isEnable();
    }

    @Override
    public HttpInputMessage beforeBodyRead(HttpInputMessage inputMessage, MethodParameter parameter, Type targetType, Class<? extends HttpMessageConverter<?>> converterType) throws IOException {
        return inputMessage;
    }

    @SneakyThrows
    @Override
    public Object afterBodyRead(Object body, HttpInputMessage inputMessage, MethodParameter parameter, Type targetType, Class<? extends HttpMessageConverter<?>> converterType) {
        if (body instanceof BaseHttpRequest) {
            String sellerNick = ((BaseHttpRequest) body).getSellerNick();
            String platformId = ((BaseHttpRequest) body).getPlatformId();
            if (checkGrayConditions(sellerNick, platformId)) {
                LOGGER.logInfo(sellerNick, platformId, "灰度名单中用户, 准备进行灰度转发");
                throw new VersionGrayHitException((BaseHttpRequest) body);
            }
        }
        return body;
    }

    @Override
    public Object handleEmptyBody(Object body, HttpInputMessage inputMessage, MethodParameter parameter, Type targetType, Class<? extends HttpMessageConverter<?>> converterType) {
        return body;
    }


    /**
     * 处理VersionGrayHitException异常, 执行灰度策略
     *
     * @param request
     */
    @ExceptionHandler(value = VersionGrayHitException.class)
    public ResponseEntity<String> catVersionGrayHitException(HttpServletRequest request, VersionGrayHitException e) throws Exception {
        RoutingDelegateUtils.RequestRedirectDTO<BaseHttpRequest> requestRedirectDTO = new RoutingDelegateUtils.RequestRedirectDTO<>();
        requestRedirectDTO.setRedirectHost(versionGrayConfig.getServiceHost());
        requestRedirectDTO.setMethod(request.getMethod());
        requestRedirectDTO.setHeaders(RoutingDelegateUtils.parseRequestHeader(request));
        requestRedirectDTO.setRequestURI(request.getRequestURI());
        requestRedirectDTO.setQueryString(request.getQueryString());
        requestRedirectDTO.setParameters(RoutingDelegateUtils.parseRequestParameter(request));
        requestRedirectDTO.setBody(e.getRequestBody());
        return RoutingDelegateUtils.redirect(requestRedirectDTO);
    }

    /**
     * 灰度命中的异常
     * <p>
     * 判断request_body中信息满足灰度条件后抛出异常信息, 通过异常处理来执行灰度逻辑
     */
    @EqualsAndHashCode(callSuper = true)
    @Data
    protected static class VersionGrayHitException extends Exception {
        private BaseHttpRequest requestBody;

        public VersionGrayHitException() {
        }

        public VersionGrayHitException(BaseHttpRequest requestBody) {
            this.requestBody = requestBody;
        }
    }

    /**
     * 校验是否满足灰度条件
     *
     * @param sellerNick
     * @param platformId
     * @return
     */
    private boolean checkGrayConditions(String sellerNick, String platformId){

        return versionGrayConfig.isEnable() &&
                (versionGrayConfig.getUsers().contains(sellerNick) || versionGrayConfig.getPlatforms().contains(platformId));
    }
}
