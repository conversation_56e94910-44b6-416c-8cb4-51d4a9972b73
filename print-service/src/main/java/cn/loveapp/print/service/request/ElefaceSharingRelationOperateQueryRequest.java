package cn.loveapp.print.service.request;

import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 面单操作日志查询request
 *
 * <AUTHOR>
 * @Date 2024/7/25 5:23 PM
 */
@Data
public class ElefaceSharingRelationOperateQueryRequest {


    /**
     * 页
     */
    private Integer page = 1;

    /**
     * 每页数量
     */
    private Integer pageSize = 20;

    /**
     * 分享id
     */
    private List<String> shardIdList;

    /**
     * 模糊搜索店铺字段（分享者、面单账号、被分享者、备注）
     */
    private String fuzzySearchStr;

    /**
     * 面单所有者平台
     */
    private String ownerStoreId;

    /**
     * 面单被分享平台
     */
    private String elefaceTargetStoreId;

    /**
     * 搜索开始时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime operateTimeStart;

    /**
     * 搜索结束时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime operateTimeEnd;

    /**
     * 面单备注搜索
     */
    private String shareMemo;

    /**
     * 业务员
     */
    private String salesman;

    /**
     * 快递公司
     */
    private String cpCode;

    /**
     * 快递公司
     */
    private List<String> cpCodeList;

    /**
     * 服务商
     */
    private String provider;

    /**
     * 面单所有者id
     */
    private String ownerSellerId;

    /**
     * 面单账户模糊搜索字段
     */
    private String ownerSearchStr;

    /**
     * 面单被分享者模糊搜索字段
     */
    private String targetSearchStr;

    /**
     * 操作类型列表，用于过滤特定类型的操作日志
     * 如果为空，则默认返回除备注更新外的所有操作类型（兼容老客户端）
     */
    private List<Integer> operateTypeList;


}
