package cn.loveapp.print.service.request;

import javax.validation.constraints.NotEmpty;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 解除分享给我的面单共享关系 request
 *
 * <AUTHOR>
 */
@ApiModel
@Data
public class SharingToMeRemoveRequest {
    /**
     * 面单所有者的sellerId
     */
    @ApiModelProperty(value = "面单所有者的sellerId", required = true)
    @NotEmpty
    private String ownerSellerId;

    /**
     * 面单所有者的平台id
     */
    @ApiModelProperty(value = "面单所有者的平台id", required = true)
    @NotEmpty
    private String ownerStoreId;

    /**
     * 面单所有者的应用
     */
    @ApiModelProperty(value = "面单所有者的应用", required = true)
    @NotEmpty
    private String ownerAppName;
}
