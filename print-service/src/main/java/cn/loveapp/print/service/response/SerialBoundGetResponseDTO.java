package cn.loveapp.print.service.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/***
 * <AUTHOR>
 * @Description 批量获取订单流水号绑定记录响应体
 * @Date 15:33 2023/9/12
 **/
@Data
@ApiModel(value = "批量获取订单流水号绑定记录响应体")
public class SerialBoundGetResponseDTO {


    /**
     * 当前最大流水号
     */
    @ApiModelProperty(value = "当前最大流水号")
    private String currentSerial;

    /**
     * 流水号集合
     */
    @ApiModelProperty(value = "订单流水号绑定集合")
    List<Serial> serialBoundList;


    @Data
    public static class Serial {
        /**
         * 流水号
         */
        @ApiModelProperty(value = "流水号")
        private String serial;

        /**
         * 业务id，合单时是mergeId，单个订单时是tid
         */
        @ApiModelProperty(value = "业务id，合单时是mergeId，单个订单时是tid")
        private String businessId;
    }
}
