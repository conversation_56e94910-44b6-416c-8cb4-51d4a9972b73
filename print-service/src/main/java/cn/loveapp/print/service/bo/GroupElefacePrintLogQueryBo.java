package cn.loveapp.print.service.bo;

import java.time.LocalDateTime;
import java.util.List;

import org.springframework.beans.BeanUtils;

import cn.loveapp.print.service.request.GroupElefacePrintLogRequest;
import lombok.Data;

/**
 * 面单统计查询条件的Bo
 *
 * <AUTHOR>
 */
@Data
public class GroupElefacePrintLogQueryBo {

    /**
     * 物流公司列表
     */
    private List<String> cpCodeList;

    /**
     * 收件人省份列表
     */
    private List<String> receiverProvinceList;

    /**
     * 电子面单服务商 {@link cn.loveapp.print.common.constant.ElefaceProviderConstant}
     */
    private String elefaceProvider;

    /**
     * 打印时间-起始日期
     */
    private LocalDateTime startTime;

    /**
     * 打印时间-结束日期
     */
    private LocalDateTime endTime;

    public static GroupElefacePrintLogQueryBo of(GroupElefacePrintLogRequest request) {
        GroupElefacePrintLogQueryBo queryBo = new GroupElefacePrintLogQueryBo();
        BeanUtils.copyProperties(request, queryBo);
        return queryBo;
    }
}
