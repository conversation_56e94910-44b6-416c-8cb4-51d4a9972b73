package cn.loveapp.print.service.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 目标用户信息的DTO 用于多店鉴权后存储用户信息
 *
 * <AUTHOR>
 * @date 2020-02-10 21:07:13
 */
@Data
@ApiModel(value = "目标用户信息(多店鉴权)")
@NoArgsConstructor
@AllArgsConstructor
public class TargetUserInfoDTO {

    /**
     * 目标用户商家id
     */
    @ApiModelProperty(value = "目标用户商家id")
    private String sellerId;

    /**
     * 目标用户Nick
     */
    @ApiModelProperty(value = "目标用户Nick")
    private String sellerNick;

    /**
     * 目标用户 targetId
     */
    @ApiModelProperty(value = "目标用户targetId")
    private String storeId;

    /**
     * 目标用户 appName
     */
    @ApiModelProperty(value = "目标用户appName")
    private String appName;
}
