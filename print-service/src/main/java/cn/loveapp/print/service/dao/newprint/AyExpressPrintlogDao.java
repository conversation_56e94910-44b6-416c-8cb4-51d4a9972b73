package cn.loveapp.print.service.dao.newprint;

import java.util.List;

import cn.loveapp.print.common.entity.AyExpressPrintlog;

/**
 * <AUTHOR>
 */
public interface AyExpressPrintlogDao {

    /**
     * 新增一条数据
     *
     * @param ayExpressPrintlog
     * @return
     */
    int insert(AyExpressPrintlog ayExpressPrintlog);

    /**
     * 通过id批量查询打印信息
     *
     * @param ids
     * @param sellerId
     * @return
     */
    List<AyExpressPrintlog> queryPrintData(List<Long> ids, String sellerId);
}
