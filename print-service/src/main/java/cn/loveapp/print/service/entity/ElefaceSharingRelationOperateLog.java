package cn.loveapp.print.service.entity;

import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 面单分享操作日志实体
 *
 * <AUTHOR>
 * @Date 2024/7/25 2:21 PM
 */
@Data
public class ElefaceSharingRelationOperateLog implements Serializable {

    private static final long serialVersionUID = 1733993382018354463L;

    /**
     * 主键自增id
     */
    private Long id;

    /**
     * 面单分享Id
     */
    private String shareId;

    /**
     * 目标用户id
     */
    private String targetSellerId;
    /**
     * 目标用户nick
     */
    private String targetSellerNick;
    /**
     * 目标用户平台
     */
    private String targetStoreId;
    /**
     * 目标用户信息应用
     */
    private String targetAppName;

    /**
     * 面单分享备注（操作时的备注）
     */
    private String shareMemo;

    /**
     * 操作类型
     */
    private Integer operateType;

    /**
     * 更新前分享数量
     */
    private Long lastShareNum;

    /**
     * 更新后分享数量
     */
    private Long newShareNum;

    /**
     * 操作终端
     */
    private String operateTerminal;

    /**
     * 操作用户
     */
    private String operator;

    /**
     * 操作时间
     */
    private LocalDateTime operatorTime;

    /**
     * 记录创建时间
     */
    private LocalDateTime gmtCreate;

    /**
     * 记录修改时间
     */
    private LocalDateTime gmtModified;


}
