package cn.loveapp.print.service.service.impl;

import cn.loveapp.print.api.proto.ShareRelationChangeRequestProto;
import cn.loveapp.print.api.proto.WaybillOperateLogStatusChangeRequestProto;
import cn.loveapp.print.common.constant.PrintGeneralConsumerConstant;
import cn.loveapp.print.common.rocketmq.RocketMQGeneralConfig;
import cn.loveapp.print.common.utils.RocketMqQueueHelper;
import cn.loveapp.print.service.service.PrintMessageSendService;
import org.apache.rocketmq.client.producer.DefaultMQProducer;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @Date 2024/9/28 4:56 PM
 */
@Service
public class PrintMessageSendServiceImpl implements PrintMessageSendService {

    @Autowired
    private DefaultMQProducer mqProducer;

    @Autowired
    private RocketMqQueueHelper rocketMqQueueHelper;

    @Autowired
    private RocketMQGeneralConfig rocketMQGeneralConfig;

    @Override
    public void pushRelationChangeMessage(ShareRelationChangeRequestProto proto) {
        String topic = rocketMQGeneralConfig.getTopic();
        Integer delayTimeLevel = rocketMQGeneralConfig.getDelayTimeLevel();
        rocketMqQueueHelper.push(topic, PrintGeneralConsumerConstant.SHARE_OPERATE_UPDATE.val, proto,
                mqProducer, delayTimeLevel);
    }

    @Override
    public void pushOperateLogStatusChangeMessage(WaybillOperateLogStatusChangeRequestProto proto) {
        String topic = rocketMQGeneralConfig.getTopic();
        Integer delayTimeLevel = rocketMQGeneralConfig.getDelayTimeLevel();
        rocketMqQueueHelper.push(topic, PrintGeneralConsumerConstant.WAYBILL_OPERATELOG_STATUS_CHANGE.val, proto,
            mqProducer, delayTimeLevel);
    }
}
