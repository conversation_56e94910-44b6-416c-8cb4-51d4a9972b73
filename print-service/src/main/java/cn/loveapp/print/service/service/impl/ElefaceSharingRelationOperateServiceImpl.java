package cn.loveapp.print.service.service.impl;

import cn.loveapp.common.utils.LoggerHelper;
import cn.loveapp.print.api.dto.ElefaceSharingRelationDTO;
import cn.loveapp.print.common.exception.CommonException;
import cn.loveapp.print.common.service.UserCenterService;
import cn.loveapp.print.common.utils.ListUtil;
import cn.loveapp.print.service.bo.ElefaceSharingRelationQueryBo;
import cn.loveapp.print.service.bo.UserInfoBo;
import cn.loveapp.print.service.convert.CommonConvertMapper;
import cn.loveapp.print.service.dao.print.ElefaceSharingRelationDao;
import cn.loveapp.print.service.dao.print.ElefaceSharingRelationOperateLogDao;
import cn.loveapp.print.service.dto.ElefaceSharingRelationOperateDTO;
import cn.loveapp.print.service.entity.ElefaceSharingRelation;
import cn.loveapp.print.service.entity.ElefaceSharingRelationOperateLog;
import cn.loveapp.print.service.response.ElefaceSharingRelationOperateListResponse;
import cn.loveapp.print.service.service.ElefaceSharingRelationOperateService;
import cn.loveapp.print.service.utils.ElefaceSharingRelationUtils;
import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2024/7/25 5:35 PM
 */
@Service
public class ElefaceSharingRelationOperateServiceImpl implements ElefaceSharingRelationOperateService {

    private static final LoggerHelper LOGGER = LoggerHelper.getLogger(ElefaceSharingRelationOperateServiceImpl.class);

    @Autowired
    private ElefaceSharingRelationDao elefaceSharingRelationDao;

    @Autowired
    private ElefaceSharingRelationOperateLogDao elefaceSharingRelationOperateLogDao;

    @Autowired
    private UserCenterService userCenterService;


    @Override
    public List<ElefaceSharingRelationOperateDTO> searchOperateListGet(ElefaceSharingRelationQueryBo queryBo, UserInfoBo userInfoBo) throws CommonException {

        ElefaceSharingRelationOperateListResponse response = searchOperateListGetByPage(queryBo, userInfoBo);
        if (response != null) {
            return response.getRelationList();
        }

        return Collections.emptyList();

    }

    @Override
    public void saveElefaceSharingRelationOperateLog(ElefaceSharingRelation elefaceSharingRelation, Long lastShareNum, Integer operateType, String operator, String operateTerminal) {
        ElefaceSharingRelationOperateLog sharingRelationOperateLog = new ElefaceSharingRelationOperateLog();
        sharingRelationOperateLog.setShareId(elefaceSharingRelation.getShareId());
        sharingRelationOperateLog.setShareMemo(elefaceSharingRelation.getShareMemo());
        sharingRelationOperateLog.setLastShareNum(lastShareNum);
        sharingRelationOperateLog.setNewShareNum(elefaceSharingRelation.getShareNum());
        sharingRelationOperateLog.setOperator(operator);
        sharingRelationOperateLog.setOperateTerminal(operateTerminal);
        sharingRelationOperateLog.setTargetAppName(elefaceSharingRelation.getTargetAppName());
        sharingRelationOperateLog.setTargetSellerNick(elefaceSharingRelation.getTargetSellerNick());
        sharingRelationOperateLog.setTargetSellerId(elefaceSharingRelation.getTargetSellerId());
        sharingRelationOperateLog.setTargetStoreId(elefaceSharingRelation.getTargetStoreId());
        sharingRelationOperateLog.setOperatorTime(LocalDateTime.now());
        sharingRelationOperateLog.setOperateType(operateType);
        try {
            elefaceSharingRelationOperateLogDao.insert(sharingRelationOperateLog);
        } catch (Exception e) {
            LOGGER.logError("面单操作日志插入失败");
        }
    }

    @Override
    public ElefaceSharingRelationOperateListResponse searchOperateListGetByPage(ElefaceSharingRelationQueryBo queryBo, UserInfoBo userInfoBo) {

        ElefaceSharingRelationOperateListResponse response = new ElefaceSharingRelationOperateListResponse();
        response.setRelationList(Collections.emptyList());
        response.setTotalResult(0);
        // 根据条件过滤出 shareId列表

        List<ElefaceSharingRelation> allRelation = new ArrayList<>();

        Integer offset = queryBo.getOffset();
        Integer limit = queryBo.getLimit();

        queryBo.setLimit(null);
        queryBo.setOffset(null);
        // 查询自己分享出去的面单（兼容淘系千牛）
        List<ElefaceSharingRelation> relationListOwner = elefaceSharingRelationDao.searchByOwnerUserAndQuery(userInfoBo.getSellerId(), userInfoBo.getStoreId(), userInfoBo.getAppName(), queryBo);
        ListUtil.addListIfNotNull(allRelation, relationListOwner);

        // 查询代理分享的（爱用多店）
        queryBo.setShareType(0);
        List<ElefaceSharingRelation> relationListTarget = elefaceSharingRelationDao.searchByProxyUserAndQuery(userInfoBo.getSellerId(), userInfoBo.getStoreId(), userInfoBo.getAppName(), queryBo);
        ListUtil.addListIfNotNull(allRelation, relationListTarget);

        if (CollectionUtils.isEmpty(allRelation)) {
            LOGGER.logInfo(userInfoBo.getSellerNick(), "", "未查寻到用户面单分享记录");
            return response;
        }

        List<String> shardIdList = queryBo.getShareIdList();

        Map<String, ElefaceSharingRelation> allShardMap = allRelation.stream().collect(Collectors.toMap(ElefaceSharingRelation::getShareId, Function.identity()));

        if (shardIdList == null) {
            queryBo.setShareIdList(Lists.newArrayList(allShardMap.keySet()));
        } else {
            // 过滤掉非自身分享的操作日志
            List<String> filterShareId = shardIdList.stream().filter(allShardMap.keySet()::contains).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(filterShareId)) {
                LOGGER.logInfo(userInfoBo.getSellerNick(), "", "未查寻到用户面单分享记录");
                return response;
            }
            queryBo.setShareIdList(filterShareId);
        }

        if (CollectionUtils.isEmpty(queryBo.getShareIdList())) {
            return response;
        }

        Integer count = elefaceSharingRelationOperateLogDao.countByQuery(queryBo);

        if (count < 1) {
            return response;
        }
        response.setTotalResult(count);

        queryBo.setLimit(limit);
        queryBo.setOffset(offset);
        List<ElefaceSharingRelationOperateLog> elefaceSharingRelationOperateLogs = elefaceSharingRelationOperateLogDao.searchListByQuery(queryBo);

        List<ElefaceSharingRelationOperateDTO> result = new ArrayList<>();
        // 返回网点
        for (ElefaceSharingRelationOperateLog operateLog : elefaceSharingRelationOperateLogs) {
            // 补充面单信息
            ElefaceSharingRelation elefaceSharingRelation = allShardMap.get(operateLog.getShareId());
            if (elefaceSharingRelation == null) {
                LOGGER.logError("正常不会走到这一步，除非不正常了打个日志");
                continue;
            }
            ElefaceSharingRelationOperateDTO operateDTO = CommonConvertMapper.INSTANCE.toElefaceSharingRelationOperate(operateLog, elefaceSharingRelation);
            result.add(operateDTO);
        }
        response.setRelationList(result);
        return response;
    }
}
