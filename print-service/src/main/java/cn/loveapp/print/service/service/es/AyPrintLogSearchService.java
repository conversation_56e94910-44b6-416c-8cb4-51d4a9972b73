package cn.loveapp.print.service.service.es;

import java.util.List;

import cn.loveapp.print.service.config.PrintLogConfig;
import org.apache.commons.lang3.BooleanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import cn.loveapp.print.service.bo.UserInfoBo;
import cn.loveapp.print.service.dao.es.ElasticsearchAyPrintLogQueryDao;
import cn.loveapp.print.service.dto.AyPrintLogListSearchDTO;
import cn.loveapp.print.service.dto.AyPrintLogSearchListAndAggDTO;
import cn.loveapp.print.service.service.PrintlogService;

/**
 * <AUTHOR>
 * @date 2024-01-23 17:13
 * @description: 打印日志高级搜索服务接口
 */
@Component
public class AyPrintLogSearchService {

    /**
     * es搜索文档返回的最大文档数
     */
    public static final Integer MAX_ES_TOTAL = 10000;

    @Autowired
    private ElasticsearchAyPrintLogQueryDao elasticsearchAyPrintLogQueryDao;

    @Autowired
    private PrintlogService printlogService;

    @Autowired
    private PrintLogConfig printLogConfig;

    public AyPrintLogSearchListAndAggDTO ayPrintLogListGetQueryByLimit(AyPrintLogListSearchDTO ayPrintLogListSearchDTO,
        List<UserInfoBo> userInfoBoList) {
        AyPrintLogSearchListAndAggDTO ayPrintLogSearchListAndAggDTO =
            elasticsearchAyPrintLogQueryDao.ayPrintLogListGetQueryByLimit(ayPrintLogListSearchDTO, userInfoBoList);

        if (ayPrintLogSearchListAndAggDTO.getTotalResults() >= MAX_ES_TOTAL) {
            elasticsearchAyPrintLogQueryDao.queryTotalCount(ayPrintLogListSearchDTO, userInfoBoList);
            ayPrintLogSearchListAndAggDTO.setTotalResults(
                printlogService.queryPrintLogCountFromCache(ayPrintLogListSearchDTO, userInfoBoList).longValue());
        }

        if (BooleanUtils.isTrue(printLogConfig.getPrintListCountStatisticsEnabled())) {
            // 聚合查询
            printlogService.ayPrintLogListGetCountStatisticsFromCache(ayPrintLogSearchListAndAggDTO,
                ayPrintLogListSearchDTO, userInfoBoList);
        }

        return ayPrintLogSearchListAndAggDTO;
    }
}
