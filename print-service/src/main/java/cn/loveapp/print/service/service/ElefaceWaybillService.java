package cn.loveapp.print.service.service;

import java.util.List;

import cn.loveapp.print.api.dto.MultiWaybillOperateLogListGetDTO;
import cn.loveapp.print.api.dto.WaybillOperateLogSaveDTO;
import cn.loveapp.print.api.request.ElefaceIsCancelGetInnerRequest;
import cn.loveapp.print.api.response.ElefaceIsCancelGetInnerResponse;
import cn.loveapp.print.common.exception.CommonException;
import cn.loveapp.print.common.exception.PlatformSdkException;
import cn.loveapp.print.service.bo.UserInfoBo;
import cn.loveapp.print.service.bo.WaybillGetResponseBo;
import cn.loveapp.print.common.dto.WaybillOperateLogDetailsDTO;
import cn.loveapp.print.common.dto.WaybillOperatelogQueryDTO;
import cn.loveapp.print.service.request.ElefaceSharingGetRequest;
import cn.loveapp.print.service.request.ElefaceWaybillBatchGetRequest;
import cn.loveapp.print.service.request.ElefaceWaybillGetRequest;
import cn.loveapp.print.service.response.ElefaceSharingGetResponseDTO;
import cn.loveapp.print.api.dto.WaybillOperatelogResponseDTO;

/**
 * 电子面单的相关操作Service
 *
 * <AUTHOR>
 */
public interface ElefaceWaybillService {

    /**
     * 面单批量取号
     *
     * @param request
     * @param userInfoBo
     * @return
     * @throws CommonException
     */
    List<WaybillGetResponseBo> waybillGetBatch(ElefaceWaybillGetRequest request, UserInfoBo userInfoBo)
        throws CommonException;


    /**
     * 面单批量取号v2（支持部分成功，返回失败原因和成功的所有面单信息）
     *
     * @param request
     * @param userInfoBo
     * @return
     * @throws CommonException
     */
    List<WaybillGetResponseBo> waybillGetBatchV2(ElefaceWaybillBatchGetRequest request, UserInfoBo userInfoBo)
            throws CommonException;

    /**
     * 取消获取的面单号
     *
     * @param cpCode
     * @param waybillCode
     * @param userInfoBo
     * @return
     * @throws CommonException
     */
    boolean waybillCancel(String cpCode, String waybillCode, Integer billVersion, Boolean isCancelFromApi,
        UserInfoBo userInfoBo) throws CommonException;

    /**
     * 获取面单操作日志列表
     *
     * @param queryDTO
     * @param userInfoBo
     * @return
     * @throws PlatformSdkException
     */
    WaybillOperatelogResponseDTO operatelogListGet(WaybillOperatelogQueryDTO queryDTO, UserInfoBo userInfoBo);

    /**
     * 保存面单操作日志的打印记录
     *
     * @param cpCode
     * @param waybillCode
     * @param userInfoBo
     */
    void saveWaybillPrintLog(String cpCode, String waybillCode, String externalInfo, UserInfoBo userInfoBo);

    /**
     * 批量保存面单操作（取号）日志
     * @param waybillOperatelogSaveDTO
     */
    void saveWaybillOperateLogBatch(List<WaybillOperateLogSaveDTO> waybillOperatelogSaveDTO);

    /**
     * 获取面单共享关系
     * @param request
     * @param userInfoBo
     * @return
     */
    ElefaceSharingGetResponseDTO waybillShareGet(ElefaceSharingGetRequest request, UserInfoBo userInfoBo);

    /**
     * 查询总数
     *
     * @param queryDTO
     * @param storeId
     * @param sellerId
     * @param appName
     * @return
     */
    Long queryCount(WaybillOperatelogQueryDTO queryDTO, String storeId, String sellerId, String appName);

    /**
     * 批量查询面单是否被回收
     *
     * @param request
     * @return
     */
    List<ElefaceIsCancelGetInnerResponse> elefaceIsCancelGet(ElefaceIsCancelGetInnerRequest request);

    /**
     * 查询面单使用明细
     *
     * @param request
     * @param userInfoBoList
     * @return
     */
    WaybillOperatelogResponseDTO multiWaybillOperateLogListGet(MultiWaybillOperateLogListGetDTO request, List<UserInfoBo> userInfoBoList);

    /**
     * 更新操作日志详情
     *
     * @param operateLogDetail
     * @param elefaceLogUserRole
     * @param userInfoBoList
     * @return
     */
    List<WaybillOperateLogDetailsDTO> waybillOperateLogDetailUpdate(List<WaybillOperateLogDetailsDTO> operateLogDetail, Integer elefaceLogUserRole, List<UserInfoBo> userInfoBoList);
}
