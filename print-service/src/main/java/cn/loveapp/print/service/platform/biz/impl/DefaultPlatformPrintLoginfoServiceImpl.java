package cn.loveapp.print.service.platform.biz.impl;

import cn.loveapp.common.constant.CommonPlatformConstants;
import cn.loveapp.print.common.entity.AyElefaceOperatelog;
import cn.loveapp.print.service.platform.biz.PlatformPrintLogInfoService;
import cn.loveapp.print.api.dto.TradeLogisticsBindingHistoryResponseDTO;
import org.springframework.stereotype.Service;

/**
 * 打印日志响应平台定制化服务实现类
 * @date 2023/09/26
 * <AUTHOR>
 *
 */
@Service
public class DefaultPlatformPrintLoginfoServiceImpl implements PlatformPrintLogInfoService {

    @Override
    public void appendInfo(TradeLogisticsBindingHistoryResponseDTO.LogisticsBindingHistoryDTO logisticsBindingHistoryDTO, AyElefaceOperatelog operatelog, String platformId, String appName) {
    }

    @Override
    public String getPlatformId() {
        return CommonPlatformConstants.PLATFORM_DEFAULT;
    }
}
