package cn.loveapp.print.service.controller;

import java.time.LocalDateTime;
import java.util.List;

import javax.sql.DataSource;

import org.springframework.beans.factory.ObjectProvider;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;

import cn.loveapp.common.utils.LoggerHelper;
import cn.loveapp.print.common.controller.BaseCloudNativeController;
import cn.loveapp.print.common.web.WarnUpRequestData;
import springfox.documentation.annotations.ApiIgnore;

/**
 * 探活Controller
 *
 * <AUTHOR>
 * @date 2019-04-16
 * @ignore
 */
@Controller
@RequestMapping("/")
@ApiIgnore
public class CloudNativeController extends BaseCloudNativeController {
    private static final LoggerHelper LOGGER = LoggerHelper.getLogger(CloudNativeController.class);

    public CloudNativeController(ObjectProvider<List<DataSource>> provider) {
        super(provider);
    }

    @Override
    protected HttpStatus checkReadNess() {
        HttpStatus status = super.checkReadNess();
        if (status == HttpStatus.OK) {
            try {
            } catch (Exception e) {
                LOGGER.logError("memcached readness失败: " + e.getMessage(), e);
                status = HttpStatus.INTERNAL_SERVER_ERROR;
            }
        }
        return status;
    }

    @Override
    protected void changeWarmUpRequestData(List<WarnUpRequestData> requestDatas) {
        LocalDateTime endTime = LocalDateTime.now();
        LocalDateTime startTime = endTime.minusHours(4);
    }
}
