package cn.loveapp.print.service.api.entity;


import com.jd.open.api.sdk.domain.ydy.PullDataService.response.pullData.PrePrintDataInfo;
import com.kuaishou.merchant.open.api.domain.express.GetEbillOrderDTO;
import com.pdd.pop.sdk.http.api.ark.response.PddCloudWaybillGetResponse;
import com.wxvideoshop.api.response.WxvideoshopElectronicBillOrdersCreateResponse;
import com.xiaohongshu.fls.opensdk.entity.express.response.ElectronicBillOrdersCreateResponse;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;

import com.alibaba.fastjson.JSONObject;
import com.pdd.pop.sdk.http.api.pop.response.PddWaybillGetResponse;
import com.taobao.api.response.CainiaoWaybillIiGetResponse;

import lombok.Data;

import java.util.Map;

/**
 * 电子面单云打印信息
 *
 * <AUTHOR>
 */
@Data
public class WaybillCloudPrintInfo {
    /**
     * 面单请求id
     */
    private String objectId;

    /**
     * 快运字母单的母单号（快手母电子面单号）
     */
    private String parentWaybillCode;

    /**
     * 面单号（快运下为子单号）
     */
    private String waybillCode;

    /**
     * 打印内容
     */
    private String printData;

    /**
     * 扩展信息
     */
    private String extraInfo;

    /**
     * 自定义打印数据，可为空，如果有则是传输给打印组件报文contents数组的第二个元素
     */
    private String customerPrintData;

    /**
     * 物流公司code
     */
    private String realCpCode;

    /**
     * 电子面单订单id(微信视频号专有字段,用于取号/取消取号)
     */
    private String waybillOrderId;

    /**
     * 包裹号（京东物流专有）
     */
    private String packageCode;

    /**
     * 淘宝（菜鸟）取号信息转换
     *
     * @param waybillCloudPrintResponse
     * @return
     */
    public static WaybillCloudPrintInfo
        of(CainiaoWaybillIiGetResponse.WaybillCloudPrintResponse waybillCloudPrintResponse) {
        WaybillCloudPrintInfo waybillCloudPrintInfo = new WaybillCloudPrintInfo();
        BeanUtils.copyProperties(waybillCloudPrintResponse, waybillCloudPrintInfo);
        return waybillCloudPrintInfo;
    }

    /**
     * 拼多多 取号信息转换
     *
     * @param modulesItem
     * @return
     */
    public static WaybillCloudPrintInfo of(PddWaybillGetResponse.InnerPddWaybillGetResponseModulesItem modulesItem) {
        WaybillCloudPrintInfo waybillCloudPrintInfo = new WaybillCloudPrintInfo();
        BeanUtils.copyProperties(modulesItem, waybillCloudPrintInfo);
        return waybillCloudPrintInfo;
    }

    /**
     * 拼多多 取号信息转换
     *
     * @param modulesItem
     * @return
     */
    public static WaybillCloudPrintInfo of(PddCloudWaybillGetResponse.PddWaybillGetResponseModulesItem modulesItem) {
        WaybillCloudPrintInfo waybillCloudPrintInfo = new WaybillCloudPrintInfo();
        BeanUtils.copyProperties(modulesItem, waybillCloudPrintInfo);
        return waybillCloudPrintInfo;
    }


    /**
     * 抖店 取号信息转换
     *
     * @param waybillInfo
     * @return
     */
    public static WaybillCloudPrintInfo of(com.doudian.api.domain.WaybillInfo waybillInfo, Map<String, String> subWaybill2Parent) {
        WaybillCloudPrintInfo waybillCloudPrintInfo = new WaybillCloudPrintInfo();

        String parentWaybillCode = subWaybill2Parent.get(waybillInfo.getTrackNo());
        if (StringUtils.isNotEmpty(parentWaybillCode)) {
            waybillCloudPrintInfo.setWaybillCode(waybillInfo.getTrackNo());
            waybillCloudPrintInfo.setParentWaybillCode(parentWaybillCode);
        } else {
            waybillCloudPrintInfo.setWaybillCode(waybillInfo.getTrackNo());
        }
        JSONObject printData = new JSONObject();
        printData.put("encryptedData", waybillInfo.getPrintData());
        printData.put("signature", waybillInfo.getSign());
        waybillCloudPrintInfo.setPrintData(printData.toJSONString());
        return waybillCloudPrintInfo;
    }

    /**
     * 快手 取号信息转换
     *
     * @param getEbillOrderDTO
     * @return
     */
    public static WaybillCloudPrintInfo of(GetEbillOrderDTO getEbillOrderDTO) {
        WaybillCloudPrintInfo waybillCloudPrintInfo = new WaybillCloudPrintInfo();
        waybillCloudPrintInfo.setWaybillCode(getEbillOrderDTO.getWaybillCode());
        JSONObject printData = new JSONObject();
        printData.put("encryptedData", getEbillOrderDTO.getPrintData());
        printData.put("signature", getEbillOrderDTO.getSignature());
        printData.put("key", getEbillOrderDTO.getKey());
        printData.put("version", getEbillOrderDTO.getVersion());
        waybillCloudPrintInfo.setPrintData(printData.toJSONString());
        waybillCloudPrintInfo.setParentWaybillCode(getEbillOrderDTO.getParentWaybillCode());
        return waybillCloudPrintInfo;
    }

    /**
     * 小红书 取号信息转换
     *
     * @param electronicBillPrintData
     * @return
     */
    public static WaybillCloudPrintInfo of(ElectronicBillOrdersCreateResponse.ElectronicBillPrintData electronicBillPrintData) {
        WaybillCloudPrintInfo waybillCloudPrintInfo = new WaybillCloudPrintInfo();
        BeanUtils.copyProperties(electronicBillPrintData, waybillCloudPrintInfo);
        return waybillCloudPrintInfo;

    }

    /**
     * 微信视频号 取号信息转换
     *
     * @param response
     * @return
     */
    public static WaybillCloudPrintInfo of(WxvideoshopElectronicBillOrdersCreateResponse response){
        WaybillCloudPrintInfo waybillCloudPrintInfo = new WaybillCloudPrintInfo();
        //BeanUtils.copyProperties(response,waybillCloudPrintInfo);
        waybillCloudPrintInfo.setWaybillCode(response.getWaybillId());
        if (StringUtils.isNotEmpty(response.getPrintInfo())){
            waybillCloudPrintInfo.setPrintData(response.getPrintInfo());
        }
        waybillCloudPrintInfo.setWaybillOrderId(response.getEwaybillOrderId());
        return waybillCloudPrintInfo;
    }

    /**
     * 京东 取号信息转换
     *
     * @param objectId
     * @param waybillInfo
     * @param parentWaybillCode
     * @param isMultiPackages
     * @return
     */
    public static WaybillCloudPrintInfo of(String objectId, PrePrintDataInfo waybillInfo, String parentWaybillCode,
        boolean isMultiPackages) {
        WaybillCloudPrintInfo waybillCloudPrintInfo = new WaybillCloudPrintInfo();
        waybillCloudPrintInfo.setObjectId(objectId);
        waybillCloudPrintInfo.setPrintData(waybillInfo.getPerPrintData());
        if (isMultiPackages) {
            // 多包裹
            waybillCloudPrintInfo.setParentWaybillCode(waybillInfo.getWayBillNo());
            waybillCloudPrintInfo.setWaybillCode(waybillInfo.getPackageCode());
        } else {
            String wayBillNo = waybillInfo.getWayBillNo();
            if (StringUtils.isNotEmpty(parentWaybillCode) && !parentWaybillCode.equals(wayBillNo)) {
                // 子母件
                waybillCloudPrintInfo.setWaybillCode(wayBillNo);
                waybillCloudPrintInfo.setParentWaybillCode(parentWaybillCode);
            } else {
                waybillCloudPrintInfo.setWaybillCode(waybillInfo.getWayBillNo());
            }
            waybillCloudPrintInfo.setPackageCode(waybillInfo.getPackageCode());
        }

        return waybillCloudPrintInfo;
    }

}
