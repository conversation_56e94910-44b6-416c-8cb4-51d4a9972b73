package cn.loveapp.print.service.platform.api.impl;

import cn.loveapp.common.constant.CommonPlatformConstants;
import cn.loveapp.common.platformsdk.xhs.XhsSDKService;
import cn.loveapp.common.utils.LoggerHelper;
import cn.loveapp.print.service.api.entity.AyWaybillApplySubscriptionInfo;
import cn.loveapp.print.service.api.entity.WaybillCloudPrintInfo;
import cn.loveapp.print.service.api.request.AyWaybillCancelRequest;
import cn.loveapp.print.service.api.request.AyWaybillGetRequest;
import cn.loveapp.print.service.api.request.AyWaybillSearchRequest;
import cn.loveapp.print.service.api.response.AyWaybillCancelResponse;
import cn.loveapp.print.service.api.response.AyWaybillGetResponse;
import cn.loveapp.print.service.api.response.AyWaybillSearchResponse;
import cn.loveapp.print.service.code.ErrorCode;
import cn.loveapp.print.service.platform.api.PlatformWaybillApiService;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.xhs.api.request.XhsElectronicBillOrderCancelRequest;
import com.xhs.api.request.XhsElectronicBillOrdersCreateRequest;
import com.xhs.api.request.XhsElectronicBillSubscribesQueryRequest;
import com.xhs.api.response.XhsElectronicBillOrderCancelResponse;
import com.xhs.api.response.XhsElectronicBillOrdersCreateResponse;
import com.xhs.api.response.XhsElectronicBillSubscribesQueryResponse;
import com.xiaohongshu.fls.opensdk.entity.express.response.ElectronicBillSubscribesQueryResponse;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 面单api service 小红书实现类
 *
 * <AUTHOR>
 */
@Service
public class XhsPlatformWaybillApiServiceImpl implements PlatformWaybillApiService {

    private static final LoggerHelper LOGGER = LoggerHelper.getLogger(XhsPlatformWaybillApiServiceImpl.class);

    @Value("${print.service.xhs.oldWaybillSearch.enable:true}")
    private Boolean oldWaybillSearchEnable;

    @Autowired
    private XhsSDKService xhsSDKService;

    /**
     * 查询面单服务订购及面单使用情况
     *
     * @param request
     * @param topSession
     * @param platformId
     * @param appName
     * @return
     */
    @Override
    public AyWaybillSearchResponse waybillSearch(AyWaybillSearchRequest request, String topSession, String platformId,
        String appName) {
        XhsElectronicBillSubscribesQueryRequest waybillIiSearchRequest = new XhsElectronicBillSubscribesQueryRequest();
        waybillIiSearchRequest.setCpCode(StringUtils.trimToEmpty(request.getCpCode()));
        waybillIiSearchRequest.setNeedUsage(true);
        waybillIiSearchRequest.setBillVersion(2);
        XhsElectronicBillSubscribesQueryResponse billSubscribesQueryResponse =
            xhsSDKService.execute(waybillIiSearchRequest, topSession, appName);
        AyWaybillSearchResponse response = new AyWaybillSearchResponse();
        response.init(billSubscribesQueryResponse);
        if (!response.isSuccess()) {
            // 失败直接返回
            return response;
        }

        List<AyWaybillApplySubscriptionInfo> waybillApplySubscriptionInfoArrayList = Lists.newArrayList();
        if (billSubscribesQueryResponse.getData() != null
            && !CollectionUtils.isEmpty(billSubscribesQueryResponse.getData().getSubscribeList())) {
            List<ElectronicBillSubscribesQueryResponse.Subscribe> subscribeList =
                billSubscribesQueryResponse.getData().getSubscribeList();
            Map<String, List<ElectronicBillSubscribesQueryResponse.Subscribe>> dataGroupByCompany = subscribeList
                .stream().collect(Collectors.groupingBy(item -> item.getCpCode() + ":" + item.getCpType()));

            List<AyWaybillApplySubscriptionInfo> ayWaybillApplySubscriptionInfos =
                dataGroupByCompany.values().stream().map(item -> AyWaybillApplySubscriptionInfo
                    .of(item.get(0).getCpCode(), item.get(0).getCpType(), 2, item)).collect(Collectors.toList());

            if (!CollectionUtils.isEmpty(ayWaybillApplySubscriptionInfos)) {
                waybillApplySubscriptionInfoArrayList.addAll(ayWaybillApplySubscriptionInfos);
            }
        }

        if (oldWaybillSearchEnable) {
            waybillIiSearchRequest.setBillVersion(1);
            XhsElectronicBillSubscribesQueryResponse oldBillSubscribesQueryResponse =
                xhsSDKService.execute(waybillIiSearchRequest, topSession, appName);

            if (!oldBillSubscribesQueryResponse.isSuccess()
                && CollectionUtils.isEmpty(waybillApplySubscriptionInfoArrayList)) {
                // 获取老的失败 并且新的为空
                response.setWaybillApplySubscriptionCols(waybillApplySubscriptionInfoArrayList);
                return response;
            } else if (oldBillSubscribesQueryResponse.getData() != null
                && !CollectionUtils.isEmpty(oldBillSubscribesQueryResponse.getData().getSubscribeList())) {
                List<ElectronicBillSubscribesQueryResponse.Subscribe> oldSubscribeList =
                    oldBillSubscribesQueryResponse.getData().getSubscribeList();
                Map<String, List<ElectronicBillSubscribesQueryResponse.Subscribe>> dataGroupByCompany = oldSubscribeList
                    .stream().collect(Collectors.groupingBy(item -> item.getCpCode() + ":" + item.getCpType()));

                // 小红书老版电子面单billVersion设置为null
                List<AyWaybillApplySubscriptionInfo> ayWaybillApplySubscriptionInfos =
                    dataGroupByCompany.values().stream().map(item -> AyWaybillApplySubscriptionInfo
                        .of(item.get(0).getCpCode(), item.get(0).getCpType(), null, item)).collect(Collectors.toList());

                if (!CollectionUtils.isEmpty(ayWaybillApplySubscriptionInfos)) {
                    waybillApplySubscriptionInfoArrayList.addAll(ayWaybillApplySubscriptionInfos);
                }
            }
        }

        response.setWaybillApplySubscriptionCols(waybillApplySubscriptionInfoArrayList);
        return response;
    }

    @Override
    public AyWaybillGetResponse waybillGet(AyWaybillGetRequest request, String topSession, String platformId,
                                           String appName) {
        AyWaybillGetResponse response = new AyWaybillGetResponse();
        XhsElectronicBillOrdersCreateRequest createRequest = null;
        try {
            createRequest = JSON.parseObject(request.getParamWaybillCloudPrintApplyNewRequest(),
                    XhsElectronicBillOrdersCreateRequest.class);
        } catch (Exception e) {
            LOGGER.logError("非法的参数：paramWaybillCloudPrintApplyNewRequest", e);
            response.setErrorCode(ErrorCode.BaseCode.PARAMS_ERR.getCode().toString());
            response.setMsg(
                    "非法的参数：paramWaybillCloudPrintApplyNewRequest = " + request.getParamWaybillCloudPrintApplyNewRequest());
            return response;
        }
        XhsElectronicBillOrdersCreateResponse createResponse = xhsSDKService.execute(createRequest, topSession, appName);
        response.init(createResponse);
        if (!response.isSuccess()) {
            return response;
        }
        response.setWaybillCloudPrintInfoList(createResponse.getData().getWayBillList().stream()
                .map(WaybillCloudPrintInfo::of).collect(Collectors.toList()));
        return response;
    }

    @Override
    public AyWaybillCancelResponse waybillCancel(AyWaybillCancelRequest request, String topSession, String platformId,
                                                 String appName) {
        AyWaybillCancelResponse response = new AyWaybillCancelResponse();

        XhsElectronicBillOrderCancelRequest xhsElectronicBillOrderCancelRequest = new XhsElectronicBillOrderCancelRequest();
        xhsElectronicBillOrderCancelRequest.setWaybillCode(request.getWaybillCode());
        xhsElectronicBillOrderCancelRequest.setCpCode(request.getCpCode());
        xhsElectronicBillOrderCancelRequest.setBillVersion(request.getBillVersion());

        XhsElectronicBillOrderCancelResponse createResponse = xhsSDKService.execute(xhsElectronicBillOrderCancelRequest, topSession, appName);
        response.init(createResponse);
        response.setCancelResult(response.isSuccess());
        return response;
    }

    /**
     * 获取平台id
     *
     * @return
     */
    @Override
    public String getPlatformId() {
        return CommonPlatformConstants.PLATFORM_XHS;
    }


}
