package cn.loveapp.print.service.dao.es;

import cn.loveapp.common.utils.LoggerHelper;
import cn.loveapp.print.api.dto.MultiWaybillOperateLogListGetDTO;
import cn.loveapp.print.common.config.es.ElasticsearchConfiguration;
import cn.loveapp.print.common.constant.EsFields;
import cn.loveapp.print.common.dao.es.CommonAyPrintLogSearchESDao;
import cn.loveapp.print.common.entity.AyPrintLogSearchEs;
import cn.loveapp.print.service.bo.UserInfoBo;
import cn.loveapp.print.service.dto.AyPrintLogSearchListAndAggDTO;
import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.elasticsearch.action.search.SearchType;
import org.elasticsearch.client.RestHighLevelClient;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.search.sort.SortBuilders;
import org.elasticsearch.search.sort.SortOrder;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.elasticsearch.core.ElasticsearchOperations;
import org.springframework.data.elasticsearch.core.ResultsMapper;
import org.springframework.data.elasticsearch.core.query.NativeSearchQueryBuilder;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

import static org.elasticsearch.index.query.QueryBuilders.*;

/**
 * 面单操作es搜索dao
 *
 * <AUTHOR>
 * @Date 2024/9/19 4:09 PM
 */
@Repository
public class ElasticsearchAyOperateLogQueryDao extends CommonAyPrintLogSearchESDao {

    private static final LoggerHelper LOGGER = LoggerHelper.getLogger(ElasticsearchAyOperateLogQueryDao.class);

    public ElasticsearchAyOperateLogQueryDao(ElasticsearchConfiguration configuration, ElasticsearchOperations operations, RestHighLevelClient client, ResultsMapper mapper) {
        super(configuration, operations, client, mapper);
    }

    /**
     * 面单操作日志列表分页查询
     *
     * @param operateLogListGetDTO
     * @param userInfoBoList
     * @return
     */
    public AyPrintLogSearchListAndAggDTO ayOperateLogListGetQueryByLimit(MultiWaybillOperateLogListGetDTO operateLogListGetDTO,
                                                                         List<UserInfoBo> userInfoBoList) {
        AyPrintLogSearchListAndAggDTO ayPrintLogSearchListAndAggDTO = new AyPrintLogSearchListAndAggDTO();
        NativeSearchQueryBuilder builder = ayPrintLogListGetBuilder(userInfoBoList);
        BoolQueryBuilder boolQuerySearchList = createBoolQuerySearchList(operateLogListGetDTO, userInfoBoList);
        builder.withQuery(filterQuery(boolQuerySearchList));
        builder.withSort(SortBuilders.fieldSort(EsFields.elefaceOperateLogElefaceGetTime).order(SortOrder.DESC));
        builder.withPageable(PageRequest.of(operateLogListGetDTO.getPage() - 1, operateLogListGetDTO.getPageSize()));
        Page<AyPrintLogSearchEs> ayPrintLogSearchEs = pageQuery(builder);

        ayPrintLogSearchListAndAggDTO.setTotalResults(ayPrintLogSearchEs.getTotalElements());
        ayPrintLogSearchListAndAggDTO.setItemSearchESList(ayPrintLogSearchEs.getContent());

        handleStatisticData(operateLogListGetDTO, userInfoBoList, ayPrintLogSearchListAndAggDTO);

        return ayPrintLogSearchListAndAggDTO;
    }

    /**
     * 处理统计项
     */
    private void handleStatisticData(MultiWaybillOperateLogListGetDTO operateLogListGetDTO,
        List<UserInfoBo> userInfoBoList, AyPrintLogSearchListAndAggDTO ayPrintLogSearchListAndAggDTO) {
        if (!BooleanUtils.isTrue(operateLogListGetDTO.isNeedStatistics())) {
            return;
        }

        if (CollectionUtils.isEmpty(userInfoBoList)) {
            return;
        }

        NativeSearchQueryBuilder searchQueryBuilder = ayPrintLogListGetBuilder(userInfoBoList);
        if (BooleanUtils.isTrue(operateLogListGetDTO.isNeedCountPrinted())) {
            if (BooleanUtils.isTrue(operateLogListGetDTO.getIsNotUseDefaultStatistic())
                && BooleanUtils.isTrue(operateLogListGetDTO.getIsPrint())) {
                // 动态更新配合条件使用
                BoolQueryBuilder boolQuerySearchList = createBoolQuerySearchList(operateLogListGetDTO, userInfoBoList);
                boolQuerySearchList.must(termQuery(EsFields.elefaceOperateLogIsPrint, true));
                NativeSearchQueryBuilder builder = ayPrintLogListGetBuilder(userInfoBoList);
                builder.withQuery(filterQuery(boolQuerySearchList));
                ayPrintLogSearchListAndAggDTO.setPrintAfterGetWaybillCount(count(builder.build()));
            } else {
                BoolQueryBuilder defaultQueryCondition =
                    generateDefaultStatisticQueryBuilder(operateLogListGetDTO, userInfoBoList);
                defaultQueryCondition.must(termQuery(EsFields.elefaceOperateLogIsPrint, true));
                searchQueryBuilder.withQuery(defaultQueryCondition);
                ayPrintLogSearchListAndAggDTO.setPrintAfterGetWaybillCount(count(searchQueryBuilder.build()));
            }

        }

        if (BooleanUtils.isTrue(operateLogListGetDTO.isNeedCountNotPrinted())) {
            if (BooleanUtils.isTrue(operateLogListGetDTO.getIsNotUseDefaultStatistic())
                && BooleanUtils.isFalse(operateLogListGetDTO.getIsPrint())) {
                // 取号未打印
                BoolQueryBuilder boolQuerySearchList = createBoolQuerySearchList(operateLogListGetDTO, userInfoBoList);
                boolQuerySearchList.mustNot(termQuery(EsFields.elefaceOperateLogIsPrint, true));
                NativeSearchQueryBuilder builder = ayPrintLogListGetBuilder(userInfoBoList);
                builder.withQuery(filterQuery(boolQuerySearchList));
                ayPrintLogSearchListAndAggDTO.setNotPrintAfterGetWaybillCount(count(builder.build()));
            } else {
                BoolQueryBuilder defaultQueryCondition =
                    generateDefaultStatisticQueryBuilder(operateLogListGetDTO, userInfoBoList);
                defaultQueryCondition.mustNot(termQuery(EsFields.elefaceOperateLogIsPrint, true));
                searchQueryBuilder.withQuery(defaultQueryCondition);
                ayPrintLogSearchListAndAggDTO.setNotPrintAfterGetWaybillCount(count(searchQueryBuilder.build()));
            }

        }

        if (BooleanUtils.isTrue(operateLogListGetDTO.isNeedCountSendGood())) {
            if (BooleanUtils.isTrue(operateLogListGetDTO.getIsNotUseDefaultStatistic())
                && BooleanUtils.isTrue(operateLogListGetDTO.getIsSendGood())) {
                // 取号已发货
                BoolQueryBuilder boolQuerySearchList = createBoolQuerySearchList(operateLogListGetDTO, userInfoBoList);
                boolQuerySearchList.must(termQuery(EsFields.elefaceOperateLogIsSendGood, true));
                NativeSearchQueryBuilder builder = ayPrintLogListGetBuilder(userInfoBoList);
                builder.withQuery(filterQuery(boolQuerySearchList));
                ayPrintLogSearchListAndAggDTO.setSendGoodAfterGetWaybillCount(count(builder.build()));
            } else {
                BoolQueryBuilder defaultQueryCondition =
                    generateDefaultStatisticQueryBuilder(operateLogListGetDTO, userInfoBoList);
                defaultQueryCondition.must(termQuery(EsFields.elefaceOperateLogIsSendGood, true));
                searchQueryBuilder.withQuery(defaultQueryCondition);
                ayPrintLogSearchListAndAggDTO.setSendGoodAfterGetWaybillCount(count(searchQueryBuilder.build()));
            }
        }

        if (BooleanUtils.isTrue(operateLogListGetDTO.isNeedCountNotSendGood())) {
            // 取号未发货
            if (BooleanUtils.isTrue(operateLogListGetDTO.getIsNotUseDefaultStatistic())
                && BooleanUtils.isFalse(operateLogListGetDTO.getIsSendGood())) {
                BoolQueryBuilder boolQuerySearchList = createBoolQuerySearchList(operateLogListGetDTO, userInfoBoList);
                boolQuerySearchList.mustNot(termQuery(EsFields.elefaceOperateLogIsSendGood, true));
                NativeSearchQueryBuilder builder = ayPrintLogListGetBuilder(userInfoBoList);
                builder.withQuery(filterQuery(boolQuerySearchList));
                ayPrintLogSearchListAndAggDTO.setNotSendGoodAfterGetWaybillCount(count(builder.build()));
            } else {
                BoolQueryBuilder defaultQueryCondition =
                    generateDefaultStatisticQueryBuilder(operateLogListGetDTO, userInfoBoList);
                defaultQueryCondition.mustNot(termQuery(EsFields.elefaceOperateLogIsSendGood, true));
                searchQueryBuilder.withQuery(defaultQueryCondition);
                ayPrintLogSearchListAndAggDTO.setNotSendGoodAfterGetWaybillCount(count(searchQueryBuilder.build()));
            }

        }

        if (BooleanUtils.isTrue(operateLogListGetDTO.isNeedCountCancel())) {
            if (BooleanUtils.isTrue(operateLogListGetDTO.getIsNotUseDefaultStatistic()) && BooleanUtils.isTrue(operateLogListGetDTO.getIsCancel())) {
                // 已回收单号
                BoolQueryBuilder boolQuerySearchList = createBoolQuerySearchList(operateLogListGetDTO, userInfoBoList);
                boolQuerySearchList.must(termQuery(EsFields.isCancel, true));
                NativeSearchQueryBuilder builder = ayPrintLogListGetBuilder(userInfoBoList);
                builder.withQuery(filterQuery(boolQuerySearchList));
                ayPrintLogSearchListAndAggDTO.setCancelAfterGetWaybillCount(count(builder.build()));
            } else {
                BoolQueryBuilder defaultQueryCondition =
                    generateDefaultStatisticQueryBuilder(operateLogListGetDTO, userInfoBoList);
                defaultQueryCondition.must(termQuery(EsFields.isCancel, true));
                searchQueryBuilder.withQuery(defaultQueryCondition);
                ayPrintLogSearchListAndAggDTO.setCancelAfterGetWaybillCount(count(searchQueryBuilder.build()));
            }
        }
    }

    private BoolQueryBuilder generateDefaultStatisticQueryBuilder(MultiWaybillOperateLogListGetDTO operateLogListGetDTO, List<UserInfoBo> userInfoBoList) {
        BoolQueryBuilder defaultQueryCondition = boolQuery();
        List<String> ayUserIds = Lists.newArrayList();
        for (UserInfoBo userInfoBo : userInfoBoList) {
            ayUserIds.add(AyPrintLogSearchEs.createAyUserId(userInfoBo.getSellerId(), userInfoBo.getStoreId(),
                userInfoBo.getAppName()));
        }

        // 用户id
        defaultQueryCondition.must(termsQuery(EsFields.ayUserId, ayUserIds));
        Integer elefaceLogUserRole = operateLogListGetDTO.getElefaceLogUserRole();
        if (elefaceLogUserRole != null) {
            defaultQueryCondition.must(termQuery(EsFields.elefaceOperateLogElefaceLogUserRole, elefaceLogUserRole));
        }

        LocalDateTime startTime = operateLogListGetDTO.getStartTime();
        if (startTime != null) {
            defaultQueryCondition.must(
                rangeQuery(EsFields.elefaceOperateLogElefaceGetTime).gte(minuteSecondFormatter.format(startTime)));
        }

        defaultQueryCondition.mustNot(termQuery(EsFields.isDeleted, true));
        return defaultQueryCondition;
    }

    /**
     * 处理es搜索条件
     *
     * @param operateLogListGetDTO
     * @param userInfoBoList
     * @return
     */
    private BoolQueryBuilder createBoolQuerySearchList(MultiWaybillOperateLogListGetDTO operateLogListGetDTO, List<UserInfoBo> userInfoBoList) {
        BoolQueryBuilder queryCondition = boolQuery();
        queryCondition.mustNot(termQuery(EsFields.isDeleted, true));

        if (operateLogListGetDTO == null) {
            return queryCondition;
        }

        List<String> ayUserIds = Lists.newArrayList();
        for (UserInfoBo userInfoBo : userInfoBoList) {
            ayUserIds.add(AyPrintLogSearchEs.createAyUserId(userInfoBo.getSellerId(), userInfoBo.getStoreId(),
                    userInfoBo.getAppName()));
        }

        if (operateLogListGetDTO.getStartTime() != null) {
            queryCondition.must(rangeQuery(EsFields.elefaceOperateLogElefaceGetTime).from(minuteSecondFormatter.format(operateLogListGetDTO.getStartTime())));
        }
        if (operateLogListGetDTO.getEndTime() != null) {
            queryCondition.must(rangeQuery(EsFields.elefaceOperateLogElefaceGetTime).to(minuteSecondFormatter.format(operateLogListGetDTO.getEndTime())));
        }

        // 用户id
        if (!CollectionUtils.isEmpty(ayUserIds)) {
            queryCondition.must(termsQuery(EsFields.ayUserId, ayUserIds));
        }

        // 面单服务商
        if (StringUtils.isNotEmpty(operateLogListGetDTO.getProvider())) {
            queryCondition.must(termQuery(EsFields.elefaceProvider, operateLogListGetDTO.getProvider()));
        }

        // 物流公司
        if (StringUtils.isNotEmpty(operateLogListGetDTO.getCpCode())) {
            queryCondition.must(termQuery(EsFields.logisticsCompanyCode, operateLogListGetDTO.getCpCode()));
        } else if (CollectionUtils.isNotEmpty(operateLogListGetDTO.getCpCodeList())) {
            queryCondition.must(termsQuery(EsFields.logisticsCompanyCode, operateLogListGetDTO.getCpCodeList()));
        }

        // 运单号搜索
        if (CollectionUtils.isNotEmpty(operateLogListGetDTO.getWaybillCodeList())) {
            queryCondition.must(termsQuery(EsFields.waybillNumber, operateLogListGetDTO.getWaybillCodeList()));
        }

        // 订单号查询
        if (!CollectionUtils.isEmpty(operateLogListGetDTO.getTidList())) {
            queryCondition.must(boolQuery().should(termsQuery(EsFields.tid, operateLogListGetDTO.getTidList()))
                    .should(termsQuery(EsFields.mergeSubTidList, operateLogListGetDTO.getTidList()))
                    .minimumShouldMatch(1));
        }

        if (!CollectionUtils.isEmpty(operateLogListGetDTO.getShareIdList())) {
            queryCondition.must(termsQuery(EsFields.shareId, operateLogListGetDTO.getShareIdList()));
        }

        if (Objects.nonNull(operateLogListGetDTO.getElefaceLogUserRole())) {
            queryCondition.must(termQuery(EsFields.elefaceOperateLogElefaceLogUserRole, operateLogListGetDTO.getElefaceLogUserRole()));
        }

        if (StringUtils.isNotEmpty(operateLogListGetDTO.getShareMemo())) {
            queryCondition.must(matchPhraseQuery(EsFields.elefaceOperateLogShareMemo, operateLogListGetDTO.getShareMemo()));
        }

        if (Objects.nonNull(operateLogListGetDTO.getIsCancel())) {
            if (BooleanUtils.isTrue(operateLogListGetDTO.getIsCancel())) {
                queryCondition.must(termQuery(EsFields.isCancel, true));
            } else {
                queryCondition.must(boolQuery().should(termsQuery(EsFields.isCancel, false))
                    .should(boolQuery().mustNot(existsQuery(EsFields.isCancel))));
            }
        }

        if (StringUtils.isNotEmpty(operateLogListGetDTO.getOwnerSellerId())) {
            queryCondition.must(termQuery(EsFields.elefaceOperateOwnerSellerId, operateLogListGetDTO.getOwnerSellerId()));
        }

        if (CollectionUtils.isNotEmpty(operateLogListGetDTO.getOwnerSellerIdList())) {
            queryCondition.must(termsQuery(EsFields.elefaceOperateOwnerSellerId, operateLogListGetDTO.getOwnerSellerIdList()));
        }

        if (Objects.nonNull(operateLogListGetDTO.getTargetSellerId())) {
            queryCondition.must(termQuery(EsFields.elefaceOperateTargetSellerId, operateLogListGetDTO.getTargetSellerId()));
        }

        if (CollectionUtils.isNotEmpty(operateLogListGetDTO.getTargetSellerIdList())) {
            queryCondition
                .must(termsQuery(EsFields.elefaceOperateTargetSellerId, operateLogListGetDTO.getTargetSellerIdList()));
        }

        if (StringUtils.isNotEmpty(operateLogListGetDTO.getSalesman())) {
            queryCondition.must(termQuery(EsFields.elefaceOperateLogSalesmanKeyWord, operateLogListGetDTO.getSalesman()));
        }

        if (StringUtils.isNotEmpty(operateLogListGetDTO.getBranchName())) {
            queryCondition.must(matchPhraseQuery(EsFields.elefaceOperateLogBranchName, operateLogListGetDTO.getBranchName()));
        }

        if (StringUtils.isNotEmpty(operateLogListGetDTO.getReceiverName())) {
            queryCondition.must(matchPhraseQuery(EsFields.receiverName, operateLogListGetDTO.getReceiverName()));
        }

        if (StringUtils.isNotEmpty(operateLogListGetDTO.getReceiverMobile())) {
            queryCondition.must(
                boolQuery().should(matchPhraseQuery(EsFields.receiverMobile, operateLogListGetDTO.getReceiverMobile()))
                    .should(matchPhraseQuery(EsFields.receiverPhone, operateLogListGetDTO.getReceiverMobile())));
        }

        if (StringUtils.isNotEmpty(operateLogListGetDTO.getReceiverAddress())) {
            queryCondition.must(matchPhraseQuery(EsFields.receiverAddress, operateLogListGetDTO.getReceiverAddress()));

        }

        if (StringUtils.isNotEmpty(operateLogListGetDTO.getElefaceTemplateName())) {
            queryCondition.must(matchPhraseQuery(EsFields.elefaceOperateLogElefaceTemplateName,
                operateLogListGetDTO.getElefaceTemplateName()));
        }

        if (StringUtils.isNotEmpty(operateLogListGetDTO.getOperatorName())) {
            queryCondition.must(matchPhraseQuery(EsFields.operatorName, operateLogListGetDTO.getOperatorName()));
        }

        // 是否打印
        if (BooleanUtils.isTrue(operateLogListGetDTO.getIsPrint())) {
            queryCondition.must(termQuery(EsFields.elefaceOperateLogIsPrint, operateLogListGetDTO.getIsPrint()));
        } else if (BooleanUtils.isFalse(operateLogListGetDTO.getIsPrint())) {
            queryCondition.mustNot(termQuery(EsFields.elefaceOperateLogIsPrint, true));
        }

        // 是否发货
        if (BooleanUtils.isTrue(operateLogListGetDTO.getIsSendGood())) {
            queryCondition.must(termQuery(EsFields.elefaceOperateLogIsSendGood, operateLogListGetDTO.getIsSendGood()));
        } else if (BooleanUtils.isFalse(operateLogListGetDTO.getIsSendGood())) {
            queryCondition.mustNot(termQuery(EsFields.elefaceOperateLogIsSendGood, true));
        }

        // 订单类型
        if (Objects.nonNull(operateLogListGetDTO.getTradeType())) {
            queryCondition.must(termQuery(EsFields.tradeType, operateLogListGetDTO.getTradeType()));
        }

        // 面单服务平台
        if (StringUtils.isNotEmpty(operateLogListGetDTO.getElefaceProvider())) {
            queryCondition.must(termQuery(EsFields.elefaceProvider,operateLogListGetDTO.getElefaceProvider()));
        }


            return queryCondition;
    }


    public Integer queryTotalCount(MultiWaybillOperateLogListGetDTO operateLogListGetDTO, List<UserInfoBo> userInfoBoList) {
        BoolQueryBuilder boolQueryBuilder = createBoolQuerySearchList(operateLogListGetDTO, userInfoBoList);
        NativeSearchQueryBuilder builder = ayPrintLogListGetBuilder(userInfoBoList);
        builder.withQuery(filterQuery(boolQueryBuilder));
        return (int) count(builder.build());
    }


    /**
     * 生成打印日志多店查询Builder
     *
     * @param userInfoBoList
     * @return
     */
    protected NativeSearchQueryBuilder ayPrintLogListGetBuilder(List<UserInfoBo> userInfoBoList) {
        List<String> routerArr = new ArrayList<>(userInfoBoList.size());

        for (UserInfoBo userInfoBo : userInfoBoList) {
            routerArr.add(userInfoBo.getSellerId());
        }
        String routerArrStr = StringUtils.join(routerArr, ",");

        NativeSearchQueryBuilder builder = new NativeSearchQueryBuilder().withSearchType(SearchType.QUERY_THEN_FETCH)
            .withFields(EsFields.sellerId, EsFields.tid, EsFields.ayPrintLogId, EsFields.mergeSubPrintLogId,
                EsFields.waybillNumber, EsFields.elefaceOperateOrderSellerId);

        builder.withIndices(getIndexName());
        builder.withRoute(routerArrStr);
        return builder;
    }


    /**
     * 打印日志分页查询
     *
     * @param builder
     * @return
     */
    private Page<AyPrintLogSearchEs> pageQuery(NativeSearchQueryBuilder builder) {
        if (CollectionUtils.isEmpty(builder.build().getIndices())) {
            builder.withIndices(AyPrintLogSearchEs.INDEX_NAME_PREFIX + "*");
        }
        return operations.queryForPage(builder.build(), AyPrintLogSearchEs.class);
    }

}
