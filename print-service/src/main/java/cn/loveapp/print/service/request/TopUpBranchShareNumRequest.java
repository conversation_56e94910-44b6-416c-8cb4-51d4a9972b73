package cn.loveapp.print.service.request;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 面单充值request
 *
 * <AUTHOR>
 */
@Data
@ApiModel(value = "面单充值请求")
public class TopUpBranchShareNumRequest {

    @ApiModelProperty(value = "共享面单充值数量", required = true)
    @NotNull
    private Long topUpNum;

    @ApiModelProperty(value = "分享Id", required = true)
    @NotEmpty
    private String shareId;

    /**
     * 是否无限分享
     */
    @ApiModelProperty(value = "是否无限分享")
    private Boolean shareNumUnlimited;

}
