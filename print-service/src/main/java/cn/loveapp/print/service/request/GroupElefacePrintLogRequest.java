package cn.loveapp.print.service.request;

import java.time.LocalDateTime;
import java.util.List;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

import cn.loveapp.common.utils.DateUtil;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 统计面单打印日志 request
 *
 * <AUTHOR>
 */
@Data
@ApiModel(value = "统计面单打印日志请求")
public class GroupElefacePrintLogRequest {

    /**
     * 物流公司列表
     */
    @ApiModelProperty(value = "物流公司列表")
    private List<String> cpCodeList;

    /**
     * 收件人省份列表
     */
    @ApiModelProperty(value = "收件人省份列表")
    private List<String> receiverProvinceList;

    /**
     * 电子面单服务商 {@link cn.loveapp.print.common.constant.ElefaceProviderConstant}
     */
    @ApiModelProperty(value = "电子面单服务商")
    private String elefaceProvider;

    /**
     * 打印时间-起始日期
     */
    @ApiModelProperty(value = "打印时间-起始日期", required = true)
    @NotNull
    private LocalDateTime startTime;

    /**
     * 打印时间-结束日期
     */
    @ApiModelProperty(value = "打印时间-结束日期", required = true)
    @NotNull
    private LocalDateTime endTime;

    public void setStartTime(@NotEmpty String startTime) {
        this.startTime = DateUtil.parseString(startTime);
    }

    public void setEndTime(@NotEmpty String endTime) {
        this.endTime = DateUtil.parseString(endTime);
    }
}
