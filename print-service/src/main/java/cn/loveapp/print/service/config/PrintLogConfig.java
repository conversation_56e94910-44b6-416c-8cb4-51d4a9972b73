package cn.loveapp.print.service.config;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

import com.google.common.collect.Lists;

import lombok.Data;

/**
 * 打印日志相关配置
 *
 * <AUTHOR>
 */
@Data
@Configuration
public class PrintLogConfig {
    /**
     * 地域别名对应的省份
     * <p>
     * e.g. 江浙沪 -> 江苏省,浙江省,上海
     */
    @Value("#{${print.service.pringlog.regionAliasToProvincesMap}}")
    private Map<String, String> regionAliasToProvincesMap;

    /**
     * 地域别名对应的城市
     * <p>
     * e.g. 珠三角 -> 广州市、深圳市、佛山市、东莞市、中山市、珠海市、江门市、肇庆市、惠州市
     */
    @Value("#{${print.service.pringlog.regionAliasToCitiesMap}}")
    private Map<String, String> regionAliasToCitiesMap;

    /**
     * 是否保存发货单日志（ay_deliver_printlog）
     */
    @Value("${print.service.pringlog.isSaveDeliver:false}")
    private Boolean isSaveDeliver;

    /**
     * 打印日志列表查询一次性最多查询数量
     */
    @Value("${print.service.pringlog.list.size.max:200}")
    private int printLogListMaxSize;

    /**
     * 打印日志列表需要查询响应的数据字段
     */
    @Value("${print.service.pringlog.list.fields:id,tid,trade_type,print_time,waybill_code,receiver_mobile,receiver_name,receiver_phone,receiver_city,receiver_detail,receiver_district,receiver_province,receiver_town,receiver_zip}")
    private List<String> printLogListFields;

    /**
     * 打印日志列表搜索默认天数
     */
    @Value("${print.service.pringlog.list.search.default.days:90}")
    private Integer printLogListSearchDefaultDays;

    /**
     * 打印日志列表专业版搜索最大天数
     */
    @Value("${print.service.pringlog.list.professionalVip.search.max.days:180}")
    private Integer printLogListProfessionalVipSearchMaxDays;

    /**
     * 打印日志列表降级平台开关，有值时对对应的平台做降级处理，无值时则不降级
     */
    @Value("${print.service.pringlog.list.degrade.platforms:}")
    private List<String> printLogListDegradePlatforms = new ArrayList<>();

    /**
     * 打印日志列表添加打印类型开关
     */
    @Value("${print.service.pringlog.list.append.printType.enabled:true}")
    private Boolean isPrintLogListAppendPrintTypeEnabled;

    /**
     * 打印日志总数缓存的时间（秒）
     */
    @Value("${items.service.cache.count.timeout:5}")
    protected Integer countCacheTimeout;

    /**
     * 打印日志字段列表统计阈值
     */
    @Value("${print.service.printLog.fieldList.statistics.threshold:200}")
    private Integer printLogFieldListStatisticsThreshold;

    /**
     * 打印日志字段统计结果大小聚合大小
     */
    @Value("${print.service.printLog.fieldList.result.size:200}")
    private Integer printLogFieldListResultSize;

    /**
     * 打印日志字段可选值聚合缓存超时时间
     */
    @Value("${print.service.printLog.fieldList.cache.statistics.timeout:300}")
    private Integer printLogFieldListStatisticsCacheTimeout;

    /**
     * 打印日志列表接口计数聚合开关
     */
    @Value("${print.service.printLogList.count.statistics.enabled:true}")
    private Boolean printListCountStatisticsEnabled;

    /**
     * 打印日志列表计数聚合缓存的时间（秒）
     */
    @Value("${print.service.printLogList.count.statistics.cache.timeout:20}")
    protected Integer printLogListCountStatisticsCacheTimeout;

    /**
     * 专业版VIP等级列表
     */
    @Value("${print.service.professional.vipList:6,8}")
    private List<Integer> professionalVipList;

    /**
     * 忽略查询的用户id
     */
    @Value("${print.service.ignoreUserIdList:}")
    private List<String> ignoreUserIdList = new ArrayList<>();

    /**
     * 获取地区别名对应的省份列表
     *
     * @param regionAlias
     * @return
     */
    public List<String> regionAliasToProvincesMap(String regionAlias) {
        String provinces = regionAliasToProvincesMap.get(regionAlias);
        if (StringUtils.isEmpty(provinces)) {
            return null;
        }
        return Lists.newArrayList(provinces.split(","));
    }

    /**
     * 获取地区别名对应的城市列表
     *
     * @param regionAlias
     * @return
     */
    public List<String> regionAliasToCitiesMap(String regionAlias) {
        String cities = regionAliasToCitiesMap.get(regionAlias);
        if (StringUtils.isEmpty(cities)) {
            return null;
        }
        return Lists.newArrayList(cities.split(","));
    }
}
