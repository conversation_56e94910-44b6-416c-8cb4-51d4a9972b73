package cn.loveapp.print.service.dao.redis;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.HashOperations;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Repository;
import org.springframework.util.CollectionUtils;

import com.alibaba.fastjson.JSON;

import cn.loveapp.common.constant.CommonConstants;
import cn.loveapp.common.utils.LoggerHelper;
import cn.loveapp.print.service.bo.UserInfoBo;
import cn.loveapp.print.service.config.SerialConfig;
import org.springframework.util.StringUtils;

/**
 * 流水号Redis 数据操作层
 *
 * <AUTHOR>
 */
@Repository
public class SerialRedisDao {
    private static final LoggerHelper LOGGER = LoggerHelper.getLogger(SerialRedisDao.class);
    /**
     * 某笔订单的redis key
     */
    private static final String TRADE_SERIAL_KEY = "trade:serial:";

    /**
     * 用户流水号index
     */
    private static final String USER_SERIAL_INDEX_KEY = "user:serial:index:";

    @Autowired
    private StringRedisTemplate stringRedisTemplate;

    @Autowired
    private SerialConfig serialConfig;

    /**
     * 生成Redis订单流水号的key
     *
     * @param tid
     * @param tradeType
     * @param storeId
     * @param sellerId
     * @param appName
     * @return
     */
    private String initTradeSerialKey(@NotNull String tid, @NotNull Integer tradeType, @NotNull String storeId,
        @NotNull String sellerId, @NotNull String appName) {
        return TRADE_SERIAL_KEY + tid + ":" + tradeType + ":" + sellerId + ":" + storeId + ":" + appName;
    }

    private String initUserSerialIndexKey(@NotNull String storeId, @NotNull String sellerId, @NotNull String appName) {
        String nowDate = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        return USER_SERIAL_INDEX_KEY + sellerId + ":" + storeId + ":" + appName + ":" + nowDate;
    }

    /**
     * 生成打印流水号的index
     *
     * @param userInfoBo
     * @return
     */
    public Long generateUserSerialIndex(UserInfoBo userInfoBo) {
        try {
            String key =
                initUserSerialIndexKey(userInfoBo.getStoreId(), userInfoBo.getSellerId(), userInfoBo.getAppName());
            Long index = stringRedisTemplate.opsForValue().increment(key, 1);
            setTimeout(key);
            return index;
        } catch (Exception e) {
            LOGGER.logError(userInfoBo.getSellerNick(), "", "生成打印流水号index失败: " + e.getMessage(), e);
        }
        return null;
    }

    /**
     * 获取用户当前的打印流水号
     *
     * @param userInfoBo
     * @return
     */
    public Long getUserCurrentSerialIndex(UserInfoBo userInfoBo) {
        try {
            //返回给前端的是当前最大流水号，0代表用户今天没有生成过流水号。前端需要+1，从1开始
            Long index = 0L;
            String key =
                initUserSerialIndexKey(userInfoBo.getStoreId(), userInfoBo.getSellerId(), userInfoBo.getAppName());
            String currentIndex = stringRedisTemplate.opsForValue().get(key);
            if (!StringUtils.isEmpty(currentIndex)) {
                index = Long.valueOf(currentIndex);
            }
            return index;
        } catch (Exception e) {
            LOGGER.logError(userInfoBo.getSellerNick(), "", "生成打印流水号index失败: " + e.getMessage(), e);
        }
        return null;
    }

    /**
     * 获取订单所有的流水号oids组合
     *
     * @param tid
     * @param tradeType
     * @param userInfoBo
     * @return
     */
    public Map<String, List<String>> getTradeSerial(@NotNull String tid, @NotNull Integer tradeType,
        UserInfoBo userInfoBo) {
        try {
            String key = initTradeSerialKey(tid, tradeType, userInfoBo.getStoreId(), userInfoBo.getSellerId(),
                userInfoBo.getAppName());
            HashOperations<String, String, String> ops = stringRedisTemplate.opsForHash();
            Map<String, String> valuesMap = ops.entries(key);
            if (!CollectionUtils.isEmpty(valuesMap)) {
                setTimeout(key);
                Map<String, List<String>> serialToOidsMap = new HashMap<>(valuesMap.size());
                for (String serial : valuesMap.keySet()) {
                    // 将存储在redis里面的字符串转换为List
                    serialToOidsMap.put(serial, JSON.parseArray(valuesMap.get(serial), String.class));
                }
                return serialToOidsMap;
            }
        } catch (Exception e) {
            LOGGER.logError(userInfoBo.getSellerId(), tid, "从redis中获取订单流水号失败" + e.getMessage(), e);
        }
        return null;
    }

    /**
     * 更新订单的流水号
     *
     * @param serial
     *            流水号
     * @param tid
     * @param oids
     * @param tradeType
     * @param userInfoBo
     */
    public void saveTradeSerial(@NotNull String serial, @NotNull String tid, @NotEmpty List<String> oids,
        @NotNull Integer tradeType, UserInfoBo userInfoBo) {
        try {
            String key = initTradeSerialKey(tid, tradeType, userInfoBo.getStoreId(), userInfoBo.getSellerId(),
                userInfoBo.getAppName());
            HashOperations<String, String, String> ops = stringRedisTemplate.opsForHash();
            ops.put(key, serial, JSON.toJSONString(oids));
            setTimeout(key);
        } catch (Exception e) {
            LOGGER.logError(userInfoBo.getSellerId(), tid, "保存订单流水号到Redis失败" + e.getMessage(), e);
        }
    }

    public void flushTradeSerial(@NotEmpty Map<String, List<String>> serialToOidsMap, @NotNull String tid,
        @NotNull Integer tradeType, UserInfoBo userInfoBo) {
        Map<String, String> valuesMap = new HashMap<>(serialToOidsMap.size());
        for (String serial : serialToOidsMap.keySet()) {
            valuesMap.put(serial, JSON.toJSONString(serialToOidsMap.get(serial)));
        }
        try {
            String key = initTradeSerialKey(tid, tradeType, userInfoBo.getStoreId(), userInfoBo.getSellerId(),
                userInfoBo.getAppName());
            HashOperations<String, String, String> ops = stringRedisTemplate.opsForHash();
            ops.putAll(key, valuesMap);
            setTimeout(key);
        } catch (Exception e) {
            LOGGER.logError(userInfoBo.getSellerNick(), tid, "更新订单流水号到Redis失败" + e.getMessage(), e);
        }
    }

    /**
     * 删除流水号缓存
     *
     * @param tids
     * @param tradeType
     * @param userInfoBo
     */
    public void deleteTradeSerial(@NotEmpty List<String> tids, @NotNull Integer tradeType, UserInfoBo userInfoBo) {
        try {
            List<String> keys = new ArrayList<>();
            for (String tid : tids) {
                keys.add(initTradeSerialKey(tid, tradeType, userInfoBo.getStoreId(), userInfoBo.getSellerId(),
                    userInfoBo.getAppName()));
            }
            stringRedisTemplate.delete(keys);
        } catch (Exception e) {
            LOGGER.logError(MDC.get(CommonConstants.nick), "",
                "tids" + JSON.toJSONString(tids) + ",删除流水号缓存失败" + e.getMessage(), e);
        }
    }

    private void setTimeout(String key) {
        stringRedisTemplate.expire(key, serialConfig.getRedisTimeout(), TimeUnit.HOURS);
    }
}
