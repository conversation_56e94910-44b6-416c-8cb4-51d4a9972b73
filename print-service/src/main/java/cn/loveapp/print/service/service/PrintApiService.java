package cn.loveapp.print.service.service;

import cn.loveapp.common.web.CommonApiResponse;
import cn.loveapp.print.api.dto.ElefaceSharingRelationDTO;
import cn.loveapp.print.api.dto.WaybillOperatelogResponseDTO;
import cn.loveapp.print.api.request.*;
import cn.loveapp.print.api.response.CountPrintLogResponse;
import cn.loveapp.print.api.response.ElefaceIsCancelGetInnerResponse;
import cn.loveapp.print.api.response.SharingBatchCancelResponse;

import java.util.List;

/**
 * @Author: zhongzijie
 * @Date: 2022/8/1 15:47
 * @Description: 打印服务
 */
public interface PrintApiService {

	/**
	 * 创建面单共享关系
	 *
	 * @param request
	 * @return
	 */
	CommonApiResponse<ElefaceSharingRelationDTO> elefaceSharingCreate(ElefaceSharingCreateInnerRequest request);

	/**
	 * 创建面单共享关系
	 *
	 * @param request
	 * @return
	 */
	CommonApiResponse<ElefaceSharingRelationDTO> multiElefaceSharingCreate(ElefaceSharingCreateInnerRequest request);

	/**
	 * 获取面单发货地址信息
	 *
	 * @param request
	 * @return
	 */
	CommonApiResponse elefaceShippaddressGetall(ElefaceShippaddressGetallInnerRequest request);

	/**
	 * 获取面单发货地址信息
	 *
	 * @param request
	 * @return
	 */
	CommonApiResponse multiElefaceShippaddressGetall(ElefaceShippaddressGetallInnerRequest request);

	/**
	 * 查询指定订单是否打印了面单和快递单
	 * @param request
	 * @return
	 */
	CommonApiResponse<Boolean> queryOrderHasPrintHistory(OrderHasprintGetInnerRequest request);

	/**
	 * 保存运单号操作日志
	 * @param request
	 * @return
	 */
	CommonApiResponse waybillOperatelogSave(WaybillOperateLogSaveRequest request);

    /**
     * 统计不同打印类型的打印日志记录数
     *
     * @param request
     * @return
     */
    CommonApiResponse<CountPrintLogResponse> countPrintLogByPrintType(CountPrintLogRequest request);

    /**
     * 批量查询面单是否被回收
     *
     * @param request
     * @return
     */
    CommonApiResponse<List<ElefaceIsCancelGetInnerResponse>> elefaceIsCancelGet(ElefaceIsCancelGetInnerRequest request);

	/**
	 * 查询面单共享关系列表
	 * @param request
	 * @return
	 */
	CommonApiResponse<List<ElefaceSharingRelationDTO>> sharingBatchGet(SharingBatchGetRequest request);

	/**
	 * 取消面单共享
	 * @param request
	 * @return
	 */
	CommonApiResponse<SharingBatchCancelResponse> sharingBatchCancel(SharingBatchCancelRequest request);

	/**
	 * 面单共享充值
	 * @param request
	 * @return
	 */
	CommonApiResponse<String> sharingTopUpNum(ShareTopUpNumRequest request);

	/**
	 * 面单共享充数量修改
	 * @param request
	 * @return
	 */
	CommonApiResponse<String> sharingQuantityModify(ShareTopUpNumRequest request);

	/**
	 * 共享面单操作日志获取
	 * @param request
	 * @return
	 */
	CommonApiResponse<WaybillOperatelogResponseDTO> waybillShareOperateLogListGet(WaybillShareOperateLogListGetRpcRequest request);

	/**
	 * 共享面单恢复接口
	 * @param request
	 * @return
	 */
	CommonApiResponse<String> sharingRecovery(ShareRecoveryRequest request);
}
