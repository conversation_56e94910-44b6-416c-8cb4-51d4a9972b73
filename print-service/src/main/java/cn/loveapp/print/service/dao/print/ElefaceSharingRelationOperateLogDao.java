package cn.loveapp.print.service.dao.print;

import cn.loveapp.print.service.bo.ElefaceSharingRelationQueryBo;
import cn.loveapp.print.service.entity.ElefaceSharingRelationOperateLog;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 电子面单共享关系操作日志表 数据库访问层
 *
 * <AUTHOR>
 * @since 2021-02-28 16:04:44
 */
public interface ElefaceSharingRelationOperateLogDao {

    /**
     * 插入一条面单记录操作日志
     *
     * @param elefaceSharingRelationOperateLog
     * @return
     */
    int insert(ElefaceSharingRelationOperateLog elefaceSharingRelationOperateLog);


    /**
     * 根据查询条件，查询面单分享操作日志列表
     *
     * @param queryBo
     * @return
     */
    List<ElefaceSharingRelationOperateLog> searchListByQuery(@Param("queryBo") ElefaceSharingRelationQueryBo queryBo);


    /**
     * 根据查询条件，查询面单分享操作日志总数
     *
     * @param queryBo
     * @return
     */
    Integer countByQuery(@Param("queryBo") ElefaceSharingRelationQueryBo queryBo);

}
