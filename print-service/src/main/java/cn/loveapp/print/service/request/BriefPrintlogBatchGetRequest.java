package cn.loveapp.print.service.request;

import java.util.List;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

import cn.loveapp.print.service.annotation.TradeTypeValidation;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class BriefPrintlogBatchGetRequest {
    @NotEmpty
    private List<String> tids;

    @NotNull
    @TradeTypeValidation
    private Integer tradeType;
}
