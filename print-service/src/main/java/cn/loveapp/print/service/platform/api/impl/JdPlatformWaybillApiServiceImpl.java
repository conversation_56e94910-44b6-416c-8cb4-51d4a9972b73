package cn.loveapp.print.service.platform.api.impl;

import cn.loveapp.common.constant.CommonPlatformConstants;
import cn.loveapp.common.platformsdk.jd.JdSDKService;
import cn.loveapp.common.utils.LoggerHelper;
import cn.loveapp.print.service.api.entity.AyWaybillApplySubscriptionInfo;
import cn.loveapp.print.service.api.entity.WaybillCloudPrintInfo;
import cn.loveapp.print.service.api.request.AyWaybillCancelRequest;
import cn.loveapp.print.service.api.request.AyWaybillGetRequest;
import cn.loveapp.print.service.api.request.AyWaybillSearchRequest;
import cn.loveapp.print.service.api.response.AyWaybillCancelResponse;
import cn.loveapp.print.service.api.response.AyWaybillGetResponse;
import cn.loveapp.print.service.api.response.AyWaybillSearchResponse;
import cn.loveapp.print.service.code.ErrorCode;
import cn.loveapp.print.service.platform.api.PlatformWaybillApiService;
import cn.loveapp.print.service.platform.domain.JdWaybillGetRequest;
import com.alibaba.fastjson.JSON;
import com.jd.open.api.sdk.domain.wujiemiandan.QueryContractApi.request.query.QueryContractRequest;
import com.jd.open.api.sdk.domain.wujiemiandan.QueryContractApi.response.query.QueryContractResponse;
import com.jd.open.api.sdk.domain.wujiemiandan.WaybillReceiveOpenApi.request.create.WaybillReceiveRequest;
import com.jd.open.api.sdk.domain.wujiemiandan.WaybillReceiveOpenApi.response.create.Response;
import com.jd.open.api.sdk.domain.wujiemiandan.WaybillReceiveOpenApi.response.create.WaybillCodeInfoDTO;
import com.jd.open.api.sdk.domain.wujiemiandan.WaybillReceiveOpenApi.response.create.WaybillReceiveResponse;
import com.jd.open.api.sdk.domain.ydy.PullDataService.request.pullData.PullDataReqDTO;
import com.jd.open.api.sdk.domain.ydy.PullDataService.request.pullData.WayBillInfo;
import com.jd.open.api.sdk.domain.ydy.PullDataService.response.pullData.PrePrintDataInfo;
import com.jd.open.api.sdk.domain.ydy.PullDataService.response.pullData.PullDataRespDTO;
import com.jd.open.api.sdk.request.wujiemiandan.LogisticsEwaybillContractQueryRequest;
import com.jd.open.api.sdk.request.wujiemiandan.LogisticsEwaybillWaybillCancelRequest;
import com.jd.open.api.sdk.request.wujiemiandan.LogisticsEwaybillWaybillCreateRequest;
import com.jd.open.api.sdk.request.ydy.PrintingPrintDataPullDataRequest;
import com.jd.open.api.sdk.response.wujiemiandan.LogisticsEwaybillContractQueryResponse;
import com.jd.open.api.sdk.response.wujiemiandan.LogisticsEwaybillWaybillCancelResponse;
import com.jd.open.api.sdk.response.wujiemiandan.LogisticsEwaybillWaybillCreateResponse;
import com.jd.open.api.sdk.response.ydy.PrintingPrintDataPullDataResponse;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2025/2/24 15:00
 */
@Service
public class JdPlatformWaybillApiServiceImpl implements PlatformWaybillApiService {

    private static final LoggerHelper LOGGER = LoggerHelper.getLogger(JdPlatformWaybillApiServiceImpl.class);

    /**
     * 京东物流公司 （其余为非京东）
     */
    private static final List<String> JD_CP_CODE_LIST = Arrays.asList("JD", "JDKY", "JDDJ", "ZY", "BDB");

    /**
     * 京东取号和打印数据调用的cpCode映射
     */
    @Value("#{${print.service.jd.cpCode.map: {BDB:'JD'}}}")
    private Map<String, String> jdCpCodeMap = new HashMap<>();

    @Autowired
    private JdSDKService jdSDKService;


    @Override
    public AyWaybillSearchResponse waybillSearch(AyWaybillSearchRequest request, String topSession, String platformId, String appName) {
        LogisticsEwaybillContractQueryRequest queryRequest = new LogisticsEwaybillContractQueryRequest();
        QueryContractRequest queryContractRequest = new QueryContractRequest();
        queryContractRequest.setVendorCode(request.getSellerId());
        queryRequest.setRequest(queryContractRequest);

        LogisticsEwaybillContractQueryResponse ewaybillContractQueryResponse = jdSDKService.execute(queryRequest, appName, topSession);
        AyWaybillSearchResponse response = new AyWaybillSearchResponse();
        response.init(ewaybillContractQueryResponse);
        if (ewaybillContractQueryResponse.getReturnType() == null) {
            // 失败直接返回
            response.setErrorCode(ErrorCode.BaseCode.REQUEST_ERR.getCode().toString());
            return response;
        }

        List<QueryContractResponse> data = ewaybillContractQueryResponse.getReturnType().getData();

        if (CollectionUtils.isEmpty(data)) {
            response.setWaybillApplySubscriptionCols(Collections.emptyList());
        } else {
            Map<String, List<QueryContractResponse>> contractGroupByCompany = data.stream()
                    .collect(Collectors.groupingBy(item -> item.getProviderCode() + ":" + item.getOperationType()));

            response.setWaybillApplySubscriptionCols(contractGroupByCompany.values().stream().map(
                    item -> AyWaybillApplySubscriptionInfo.of(item, item.get(0)))
                            .collect(Collectors.toList()));
        }
        return response;
    }

    @Override
    public AyWaybillGetResponse waybillGet(AyWaybillGetRequest request, String topSession, String platformId, String appName) {
        AyWaybillGetResponse response = new AyWaybillGetResponse();

        JdWaybillGetRequest waybillGetRequest = null;
        try {
            waybillGetRequest = JSON.parseObject(request.getParamWaybillCloudPrintApplyNewRequest(), JdWaybillGetRequest.class);
        } catch (Exception e) {
            LOGGER.logError("非法的参数：paramWaybillCloudPrintApplyNewRequest", e);
            response.setErrorCode(ErrorCode.BaseCode.PARAMS_ERR.getCode().toString());
            response.setMsg(
                    "非法的参数：paramWaybillCloudPrintApplyNewRequest = " + request.getParamWaybillCloudPrintApplyNewRequest());
            return response;
        }

        if (waybillGetRequest == null || waybillGetRequest.getRequest() == null) {
            LOGGER.logError("非法的参数：paramWaybillCloudPrintApplyNewRequest");
            response.setErrorCode(ErrorCode.BaseCode.PARAMS_ERR.getCode().toString());
            response.setMsg(
                    "非法的参数：paramWaybillCloudPrintApplyNewRequest = " + request.getParamWaybillCloudPrintApplyNewRequest());
            return response;
        }

        WaybillReceiveRequest waybillReceiveRequest = waybillGetRequest.getRequest();

        LogisticsEwaybillWaybillCreateRequest logisticsEwaybillWaybillCreateRequest = new LogisticsEwaybillWaybillCreateRequest();
        logisticsEwaybillWaybillCreateRequest.setRequest(waybillReceiveRequest);
        LogisticsEwaybillWaybillCreateResponse logisticsEwaybillWaybillCreateResponse = jdSDKService.execute(logisticsEwaybillWaybillCreateRequest, appName, topSession);

        if (logisticsEwaybillWaybillCreateResponse == null || logisticsEwaybillWaybillCreateResponse.getResponse() == null) {
            LOGGER.logError("取号调用失败");
            response.setErrorCode(ErrorCode.BaseCode.REQUEST_ERR.getCode().toString());
            response.setMsg("取号调用失败");
            return response;
        }

        Response createResponseResponse = logisticsEwaybillWaybillCreateResponse.getResponse();

        WaybillReceiveResponse data = createResponseResponse.getData();
        if (data == null) {
            LOGGER.logError("取号调用失败");
            response.setErrorCode(String.valueOf(createResponseResponse.getStatusCode()));
            response.setMsg(createResponseResponse.getStatusMessage());
            return response;
        }

        String cpCode = waybillReceiveRequest.getProviderCode();

        PrintingPrintDataPullDataRequest dataPullDataRequest = new PrintingPrintDataPullDataRequest();

        boolean isJdLogistic = JD_CP_CODE_LIST.contains(cpCode);
        Integer popFlag = waybillGetRequest.getPopFlag();
        List<WaybillCodeInfoDTO> waybillCodeInfoList = data.getWaybillCodeInfoList();
        List<WayBillInfo> wayBillInfos = waybillCodeInfoList.stream().map(wayBillCode -> {
            WayBillInfo wayBillInfo = new WayBillInfo();
            if (popFlag != null && popFlag == 1) {
                wayBillInfo.setOrderNo(data.getPlatformOrderNo());
            }
            wayBillInfo.setPopFlag(popFlag);
            if (isJdLogistic) {
                wayBillInfo.setJdWayBillCode(wayBillCode.getWaybillCode());
            } else {
                wayBillInfo.setWayBillCode(wayBillCode.getWaybillCode());
            }
            return wayBillInfo;
        }).collect(Collectors.toList());

        Map<String, String> parameters = new HashMap<>();

        PullDataReqDTO param = new PullDataReqDTO();

        String pullDateCpCode = jdCpCodeMap.getOrDefault(cpCode, cpCode);

        param.setCpCode(pullDateCpCode);
        param.setObjectId(waybillGetRequest.getObjectId());
        if (isJdLogistic) {
            parameters.put("ewCustomerCode", waybillReceiveRequest.getSettlementCode());
        } else {
            parameters.put("eCustomerCode", waybillReceiveRequest.getVendorCode());
        }
        param.setParameters(parameters);
        param.setWayBillInfos(wayBillInfos);
        dataPullDataRequest.setParam1(param);

        PrintingPrintDataPullDataResponse printDataPullDataResponse = jdSDKService.execute(dataPullDataRequest, appName, topSession);

        if (printDataPullDataResponse == null || printDataPullDataResponse.getReturnType() == null) {
            LOGGER.logError("获取打印数据失败");
            response.setErrorCode(ErrorCode.BaseCode.REQUEST_ERR.getCode().toString());
            response.setMsg("获取打印数据失败");
            return response;
        }

        PullDataRespDTO returnType = printDataPullDataResponse.getReturnType();
        List<PrePrintDataInfo> prePrintDataList = returnType.getPrePrintDatas();
        if (prePrintDataList == null || prePrintDataList.isEmpty()) {
            return response;
        }

        boolean childMotherOrder = waybillReceiveRequest.getChildMotherOrder();
        Integer waybillCount = waybillReceiveRequest.getWaybillCount();
        PrePrintDataInfo prePrintDataInfo = prePrintDataList.get(0);
        List<WaybillCloudPrintInfo> waybillCloudPrintInfos = null;
        if (StringUtils.isNotEmpty(prePrintDataInfo.getPackageCode())) {
            waybillCloudPrintInfos = prePrintDataList.stream()
                .map(waybillInfo -> WaybillCloudPrintInfo.of(returnType.getObjectId(), waybillInfo, null, true))
                .collect(Collectors.toList());
        } else {
            String parentWaybillCode = null;
            if (childMotherOrder && waybillCount != null && waybillCount > 1) {
                // 子母件取号
                WayBillInfo wayBillInfo = wayBillInfos.get(0);
                parentWaybillCode = wayBillInfo.getWayBillCode() != null ? wayBillInfo.getWayBillCode()
                    : wayBillInfo.getJdWayBillCode();
            }

            String finalParentWaybillCode = parentWaybillCode;
            waybillCloudPrintInfos = prePrintDataList.stream().map(waybillInfo -> WaybillCloudPrintInfo
                .of(returnType.getObjectId(), waybillInfo, finalParentWaybillCode, false)).collect(Collectors.toList());
        }

        response.setWaybillCloudPrintInfoList(waybillCloudPrintInfos);
        return response;
    }

    private String getParentWaybillCode(WaybillReceiveRequest waybillReceiveRequest, List<WayBillInfo> wayBillInfos) {
        boolean childMotherOrder = waybillReceiveRequest.getChildMotherOrder();
        Integer waybillCount = waybillReceiveRequest.getWaybillCount();

        String parentWaybillCode;
        if (childMotherOrder && waybillCount != null && waybillCount > 1) {
            // 子母件取号
            WayBillInfo wayBillInfo = wayBillInfos.get(0);
            parentWaybillCode = wayBillInfo.getWayBillCode() != null ? wayBillInfo.getWayBillCode() : wayBillInfo.getJdWayBillCode();
        } else {
            parentWaybillCode = null;
        }
        return parentWaybillCode;
    }

    @Override
    public AyWaybillCancelResponse waybillCancel(AyWaybillCancelRequest request, String topSession, String platformId, String appName) {

        AyWaybillCancelResponse response = new AyWaybillCancelResponse();

        LogisticsEwaybillWaybillCancelRequest waybillCancelRequest = new LogisticsEwaybillWaybillCancelRequest();
        waybillCancelRequest.setCancelReason("用户取消");
        waybillCancelRequest.setWaybillCode(request.getWaybillCode());
        waybillCancelRequest.setVendorCode(request.getSellerId());
        waybillCancelRequest.setProviderCode(request.getCpCode());

        LogisticsEwaybillWaybillCancelResponse cancelResponse = jdSDKService.execute(waybillCancelRequest, appName, topSession);
        response.init(cancelResponse);
        if (cancelResponse == null || cancelResponse.getResponse() == null) {
            response.setCancelResult(Boolean.FALSE);
            response.setSubMessage("调用异常");
        } else if (cancelResponse.getResponse().getStatusCode() == 1000){
            response.setCancelResult(Boolean.TRUE);
        } else {
            response.setCancelResult(Boolean.FALSE);
            response.setSubMessage(cancelResponse.getResponse().getStatusMessage());
            response.setSubCode(String.valueOf(cancelResponse.getResponse().getStatusCode()));
        }
        return response;
    }

    @Override
    public String getPlatformId() {
        return CommonPlatformConstants.PLATFORM_JD;
    }
}
