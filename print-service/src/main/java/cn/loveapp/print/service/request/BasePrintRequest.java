package cn.loveapp.print.service.request;

import java.time.LocalDateTime;
import java.util.List;

import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

import cn.loveapp.print.service.annotation.TradeTypeValidation;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
@ApiModel
public class BasePrintRequest {

    /**
     * 订单信息
     */
    @ApiModelProperty(value = "订单信息", required = true)
    @NotEmpty
    @Valid
    private List<PrintTradeInfoDTO> tradeInfoList;

    /**
     * 合单订单主单tid
     */
    @ApiModelProperty(value = "合单订单主单tid")
    private String mergeTid;

    /**
     * 是否为拆单打印
     */
    @ApiModelProperty(value = "是否为拆单打印", required = true)
    @NotNull
    private Boolean isSplit;

    /**
     * 订单类型 0-普通订单 1-自由打印订单
     */
    @ApiModelProperty(value = "订单类型 0-普通订单 1-自由打印订单", required = true)
    @NotNull
    @TradeTypeValidation
    private Integer tradeType;

    /**
     * 打印流水号 没有开启流水号则为空
     */
    @ApiModelProperty(value = "打印流水号 没有开启流水号则为空")
    private String serial;

    /**
     * 打印机
     */
    @ApiModelProperty(value = "打印机", required = true)
    @NotEmpty
    private String printer;

    /**
     * 打印数量
     */
    @ApiModelProperty(value = "打印数量", required = true)
    @NotNull
    private Integer printCount;

    /**
     * 买家Nick
     */
    @ApiModelProperty(value = "买家Nick")
    private String buyerNick;

    /**
     * 操作人
     */
    @ApiModelProperty(value = "真实快递公司code")
    private String operatorName;

    /**
     * 打印时间
     */
    @ApiModelProperty(value = "打印时间")
    private LocalDateTime printTime;


    /**
     * 批次号
     */
    @ApiModelProperty(value = "批次号")
    private String batchId;

    /**
     * 当前批次序号
     */
    @ApiModelProperty(value = "当前批次序号")
    private List<Integer> numbersInBatch;

    /**
     * 当前批次总数
     */
    @ApiModelProperty(value = "当前批次总数")
    private Integer batchTotals;
}
