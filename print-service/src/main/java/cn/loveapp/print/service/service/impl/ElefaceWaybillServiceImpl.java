package cn.loveapp.print.service.service.impl;

import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import javax.annotation.Nullable;
import javax.annotation.Resource;

import cn.loveapp.common.utils.DateUtil;
import cn.loveapp.orders.dto.SavePrintLogDTO;
import cn.loveapp.print.api.dto.MultiWaybillOperateLogListGetDTO;
import cn.loveapp.print.api.dto.WaybillOperateLogSaveDTO;
import cn.loveapp.print.api.proto.WaybillOperateLogStatusChangeRequestProto;
import cn.loveapp.print.api.request.ElefaceIsCancelGetInnerRequest;
import cn.loveapp.print.api.response.ElefaceIsCancelGetInnerResponse;
import cn.loveapp.print.common.constant.*;
import cn.loveapp.print.common.dto.WayBillOperateLogExtDTO;
import cn.loveapp.print.common.dto.WaybillOperateLogDetailsDTO;
import cn.loveapp.print.common.dto.WaybillOperatelogQueryDTO;
import cn.loveapp.print.common.entity.AyPrintLogSearchEs;
import cn.loveapp.print.common.utils.ListUtil;
import cn.loveapp.print.service.bo.ApiErrorBo;
import cn.loveapp.print.service.constant.ElefaceSharingRelationOperateConstant;
import cn.loveapp.print.service.convert.CommonConvertMapper;
import cn.loveapp.print.service.dao.es.ElasticsearchAyPrintLogQueryDao;
import cn.loveapp.print.service.dao.print.ElefaceSharingRelationDao;
import cn.loveapp.print.service.dao.redis.WaybillRedisDao;
import cn.loveapp.print.service.dto.*;
import cn.loveapp.print.service.entity.WaybillGetRedisEntity;
import cn.loveapp.print.service.request.*;
import cn.loveapp.print.service.service.*;
import cn.loveapp.print.service.service.es.AyElefaceOperateLogSearchService;
import cn.loveapp.uac.response.UserInfoResponse;
import com.alibaba.fastjson.JSONException;
import com.alibaba.fastjson.JSONObject;
import cn.loveapp.print.service.response.ElefaceSharingGetResponseDTO;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections4.ListUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSON;
import com.google.common.annotations.VisibleForTesting;
import com.google.common.collect.Lists;

import cn.loveapp.common.utils.LoggerHelper;
import cn.loveapp.print.common.entity.AyElefaceOperatelog;
import cn.loveapp.print.common.exception.*;
import cn.loveapp.print.common.service.UserCenterService;
import cn.loveapp.print.service.api.request.AyWaybillCancelRequest;
import cn.loveapp.print.service.api.request.AyWaybillGetRequest;
import cn.loveapp.print.service.api.response.AyWaybillCancelResponse;
import cn.loveapp.print.service.api.response.AyWaybillGetResponse;
import cn.loveapp.print.service.bo.UserInfoBo;
import cn.loveapp.print.service.bo.WaybillGetResponseBo;
import cn.loveapp.print.service.constant.ElefaceSharingRelationConstant;
import cn.loveapp.print.common.dao.newprint.AyElefaceOperatelogDao;
import cn.loveapp.print.service.dao.redis.ElefaceSharingRelationLockRedisDao;
import cn.loveapp.print.service.entity.ElefaceSharingRelation;
import cn.loveapp.print.api.dto.WaybillOperatelogResponseDTO;
import cn.loveapp.print.service.service.ElefaceAccountService;
import cn.loveapp.print.service.service.ElefaceWaybillService;
import cn.loveapp.print.service.platform.api.PlatformWaybillApiService;
import cn.loveapp.print.service.service.PrintlogService;

import static cn.loveapp.print.service.constant.ElefaceSharingRelationConstant.CANCEL_RECHARGE;

/**
 * <AUTHOR>
 */
@Service
public class ElefaceWaybillServiceImpl implements ElefaceWaybillService {
    private final static LoggerHelper LOGGER = LoggerHelper.getLogger(ElefaceWaybillServiceImpl.class);

    private final static String TEMPLATE_URL_STR = "templateURL";

    /**
     * 接口执行状态-成功
     */
    public static final String SUCCESS = "success";

    /**
     * 接口执行状态-失败
     */
    public static final String ERROR = "error";

    /**
     * 接口执行状态-运行中
     */
    public static final String RUNNING = "running";

    @Resource
    private AyElefaceOperatelogDao ayElefaceOperatelogDao;

    @Autowired
    private UserCenterService userCenterService;

    @Autowired
    private PrintlogService printlogService;

    @Autowired
    private ElefaceAccountService elefaceAccountService;

    @Autowired
    private ElefaceSharingRelationLockRedisDao sharingRelationLockRedisDao;

    @Autowired
    private PlatformWaybillApiService platformWaybillApiService;

    @Autowired
    private OrdersService ordersService;

    @Autowired
    private WaybillRedisDao waybillRedisDao;

    @Autowired
    private ElasticsearchAyPrintLogQueryDao elasticsearchAyPrintLogQueryDao;

    @Autowired
    private AyElefaceOperateLogSearchService ayElefaceOperateLogSearchService;

    @Resource
    private ElefaceSharingRelationDao elefaceSharingRelationDao;

    @Autowired
    private ElefaceSharingRelationOperateService elefaceSharingRelationOperateService;

    @Autowired
    private PrintMessageSendService printMessageSendService;

	/**
	 * 面单取号
	 *
	 * @param request
	 * @param userInfoBo
	 * @return
	 * @throws CommonException
	 */
	@Override
	public List<WaybillGetResponseBo> waybillGetBatch(ElefaceWaybillGetRequest request, UserInfoBo userInfoBo) throws CommonException {
		List<PrintTradeInfoDTO> tradeInfoList = request.getTradeInfoList();
		String mergeTid = request.getMergeTid();
		String cpCode = request.getCpCode();
		Integer tradeType = request.getTradeType();
		String logisticsCompany = request.getLogisticsCompany();
		String serial = request.getSerial();
		String shareId = request.getShareId();
		Boolean isMultiShops = userInfoBo.getIsMultiShops();
        String brandCode = request.getBrandCode();
        String elefaceTemplateName = request.getElefaceTemplateName();
        List<String> paramWaybillCloudPrintApplyNewRequestList = request.getParamWaybillCloudPrintApplyNewRequestList();
        ElefaceSharingRelation sharingRelation = null;
        String topSession;
        // 面单所有者的信息
        String ownerSellerNick;
        String ownerSellerId;
        String ownerStoreId;
        String ownerAppName;
        Integer billVersion = null;
        // 面单被分享者的信息
        String targetSellerNick = null;
        String targetSellerId = null;
        String targetStoreId = null;
        String targetAppName = null;

        // 面单被代理者的信息
        String proxySellerNick = null;
        String proxySellerId = null;
        String proxyStoreId = null;
        String proxyAppName = null;

        boolean isShared = shareId != null;
        // 是否使用分享的面单
        boolean isUseShare = false;
        // 面单共享关系是否需要加锁
        boolean isShareNeedLock = true;
        List<WaybillGetResponseBo> responseList = Lists.newArrayList();
        if (isShared){
            sharingRelation = elefaceAccountService.getSharingRelationByShareId(shareId);
            if (ElefaceSharingRelationConstant.SHARE_NUM_UNLIMITED.equals(sharingRelation.getShareNum())) {
                // 无限分享，面单共享关系无需加锁
                isShareNeedLock = false;
                LOGGER.logInfo("该面单分享数量无限制，面单共享关系无需加锁");
            }
        } else {
            // 无面单共享id，无需加锁
            isShareNeedLock = false;
            LOGGER.logInfo("无分享面单，面单共享关系无需加锁");
        }

        // 锁住面单共享关系
        String lockValue = null;
        if (isShareNeedLock) {
            lockValue = sharingRelationLockRedisDao.lockSharingRelation(shareId);
        }
        try {
            if (isShared) {
                sharingRelation = elefaceAccountService.getSharingRelationByShareId(shareId);
                if (!elefaceAccountService.checkTargetUserSharingRelation(sharingRelation, userInfoBo)
                    || !ElefaceSharingRelationConstant.STATUS_VALID.equals(sharingRelation.getStatus())) {
                    throw new ElefaceSharingNotExistException();
                }
                if (!ElefaceSharingRelationConstant.SHARE_NUM_UNLIMITED.equals(sharingRelation.getShareNum())
                    && paramWaybillCloudPrintApplyNewRequestList.size() + sharingRelation.getUsedNum() > sharingRelation
                        .getShareNum()) {
                    // 超过数量限制
                    throw new ShareElefaceNumExceedException();
                }
                ownerSellerNick = sharingRelation.getOwnerSellerNick();
                ownerSellerId = sharingRelation.getOwnerSellerId();
                ownerStoreId = sharingRelation.getOwnerStoreId();
                ownerAppName = sharingRelation.getOwnerAppName();
                targetSellerNick = sharingRelation.getTargetSellerNick();
                targetSellerId = sharingRelation.getTargetSellerId();
                targetStoreId = sharingRelation.getTargetStoreId();
                targetAppName = sharingRelation.getTargetAppName();

                proxySellerNick = sharingRelation.getProxySellerNick();
                proxySellerId = sharingRelation.getProxySellerId();
                proxyAppName = sharingRelation.getProxyAppName();
                proxyStoreId = sharingRelation.getProxyStoreId();
                billVersion = sharingRelation.getBillVersion();
                if (!ElefaceShareType.SHARE_TYPE_AGENT.equals(sharingRelation.getShareType())) {
                    isUseShare = true;
                }
            } else {
	            if (isMultiShops) {
		            ownerSellerNick = userInfoBo.getSellerNick();
		            ownerSellerId = userInfoBo.getSellerId();
		            ownerStoreId = userInfoBo.getStoreId();
		            ownerAppName = userInfoBo.getAppName();
	            } else {
		            ownerSellerNick = userInfoBo.getSessionNick();
		            ownerSellerId = userInfoBo.getSessionSellerId();
		            ownerStoreId = userInfoBo.getSessionStoreId();
		            ownerAppName = userInfoBo.getSessionAppName();
	            }
            }
            topSession = userCenterService.getTopSession(ownerStoreId, ownerAppName, ownerSellerNick, ownerSellerId);
            if (null == topSession) {
                throw new UnauthorizedException();
            }

            responseList = doWaybillIiGetBatchSingle(paramWaybillCloudPrintApplyNewRequestList, topSession,
                ownerStoreId, ownerAppName);

            if (isShared) {
                elefaceAccountService.addRelationUsedNum(sharingRelation.getShareId(), (long)responseList.size(),
                    userInfoBo);
            }
        } finally {
            sharingRelationLockRedisDao.unlockSharingRelation(shareId, lockValue);
        }

        // 操作日志落库
        List<AyElefaceOperatelog> operatelogList = new ArrayList<>();
        LocalDateTime now = this.LocalDateTimeNow();
        for (PrintTradeInfoDTO tradeInfo : tradeInfoList) {
            WayBillOperateLogExtDTO wayBillOperateLogExtDTO = tradeInfo.getWayBillOperateLogExtDTO();
            for (WaybillGetResponseBo waybillGetResponseBo : responseList) {
                // 判断是否为快运字母件
                boolean isChild = !StringUtils.isEmpty(waybillGetResponseBo.getParentWaybillCode());
                AyElefaceOperatelog operatelog = new AyElefaceOperatelog();
                operatelog.setRealCpCode(waybillGetResponseBo.getRealCpCode());
                operatelog.setProvider(waybillGetResponseBo.getProvider());
                operatelog.setObjectId(waybillGetResponseBo.getObjectId());
                if (isChild) {
                    operatelog.setWaybillCode(waybillGetResponseBo.getParentWaybillCode());
                    operatelog.setChildWaybillCode(waybillGetResponseBo.getWaybillCode());
                } else {
                    operatelog.setWaybillCode(waybillGetResponseBo.getWaybillCode());
                    // 写一个字符串0用来做MySQL的唯一约束
                    operatelog.setChildWaybillCode("0");
                }
                operatelog.setOwnerNick(ownerSellerNick);
                operatelog.setOwnerSellerId(ownerSellerId);
                operatelog.setOwnerStoreId(ownerStoreId);
                operatelog.setOwnerAppName(ownerAppName);
                operatelog.setTargetSellerNick(targetSellerNick);
                operatelog.setTargetSellerId(targetSellerId);
                operatelog.setTargetStoreId(targetStoreId);
                operatelog.setTargetAppName(targetAppName);
                operatelog.setProxySellerNick(proxySellerNick);
                operatelog.setProxySellerId(proxySellerId);
                operatelog.setProxyStoreId(proxyStoreId);
                operatelog.setProxyAppName(proxyAppName);
                operatelog.setShareId(shareId);
                operatelog.setIsChild(isChild);
                operatelog.setIsUseShare(isUseShare);
                try {
                    if(waybillGetResponseBo.getPrintData() != null && !waybillGetResponseBo.getPrintData().contains(TEMPLATE_URL_STR)){
                        // 如果api返回值无打印模版url，则将前端传进来的url保存起来，取号和打印分开操作时需要使用
                        JSONObject jsonObject = JSONObject.parseObject(waybillGetResponseBo.getPrintData());
                        jsonObject.put(TEMPLATE_URL_STR, request.getTemplateURL());
                        operatelog.setPrintData(JSON.toJSONString(jsonObject));
                    } else {
                        operatelog.setPrintData(waybillGetResponseBo.getPrintData());
                    }
                } catch (JSONException e) {
                    //返回的打印报文printInfo不是json格式,直接存储printData
                    LOGGER.logInfo(userInfoBo.getSellerNick(), null, "printData非json格式,使用原始数据");
                    operatelog.setPrintData(waybillGetResponseBo.getPrintData());
                }
                operatelog.setSerial(serial);
                operatelog.setMergeTid(mergeTid);
                operatelog.setTid(tradeInfo.getTid());
                operatelog.setOids(tradeInfo.getOids());
                operatelog.setTradeType(tradeType);
                operatelog.setLogisticsCompany(logisticsCompany);
                operatelog.setCpCode(cpCode);
                operatelog.setGetOperatorStoreId(userInfoBo.getOperatorStoreId());
                operatelog.setGetOperateTerminal(userInfoBo.getOperateTerminal());
                operatelog.setStoreId(userInfoBo.getStoreId());
                operatelog.setSellerId(userInfoBo.getSellerId());
                operatelog.setAppName(userInfoBo.getAppName());
                operatelog.setSellerNick(userInfoBo.getSellerNick());
                operatelog.setGetTime(now);
                operatelog.setBrandCode(brandCode);
                operatelog.setBillVersion(billVersion);
                operatelog.setExternalInfo(elefaceTemplateName);
                if (wayBillOperateLogExtDTO != null) {
                    operatelog.setOtherExternalInfoDTO(wayBillOperateLogExtDTO);
                }

                if (StringUtils.isNotEmpty(request.getOperator())) {
                    operatelog.setGetOperator(request.getOperator());
                } else {
                    operatelog.setGetOperator(userInfoBo.getOperator());
                }

                operatelogList.add(operatelog);
            }
        }
        try {
            // 多店铺取号会导致ayOperateLogs插入没有返回mysql的主键id 根据用户分组操作
            Map<String, List<AyElefaceOperatelog>> sellerIdAndoperatelogListMap =
                operatelogList.stream().collect(Collectors.groupingBy(AyElefaceOperatelog::getSellerId));
            List<AyElefaceOperatelog> operateLogESSource = Lists.newArrayList();
            for (String sellerId : sellerIdAndoperatelogListMap.keySet()) {
                List<AyElefaceOperatelog> ayOperateLogs = sellerIdAndoperatelogListMap.get(sellerId);
                ayElefaceOperatelogDao.batchInsert(ayOperateLogs);
                operateLogESSource.addAll(ayOperateLogs);
            }

            List<AyPrintLogSearchEs> logSearchEs = AyPrintLogSearchEs.of(operateLogESSource);
            if (CollectionUtils.isNotEmpty(logSearchEs)) {
                elasticsearchAyPrintLogQueryDao.saveAll(logSearchEs, Boolean.TRUE);
            }

        } catch (DuplicateKeyException e) {
            LOGGER.logError(userInfoBo.getSellerNick(), "-", "重复获取面单号，插入失败", e);
        }

        // 同步取号记录到订单service
        if (!tradeType.equals(TradeTypeConstant.CUSTOM_TRADE) && CollectionUtils.isNotEmpty(operatelogList)) {
            Map<String, List<AyElefaceOperatelog>> syncMap = operatelogList.stream().collect(Collectors.groupingBy(AyElefaceOperatelog::getWaybillCode));
            for (String waybillCode : syncMap.keySet()){
                List<AyElefaceOperatelog> ayEffaceOperates = syncMap.get(waybillCode);
                ordersService.syncPrintLog(userInfoBo, mergeTid,
                        ayEffaceOperates.stream().map(AyElefaceOperatelog::getTid).collect(Collectors.toList()), null,
                        PrintTypeConstant.GET_ELEFACE, waybillCode, logisticsCompany, null);
            }
        }

        return responseList;
    }

    @Override
    public List<WaybillGetResponseBo> waybillGetBatchV2(ElefaceWaybillBatchGetRequest request, UserInfoBo userInfoBo) throws CommonException {
        String sellerNick = userInfoBo.getSellerNick();
        String sellerId = userInfoBo.getSellerId();
        String storeId = userInfoBo.getStoreId();
        String appName = userInfoBo.getAppName();

        String waybillGetRequestId = request.getWaybillGetRequestId();
        // 是否需要幂等逻辑
        boolean isNeedIdempotenceCheck = waybillGetRequestId != null;

        // 第一次调用,初始化redisKey
        WaybillGetRedisEntity waybillGetRedis = new WaybillGetRedisEntity();
        waybillGetRedis.setCpCode(request.getCpCode());

        if (isNeedIdempotenceCheck) {
            // 实现接口幂等（支持多次调用）
            WaybillGetRedisEntity lastWaybillGetRedisEntity = waybillRedisDao.getWayBillGetRedisEntity(waybillGetRequestId, sellerId, appName, storeId);
            if (lastWaybillGetRedisEntity != null) {
                switch (lastWaybillGetRedisEntity.getStatus()) {
                    case RUNNING:
                        // 上次调用未完成，抛异常，接口重试
                        throw new WaybillGetException("面单获取中");
                    case SUCCESS:
                        // 接口运行成功，直接从面单操作日志（DB）返回面单获取记录
                        List<WaybillGetResponseBo> wayBillFromDB = getWayBillFromDB(request.getElefaceGetPrepareInfos(), lastWaybillGetRedisEntity, userInfoBo);
                        if (CollectionUtils.isNotEmpty(wayBillFromDB)) {
                            return wayBillFromDB;
                        }
                        // 从库中获取为空则重新尝试取号
                        LOGGER.logInfo(sellerNick, "-", "从操作日志获取面单失败： lastWaybillGetRedisEntity" + JSON.toJSONString(lastWaybillGetRedisEntity));
                    case ERROR:
                    default:
                        // 上次调用异常，设置为运行中，开始重试。
                        waybillGetRedis.setStatus(RUNNING);
                        break;
                }
            } else {
                // 初始化状态为运行中
                waybillGetRedis.setStatus(RUNNING);
            }
            waybillRedisDao.setWayBillGetRedisEntity(waybillGetRequestId, waybillGetRedis, sellerId, appName, storeId);
        }

        try {
            waybillGetRedis.setStatus(SUCCESS);
            return waybillGetBatchFromApi(request, userInfoBo, waybillGetRedis);
        } catch (Exception e) {
            waybillGetRedis.setStatus(ERROR);
            throw e;
        } finally {
            if (isNeedIdempotenceCheck) {
                waybillRedisDao.setWayBillGetRedisEntity(waybillGetRequestId, waybillGetRedis, sellerId, appName, storeId);
            }
        }

    }

    /**
     * 根据缓存查询数据库面单获取详情
     *
     * @param elefaceGetPrepareInfos
     * @param waybillGetRedisEntity
     * @param userInfoBo
     * @return
     */
    private List<WaybillGetResponseBo> getWayBillFromDB(List<ElefaceGetPrepareInfoDTO> elefaceGetPrepareInfos, WaybillGetRedisEntity waybillGetRedisEntity, UserInfoBo userInfoBo) {
        List<WaybillGetResponseBo> waybillGetResponseBos = new ArrayList<>();
        List<String> objectIdList = elefaceGetPrepareInfos.stream()
                .map(ElefaceGetPrepareInfoDTO::getObjectId)
                .collect(Collectors.toList());

        List<ElefaceGetPrepareInfoDTO> distinctUser = elefaceGetPrepareInfos.stream()
                .collect(Collectors.collectingAndThen(
                        Collectors.toCollection(() -> new TreeSet<>(Comparator
                                .comparing(ElefaceGetPrepareInfoDTO::getSellerId)
                                .thenComparing(ElefaceGetPrepareInfoDTO::getStoreId)
                                .thenComparing(ElefaceGetPrepareInfoDTO::getAppName))),
                        ArrayList::new));

        if (CollectionUtils.isNotEmpty(waybillGetRedisEntity.getSuccessWaybillCodeList())) {
            List<AyElefaceOperatelog> operatelogs = new ArrayList<>();
            // 按用户维度分批查询
            for (ElefaceGetPrepareInfoDTO group : distinctUser) {
                List<AyElefaceOperatelog> elefaceOperatelogs = ayElefaceOperatelogDao.queryByWaybillCodeList(
                        waybillGetRedisEntity.getCpCode(),
                        waybillGetRedisEntity.getSuccessWaybillCodeList(),
                        group.getStoreId(),
                        group.getSellerId(),
                        group.getAppName());
                if (CollectionUtils.isNotEmpty(elefaceOperatelogs)) {
                    operatelogs.addAll(elefaceOperatelogs);
                }
            }

            if (CollectionUtils.isEmpty(operatelogs)) {
                return null;
            }
            // 按照面单号和请求id分组
            Map<String, List<AyElefaceOperatelog>> groupMap = operatelogs.stream().filter(log -> objectIdList.contains(log.getObjectId()))
                    .collect(Collectors.groupingBy(log -> log.getWaybillCode() + "-" + log.getObjectId()));
            for (List<AyElefaceOperatelog> operateLogs : groupMap.values()) {
                if (CollectionUtils.isEmpty(operateLogs)) {
                    continue;
                }
                AyElefaceOperatelog ayElefaceOperatelog = operateLogs.get(0);
                WaybillGetResponseBo waybillGetResponse = new WaybillGetResponseBo();
                waybillGetResponse.setProvider(ayElefaceOperatelog.getProvider());
                waybillGetResponse.setWaybillCode(ayElefaceOperatelog.getWaybillCode());
                if (ayElefaceOperatelog.getIsChild()) {
                    waybillGetResponse.setParentWaybillCode(ayElefaceOperatelog.getWaybillCode());
                    waybillGetResponse.setWaybillCode(ayElefaceOperatelog.getChildWaybillCode());
                } else {
                    waybillGetResponse.setWaybillCode(ayElefaceOperatelog.getWaybillCode());
                }
                waybillGetResponse.setPrintData(ayElefaceOperatelog.getPrintData());
                waybillGetResponse.setObjectId(ayElefaceOperatelog.getObjectId());
                waybillGetResponse.setSuccess(true);
                waybillGetResponseBos.add(waybillGetResponse);
            }
        }

        if (CollectionUtils.isNotEmpty(waybillGetRedisEntity.getApiErrorList())) {
            for (ApiErrorBo apiErrorBo : waybillGetRedisEntity.getApiErrorList()) {
                WaybillGetResponseBo waybillGetResponse = new WaybillGetResponseBo();
                waybillGetResponse.setObjectId(apiErrorBo.getObjectId());
                waybillGetResponse.setApiError(apiErrorBo);
                waybillGetResponseBos.add(waybillGetResponse);
            }
        }

        // 排序,按照入参请求顺序返回
        waybillGetResponseBos.sort(Comparator.comparingInt(
                waybill -> objectIdList.indexOf(waybill.getObjectId())
        ));

        return waybillGetResponseBos;
    }

    private List<WaybillGetResponseBo> waybillGetBatchFromApi(ElefaceWaybillBatchGetRequest request, UserInfoBo userInfoBo, WaybillGetRedisEntity waybillGetRedis) throws CommonException {
        String cpCode = request.getCpCode();
        String logisticsCompany = request.getLogisticsCompany();
        String shareId = request.getShareId();
        String brandCode = request.getBrandCode();
        ElefaceSharingRelation sharingRelation = null;
        String topSession;
        // 面单所有者的信息
        String ownerSellerNick;
        String ownerSellerId;
        String ownerStoreId;
        String ownerAppName;

        // 面单被分享者的信息
        String targetSellerNick = null;
        String targetSellerId = null;
        String targetStoreId = null;
        String targetAppName = null;

        // 面单被代理者的信息
        String proxySellerNick = null;
        String proxySellerId = null;
        String proxyStoreId = null;
        String proxyAppName = null;

        // 面单分享备注
        String shareMemo = null;
        String branchName = null;
        String salesman = null;

        List<ElefaceGetPrepareInfoDTO> elefaceGetPrepareInfos = request.getElefaceGetPrepareInfos();
        // 操作日志
        List<AyElefaceOperatelog> operatelogList = new ArrayList<>();
        // 订单打印同步列表
        List<SavePrintLogDTO> syncPrintLogList = new ArrayList<>();
        LocalDateTime now = this.LocalDateTimeNow();

        boolean isShared = shareId != null;
        // 是否使用分享的面单
        boolean isUseShare = false;

        List<WaybillGetResponseBo> responseBos = Lists.newArrayList();

        // 锁住面单共享关系, 提前计算数量并施放面单锁，防止并发调用等待时间过长
        String lockValue = null;
        int useNum = 0;
        int realUseNum = 0;
        try {
            if (isShared) {
                sharingRelation = elefaceAccountService.getSharingRelationByShareId(shareId);
                if (sharingRelation == null) {
                    throw new ElefaceSharingNotExistException();
                }
                if (ElefaceSharingRelationConstant.SHARE_NUM_UNLIMITED.equals(sharingRelation.getShareNum())) {
                    // 无限分享，面单共享关系无需加锁
                    LOGGER.logInfo("该面单分享数量无限制，面单共享关系无需加锁");
                } else {
                    lockValue = sharingRelationLockRedisDao.lockSharingRelation(shareId);
                    sharingRelation = elefaceAccountService.getSharingRelationByShareId(shareId);
                }

                ownerSellerNick = sharingRelation.getOwnerSellerNick();
                ownerSellerId = sharingRelation.getOwnerSellerId();
                ownerStoreId = sharingRelation.getOwnerStoreId();
                ownerAppName = sharingRelation.getOwnerAppName();

                targetSellerNick = sharingRelation.getTargetSellerNick();
                targetSellerId = sharingRelation.getTargetSellerId();
                targetStoreId = sharingRelation.getTargetStoreId();
                targetAppName = sharingRelation.getTargetAppName();

                proxySellerNick = sharingRelation.getProxySellerNick();
                proxySellerId = sharingRelation.getProxySellerId();
                proxyAppName = sharingRelation.getProxyAppName();
                proxyStoreId = sharingRelation.getProxyStoreId();

                shareMemo = sharingRelation.getShareMemo();
                branchName = sharingRelation.getBranchName();
                salesman = sharingRelation.getSalesman();

                if (!ElefaceShareType.SHARE_TYPE_AGENT.equals(sharingRelation.getShareType())) {
                    isUseShare = true;
                }

                if (!elefaceAccountService.checkTargetUserSharingRelation(sharingRelation, userInfoBo)
                        || !ElefaceSharingRelationConstant.STATUS_VALID.equals(sharingRelation.getStatus())) {
                    throw new ElefaceSharingNotExistException();
                }
                if (!ElefaceSharingRelationConstant.SHARE_NUM_UNLIMITED.equals(sharingRelation.getShareNum())
                        && elefaceGetPrepareInfos.size() + sharingRelation.getUsedNum() > sharingRelation
                        .getShareNum()) {
                    // 超过数量限制
                    throw new ShareElefaceNumExceedException();
                }
                // 记录使用的面单数量
                useNum = elefaceGetPrepareInfos.size();
                if (useNum != 0) {
                    elefaceAccountService.addRelationUsedNum(sharingRelation.getShareId(), (long) useNum,
                            userInfoBo);
                }
            } else {
                LOGGER.logInfo("无分享面单，面单共享关系无需加锁");
                if (userInfoBo.getIsMultiShops()) {
                    ownerSellerNick = userInfoBo.getSellerNick();
                    ownerSellerId = userInfoBo.getSellerId();
                    ownerStoreId = userInfoBo.getStoreId();
                    ownerAppName = userInfoBo.getAppName();
                } else {
                    ownerSellerNick = userInfoBo.getSessionNick();
                    ownerSellerId = userInfoBo.getSessionSellerId();
                    ownerStoreId = userInfoBo.getSessionStoreId();
                    ownerAppName = userInfoBo.getSessionAppName();
                }
            }
        } finally {
            sharingRelationLockRedisDao.unlockSharingRelation(shareId, lockValue);
            lockValue = null;
        }

        try {
            topSession = userCenterService.getTopSession(ownerStoreId, ownerAppName, ownerSellerNick, ownerSellerId);
            if (null == topSession) {
                throw new UnauthorizedException();
            }
            Set<String> successWaybillSet = new HashSet<>();
            for (ElefaceGetPrepareInfoDTO elefaceGetPrepareInfoDTO : elefaceGetPrepareInfos) {
                List<PrintTradeInfoDTO> tradeInfoList = elefaceGetPrepareInfoDTO.getTradeInfoList();
                String mergeTid = elefaceGetPrepareInfoDTO.getMergeTid();
                Integer tradeType = elefaceGetPrepareInfoDTO.getTradeType();
                String templateURL = elefaceGetPrepareInfoDTO.getTemplateURL();
                String logisticsTemplateId = elefaceGetPrepareInfoDTO.getLogisticsTemplateId();
                String serial = elefaceGetPrepareInfoDTO.getSerial();
                String objectId = elefaceGetPrepareInfoDTO.getObjectId();
                Integer billVersion = elefaceGetPrepareInfoDTO.getBillVersion();
                String paramWaybillCloudPrintApplyNewRequest = elefaceGetPrepareInfoDTO.getParamWaybillCloudPrintApplyNewRequest();
                ApiErrorBo apiError = new ApiErrorBo(objectId);
                try {
                    List<WaybillGetResponseBo> waybillGetResponseBos = doWaybillIiGetSingle(paramWaybillCloudPrintApplyNewRequest, topSession,
                            ownerStoreId, ownerAppName, apiError);
                    responseBos.addAll(waybillGetResponseBos);
                    realUseNum = realUseNum + waybillGetResponseBos.size();

                    Map<String, Set<String>> syncMap = new HashMap<>();

                    // 记录需要更新的日志
                    Map<String, TargetUserInfoDTO> wayBillCodeAndTargetUserMap = new HashMap<>();
                    TargetUserInfoDTO targetUserInfo = getTargetUserInfo(elefaceGetPrepareInfoDTO, userInfoBo);
                    for (PrintTradeInfoDTO tradeInfo : tradeInfoList) {
                        WayBillOperateLogExtDTO wayBillOperateLogExtDTO = tradeInfo.getWayBillOperateLogExtDTO();
                        for (WaybillGetResponseBo waybillGetResponse : waybillGetResponseBos) {
                            // 判断是否为快运字母件
                            boolean isChild = !StringUtils.isEmpty(waybillGetResponse.getParentWaybillCode());
                            AyElefaceOperatelog operatelog = new AyElefaceOperatelog();
                            operatelog.setRealCpCode(waybillGetResponse.getRealCpCode());
                            operatelog.setProvider(waybillGetResponse.getProvider());
                            operatelog.setObjectId(waybillGetResponse.getObjectId());
                            if (isChild) {
                                operatelog.setWaybillCode(waybillGetResponse.getParentWaybillCode());
                                operatelog.setChildWaybillCode(waybillGetResponse.getWaybillCode());
                            } else {
                                operatelog.setWaybillCode(waybillGetResponse.getWaybillCode());
                                // 写一个字符串0用来做MySQL的唯一约束
                                operatelog.setChildWaybillCode("0");
                            }
                            successWaybillSet.add(operatelog.getWaybillCode());
                            operatelog.setOwnerNick(ownerSellerNick);
                            operatelog.setOwnerSellerId(ownerSellerId);
                            operatelog.setOwnerStoreId(ownerStoreId);
                            operatelog.setOwnerAppName(ownerAppName);
                            operatelog.setTargetSellerNick(targetSellerNick);
                            operatelog.setTargetSellerId(targetSellerId);
                            operatelog.setTargetStoreId(targetStoreId);
                            operatelog.setTargetAppName(targetAppName);
                            operatelog.setProxySellerNick(proxySellerNick);
                            operatelog.setProxySellerId(proxySellerId);
                            operatelog.setProxyStoreId(proxyStoreId);
                            operatelog.setProxyAppName(proxyAppName);
                            operatelog.setShareId(shareId);
                            operatelog.setIsChild(isChild);
                            operatelog.setIsUseShare(isUseShare);
                            operatelog.setLogisticsTemplateId(logisticsTemplateId);
                            try {
                                if (waybillGetResponse.getPrintData() != null && !waybillGetResponse.getPrintData().contains(TEMPLATE_URL_STR)) {
                                    // 如果api返回值无打印模版url，则将前端传进来的url保存起来，取号和打印分开操作时需要使用
                                    JSONObject jsonObject = JSONObject.parseObject(waybillGetResponse.getPrintData());
                                    jsonObject.put(TEMPLATE_URL_STR, templateURL);
                                    operatelog.setPrintData(JSON.toJSONString(jsonObject));
                                } else {
                                    operatelog.setPrintData(waybillGetResponse.getPrintData());
                                }
                            } catch (JSONException e) {
                                //返回的打印报文printInfo不是json格式,直接存储printData
                                LOGGER.logInfo(userInfoBo.getSellerNick(), null, "printData非json格式,使用原始数据");
                                operatelog.setPrintData(waybillGetResponse.getPrintData());
                            }
                            operatelog.setSerial(serial);
                            operatelog.setMergeTid(mergeTid);
                            operatelog.setTid(tradeInfo.getTid());
                            operatelog.setOids(tradeInfo.getOids());
                            operatelog.setTradeType(tradeType);
                            operatelog.setLogisticsCompany(logisticsCompany);
                            operatelog.setCpCode(cpCode);
                            operatelog.setGetOperatorStoreId(userInfoBo.getOperatorStoreId());
                            operatelog.setGetOperateTerminal(userInfoBo.getOperateTerminal());
                            operatelog.setStoreId(targetUserInfo.getStoreId());
                            operatelog.setSellerId(targetUserInfo.getSellerId());
                            operatelog.setAppName(targetUserInfo.getAppName());
                            operatelog.setSellerNick(targetUserInfo.getSellerNick());
                            operatelog.setGetTime(now);
                            operatelog.setBrandCode(brandCode);
                            operatelog.setShareMemo(shareMemo);
                            operatelog.setSalesman(salesman);
                            operatelog.setBranchName(branchName);
                            operatelog.setBillVersion(billVersion);
                            if (!Objects.isNull(wayBillOperateLogExtDTO)) {
                                operatelog.setOtherExternalInfo(JSON.toJSONString(wayBillOperateLogExtDTO));
                            }

                            if (StringUtils.isNotEmpty(request.getOperator())) {
                                operatelog.setGetOperator(request.getOperator());
                            } else {
                                operatelog.setGetOperator(userInfoBo.getOperator());
                            }

                            operatelogList.add(operatelog);
                            Set<String> tids = syncMap.get(operatelog.getWaybillCode());
                            if (tids == null) {
                                tids = new HashSet<>();
                            }
                            tids.add(tradeInfo.getTid());
                            syncMap.put(operatelog.getWaybillCode(), tids);
                            //一个物流单号下不可能跨店
                            wayBillCodeAndTargetUserMap.put(operatelog.getWaybillCode(), targetUserInfo);
                        }
                    }
                    for (Map.Entry<String, Set<String>> tidSet : syncMap.entrySet()) {
                        String wayBillCode = tidSet.getKey();
                        SavePrintLogDTO syncPrintLog = new SavePrintLogDTO();
                        TargetUserInfoDTO targetUserInfoDTO = wayBillCodeAndTargetUserMap.get(wayBillCode);
                        if (targetUserInfoDTO != null) {
                            syncPrintLog.setAppName(targetUserInfoDTO.getAppName());
                            syncPrintLog.setStoreId(targetUserInfoDTO.getStoreId());
                            syncPrintLog.setSellerNick(targetUserInfoDTO.getSellerNick());
                            syncPrintLog.setSellerId(targetUserInfoDTO.getSellerId());
                        } else {
                            syncPrintLog.setAppName(userInfoBo.getAppName());
                            syncPrintLog.setStoreId(userInfoBo.getStoreId());
                            syncPrintLog.setSellerNick(userInfoBo.getSellerNick());
                            syncPrintLog.setSellerId(userInfoBo.getSellerId());
                        }
                        syncPrintLog.setPrintMergeTid(mergeTid);
                        syncPrintLog.setPrintCompany(logisticsCompany);
                        syncPrintLog.setPrintInvoice(wayBillCode);
                        syncPrintLog.setPrintType(PrintTypeConstant.GET_ELEFACE);
                        syncPrintLog.setPrintTid(Lists.newArrayList(tidSet.getValue()));
                        syncPrintLogList.add(syncPrintLog);
                    }
                } catch (Exception e) {
                    // 取号失败返回错误信息
                    WaybillGetResponseBo errorWaybillGetResponse = new WaybillGetResponseBo();
                    errorWaybillGetResponse.setObjectId(objectId);
                    errorWaybillGetResponse.setErrMsg(e.getMessage());
                    errorWaybillGetResponse.setApiError(apiError);
                    errorWaybillGetResponse.setSuccess(Boolean.FALSE);
                    responseBos.add(errorWaybillGetResponse);
                    // 添加失败的
                    waybillGetRedis.addError(apiError);
                    LOGGER.logError("取号失败：" + e.getMessage(), e);
                }
            }

            // 添加成功的面单加入缓存
            if (CollectionUtils.isNotEmpty(successWaybillSet)) {
                waybillGetRedis.addSuccessWaybillCode(successWaybillSet);
            }

        } finally {
            if (isShared) {
                try {
                    // 提前扣除数量与实际使用面单数量不负，需要补回多扣的数量
                    if (useNum != realUseNum) {
                        lockValue = sharingRelationLockRedisDao.lockSharingRelation(shareId);
                        int subUseNum = realUseNum - useNum;
                        elefaceAccountService.addRelationUsedNum(sharingRelation.getShareId(), (long) subUseNum, userInfoBo);
                    }
                } finally {
                    sharingRelationLockRedisDao.unlockSharingRelation(shareId, lockValue);
                }
            }
        }

        try {
            // 日志入库
            if (CollectionUtils.isNotEmpty(operatelogList)) {
                // 多店铺取号会导致ayOperateLogs插入没有返回mysql的主键id 根据用户分组操作
                Map<String, List<AyElefaceOperatelog>> sellerIdAndoperatelogListMap =
                    operatelogList.stream().collect(Collectors.groupingBy(AyElefaceOperatelog::getSellerId));
                List<AyElefaceOperatelog> operateLogESSource = Lists.newArrayList();
                for (String sellerId : sellerIdAndoperatelogListMap.keySet()) {
                    List<AyElefaceOperatelog> ayOperateLogs = sellerIdAndoperatelogListMap.get(sellerId);
                    ayElefaceOperatelogDao.batchInsert(ayOperateLogs);
                    operateLogESSource.addAll(ayOperateLogs);
                }

                List<AyPrintLogSearchEs> logSearchEs = AyPrintLogSearchEs.of(operateLogESSource);
                if (CollectionUtils.isNotEmpty(logSearchEs)) {
                    elasticsearchAyPrintLogQueryDao.saveAll(logSearchEs, Boolean.TRUE);
                }
            }
        } catch (DuplicateKeyException e) {
            LOGGER.logError(userInfoBo.getSellerNick(), "-", "重复获取面单号，插入失败", e);
        }

        try {
            // 打印订单同步
            if (CollectionUtils.isNotEmpty(syncPrintLogList)) {
                ordersService.asyncSavePrintLogBatch(syncPrintLogList);
            }
        } catch (DuplicateKeyException e) {
            LOGGER.logError(userInfoBo.getSellerNick(), "-", "打印订单同步，同步失败", e);
        }

        return responseBos;
    }

    /**
     * 获取目标用户数据，当入参中为空时，使用的是当前token中的用户信息
     * @param elefaceGetPrepareInfoDTO
     * @param userInfoBo
     * @return
     */
    private TargetUserInfoDTO getTargetUserInfo(ElefaceGetPrepareInfoDTO elefaceGetPrepareInfoDTO, UserInfoBo userInfoBo){
        String targetStoreId = elefaceGetPrepareInfoDTO.getStoreId();
        String targetSellerId = elefaceGetPrepareInfoDTO.getSellerId();
        String targetAppName = elefaceGetPrepareInfoDTO.getAppName();
        String targetSellerNick = elefaceGetPrepareInfoDTO.getSellerNick();
        if (StringUtils.isEmpty(targetStoreId)) {
            targetStoreId = userInfoBo.getStoreId();
        }
        if (StringUtils.isEmpty(targetSellerId)) {
            targetSellerId = userInfoBo.getSellerId();
        }
        if (StringUtils.isEmpty(targetAppName)) {
            targetAppName = userInfoBo.getAppName();
        }
        if (StringUtils.isEmpty(targetSellerNick)) {
            targetSellerNick = userInfoBo.getSellerNick();
        }
        return new TargetUserInfoDTO(targetSellerId, targetSellerNick, targetStoreId, targetAppName);
    }

    /**
     * waybill.get 取号
     *
     * @param paramWaybillCloudPrintApplyNewRequestList
     * @param topSession
     * @param platformId
     * @param appName
     * @return
     */
    protected List<WaybillGetResponseBo> doWaybillIiGetBatchSingle(
        List<String> paramWaybillCloudPrintApplyNewRequestList, String topSession, String platformId, String appName)
        throws PlatformSdkException {
        List<WaybillGetResponseBo> waybillGetResponseBoList = new ArrayList<>();
        AyWaybillGetRequest ayWaybillGetRequest = new AyWaybillGetRequest();
        for (String paramWaybillCloudPrintApplyNewRequest : paramWaybillCloudPrintApplyNewRequestList) {
            ayWaybillGetRequest.setParamWaybillCloudPrintApplyNewRequest(paramWaybillCloudPrintApplyNewRequest);
            AyWaybillGetResponse ayWaybillGetResponse =
                platformWaybillApiService.waybillGet(ayWaybillGetRequest, topSession, platformId, appName);
            if (!ayWaybillGetResponse.isSuccess()) {
                throw new PlatformSdkException(JSON.toJSONString(ayWaybillGetResponse));
            }
            waybillGetResponseBoList.addAll(ayWaybillGetResponse.getWaybillCloudPrintInfoList().stream().map(item -> {
                WaybillGetResponseBo waybillGetResponseBo = new WaybillGetResponseBo();
                BeanUtils.copyProperties(item, waybillGetResponseBo);
                waybillGetResponseBo.setSuccess(Boolean.TRUE);
                waybillGetResponseBo.setProvider(ElefaceProviderConstant.getProvider(platformId));
                return waybillGetResponseBo;
            }).collect(Collectors.toList()));
        }
        return waybillGetResponseBoList;
    }

    /**
     * waybill.get 取号
     *
     * @param paramWaybillCloudPrintApplyNewRequest
     * @param topSession
     * @param platformId
     * @param appName
     * @return
     */
    protected List<WaybillGetResponseBo> doWaybillIiGetSingle(
            String paramWaybillCloudPrintApplyNewRequest, String topSession, String platformId, String appName, ApiErrorBo apiError)
            throws PlatformSdkException {
        List<WaybillGetResponseBo> waybillGetResponseBoList = new ArrayList<>();
        AyWaybillGetRequest ayWaybillGetRequest = new AyWaybillGetRequest();
        ayWaybillGetRequest.setParamWaybillCloudPrintApplyNewRequest(paramWaybillCloudPrintApplyNewRequest);
        AyWaybillGetResponse ayWaybillGetResponse =
                platformWaybillApiService.waybillGet(ayWaybillGetRequest, topSession, platformId, appName);
        if (!ayWaybillGetResponse.isSuccess()) {
            LOGGER.logError("面单获取失败：" + JSON.toJSONString(ayWaybillGetResponse));
            apiError.setCode(ayWaybillGetResponse.getErrorCode());
            apiError.setMsg(ayWaybillGetResponse.getMsg());
            apiError.setSubCode(ayWaybillGetResponse.getSubCode());
            apiError.setSubMsg(ayWaybillGetResponse.getSubMsg());
            apiError.setRequestId(ayWaybillGetResponse.getRequestId());
            throw new PlatformSdkException(ayWaybillGetResponse.getMsg() + "," + ayWaybillGetResponse.getSubMsg());
        }
        waybillGetResponseBoList.addAll(ayWaybillGetResponse.getWaybillCloudPrintInfoList().stream().map(item -> {
            WaybillGetResponseBo waybillGetResponseBo = new WaybillGetResponseBo();
            BeanUtils.copyProperties(item, waybillGetResponseBo);

            if (StringUtils.isNotEmpty(item.getWaybillOrderId())){
                if (StringUtils.isNotEmpty(item.getObjectId()) && !item.getWaybillOrderId().equals(item.getObjectId())){
                    throw new IllegalArgumentException("面单获取失败:参数异常,objectId:" + item.getObjectId() + "与waybillOrderId:" + item.getWaybillOrderId() + "不一致");
                }
                //利用ObjectId暂存微信视频号小店取消取号所需字段waybillOrderId
                waybillGetResponseBo.setObjectId(item.getWaybillOrderId());
            }
            waybillGetResponseBo.setSuccess(Boolean.TRUE);
            waybillGetResponseBo.setProvider(ElefaceProviderConstant.getProvider(platformId));
            return waybillGetResponseBo;
        }).collect(Collectors.toList()));
        return waybillGetResponseBoList;
    }

    /**
     * 取消获取的面单号
     *
     * @param cpCode
     * @param waybillCode
     * @param userInfoBo
     * @return
     * @throws CommonException
     */
    @Override
    public boolean waybillCancel(String cpCode, String waybillCode, Integer billVersion, Boolean isCancelFromApi,
        UserInfoBo userInfoBo) throws CommonException {
        List<AyElefaceOperatelog> ayElefaceOperatelogs = ayElefaceOperatelogDao.queryByWaybillCode(cpCode, waybillCode,
            userInfoBo.getStoreId(), userInfoBo.getSellerId(), userInfoBo.getAppName());
        if (CollectionUtils.isEmpty(ayElefaceOperatelogs)) {
            throw new WaybillNotExistException();
        }
        AyElefaceOperatelog operatelog = ayElefaceOperatelogs.get(0);
        String ownerSellerNick = operatelog.getOwnerNick();
        String ownerSellerId = operatelog.getOwnerSellerId();
        String ownerStoreId = operatelog.getOwnerStoreId();
        String ownerAppName = operatelog.getOwnerAppName();

        //微信视频号将Object暂存ewaybillOrderId
        String waybillOrderId = operatelog.getObjectId();

        if (StringUtils.isAnyEmpty(ownerSellerNick, ownerSellerId, ownerStoreId, ownerAppName)) {
            // 这里做个兼容 旧的表数据里面面单的owner信息, 说明不是共享面单
            ownerSellerNick = userInfoBo.getSellerNick();
            ownerSellerId = userInfoBo.getSellerId();
            ownerStoreId = userInfoBo.getStoreId();
            ownerAppName = userInfoBo.getAppName();
        }

        boolean cancelResult = false;
        String shareId = operatelog.getShareId();

        if (!BooleanUtils.isTrue(isCancelFromApi)) {
            // 这里sellerId 传null 也是能获取到用户topSession的
            String topSession = userCenterService.getTopSession(ownerStoreId, ownerAppName, ownerSellerNick, ownerSellerId);
            if (null == topSession) {
                throw new UnauthorizedException();
            }
            try {
                cancelResult = doWaybillCancel(cpCode, waybillCode, billVersion, topSession, ownerStoreId, ownerAppName, waybillOrderId, ownerSellerId);
            } catch (PlatformSdkException e) {
                throw new PlatformSdkException(e.getMessage());
            }
        } else {
            cancelResult = true;
        }

        LocalDateTime cancelTime = this.LocalDateTimeNow();
        if (cancelResult) {
            if (!operatelog.getIsCancel()) {
                //第一次调用取消，处理分享记录
                handleCancelSharingRelation(shareId, userInfoBo);
            }

            // 操作日志落库
            saveWaybillCancelOperateLog(ayElefaceOperatelogs, cpCode, waybillCode, userInfoBo, cancelTime);
            // 同步打印记录的面单已取消
            printlogService.saveWaybillIsCancel(cpCode, waybillCode, userInfoBo);
            // 同步订单打印信息
            Map<String, List<AyElefaceOperatelog>> mergeTidToTradeMainListMap = ayElefaceOperatelogs.stream()
                    .collect(Collectors.groupingBy(item -> StringUtils.trimToEmpty(item.getMergeTid())));
            for (String mergeTid : mergeTidToTradeMainListMap.keySet()) {
                List<AyElefaceOperatelog> operatelogs = mergeTidToTradeMainListMap.get(mergeTid);
                Map<String, List<String>> tidAndOids = new HashMap<>(operatelogs.size());
                operatelogs.stream().filter(log -> log.getOids() != null).forEach(log -> {
                    tidAndOids.put(log.getTid(), Lists.newArrayList(log.getOids().split(",")));
                });
                // 同步打印记录到订单service
                ordersService.syncPrintLog(userInfoBo, mergeTid, Lists.newArrayList(tidAndOids.keySet()), tidAndOids,
                        PrintTypeConstant.CANCEL_ELEFACE, waybillCode, operatelog.getLogisticsCompany(), null);
            }
        }
        return cancelResult;
    }

    private void handleCancelSharingRelation(String shareId, UserInfoBo userInfoBo) {
        if (shareId == null) {
            return;
        }

        String lockValue = null;
        ElefaceSharingRelation sharingRelation = null;

        try {
            lockValue = sharingRelationLockRedisDao.lockSharingRelation(shareId);
            sharingRelation = elefaceAccountService.getSharingRelationByShareId(shareId);
            if (sharingRelation == null) {
               return;
            }
            long shareNum = sharingRelation.getShareNum() != null ? sharingRelation.getShareNum() : 0;
            long cancelNum = sharingRelation.getCancelNum() != null ? sharingRelation.getCancelNum() : 0;

            String proxySellerId = sharingRelation.getProxySellerId();
            String proxySellerNick = sharingRelation.getProxySellerNick();
            String proxyStoreId = sharingRelation.getProxyStoreId();
            String proxyAppName = sharingRelation.getProxyAppName();
            boolean setting = false;
            if (StringUtils.isNoneEmpty(proxySellerId, proxySellerNick, proxyStoreId, proxyAppName)) {
                Map<String, String> userSettings = userCenterService.getUserSettings(Lists.newArrayList(CANCEL_RECHARGE), proxySellerId, proxySellerNick, proxyAppName, proxyStoreId);
                String isCancelRechargeToTarget = userSettings.get(CANCEL_RECHARGE);
                setting = BooleanUtils.toBoolean(isCancelRechargeToTarget);
            }

            // 有效并且回充开关开启
            if (ElefaceSharingRelationConstant.STATUS_VALID.equals(sharingRelation.getStatus()) && setting) {
                if (shareNum >= 0) {
                    // 有限制分享才+1 -1无限制分享不需要相加
                    sharingRelation.setShareNum(shareNum + 1);
                }
                elefaceSharingRelationOperateService.saveElefaceSharingRelationOperateLog(sharingRelation, shareNum, ElefaceSharingRelationOperateConstant.CANCEL_ADD, userInfoBo.getMallOperate(), userInfoBo.getOperateTerminal());
            }
            cancelNum += 1;
            elefaceSharingRelationDao.updateShareNumAndCancelNum(shareId, sharingRelation.getShareNum(), cancelNum);
        } catch (Exception e) {
            LOGGER.logError("更新回收数量失败：shareId:" + shareId, e);
        } finally {
            sharingRelationLockRedisDao.unlockSharingRelation(shareId, lockValue);
        }
    }

    /**
     * waybill.cancel 取消获取的面单号
     *
     * @param cpCode
     * @param waybillCode
     * @param billVersion
     * @param topSession
     * @param platformId
     * @param appName
     * @param ownerSellerId
     * @return
     * @throws PlatformSdkException
     */
    protected boolean doWaybillCancel(String cpCode, String waybillCode, Integer billVersion, String topSession, String platformId,
                                      String appName, @Nullable String waybillOrderId, String ownerSellerId) throws PlatformSdkException {
        AyWaybillCancelRequest request = new AyWaybillCancelRequest();
        request.setCpCode(cpCode);
        request.setWaybillCode(waybillCode);
        request.setWaybillOrderId(waybillOrderId);
        request.setBillVersion(billVersion);
        request.setSellerId(ownerSellerId);

        AyWaybillCancelResponse response =
            platformWaybillApiService.waybillCancel(request, topSession, platformId, appName);
        if (!response.isSuccess()) {
            throw new PlatformSdkException(JSON.toJSONString(response));
        }
        return BooleanUtils.isTrue(response.getCancelResult());
    }

    /**
     * 保存面单取消日志
     *
     * @param ayElefaceOperateLogs
     * @param cpCode
     * @param waybillCode
     * @param userInfoBo
     * @param cancelTime
     */
    protected void saveWaybillCancelOperateLog(List<AyElefaceOperatelog> ayElefaceOperateLogs, String cpCode,
        String waybillCode, UserInfoBo userInfoBo, LocalDateTime cancelTime) {

        AyElefaceOperatelog cancelOplog = new AyElefaceOperatelog();
        cancelOplog.setCpCode(cpCode);
        cancelOplog.setWaybillCode(waybillCode);
        cancelOplog.setCancelOperator(userInfoBo.getOperator());
        cancelOplog.setCancelOperatorStoreId(userInfoBo.getOperatorStoreId());
        cancelOplog.setCancelOperateTerminal(userInfoBo.getOperateTerminal());
        cancelOplog.setIsCancel(true);
        cancelOplog.setCancelTime(cancelTime);

        ayElefaceOperatelogDao.saveCancelInfo(cancelOplog, userInfoBo.getStoreId(), userInfoBo.getSellerId(),
            userInfoBo.getAppName());

        ayElefaceOperateLogs.forEach(e-> {
            e.setIsCancel(true);
            e.setCancelTime(cancelTime);
        });

        List<AyPrintLogSearchEs> logSearchEs = AyPrintLogSearchEs.of(ayElefaceOperateLogs);
        if (CollectionUtils.isNotEmpty(logSearchEs)) {
            Date modified = cancelTime == null ? new Date() : DateUtil.convertLocalDateTimetoDate(cancelTime);
            logSearchEs = logSearchEs.stream().peek(s -> s.setGmtModified(modified)).collect(Collectors.toList());
            elasticsearchAyPrintLogQueryDao.batchUpdateByIdsWithNotNull(logSearchEs,
                Lists.newArrayList(EsFields.isCancel));
        }
    }

    @Override
    public WaybillOperatelogResponseDTO operatelogListGet(WaybillOperatelogQueryDTO queryDTO, UserInfoBo userInfoBo) {
        WaybillOperatelogResponseDTO responseDTO = new WaybillOperatelogResponseDTO();
        Long totalResult = ayElefaceOperatelogDao.queryCount(queryDTO, userInfoBo.getStoreId(),
            userInfoBo.getSellerId(), userInfoBo.getAppName());
        responseDTO.setTotalResult(totalResult);
        List<WaybillOperatelogResponseDTO.WaybillOperatelogDTO> operatelogDTOList = new ArrayList<>();
        if (totalResult > 0) {
            List<AyElefaceOperatelog> ayElefaceOperatelogList = ayElefaceOperatelogDao.queryListOrderByOperateTime(
                queryDTO, userInfoBo.getStoreId(), userInfoBo.getSellerId(), userInfoBo.getAppName());
            for (AyElefaceOperatelog operatelog : ayElefaceOperatelogList) {
                WaybillOperatelogResponseDTO.WaybillOperatelogDTO waybillOperatelogDTO = generalWaybillOperate(operatelog);
                operatelogDTOList.add(waybillOperatelogDTO);
            }
        }
        responseDTO.setOperatelogDTOList(operatelogDTOList);
        return responseDTO;
    }

    private WaybillOperatelogResponseDTO.WaybillOperatelogDTO generalWaybillOperate(AyElefaceOperatelog operateLog) {
        WaybillOperatelogResponseDTO.WaybillOperatelogDTO waybillOperatelogDTO = CommonConvertMapper.INSTANCE.toWaybillOperateLogDTO(operateLog);
        // 合单特殊处理
        if (CollectionUtils.isEmpty(operateLog.getMergeSubTidList())) {
            waybillOperatelogDTO.setTidList(Lists.newArrayList(operateLog.getTid()));
        } else {
            waybillOperatelogDTO.setTidList(operateLog.getMergeSubTidList());
        }
        return waybillOperatelogDTO;
    }

    @Override
    public void saveWaybillPrintLog(String cpCode, String waybillCode, String externalInfo, UserInfoBo userInfoBo) {
        String sellerId = userInfoBo.getSellerId();
        String sellerNick = userInfoBo.getSellerNick();
        String storeId = userInfoBo.getStoreId();
        String appName = userInfoBo.getAppName();
        ayElefaceOperatelogDao.printCountIncrement(cpCode, waybillCode, externalInfo, storeId,
            sellerId, appName);
        // 发生个打印状态同步消息
        WaybillOperateLogStatusChangeRequestProto waybillOperateLogStatusChangeRequestProto =
            new WaybillOperateLogStatusChangeRequestProto();
        waybillOperateLogStatusChangeRequestProto.setIsPrint(true);
        waybillOperateLogStatusChangeRequestProto.setSellerId(sellerId);
        waybillOperateLogStatusChangeRequestProto.setSellerNick(sellerNick);
        waybillOperateLogStatusChangeRequestProto.setStoreId(storeId);
        waybillOperateLogStatusChangeRequestProto.setAppName(appName);
        waybillOperateLogStatusChangeRequestProto.setCpCode(cpCode);
        waybillOperateLogStatusChangeRequestProto.setWaybillCode(waybillCode);
        printMessageSendService.pushOperateLogStatusChangeMessage(waybillOperateLogStatusChangeRequestProto);
    }

    @Override
    public void saveWaybillOperateLogBatch(List<WaybillOperateLogSaveDTO> waybillOperateLogSaveDTOS) {
        // 操作日志落库
        List<AyElefaceOperatelog> operatelogList = new ArrayList<>();
        LocalDateTime now = this.LocalDateTimeNow();
        for (WaybillOperateLogSaveDTO waybillOperatelogSaveDTO : waybillOperateLogSaveDTOS) {
            // 判断是否为快运字母件
            boolean isChild = !StringUtils.isEmpty(waybillOperatelogSaveDTO.getParentWaybillCode());
            AyElefaceOperatelog operatelog = new AyElefaceOperatelog();
            operatelog.setProvider(waybillOperatelogSaveDTO.getProvider());
            operatelog.setObjectId(waybillOperatelogSaveDTO.getObjectId());
            if (isChild) {
                operatelog.setWaybillCode(waybillOperatelogSaveDTO.getParentWaybillCode());
                operatelog.setChildWaybillCode(waybillOperatelogSaveDTO.getWaybillCode());
            } else {
                operatelog.setWaybillCode(waybillOperatelogSaveDTO.getWaybillCode());
                // 写一个字符串0用来做MySQL的唯一约束
                operatelog.setChildWaybillCode("0");
            }
            operatelog.setOwnerNick(waybillOperatelogSaveDTO.getOwnerSellerNick());
            operatelog.setOwnerSellerId(waybillOperatelogSaveDTO.getOwnerSellerId());
            operatelog.setOwnerStoreId(waybillOperatelogSaveDTO.getOwnerStoreId());
            operatelog.setOwnerAppName(waybillOperatelogSaveDTO.getOwnerAppName());
            operatelog.setIsChild(isChild);
            operatelog.setPrintData(waybillOperatelogSaveDTO.getPrintData());
            operatelog.setSerial(waybillOperatelogSaveDTO.getSerial());
            operatelog.setMergeTid(waybillOperatelogSaveDTO.getMergeTid());
            operatelog.setTid(waybillOperatelogSaveDTO.getTid());
            operatelog.setOids(waybillOperatelogSaveDTO.getOids());
            operatelog.setTradeType(waybillOperatelogSaveDTO.getTradeType());
            operatelog.setLogisticsCompany(waybillOperatelogSaveDTO.getLogisticsCompany());
            operatelog.setCpCode(waybillOperatelogSaveDTO.getCpCode());
            operatelog.setGetOperator(waybillOperatelogSaveDTO.getOperator());
            operatelog.setGetOperatorStoreId(waybillOperatelogSaveDTO.getOperatorStoreId());
            operatelog.setGetOperateTerminal(waybillOperatelogSaveDTO.getOperateTerminal());
            operatelog.setStoreId(waybillOperatelogSaveDTO.getStoreId());
            operatelog.setSellerId(waybillOperatelogSaveDTO.getSellerId());
            operatelog.setAppName(waybillOperatelogSaveDTO.getAppName());
            operatelog.setSellerNick(waybillOperatelogSaveDTO.getSellerNick());
            operatelog.setGetTime(now);
            operatelog.setBrandCode(waybillOperatelogSaveDTO.getBrandCode());
            operatelog.setExternalInfo(waybillOperatelogSaveDTO.getExternalInfo());
            operatelog.setRealCpCode(waybillOperatelogSaveDTO.getRealCpCode());
            operatelog.setTargetSellerNick(waybillOperatelogSaveDTO.getTargetNick());
            operatelog.setTargetAppName(waybillOperatelogSaveDTO.getTargetAppName());
            operatelog.setTargetStoreId(waybillOperatelogSaveDTO.getTargetStoreId());
            operatelog.setTargetSellerId(waybillOperatelogSaveDTO.getTargetSellerId());
            operatelog.setShareId(waybillOperatelogSaveDTO.getShareId());
            operatelog.setBillVersion(waybillOperatelogSaveDTO.getBillVersion());
            operatelogList.add(operatelog);
        }
        try {
            // 多店铺取号会导致ayOperateLogs插入没有返回mysql的主键id 根据用户分组操作
            Map<String, List<AyElefaceOperatelog>> sellerIdAndoperatelogListMap =
                operatelogList.stream().collect(Collectors.groupingBy(AyElefaceOperatelog::getSellerId));
            List<AyElefaceOperatelog> operateLogESSource = Lists.newArrayList();
            for (String sellerId : sellerIdAndoperatelogListMap.keySet()) {
                List<AyElefaceOperatelog> ayOperateLogs = sellerIdAndoperatelogListMap.get(sellerId);
                ayElefaceOperatelogDao.batchInsert(ayOperateLogs);
                operateLogESSource.addAll(ayOperateLogs);
            }

            List<AyPrintLogSearchEs> logSearchEs = AyPrintLogSearchEs.of(operateLogESSource);
            if (CollectionUtils.isNotEmpty(logSearchEs)) {
                elasticsearchAyPrintLogQueryDao.saveAll(logSearchEs, Boolean.TRUE);
            }
        } catch (DuplicateKeyException e) {
            LOGGER.logError("重复获取面单号，插入失败", e);
        }
    }

    @Override
    public ElefaceSharingGetResponseDTO waybillShareGet(ElefaceSharingGetRequest request, UserInfoBo userInfoBo) {
        ElefaceSharingGetResponseDTO elefaceSharingGetResponseDTO = new ElefaceSharingGetResponseDTO();
        ElefaceSharingRelation sharingRelation = elefaceAccountService.getSharingRelationByShareId(request.getShardId());
        elefaceSharingGetResponseDTO.setSharingRelation(sharingRelation);
        return elefaceSharingGetResponseDTO;
    }

    @Override
    public Long queryCount(WaybillOperatelogQueryDTO queryDTO, String storeId, String sellerId, String appName) {
        return ayElefaceOperatelogDao.queryCount(queryDTO, storeId, sellerId, appName);
    }

    @Override
    public List<ElefaceIsCancelGetInnerResponse> elefaceIsCancelGet(ElefaceIsCancelGetInnerRequest request) {
        return ayElefaceOperatelogDao.elefaceIsCancelGet(request);
    }

    @Override
    public WaybillOperatelogResponseDTO multiWaybillOperateLogListGet(MultiWaybillOperateLogListGetDTO operateLogListGetDTO, List<UserInfoBo> userInfoBoList) {

        AyPrintLogSearchListAndAggDTO ayPrintLogSearchListAndAggDTO = ayElefaceOperateLogSearchService.ayPrintLogListGetQueryByLimit(operateLogListGetDTO, userInfoBoList);

        WaybillOperatelogResponseDTO waybillOperatelogResponseDTO = new WaybillOperatelogResponseDTO();
        if (Objects.isNull(ayPrintLogSearchListAndAggDTO) || CollectionUtils.isEmpty(ayPrintLogSearchListAndAggDTO.getItemSearchESList())) {
            waybillOperatelogResponseDTO.setTotalResult(0L);
            waybillOperatelogResponseDTO.setOperatelogDTOList(Collections.emptyList());
            return waybillOperatelogResponseDTO;
        }

        List<AyPrintLogSearchEs> itemSearchESList = ayPrintLogSearchListAndAggDTO.getItemSearchESList();

        Map<String, List<AyPrintLogSearchEs>> sellerIdAndSearchInfoMap =
                itemSearchESList.stream().collect(Collectors.groupingBy(logSearchEs-> Optional.ofNullable(logSearchEs.getElefaceOperateLog())
                    .map(AyPrintLogSearchEs.ElefaceOperateLog::getOrderSellerId)
                    .orElse(logSearchEs.getSellerId())));

        List<AyElefaceOperatelog> operateLogs = new ArrayList<>();
        for (String sellerId : sellerIdAndSearchInfoMap.keySet()) {
            List<Long> mysqlIds = sellerIdAndSearchInfoMap.get(sellerId).stream()
                    .flatMap(ayPrintLogSearchEs -> {
                        List<Long> mergeSubPrintLogId = ayPrintLogSearchEs.getMergeSubPrintLogId();
                        // 如果 mergeSubPrintLogId 不为空且有值，返回它；否则返回 ayPrintLogId
                        if (mergeSubPrintLogId != null && !mergeSubPrintLogId.isEmpty()) {
                            return mergeSubPrintLogId.stream();
                        } else {
                            return Stream.of(ayPrintLogSearchEs.getAyPrintLogId());
                        }
                    }).collect(Collectors.toList());
            List<AyElefaceOperatelog> ayPrintLogList = ayElefaceOperatelogDao.queryByIds(sellerId, mysqlIds);
            operateLogs.addAll(ayPrintLogList);
        }

        // 从sql中查询的数据如果是合单，则每一条数据需要添加key为MergeTid和key为tid两条记录进去 es中存的老数据有问题会存在情景：是合单 但是tid放的还是正常订单tid非合单tid, 会导致后面从map中取数据拿不到的情况
        Map<String, List<AyElefaceOperatelog>> waybill2Log = operateLogs.stream()
            .flatMap(s -> {
                List<Map.Entry<String, AyElefaceOperatelog>> entries = new ArrayList<>();
                if (StringUtils.isNotEmpty(s.getMergeTid())) {
                    entries.add(new AbstractMap.SimpleEntry<>(s.getWaybillCode() + s.getMergeTid(), s));
                }
                if (StringUtils.isNotEmpty(s.getTid())) {
                    entries.add(new AbstractMap.SimpleEntry<>(s.getWaybillCode() + s.getTid(), s));
                }
                return entries.stream();
            })
            .collect(Collectors.groupingBy(Map.Entry::getKey, Collectors.mapping(Map.Entry::getValue, Collectors.toList())));

        // 排序+处理结果
        List<WaybillOperatelogResponseDTO.WaybillOperatelogDTO> operatelogDTOList = new ArrayList<>();
        Set<String> shareId = operateLogs.stream().map(AyElefaceOperatelog::getShareId).collect(Collectors.toSet());
        Map<String, ElefaceSharingRelation> shareId2Relation = new HashMap<>();
        if (CollectionUtils.isNotEmpty(shareId)) {
            List<ElefaceSharingRelation> relationList = elefaceSharingRelationDao.queryByShareIds(Lists.newArrayList(shareId));
            shareId2Relation = relationList.stream().collect(Collectors.toMap(ElefaceSharingRelation::getShareId, Function.identity(), (v1, v2) -> v1));
        }

        Map<String, UserInfoResponse> userInfoResponseMap = new HashMap<>();
        for (AyPrintLogSearchEs ayPrintLogSearchEs : itemSearchESList) {
            String waybillNumber = ayPrintLogSearchEs.getWaybillNumber();
            List<AyElefaceOperatelog> ayElefaceOperatelogList = waybill2Log.get(waybillNumber
                + (StringUtils.isNotEmpty(ayPrintLogSearchEs.getTid()) ? ayPrintLogSearchEs.getTid() : ""));
            if (CollectionUtils.isEmpty(ayElefaceOperatelogList)) {
                LOGGER.logError("搜索异常，es与mysql数据对不上：" + waybillNumber + ", sellerId:" + ayPrintLogSearchEs.getSellerId());
                continue;
            }
            AyElefaceOperatelog ayElefaceOperatelog = ayElefaceOperatelogList.get(0);
            if (ayElefaceOperatelogList.size() > 1 && ayElefaceOperatelog.getMergeTid() != null) {
                // 合单
                Set<String> tidSet = ayElefaceOperatelogList.stream().map(AyElefaceOperatelog::getTid).collect(Collectors.toSet());
                ayElefaceOperatelog.setMergeSubTidList(Lists.newArrayList(tidSet));
            } else if (ayElefaceOperatelogList.size() > 1) {
                // 子母件
                Optional<AyElefaceOperatelog> any = ayElefaceOperatelogList.stream().filter(log -> Objects.equals(ayPrintLogSearchEs.getAyPrintLogId(), log.getId())).findAny();
                if (any.isPresent()) {
                    ayElefaceOperatelog = any.get();
                } else {
                    LOGGER.logError("搜索异常，es与mysql数据对不上：" + waybillNumber + ", sellerId:" + ayPrintLogSearchEs.getSellerId());
                    continue;
                }
            }
            ElefaceSharingRelation elefaceSharingRelation = shareId2Relation.get(ayElefaceOperatelog.getShareId());
            WaybillOperatelogResponseDTO.WaybillOperatelogDTO waybillOperatelogDTO = generalWaybillOperate(ayElefaceOperatelog, elefaceSharingRelation);
            waybillOperatelogDTO.setOrderSellerId(ayElefaceOperatelog.getSellerId());
            waybillOperatelogDTO.setOrderSellerNick(ayElefaceOperatelog.getSellerNick());
            waybillOperatelogDTO.setOrderStoreId(ayElefaceOperatelog.getStoreId());
            waybillOperatelogDTO.setOrderAppName(ayElefaceOperatelog.getAppName());
            waybillOperatelogDTO.setLogisticsTemplateId(ayElefaceOperatelog.getLogisticsTemplateId());
            waybillOperatelogDTO.setBillVersion(ayElefaceOperatelog.getBillVersion());
            String userKey = ayElefaceOperatelog.getStoreId()+ayElefaceOperatelog.getAppName()+ayElefaceOperatelog.getSellerNick();
            UserInfoResponse userInfo = userInfoResponseMap.get(userKey);
            if (userInfo == null) {
                userInfo = userCenterService.getUserInfo(ayElefaceOperatelog.getStoreId(), ayElefaceOperatelog.getAppName(), ayElefaceOperatelog.getSellerNick());
                userInfoResponseMap.put(userKey, userInfo);
            }
            if (userInfo != null && userInfo.getMallName() != null) {
                waybillOperatelogDTO.setOrderMallName(userInfo.getMallName());
            } else {
                waybillOperatelogDTO.setOrderMallName(ayElefaceOperatelog.getSellerNick());
            }
            operatelogDTOList.add(waybillOperatelogDTO);
        }

        waybillOperatelogResponseDTO.setOperatelogDTOList(operatelogDTOList);
        waybillOperatelogResponseDTO.setTotalResult(ayPrintLogSearchListAndAggDTO.getTotalResults());
        waybillOperatelogResponseDTO
            .setPrintAfterGetWaybillCount(ayPrintLogSearchListAndAggDTO.getPrintAfterGetWaybillCount());
        waybillOperatelogResponseDTO
            .setNotPrintAfterGetWaybillCount(ayPrintLogSearchListAndAggDTO.getNotPrintAfterGetWaybillCount());
        waybillOperatelogResponseDTO
            .setSendGoodAfterGetWaybillCount(ayPrintLogSearchListAndAggDTO.getSendGoodAfterGetWaybillCount());
        waybillOperatelogResponseDTO
            .setNotSendGoodAfterGetWaybillCount(ayPrintLogSearchListAndAggDTO.getNotSendGoodAfterGetWaybillCount());
        waybillOperatelogResponseDTO
            .setCancelAfterGetWaybillCount(ayPrintLogSearchListAndAggDTO.getCancelAfterGetWaybillCount());
        return waybillOperatelogResponseDTO;
    }

    @Override
    public List<WaybillOperateLogDetailsDTO> waybillOperateLogDetailUpdate(List<WaybillOperateLogDetailsDTO> operateLogDetail, Integer elefaceLogUserRole, List<UserInfoBo> userInfoBoList) {

        if (CollectionUtils.isEmpty(operateLogDetail)) {
            return operateLogDetail;
        }

        Map<String, WaybillOperateLogDetailsDTO> waybillOperateLogDetailsMap = operateLogDetail.stream().collect(Collectors.toMap(WaybillOperateLogDetailsDTO::getWaybillCode, Function.identity(), (v1, v2) -> v1));

        List<String> waybillCodeList = new ArrayList<>(waybillOperateLogDetailsMap.keySet());
        if (CollectionUtils.isEmpty(waybillCodeList)) {
            return operateLogDetail;
        }
        MultiWaybillOperateLogListGetDTO searchDTO = new MultiWaybillOperateLogListGetDTO();
        searchDTO.setPage(1);
        searchDTO.setPageSize(20);
        searchDTO.setElefaceLogUserRole(elefaceLogUserRole);

        List<AyPrintLogSearchEs> itemSearchESList = new ArrayList<>();

        // 防止一次性查询过多，分段查询
        if (waybillCodeList.size() > 20) {
            List<List<String>> partition = ListUtils.partition(waybillCodeList, 20);
            for (List<String> codes : partition) {
                searchDTO.setWaybillCodeList(codes);
                AyPrintLogSearchListAndAggDTO ayPrintLogSearchListAndAggDTO = ayElefaceOperateLogSearchService.ayPrintLogListGetQueryByLimit(searchDTO, userInfoBoList);
                ListUtil.addListIfNotNull(itemSearchESList, ayPrintLogSearchListAndAggDTO.getItemSearchESList());
            }
        } else {
            searchDTO.setWaybillCodeList(waybillCodeList);
            AyPrintLogSearchListAndAggDTO ayPrintLogSearchListAndAggDTO = ayElefaceOperateLogSearchService.ayPrintLogListGetQueryByLimit(searchDTO, userInfoBoList);
            ListUtil.addListIfNotNull(itemSearchESList, ayPrintLogSearchListAndAggDTO.getItemSearchESList());
        }

        for (AyPrintLogSearchEs logSearchEs : itemSearchESList) {
            String waybillNumber = logSearchEs.getWaybillNumber();
            String sellerId = Optional.ofNullable(logSearchEs.getElefaceOperateLog())
                .map(AyPrintLogSearchEs.ElefaceOperateLog::getOrderSellerId)
                .orElse(logSearchEs.getSellerId());
            WaybillOperateLogDetailsDTO operateLogDetailsDTO = waybillOperateLogDetailsMap.get(waybillNumber);
            Long ayPrintLogId = logSearchEs.getAyPrintLogId();
            List<Long> mergeSubPrintLogId = logSearchEs.getMergeSubPrintLogId();
            if (mergeSubPrintLogId == null) {
                mergeSubPrintLogId = Lists.newArrayList(ayPrintLogId);
            }
            int i = ayElefaceOperatelogDao.updateDetailByIds(sellerId, mergeSubPrintLogId, operateLogDetailsDTO);
            if (i > 0) {
                operateLogDetailsDTO.setUpdateStatus(true);
            }
        }

        return new ArrayList<>(waybillOperateLogDetailsMap.values());
    }

    private WaybillOperatelogResponseDTO.WaybillOperatelogDTO
        generalWaybillOperate(AyElefaceOperatelog ayElefaceOperatelog, ElefaceSharingRelation elefaceSharingRelation) {
        WaybillOperatelogResponseDTO.WaybillOperatelogDTO waybillOperatelogDTO =
            generalWaybillOperate(ayElefaceOperatelog);
        if (!Objects.isNull(elefaceSharingRelation)) {
            waybillOperatelogDTO.setBranchName(elefaceSharingRelation.getBranchName());
            waybillOperatelogDTO.setOwnerMallName(elefaceSharingRelation.getOwnerMallName());
            waybillOperatelogDTO.setTargetMallName(elefaceSharingRelation.getTargetMallName());
            waybillOperatelogDTO.setShareMemo(elefaceSharingRelation.getShareMemo());
            waybillOperatelogDTO.setSalesman(elefaceSharingRelation.getSalesman());
        }

        return waybillOperatelogDTO;
    }

    @VisibleForTesting
    protected LocalDateTime LocalDateTimeNow() {
        return LocalDateTime.now();
    }
}
