package cn.loveapp.print.service.convert;

import cn.loveapp.print.api.dto.ElefaceSharingRelationDTO;
import cn.loveapp.print.api.dto.TargetPrintUserInfo;
import cn.loveapp.print.api.dto.WaybillOperatelogResponseDTO;
import cn.loveapp.print.common.entity.AyDistributeInfo;
import cn.loveapp.print.common.entity.AyElefaceOperatelog;
import cn.loveapp.print.service.bo.ElefacePrintBo;
import cn.loveapp.print.service.bo.ElefaceSharingRelationQueryBo;
import cn.loveapp.print.service.bo.UserInfoBo;
import cn.loveapp.print.service.dto.DistributeOrderInfoDTO;
import cn.loveapp.print.service.dto.ElefaceSharingRelationOperateDTO;
import cn.loveapp.print.service.entity.ElefaceSharingRelation;
import cn.loveapp.print.service.entity.ElefaceSharingRelationOperateLog;
import cn.loveapp.print.service.request.ElefaceSharingRelationOperateQueryRequest;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.control.DeepClone;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2023/11/8 3:31 PM
 */
@Mapper
public interface CommonConvertMapper {

    CommonConvertMapper INSTANCE = Mappers.getMapper(CommonConvertMapper.class);

    @Mapping(target = "recipient", source = "recipient", mappingControl = DeepClone.class)
    ElefacePrintBo cpElefacePrintBo(ElefacePrintBo elefacePrintBo);

    AyDistributeInfo toAyDistribute(DistributeOrderInfoDTO ayDistributeInfo);

    ElefaceSharingRelationQueryBo toElefaceSharingRelationOperateQueryBo(ElefaceSharingRelationOperateQueryRequest request);

    @Mapping(source = "elefaceSharingRelation.shareMemo", target = "shareMemo")
    @Mapping(source = "operateLog.shareId", target = "shareId")
    @Mapping(source = "operateLog.targetSellerId", target = "targetSellerId")
    @Mapping(source = "operateLog.targetSellerNick", target = "targetSellerNick")
    @Mapping(source = "operateLog.targetStoreId", target = "targetStoreId")
    @Mapping(source = "operateLog.targetAppName", target = "targetAppName")
    ElefaceSharingRelationOperateDTO toElefaceSharingRelationOperateDTO(ElefaceSharingRelationOperateLog operateLog, ElefaceSharingRelationDTO elefaceSharingRelation);

    @Mapping(source = "elefaceSharingRelation.shareMemo", target = "shareMemo")
    @Mapping(source = "operateLog.shareId", target = "shareId")
    @Mapping(source = "operateLog.targetSellerId", target = "targetSellerId")
    @Mapping(source = "operateLog.targetSellerNick", target = "targetSellerNick")
    @Mapping(source = "operateLog.targetStoreId", target = "targetStoreId")
    @Mapping(source = "operateLog.targetAppName", target = "targetAppName")
    ElefaceSharingRelationOperateDTO toElefaceSharingRelationOperate(ElefaceSharingRelationOperateLog operateLog, ElefaceSharingRelation elefaceSharingRelation);

    List<ElefaceSharingRelationDTO> toElefaceSharingRelationDTOList(List<ElefaceSharingRelation> allRelation);

    List<UserInfoBo> toUserInfoBo(List<TargetPrintUserInfo> userInfoList);

    WaybillOperatelogResponseDTO.WaybillOperatelogDTO toWaybillOperateLogDTO(AyElefaceOperatelog operateLog);
}
