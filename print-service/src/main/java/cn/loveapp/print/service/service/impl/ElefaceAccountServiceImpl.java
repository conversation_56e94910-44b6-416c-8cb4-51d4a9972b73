package cn.loveapp.print.service.service.impl;

import java.util.*;
import java.util.concurrent.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import cn.loveapp.common.constant.CommonPlatformConstants;
import cn.loveapp.print.api.dto.ElefaceSharingRelationDTO;
import cn.loveapp.print.api.dto.TargetPrintUserInfo;
import cn.loveapp.print.api.proto.ShareRelationChangeRequestProto;
import cn.loveapp.print.api.request.SharingBatchGetRequest;
import cn.loveapp.print.api.response.SharingBatchCancelResponse;
import cn.loveapp.print.api.contant.ElefaceGetType;
import cn.loveapp.print.common.constant.BillVersionEnum;
import cn.loveapp.print.common.constant.ElefaceShareType;
import cn.loveapp.print.common.utils.ListUtil;
import cn.loveapp.print.common.utils.ShardingUtils;
import cn.loveapp.print.service.bo.ElefaceSharingRelationQueryBo;
import cn.loveapp.print.service.constant.ElefaceSharingRelationOperateConstant;
import cn.loveapp.print.service.request.ElefaceSharingProxyListRequest;
import cn.loveapp.print.service.request.MultiProxySharingToOthersTargetListRequest;
import cn.loveapp.print.service.response.MultiProxySharingToOthersTargetListResponse;
import cn.loveapp.print.service.response.MultiSharingToOthersListResponse;
import cn.loveapp.print.service.service.ElefaceSharingRelationOperateService;
import cn.loveapp.print.service.service.PrintMessageSendService;
import cn.loveapp.print.service.service.ShopsService;
import cn.loveapp.shops.api.dto.ShopsMemberDTO;
import cn.loveapp.shops.api.response.ShopsResponse;
import cn.loveapp.uac.response.UserFullInfoResponse;
import com.google.common.util.concurrent.ThreadFactoryBuilder;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;

import cn.loveapp.common.utils.LoggerHelper;
import cn.loveapp.print.common.constant.ElefaceProviderConstant;
import cn.loveapp.print.common.exception.*;
import cn.loveapp.print.common.service.UserCenterService;
import cn.loveapp.print.service.api.entity.AyWaybillApplySubscriptionInfo;
import cn.loveapp.print.service.api.request.AyWaybillSearchRequest;
import cn.loveapp.print.service.api.response.AyWaybillSearchResponse;
import cn.loveapp.print.service.bo.ElefaceSharingCreateBo;
import cn.loveapp.print.service.bo.UserInfoBo;
import cn.loveapp.print.service.constant.ElefaceSharingRelationConstant;
import cn.loveapp.print.service.dao.print.ElefaceSharingRelationDao;
import cn.loveapp.print.service.dao.redis.ElefaceSharingRelationLockRedisDao;
import cn.loveapp.print.service.dto.WaybillBranchShippAddressDTO;
import cn.loveapp.print.service.entity.ElefaceSharingRelation;
import cn.loveapp.print.service.service.ElefaceAccountService;
import cn.loveapp.print.service.platform.api.PlatformWaybillApiService;
import cn.loveapp.print.service.utils.ElefaceSharingRelationUtils;
import cn.loveapp.uac.response.UserInfoResponse;

/**
 * 面单账号相关服务 service 实现类
 *
 * <AUTHOR>
 */
@Service
public class ElefaceAccountServiceImpl implements ElefaceAccountService {

    private static final LoggerHelper LOGGER = LoggerHelper.getLogger(ElefaceAccountServiceImpl.class);


    @Autowired
    private UserCenterService userCenterService;

    @Autowired
    private PlatformWaybillApiService platformWaybillApiService;

    @Autowired
    private ElefaceSharingRelationDao elefaceSharingRelationDao;


    @Autowired
    private ElefaceSharingRelationLockRedisDao sharingRelationLockRedisDao;

    @Autowired
    private ShopsService shopsService;

    @Autowired
    private ElefaceSharingRelationOperateService elefaceSharingRelationOperateService;

    @Autowired
    private PrintMessageSendService printMessageSendService;

    @Value("${print.branch.get.threadPool.size:20}")
    private int printBranchGetThreadPoolSize;

    @Override
    public List<WaybillBranchShippAddressDTO> getAllBranchesShippAddress(UserInfoBo userInfo, Boolean isIncludeUnAvailable) throws CommonException {
        String sellerNick = StringUtils.isNotEmpty(userInfo.getSellerNick()) ? userInfo.getSellerNick() : userInfo.getSessionNick();
        String storeId = StringUtils.isNotEmpty(userInfo.getStoreId()) ? userInfo.getStoreId() : userInfo.getSessionStoreId();
        String appName = StringUtils.isNotEmpty(userInfo.getAppName()) ? userInfo.getAppName() : userInfo.getSessionAppName();

        List<WaybillBranchShippAddressDTO> waybillBranchShippAddressDTOList = Lists.newArrayList();

        // 未启用的平台不需要获取自己平台的面单信息
        if (ElefaceProviderConstant.getEnabledPlatforms().contains(storeId)) {
            String topSession = userCenterService.getTopSession(storeId, appName, sellerNick);
            AyWaybillSearchRequest waybillSearchRequest = new AyWaybillSearchRequest();
            waybillSearchRequest.setSellerId(userInfo.getSellerId());
            AyWaybillSearchResponse waybillSearchResponse =
                    platformWaybillApiService.waybillSearch(waybillSearchRequest, topSession, storeId, appName);
            if (!waybillSearchResponse.isSuccess()) {
//                throw new PlatformSdkException("waybill.search接口请求异常: " + JSON.toJSONString(waybillSearchResponse));
                LOGGER.logError(sellerNick, "", "waybill.search接口请求异常: " + JSON.toJSONString(waybillSearchResponse));
            } else {
                // 先处理非共享的面单
                for (AyWaybillApplySubscriptionInfo subscriptionInfo : waybillSearchResponse
                        .getWaybillApplySubscriptionCols()) {
                    String cpCode = subscriptionInfo.getCpCode();
                    Long cpType = subscriptionInfo.getCpType();
                    Integer billVersion = subscriptionInfo.getBillVersion();
                    for (AyWaybillApplySubscriptionInfo.WaybillBranchAccount branchAccount : subscriptionInfo
                            .getBranchAccountCols()) {
                        for (AyWaybillApplySubscriptionInfo.AddressDto addressDto : branchAccount.getShippAddressCols()) {
                            WaybillBranchShippAddressDTO waybillBranchShippAddress =
                                WaybillBranchShippAddressDTO.of(cpCode, cpType, billVersion, branchAccount, addressDto);
                            waybillBranchShippAddress.setProvider(ElefaceProviderConstant.getProvider(storeId));
                            waybillBranchShippAddressDTOList.add(waybillBranchShippAddress);
                        }
                    }
                }
            }

        }

        //多店铺下，店铺不需要查询面单共享关系.使用此参数控制是否需要查询共享面单关系
        if (userInfo.getIsMultiShops()) {
            return waybillBranchShippAddressDTOList;
        }

        // 获取面单共享关系
        List<ElefaceSharingRelation> sharingRelations = getSharingRelationsShareToMeValid(userInfo);
        addBranchesShippAddressByRelation(waybillBranchShippAddressDTOList, sharingRelations, isIncludeUnAvailable, sellerNick);

        return waybillBranchShippAddressDTOList;
    }


    @Override
    public List<WaybillBranchShippAddressDTO> getMultiAllBranchesShippAddress(UserInfoBo userInfo, Boolean isIncludeUnAvailable, String getType) throws CommonException {
        String sellerNick = StringUtils.isNotEmpty(userInfo.getSellerNick()) ? userInfo.getSellerNick() : userInfo.getSessionNick();
        String storeId = StringUtils.isNotEmpty(userInfo.getStoreId()) ? userInfo.getStoreId() : userInfo.getSessionStoreId();
        String appName = StringUtils.isNotEmpty(userInfo.getAppName()) ? userInfo.getAppName() : userInfo.getSessionAppName();
        String sellerId = StringUtils.isNotEmpty(userInfo.getSellerId()) ? userInfo.getSellerId() : userInfo.getSessionSellerId();

        List<WaybillBranchShippAddressDTO> waybillBranchShippAddressDTOList = Lists.newArrayList();

        // 未启用的平台不需要获取自己平台的面单信息
        if (ElefaceProviderConstant.getEnabledPlatforms().contains(storeId) && !ElefaceGetType.GET_OTHER.equals(getType)) {
            String topSession = userCenterService.getTopSession(storeId, appName, sellerNick);
            AyWaybillSearchRequest waybillSearchRequest = new AyWaybillSearchRequest();
            waybillSearchRequest.setSellerId(sellerId);
            AyWaybillSearchResponse waybillSearchResponse =
                    platformWaybillApiService.waybillSearch(waybillSearchRequest, topSession, storeId, appName);
            if (!waybillSearchResponse.isSuccess()) {
                LOGGER.logError(sellerNick, "", "waybill.search接口请求异常: " + JSON.toJSONString(waybillSearchResponse));
            } else {
                // 先处理非共享的面单
                for (AyWaybillApplySubscriptionInfo subscriptionInfo : waybillSearchResponse
                        .getWaybillApplySubscriptionCols()) {
                    String cpCode = subscriptionInfo.getCpCode();
                    Long cpType = subscriptionInfo.getCpType();
                    Integer billVersion = subscriptionInfo.getBillVersion();
                    for (AyWaybillApplySubscriptionInfo.WaybillBranchAccount branchAccount : subscriptionInfo
                            .getBranchAccountCols()) {
                        for (AyWaybillApplySubscriptionInfo.AddressDto addressDto : branchAccount.getShippAddressCols()) {
                            WaybillBranchShippAddressDTO waybillBranchShippAddress =
                                WaybillBranchShippAddressDTO.of(cpCode, cpType, billVersion, branchAccount, addressDto);
                            waybillBranchShippAddress.setProvider(ElefaceProviderConstant.getProvider(storeId));
                            waybillBranchShippAddressDTOList.add(waybillBranchShippAddress);
                        }
                    }
                }
            }

        }

        List<ElefaceSharingRelation> sharingRelations = new ArrayList<>();
        ElefaceSharingRelationQueryBo queryBo = new ElefaceSharingRelationQueryBo();
        queryBo.setStatus(ElefaceSharingRelationConstant.STATUS_VALID);
        // 获取代理的
        if (!ElefaceGetType.GET_OTHER.equals(getType)) {
            // 获取有效并且自己代理的电子面单
            queryBo.setShareType(1);
            List<ElefaceSharingRelation> relationList = elefaceSharingRelationDao.searchByTargetUserAndQuery(sellerId, storeId, appName, queryBo);
            ListUtil.addListIfNotNull(sharingRelations, relationList);
        }

        if (!ElefaceGetType.GET_OWNER.equals(getType)) {
            // 获取有效并且自己被分享的电子面单
            queryBo.setShareType(0);
            if (CommonPlatformConstants.PLATFORM_TAO.equals(userInfo.getSessionStoreId())) {
                // 千牛端不考虑店铺群分享共用
                List<ElefaceSharingRelation> relationList = elefaceSharingRelationDao.searchByTargetUserAndQuery(sellerId, storeId, appName, queryBo);
                ListUtil.addListIfNotNull(sharingRelations, relationList);
            } else {
                // 判断店铺群
                ShopsResponse shopsResponse = shopsService.shopGet(sellerId, sellerNick, storeId, appName, true);
                if (shopsResponse != null
                    && CollectionUtils.isNotEmpty(shopsResponse.getShopsMemberList())) {
                    // 只有爱用店铺群下，所有被分享的面单才可公用
                    for (ShopsMemberDTO shopsMember : shopsResponse.getShopsMemberList()) {
                        List<ElefaceSharingRelation> relationList = elefaceSharingRelationDao.searchByTargetUserAndQuery(shopsMember.getSellerId(), shopsMember.getStoreId(),
                            shopsMember.getAppName(), queryBo);
                        ListUtil.addListIfNotNull(sharingRelations, relationList);
                    }
                } else {
                    List<ElefaceSharingRelation> relationList = elefaceSharingRelationDao.searchByTargetUserAndQuery(sellerId, storeId, appName, queryBo);
                    ListUtil.addListIfNotNull(sharingRelations, relationList);
                }
            }
        }

        addBranchesShippAddressByRelation(waybillBranchShippAddressDTOList, sharingRelations, isIncludeUnAvailable, sellerNick);

        return waybillBranchShippAddressDTOList;
    }


    /**
     * 根据分享记录获取面单网点信息
     *
     * @param waybillBranchShippAddressDTOList
     * @param sharingRelations
     * @param isIncludeUnAvailable
     * @param sellerNick
     */
    private void addBranchesShippAddressByRelation(List<WaybillBranchShippAddressDTO> waybillBranchShippAddressDTOList,
                                                   List<ElefaceSharingRelation> sharingRelations,
                                                   Boolean isIncludeUnAvailable,
                                                   String sellerNick) {
        if (CollectionUtils.isEmpty(sharingRelations)) {
            return;
        }
        Map<String, List<ElefaceSharingRelation>> ownerSellerNickMap =
                sharingRelations.stream().collect(Collectors.groupingBy(e-> e.getOwnerSellerNick() + e.getOwnerStoreId() + e.getOwnerAppName()));

        List<CompletableFuture<List<WaybillBranchShippAddressDTO>>> futures = new ArrayList<>();

        int elefaceSharingRelationsSize = ownerSellerNickMap.values().size();

        int poolSize = printBranchGetThreadPoolSize > elefaceSharingRelationsSize ? elefaceSharingRelationsSize : printBranchGetThreadPoolSize;
        ExecutorService printBranchGetPool = Executors.newFixedThreadPool(poolSize);

        for (List<ElefaceSharingRelation> elefaceSharingRelations : ownerSellerNickMap.values()) {
            if (CollectionUtils.isEmpty(elefaceSharingRelations)) {
                continue;
            }

            // 多用户面单并发调用，提高接口速度
            futures.add(CompletableFuture.supplyAsync(() -> {
                List<WaybillBranchShippAddressDTO> result = new ArrayList<>();
                ElefaceSharingRelation first = elefaceSharingRelations.get(0);
                String ownerSellerNick = first.getOwnerSellerNick();
                String ownerStoreId = first.getOwnerStoreId();
                String ownerAppName = first.getOwnerAppName();
                String ownerSellerId = first.getOwnerSellerId();

                if (!ElefaceProviderConstant.getEnabledPlatforms().contains(ownerStoreId)) {
                    LOGGER.logWarn(sellerNick, "-", "面单共享关系是未启用的面单平台, sharingRelation => " + JSON.toJSONString(ownerStoreId));
                    return result;
                }

                String shareUserTopSession = userCenterService.getTopSession(ownerStoreId, ownerAppName, ownerSellerNick, ownerSellerId);

                if (StringUtils.isEmpty(shareUserTopSession)) {
                    if (BooleanUtils.isTrue(isIncludeUnAvailable)) {
                        result.addAll(elefaceSharingRelations.stream().map(e -> WaybillBranchShippAddressDTO.ofUnAvailable(e, "共享的用户授权失效")).collect(Collectors.toList()));
                    }
                    return result;
                }

                AyWaybillSearchRequest shareUserWaybillSearchRequest = new AyWaybillSearchRequest();
                shareUserWaybillSearchRequest.setSellerId(ownerSellerId);
                AyWaybillSearchResponse shareUserWaybillSearchResponse = platformWaybillApiService
                        .waybillSearch(shareUserWaybillSearchRequest, shareUserTopSession, ownerStoreId, ownerAppName);

                if (!shareUserWaybillSearchResponse.isSuccess() || CollectionUtils.isEmpty(shareUserWaybillSearchResponse.getWaybillApplySubscriptionCols())) {
                    LOGGER.logWarn(sellerNick, "-", "获取共享的waybill.search失败, response => " + JSON.toJSONString(shareUserWaybillSearchResponse));
                    if (BooleanUtils.isTrue(isIncludeUnAvailable)) {
                        result.addAll(elefaceSharingRelations.stream().map(e -> WaybillBranchShippAddressDTO.ofUnAvailable(e, shareUserWaybillSearchResponse.getAllErrorMassage())).collect(Collectors.toList()));
                    }
                    return result;
                }

                for (ElefaceSharingRelation sharingRelation : elefaceSharingRelations) {
                    String cpCode = sharingRelation.getCpCode();
                    Long cpType = sharingRelation.getCpType();
                    Integer billVersion = sharingRelation.getBillVersion();
                    String branchCode = sharingRelation.getBranchCode();
                    for (AyWaybillApplySubscriptionInfo shareUserSubscriptionInfo : shareUserWaybillSearchResponse
                            .getWaybillApplySubscriptionCols()) {
                        if (!cpCode.equals(shareUserSubscriptionInfo.getCpCode())
                                || !cpType.equals(shareUserSubscriptionInfo.getCpType())) {
                            // cpCode|cpType 与共享关系不一致, 跳过
                            continue;
                        }
                        for (AyWaybillApplySubscriptionInfo.WaybillBranchAccount shareUserBranchAccount : shareUserSubscriptionInfo
                                .getBranchAccountCols()) {
                            if (StringUtils.isNotEmpty(shareUserBranchAccount.getBranchCode())
                                    && !branchCode.equals(shareUserBranchAccount.getBranchCode())) {
                                // branchCode与共享关系不一致, 跳过
                                continue;
                            }
                            if (StringUtils.isEmpty(shareUserBranchAccount.getBranchCode())
                                    && !branchCode.equals(shareUserSubscriptionInfo.getCpCode())) {
                                // branchCode为空时, 分享关系的branchCode 应该与 cpCode 一致, 不一致跳过
                                continue;
                            }

                            if (!Objects.isNull(shareUserSubscriptionInfo.getBillVersion())
                                && shareUserSubscriptionInfo.getBillVersion() > BillVersionEnum.oldVersion.val
                                && !Objects.equals(shareUserSubscriptionInfo.getBillVersion(), billVersion)) {
                                // xhs新老电子面单不兼容 平台返回新的电子面单 库里是老的面单分享记录 直接跳过
                                continue;
                            }

                            if (!sharingRelation.getSegmentCode().equals(shareUserBranchAccount.getSegmentCode())) {
                                // segmentCode与共享关系不一致, 跳过
                                continue;
                            }
                            for (AyWaybillApplySubscriptionInfo.AddressDto addressDto : shareUserBranchAccount
                                    .getShippAddressCols()) {
                                String shippAddressMd5 =
                                        ElefaceSharingRelationUtils.generateShippAddressMd5(addressDto.getProvince(),
                                                addressDto.getCity(), addressDto.getDistrict(), addressDto.getDetail());
                                if (!shippAddressMd5.equals(sharingRelation.getShippAddressMd5())) {
                                    // 发货地址不一致，跳过
                                    continue;
                                }
                                WaybillBranchShippAddressDTO waybillBranchShippAddressDTO =
                                    WaybillBranchShippAddressDTO.of(cpCode, cpType,
                                        shareUserSubscriptionInfo.getBillVersion(), shareUserBranchAccount, addressDto);
                                if (ElefaceShareType.SHARE_TYPE_AGENT.equals(sharingRelation.getShareType())) {
                                    // 分享模式
                                    sharingRelation.setUsedNum(waybillBranchShippAddressDTO.getAllocatedQuantity());
                                    sharingRelation.setCancelNum(waybillBranchShippAddressDTO.getCancelQuantity());
                                }
                                waybillBranchShippAddressDTO.setSharingRelation(sharingRelation);
                                waybillBranchShippAddressDTO.setProvider(sharingRelation.getProvider());
                                result.add(waybillBranchShippAddressDTO);
                            }
                        }
                    }
                }

                return result;
            }, printBranchGetPool));
        }

        // 等待所有任务完成并收集结果
        List<WaybillBranchShippAddressDTO> waybillBranchShippAddressFutureList = futures.stream()
                .map(CompletableFuture::join)
                .flatMap(List::stream)
                .collect(Collectors.toList());

        try {
            printBranchGetPool.shutdownNow();
        } catch (Exception e) {
            LOGGER.logError("线程池关闭失败" + e.getMessage(), e);
        }

        if (CollectionUtils.isNotEmpty(waybillBranchShippAddressFutureList)) {
            // 固定排序
            waybillBranchShippAddressDTOList.sort(Comparator.comparing(WaybillBranchShippAddressDTO::getCpCode));
            waybillBranchShippAddressDTOList.addAll(waybillBranchShippAddressFutureList);
        }

    }

    /**
     * 充值共享面单
     *
     * @param userInfoBo
     * @param shareId
     * @param topUpNum
     * @throws CommonException
     */
    @Override
    public void topUpBranchShareNum(UserInfoBo userInfoBo, String shareId, Long topUpNum) throws CommonException {
        String lockValue = sharingRelationLockRedisDao.lockSharingRelation(shareId);
        try {
            ElefaceSharingRelation sharingRelation = getSharingRelationByShareId(shareId);
            if (!checkOwnerUserSharingRelation(sharingRelation, userInfoBo)
                    || !ElefaceSharingRelationConstant.STATUS_VALID.equals(sharingRelation.getStatus())) {
                throw new ElefaceSharingNotExistException();
            }
            if (ElefaceSharingRelationConstant.SHARE_NUM_UNLIMITED.equals(sharingRelation.getShareNum())) {
                LOGGER.logWarn(userInfoBo.getSellerNick(), shareId, "无限制分享，无法进行充值");
                return;
            }
            Long lastShareNum = sharingRelation.getShareNum();
            elefaceSharingRelationDao.addShareNum(shareId, topUpNum);
            elefaceSharingRelationOperateService.saveElefaceSharingRelationOperateLog
                    (sharingRelation, lastShareNum, ElefaceSharingRelationOperateConstant.UPDATE, userInfoBo.getMallOperate(), userInfoBo.getOperateTerminal());
        } finally {
            sharingRelationLockRedisDao.unlockSharingRelation(shareId, lockValue);
        }
    }

    /**
     * 获取用户共享出去的 有效的面单共享关系
     *
     * @param userInfoBo
     * @return
     */
    @Override
    public List<ElefaceSharingRelation> getSharingRelationsShareToOthersValid(UserInfoBo userInfoBo) {
        return elefaceSharingRelationDao.queryByOwnerUserAndStatus(userInfoBo.getSessionSellerId(),
                userInfoBo.getSessionStoreId(), userInfoBo.getSessionAppName(),
                ElefaceSharingRelationConstant.STATUS_VALID);
    }

    /**
     * 获取用户共享出去的 有效的面单共享关系
     *
     * @param userInfoBo
     * @return
     */
    @Override
    public List<ElefaceSharingRelationDTO> getMultiSharingRelationsShareToOthersValid(UserInfoBo userInfoBo, ElefaceSharingRelationQueryBo queryBo) {

        List<ElefaceSharingRelation> allRelation = new ArrayList<>();

        // 查询自己分享出去的面单（兼容淘系千牛）
        List<ElefaceSharingRelation> relationListOwner = elefaceSharingRelationDao.searchByOwnerUserAndQuery(userInfoBo.getSellerId(), userInfoBo.getStoreId(), userInfoBo.getAppName(), queryBo);
        ListUtil.addListIfNotNull(allRelation, relationListOwner);

        // 查询代理分享的（爱用多店）
        queryBo.setShareType(0);
        List<ElefaceSharingRelation> relationListTarget = elefaceSharingRelationDao.searchByProxyUserAndQuery(userInfoBo.getSellerId(), userInfoBo.getStoreId(), userInfoBo.getAppName(), queryBo);
        ListUtil.addListIfNotNull(allRelation, relationListTarget);

        queryBo.setShareMemo(queryBo.getFuzzySearchStr());
        return ElefaceSharingRelationUtils.generalElefaceSharingRelationResult(allRelation, userCenterService, queryBo);
    }

    @Override
    public MultiSharingToOthersListResponse multiProxySharingToOthersList(UserInfoBo userInfoBo, ElefaceSharingRelationQueryBo queryBo) {

        queryBo.setShareType(0);

        MultiSharingToOthersListResponse response = new MultiSharingToOthersListResponse();

        int count = elefaceSharingRelationDao.countByProxyUserAndQuery(userInfoBo.getSellerId(), userInfoBo.getStoreId(), userInfoBo.getAppName(), queryBo);

        response.setTotalResult(count);
        if (count > 0) {
            List<ElefaceSharingRelation> relationListTarget = elefaceSharingRelationDao.searchByProxyUserAndQuery(userInfoBo.getSellerId(), userInfoBo.getStoreId(), userInfoBo.getAppName(), queryBo);
            response.setRelationList(relationListTarget);
        } else {
            response.setRelationList(Collections.emptyList());
        }
        return response;
    }

    /**
     * 获取用户被共享的 有效的面单共享关系
     *
     * @param userInfoBo
     * @return
     */
    @Override
    public List<ElefaceSharingRelation> getSharingRelationsShareToMeValid(UserInfoBo userInfoBo) {
        return elefaceSharingRelationDao.queryByTargetUserAndStatus(userInfoBo.getSessionSellerId(),
                userInfoBo.getSessionStoreId(), userInfoBo.getSessionAppName(),
                ElefaceSharingRelationConstant.STATUS_VALID);
    }

    /**
     * 获取用户被共享的 当前被取消的面单共享关系
     *
     * @param userInfoBo
     * @return
     */
    @Override
    public List<ElefaceSharingRelation> getSharingRelationsShareToMeCanceled(UserInfoBo userInfoBo) {
        return elefaceSharingRelationDao.queryByTargetUserAndStatus(userInfoBo.getSessionSellerId(),
                userInfoBo.getSessionStoreId(), userInfoBo.getSessionAppName(),
                ElefaceSharingRelationConstant.STATUS_CANCELED);
    }

    /**
     * 确认 面单共享关系已被取消
     *
     * @param shareIds
     * @param userInfoBo
     */
    @Override
    public void confirmSharingRelationsShareToMeCanceled(List<String> shareIds, UserInfoBo userInfoBo) {
        List<ElefaceSharingRelation> sharingRelations = elefaceSharingRelationDao.queryByShareIds(shareIds);
        // 过滤符合条件的共享关系
        sharingRelations = sharingRelations.stream()
                .filter(item -> checkTargetUserSharingRelation(item, userInfoBo)
                        && ElefaceSharingRelationConstant.STATUS_CANCELED.equals(item.getStatus()))
                .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(sharingRelations)) {
            elefaceSharingRelationDao.deleteByShareIds(
                    sharingRelations.stream().map(ElefaceSharingRelation::getShareId).collect(Collectors.toList()));
        }
    }

    /**
     * 获取指定id的共享关系
     *
     * @param shareId
     * @return
     */
    @Override
    public ElefaceSharingRelation getSharingRelationByShareId(String shareId) {
        return elefaceSharingRelationDao.queryByShareId(shareId);
    }

    /**
     * 创建面单共享关系
     *
     * @param sharingCreateBo
     * @return
     */
    @Override
    public ElefaceSharingRelation createSharingRelation(ElefaceSharingCreateBo sharingCreateBo) throws CommonException {
        UserInfoResponse ownerUser = userCenterService.getUserInfo(sharingCreateBo.getOwnerStoreId(),
                sharingCreateBo.getOwnerAppName(), sharingCreateBo.getOwnerSellerNick());
        if (ownerUser == null) {
            throw new OwnerUserNotExistException();
        }
        sharingCreateBo.setOwnerSellerId(ownerUser.getSellerId());
        sharingCreateBo.setOwnerMallName(ownerUser.getMallName() != null ? ownerUser.getMallName() : ownerUser.getSellerNick());

        // 校验 target_user 是否存在
        UserInfoResponse targetUser = userCenterService.getUserInfo(sharingCreateBo.getTargetStoreId(),
                sharingCreateBo.getTargetAppName(), sharingCreateBo.getTargetSellerNick());
        if (targetUser == null) {
            throw new TargetUserNotExistException();
        }
        sharingCreateBo.setTargetSellerId(targetUser.getSellerId());
        sharingCreateBo.setTargetMallName(targetUser.getMallName() != null ? targetUser.getMallName() : targetUser.getSellerNick());

        if (sharingCreateBo.getProxySellerNick() != null) {
            // 校验 target_user 是否存在
            UserInfoResponse proxyUser = userCenterService.getUserInfo(sharingCreateBo.getProxyStoreId(),
                    sharingCreateBo.getProxyAppName(), sharingCreateBo.getProxySellerNick());
            if (proxyUser == null) {
                throw new TargetUserNotExistException();
            }
            sharingCreateBo.setProxySellerId(proxyUser.getSellerId());
            sharingCreateBo.setProxyMallName(proxyUser.getMallName() != null ? proxyUser.getMallName() : proxyUser.getSellerNick());
        }
        ElefaceSharingRelation sharingRelation = ElefaceSharingRelation.of(sharingCreateBo);
        String shippAddressMd5 = ElefaceSharingRelationUtils.generateShippAddressMd5(
                sharingRelation.getShippAddressProvince(), sharingRelation.getShippAddressCity(),
                sharingRelation.getShippAddressDistrict(), sharingRelation.getShippAddressDetail());
        sharingRelation.setShippAddressMd5(shippAddressMd5);
        sharingRelation.generateShareId();
        LOGGER.logInfo(sharingRelation.getOwnerSellerNick(), sharingRelation.getShareId(), "生成新的share_id");
        String lockValue = sharingRelationLockRedisDao.lockSharingRelation(sharingRelation.getShareId());
        try {
            ElefaceSharingRelation lastRelation =
                    elefaceSharingRelationDao.queryByShareId(sharingRelation.getShareId());
            Long lastShareNum = null;
            if (lastRelation != null
                    && ElefaceSharingRelationConstant.STATUS_CANCELED.equals(lastRelation.getStatus())) {
                // 如果库里面存在已经被取消的共享关系, 直接删除
                elefaceSharingRelationDao.deleteByShareId(sharingRelation.getShareId());
                lastShareNum = 0L;
            }
            elefaceSharingRelationDao.insert(sharingRelation);
            elefaceSharingRelationOperateService.saveElefaceSharingRelationOperateLog
                    (sharingRelation, lastShareNum, ElefaceSharingRelationOperateConstant.CREATE, sharingCreateBo.getOperator(), sharingCreateBo.getOperateTerminal());
        } catch (DuplicateKeyException e) {
            throw new ElefaceSharingAlreadyExistException();
        } finally {
            sharingRelationLockRedisDao.unlockSharingRelation(sharingRelation.getShareId(), lockValue);
        }
        return sharingRelation;
    }


    @Override
    public ElefaceSharingRelation multiCreateSharingRelation(ElefaceSharingCreateBo sharingCreateBo) throws CommonException {
        if (sharingCreateBo == null) {
            return null;
        }

        String targetAppName = sharingCreateBo.getTargetAppName();
        String targetSellerId = sharingCreateBo.getTargetSellerId();
        String targetStoreId = sharingCreateBo.getTargetStoreId();
        String targetSellerNick = sharingCreateBo.getTargetSellerNick();

        String targetMallName = sharingCreateBo.getTargetMallName();
        if (targetSellerNick == null && targetMallName != null) {
            // 需要根据店铺查询
            UserFullInfoResponse targetUser = userCenterService.getSellerNickByMallName(targetMallName, targetStoreId, targetAppName);
            if (targetUser == null) {
                LOGGER.logError("根据店铺名，找不到用户");
                throw new TargetUserNotExistException();
            }
            targetSellerNick = targetUser.getSellerNick();
            targetSellerId = targetUser.getSellerId();
            sharingCreateBo.setTargetSellerNick(targetSellerNick);
            sharingCreateBo.setTargetSellerId(targetSellerId);
        }
        if (targetSellerNick == null) {
            LOGGER.logError("缺失被分享店铺信息");
            throw new TargetUserNotExistException();
        }

        return createSharingRelation(sharingCreateBo);
    }

    /**
     * 取消面单共享
     *
     * @param shareId
     * @param userInfoBo
     * @param cancel
     * @return
     */
    @Override
    public void cancelSharingRelation(String shareId, UserInfoBo userInfoBo, Integer cancel) throws CommonException {
        String lockValue = sharingRelationLockRedisDao.lockSharingRelation(shareId);
        try {
            ElefaceSharingRelation sharingRelation = getSharingRelationByShareId(shareId);
            if (!checkOwnerUserSharingRelation(sharingRelation, userInfoBo)
                    || !ElefaceSharingRelationConstant.STATUS_VALID.equals(sharingRelation.getStatus())) {
                throw new ElefaceSharingNotExistException();
            }
            elefaceSharingRelationDao.updateStatus(shareId, ElefaceSharingRelationConstant.STATUS_CANCELED);
            elefaceSharingRelationOperateService.saveElefaceSharingRelationOperateLog
                    (sharingRelation, sharingRelation.getShareNum(), cancel, userInfoBo.getMallOperate(), userInfoBo.getOperateTerminal());
        } finally {
            sharingRelationLockRedisDao.unlockSharingRelation(shareId, lockValue);
        }
    }

    /**
     * 记录共享关系面单使用数量
     *
     * @param shareId
     * @param useNum
     * @param userInfoBo
     */
    @Override
    public void addRelationUsedNum(String shareId, Long useNum, UserInfoBo userInfoBo) {
        elefaceSharingRelationDao.addUsedNum(shareId, useNum);
    }

    /**
     * 判断共享关系是否有效
     *
     * @param sharingRelation
     * @param userInfoBo
     * @return
     */
    @Override
    public boolean checkOwnerUserSharingRelation(ElefaceSharingRelation sharingRelation, UserInfoBo userInfoBo) {
        if (sharingRelation == null) {
            LOGGER.logWarn(userInfoBo.getSessionNick(), "-", "面单共享关系不存在");
            return false;
        }

        if (sharingRelation.getShareType() == 1
                && sharingRelation.getTargetSellerId().equals(userInfoBo.getSellerId())
                && sharingRelation.getTargetStoreId().equals(userInfoBo.getStoreId())
                && sharingRelation.getTargetAppName().equals(userInfoBo.getAppName())) {
            // 面单代理模式（爱用账号）
            return true;
        }

        if (sharingRelation.getShareType() == 0
                && userInfoBo.getSellerId().equals(sharingRelation.getProxySellerId())
                && userInfoBo.getStoreId().equals(sharingRelation.getProxyStoreId())
                && userInfoBo.getAppName().equals(sharingRelation.getProxyAppName())) {
            // 面单代理模式（爱用账号）所有者
            return true;
        }

        if (!sharingRelation.getOwnerSellerId().equals(userInfoBo.getSellerId())
                || !sharingRelation.getOwnerStoreId().equals(userInfoBo.getStoreId())
                || !sharingRelation.getOwnerAppName().equals(userInfoBo.getAppName())) {
            LOGGER.logWarn(userInfoBo.getSessionNick(), sharingRelation.getShareId(), "当前用户非共享关系owner");
            return false;
        }
        return true;
    }

    /**
     * 判断共享关系是否有效 target(被分享者)
     *
     * @param sharingRelation
     * @param userInfoBo
     * @return
     */
    @Override
    public boolean checkTargetUserSharingRelation(ElefaceSharingRelation sharingRelation, UserInfoBo userInfoBo) {
        if (sharingRelation == null) {
            LOGGER.logWarn(userInfoBo.getSessionNick(), "-", "面单共享关系不存在");
            return false;
        }

        String sessionSellerId = userInfoBo.getSessionSellerId();
        String sessionNick = userInfoBo.getSessionNick();
        String sessionStoreId = userInfoBo.getSessionStoreId();
        String sessionAppName = userInfoBo.getSessionAppName();


        if (!CommonPlatformConstants.PLATFORM_TAO.equals(sessionStoreId)) {
            // 如果是爱用账号，查询爱用账号下所有被分享的店铺
            ShopsResponse shopsResponse = shopsService.shopGet(sessionSellerId, sessionNick, sessionStoreId, sessionAppName, true);
            if (shopsResponse != null && CollectionUtils.isNotEmpty(shopsResponse.getShopsMemberList())) {
                // 多店(包含erp)店铺群 分享的面单才可共用
                for (ShopsMemberDTO shopsMember : shopsResponse.getShopsMemberList()) {
                    if (sharingRelation.getTargetSellerId().equals(shopsMember.getSellerId())
                        && sharingRelation.getTargetStoreId().equals(shopsMember.getStoreId())
                        && sharingRelation.getTargetAppName().equals(shopsMember.getAppName())) {
                        return true;
                    }
                }
                LOGGER.logInfo(userInfoBo.getSessionNick(), sharingRelation.getShareId(), "当前店铺群不存在可共用面单");
            }
        }

        if (!sharingRelation.getTargetSellerId().equals(sessionSellerId)
            || !sharingRelation.getTargetStoreId().equals(sessionStoreId)
            || !sharingRelation.getTargetAppName().equals(sessionAppName)) {
            LOGGER.logWarn(userInfoBo.getSessionNick(), sharingRelation.getShareId(), "当前用户非共享关系target");
            return false;
        }

        return true;
    }

    @Override
    public SharingBatchCancelResponse removeSharingRelations(String ownerSellerId, String ownerStoreId, String ownerAppName,
                                       UserInfoBo userInfoBo) {

        ElefaceSharingRelationQueryBo queryBo = new ElefaceSharingRelationQueryBo();
        queryBo.setOwnerStoreId(ownerStoreId);
        queryBo.setOwnerSellerId(ownerSellerId);
        queryBo.setOwnerAppName(ownerAppName);
        queryBo.setStatus(ElefaceSharingRelationConstant.STATUS_VALID);

        // 查询我代理的
        queryBo.setShareType(1);
        List<ElefaceSharingRelation> sharingRelationList = elefaceSharingRelationDao.searchByTargetUserAndQuery(userInfoBo.getSellerId(),
                userInfoBo.getStoreId(), userInfoBo.getAppName(), queryBo);

        if (CollectionUtils.isNotEmpty(sharingRelationList)) {
            for (ElefaceSharingRelation elefaceSharingRelation : sharingRelationList) {
                String lockValue = sharingRelationLockRedisDao.lockSharingRelation(elefaceSharingRelation.getShareId());
                try {
                    elefaceSharingRelationDao.deleteByShareId(elefaceSharingRelation.getShareId());
                } finally {
                    sharingRelationLockRedisDao.unlockSharingRelation(elefaceSharingRelation.getShareId(), lockValue);
                }
            }
        }

        queryBo.setShareType(0);
        // 查询我代理分享的面单出去的面单
        List<ElefaceSharingRelation> relationList = elefaceSharingRelationDao.searchByProxyUserAndQuery(userInfoBo.getSellerId(),
                userInfoBo.getStoreId(), userInfoBo.getAppName(), queryBo);

        SharingBatchCancelResponse response = new SharingBatchCancelResponse();
        if (CollectionUtils.isEmpty(relationList)) {
            return response;
        }

        // 同步取消我分享的面单
        for (ElefaceSharingRelation shareInfo : relationList) {
            String shareId = shareInfo.getShareId();
            try {
                cancelSharingRelation(shareId, userInfoBo, ElefaceSharingRelationOperateConstant.UNTIE);
                response.setSuccess(shareId);
            } catch (CommonException e) {
                response.setError(shareId);
            }
        }
        return response;
    }

    @Override
    public List<ElefaceSharingRelation> getSharingRelationsShareToOtherCanceled(UserInfoBo userInfoBo) {
        return elefaceSharingRelationDao.queryByOwnerUserAndStatus(userInfoBo.getSessionSellerId(),
                userInfoBo.getSessionStoreId(), userInfoBo.getSessionAppName(),
                ElefaceSharingRelationConstant.STATUS_CANCELED);
    }

    @Override
    public List<ElefaceSharingRelation> sharingBatchGet(SharingBatchGetRequest request) {
        if (CollectionUtils.isEmpty(request.getShareIdList())) {
            return null;
        }
        return elefaceSharingRelationDao.queryByShareIdsAndStatusList(request.getShareIdList(), request.getStatusList());
    }

    @Override
    public void sharingQuantityModify(UserInfoBo userInfoBo, String shareId, Long surplusNum, Boolean shareNumUnlimited) throws CommonException {
        String lockValue = sharingRelationLockRedisDao.lockSharingRelation(shareId);
        try {
            ElefaceSharingRelation sharingRelation = getSharingRelationByShareId(shareId);
            if (!checkOwnerUserSharingRelation(sharingRelation, userInfoBo)
                    || !ElefaceSharingRelationConstant.STATUS_VALID.equals(sharingRelation.getStatus())) {
                throw new ElefaceSharingNotExistException();
            }

            long shareNum;
            if (BooleanUtils.isTrue(shareNumUnlimited)) {
                // 修改为无限分享
                shareNum = ElefaceSharingRelationConstant.SHARE_NUM_UNLIMITED;
            } else if (sharingRelation.getShareNum() < 0) {
                // 原先为无限制，修改为可分享
                shareNum = surplusNum;
            } else {
                // 当前分享数量 = 使用数量 + 剩余数量
                shareNum = sharingRelation.getShareNum() + surplusNum;
                if (shareNum < 0) {
                    shareNum = 0;
                }
            }
            Long lastShareNum = sharingRelation.getShareNum();
            elefaceSharingRelationDao.updateShareNum(shareId, shareNum);
            sharingRelation.setShareNum(shareNum);
            elefaceSharingRelationOperateService.saveElefaceSharingRelationOperateLog
                    (sharingRelation, lastShareNum, ElefaceSharingRelationOperateConstant.UPDATE, userInfoBo.getMallOperate(), userInfoBo.getOperateTerminal());
        } finally {
            sharingRelationLockRedisDao.unlockSharingRelation(shareId, lockValue);
        }
    }

    @Override
    public void sharingRecovery(UserInfoBo userInfoBo, String shareId, Long recoveryNum, Boolean shareNumUnlimited) throws CommonException {
        String lockValue = sharingRelationLockRedisDao.lockSharingRelation(shareId);
        try {
            ElefaceSharingRelation sharingRelation = getSharingRelationByShareId(shareId);
            if (!checkOwnerUserSharingRelation(sharingRelation, userInfoBo)
                    && !ElefaceSharingRelationConstant.STATUS_CANCELED.equals(sharingRelation.getStatus())) {
                throw new ElefaceSharingNotExistException();
            }
            long shareNum;
            if (BooleanUtils.isTrue(shareNumUnlimited)) {
                // 修改为无限分享
                shareNum = ElefaceSharingRelationConstant.SHARE_NUM_UNLIMITED;
            } else {
                // 当前分享数量 = 使用数量
                shareNum = recoveryNum;
            }
            Long lastShareNum = sharingRelation.getShareNum();
            elefaceSharingRelationDao.updateStatusAndShareNum(shareId, ElefaceSharingRelationConstant.STATUS_VALID, shareNum);
            sharingRelation.setShareNum(shareNum);
            elefaceSharingRelationOperateService.saveElefaceSharingRelationOperateLog
                    (sharingRelation, lastShareNum, ElefaceSharingRelationOperateConstant.RECOVERY, userInfoBo.getMallOperate(), userInfoBo.getOperateTerminal());
        } finally {
            sharingRelationLockRedisDao.unlockSharingRelation(shareId, lockValue);
        }
    }

    @Override
    public void saveShareMemo(UserInfoBo userInfoBo, String shareId, String shareMemo, String salesman) throws CommonException {

        boolean hasUpdate = false;
        String lockValue = sharingRelationLockRedisDao.lockSharingRelation(shareId);

        ElefaceSharingRelation sharingRelation = null;
        String originalShareMemo = null;
        String originalSalesman = null;
        try {

            sharingRelation = getSharingRelationByShareId(shareId);
            if (!checkOwnerUserSharingRelation(sharingRelation, userInfoBo)) {
                throw new ElefaceSharingNotExistException();
            }

            // 记录修改前的备注和业务员信息，用于操作日志
            originalShareMemo = sharingRelation.getShareMemo();
            originalSalesman = sharingRelation.getSalesman();

            if (StringUtils.isNotEmpty(shareMemo) && !shareMemo.equals(sharingRelation.getShareMemo())) {
                hasUpdate = true;
            } else {
                shareMemo = sharingRelation.getShareMemo();
            }

            if (StringUtils.isNotEmpty(salesman) && !salesman.equals(sharingRelation.getSalesman())) {
                hasUpdate = true;
            } else {
                salesman = sharingRelation.getSalesman();
            }

            if (!hasUpdate) {
                return;
            }

            // 执行备注修改操作
            elefaceSharingRelationDao.updateShareMemoOrSalesmanByShareId(shareId, shareMemo, salesman);

            // 更新sharingRelation对象中的备注信息，用于操作日志记录
            sharingRelation.setShareMemo(shareMemo);
            sharingRelation.setSalesman(salesman);

            // 记录面单分享记录的操作日志
            elefaceSharingRelationOperateService.saveElefaceSharingRelationOperateLog(
                    sharingRelation,
                    sharingRelation.getShareNum(), // 当前分享数量作为lastShareNum
                    ElefaceSharingRelationOperateConstant.MEMO_UPDATE,
                    userInfoBo.getMallOperate(),
                    userInfoBo.getOperateTerminal()
            );

            LOGGER.logInfo(userInfoBo.getSessionNick(), shareId,
                    String.format("面单备注修改操作日志已记录 - 原备注: [%s] -> 新备注: [%s], 原业务员: [%s] -> 新业务员: [%s]",
                            originalShareMemo, shareMemo, originalSalesman, salesman));

            ShareRelationChangeRequestProto proto = new ShareRelationChangeRequestProto();
            proto.setShareId(shareId);
            proto.setNewShareMemo(shareMemo);
            proto.setNewSalesman(salesman);
            printMessageSendService.pushRelationChangeMessage(proto);

        } finally {
            sharingRelationLockRedisDao.unlockSharingRelation(shareId, lockValue);
        }

    }

    @Override
    public List<WaybillBranchShippAddressDTO> multiBranchProxyListGet(ElefaceSharingProxyListRequest request, UserInfoBo userInfo) throws CommonException {

        String sellerNick = StringUtils.isNotEmpty(userInfo.getSellerNick()) ? userInfo.getSellerNick() : userInfo.getSessionNick();
        String storeId = StringUtils.isNotEmpty(userInfo.getStoreId()) ? userInfo.getStoreId() : userInfo.getSessionStoreId();
        String appName = StringUtils.isNotEmpty(userInfo.getAppName()) ? userInfo.getAppName() : userInfo.getSessionAppName();
        String sellerId = StringUtils.isNotEmpty(userInfo.getSellerId()) ? userInfo.getSellerId() : userInfo.getSessionSellerId();

        List<WaybillBranchShippAddressDTO> waybillBranchShippAddressDTOList = Lists.newArrayList();


        ElefaceSharingRelationQueryBo queryBo = new ElefaceSharingRelationQueryBo();
        queryBo.setStatus(ElefaceSharingRelationConstant.STATUS_VALID);
        // 获取代理的
        queryBo.setShareType(1);
        List<ElefaceSharingRelation> relationList = elefaceSharingRelationDao.searchByTargetUserAndQuery(sellerId, storeId, appName, queryBo);

        if (CollectionUtils.isEmpty(relationList)) {
            return Collections.emptyList();
        }

        List<ElefaceSharingRelation> sharingRelations = elefaceSharingRelationDao.countByProxyRelation(sellerId, storeId, appName);
        if (CollectionUtils.isNotEmpty(sharingRelations)) {
            Map<String, ElefaceSharingRelation> soleKeyToShare = sharingRelations.stream().collect(Collectors.toMap(ElefaceSharingRelation::getSoleKey, Function.identity(), (v1, v2) -> v1));
            relationList.forEach(s -> {
                ElefaceSharingRelation e = soleKeyToShare.get(s.getSoleKey());
                if (e == null) {
                    return;
                }

                Long userNum = ShardingUtils.addLongs(s.getUsedNum(), e.getUsedNum());
                Long cancelNum = ShardingUtils.addLongs(s.getCancelNum(), e.getCancelNum());
                s.setUsedNum(userNum);
                s.setCancelNum(cancelNum);
            });

        }
        addBranchesShippAddressByRelation(waybillBranchShippAddressDTOList, relationList, request.getIsIncludeUnAvailable(), sellerNick);

        return waybillBranchShippAddressDTOList;
    }

    @Override
    public MultiProxySharingToOthersTargetListResponse multiProxySharingToOthersTargetList(UserInfoBo userInfoBo, MultiProxySharingToOthersTargetListRequest request) {

        MultiProxySharingToOthersTargetListResponse response = new MultiProxySharingToOthersTargetListResponse();

        ElefaceSharingRelationQueryBo queryBo = new ElefaceSharingRelationQueryBo();
        queryBo.setTargetSearchStr(request.getTargetSellerSearch());
        int count = elefaceSharingRelationDao.countByProxyUserAndGroupByTarget(userInfoBo.getSellerId(), userInfoBo.getStoreId(), userInfoBo.getAppName(), queryBo);
        response.setTotalResult(count);

        Integer offset = request.getPage() - 1;
        Integer limit = request.getPageSize();
        queryBo.setOffset(offset);
        queryBo.setLimit(limit);
        if (count > 0) {
            List<TargetPrintUserInfo> relationListTarget = elefaceSharingRelationDao.searchByProxyUserAndGroupByTarget(userInfoBo.getSellerId(), userInfoBo.getStoreId(), userInfoBo.getAppName(), queryBo);
            response.setTargetSellerList(relationListTarget);
        } else {
            response.setTargetSellerList(Collections.emptyList());
        }
        return response;
    }
}
