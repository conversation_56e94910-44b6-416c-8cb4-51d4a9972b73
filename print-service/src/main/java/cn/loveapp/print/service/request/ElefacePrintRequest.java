package cn.loveapp.print.service.request;

import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

import cn.loveapp.print.common.dto.RecipientDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 电子面单打印请求参数
 *
 * <AUTHOR>
 */
@Data
@ApiModel(value = "电子面单打印请求参数")
public class ElefacePrintRequest extends BasePrintRequest {

    /**
     * 面单服务商 {@link cn.loveapp.print.common.constant.ElefaceProviderConstant}
     */
    @ApiModelProperty(value = "面单服务商")
    private String provider;

    /**
     * 物流公司Code
     */
    @ApiModelProperty(value = "物流公司Code", required = true)
    @NotEmpty
    private String cpCode;

    /**
     * 物流公司
     */
    @ApiModelProperty(value = "物流公司", required = true)
    @NotEmpty
    private String logisticsCompany;

    /**
     * 面单号，子母件模式下为子单号
     */
    @ApiModelProperty(value = "面单号，子母件模式下为子单号", required = true)
    @NotEmpty
    private String waybillCode;

    /**
     * 字母件的子单号
     */
    @ApiModelProperty(value = "字母件的子单号")
    private String childWaybillCode;

    /**
     * 收件人信息
     */
    @ApiModelProperty(value = "收件人信息", required = true)
    @NotNull
    @Valid
    private RecipientDTO recipient;

    /**
     * 发货人信息
     */
    @ApiModelProperty(value = "发货人信息")
    private RecipientDTO sender;

    /**
     * 打印内容
     */
    @ApiModelProperty(value = "打印内容", required = true)
    @NotEmpty
    private String printData;

    /**
     * 自定义区域打印内容
     */
    @ApiModelProperty(value = "自定义区域打印内容")
    private String customData;

    /**
     * 子品牌
     */
    @ApiModelProperty(value = "子品牌")
    private String brandCode;

    /**
     * 交易（TAO） 扩展字段
     */
    @ApiModelProperty(value = "交易（TAO） 扩展字段")
    private String externalInfo;

    /**
     * 真实快递公司code
     */
    @ApiModelProperty(value = "真实快递公司code")
    private String realCpCode;

    /**
     * 物流模板名称
     */
    @ApiModelProperty(value = "物流模板名称")
    private String logisticsTemplateName;

    /**
     * 电子面单版本号，1-默认值旧版电子面单 2-新版电子面单 (XHS使用)
     */
    @ApiModelProperty(value = "电子面单版本号")
    private Integer billVersion;

}
