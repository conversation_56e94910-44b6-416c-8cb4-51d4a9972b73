package cn.loveapp.print.service.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@ApiModel("流水号获取响应体")
public class SerialGetResponseDTO {
    /**
     * 流水号
     */
    @ApiModelProperty(value = "流水号")
    private String serial;

    /**
     * 合单订单的主单tid
     */
    @ApiModelProperty(value = "合单订单的主单tid")
    private String mergeTid;

    /**
     * 订单列表
     */
    @ApiModelProperty(value = "订单列表")
    private List<String> tidList;

    public SerialGetResponseDTO(String serial) {
        this.serial = serial;
    }
}
