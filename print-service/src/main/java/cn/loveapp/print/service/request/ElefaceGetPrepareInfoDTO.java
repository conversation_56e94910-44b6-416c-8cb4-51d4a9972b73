package cn.loveapp.print.service.request;

import cn.loveapp.print.service.annotation.TradeTypeValidation;
import cn.loveapp.print.service.dto.TargetUserInfoDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 面单获取准备数据
 *
 * @program: print-services-group
 * @description: 面单获取准备数据
 * @author: z<PERSON><PERSON><PERSON><PERSON>
 * @create: 2022/12/22 18:44
 **/
@Data
@ApiModel(value = "面单获取准备数据")
public class ElefaceGetPrepareInfoDTO extends TargetUserInfoDTO {

    /**
     * 电子面单请求ID
     */
    @ApiModelProperty(value = "电子面单请求ID", required = true)
    @NotNull
    private String objectId;

    /**
     * 电子面单云打印接口请求参数
     */
    @ApiModelProperty(value = "电子面单云打印接口请求参数", required = true)
    @NotEmpty
    private String paramWaybillCloudPrintApplyNewRequest;

    /**
     * 打印的订单信息
     */
    @ApiModelProperty(value = "打印的订单信息", required = true)
    @NotEmpty
    @Valid
    private List<PrintTradeInfoDTO> tradeInfoList;

    /**
     * 合单订单的主单tid
     */
    @ApiModelProperty(value = "合单订单的主单tid")
    private String mergeTid;

    /**
     * 订单类型
     */
    @ApiModelProperty(value = "订单类型", required = true)
    @NotNull
    @TradeTypeValidation
    private Integer tradeType;

    /**
     * 流水号
     */
    @ApiModelProperty(value = "流水号")
    private String serial;

    /**
     * 打印模版url(多平台必传，淘宝可为空)
     */
    @ApiModelProperty(value = "打印模版url(多平台必传，淘宝可为空)")
    private String templateURL;

    /**
     * 物流模版id
     */
    @ApiModelProperty("物流模版id")
    private String logisticsTemplateId;

    /**
     * 电子面单版本号，1-默认值旧版电子面单 2-新版电子面单 (XHS使用)
     */
    @ApiModelProperty(value = "电子面单版本号")
    private Integer billVersion;

}
