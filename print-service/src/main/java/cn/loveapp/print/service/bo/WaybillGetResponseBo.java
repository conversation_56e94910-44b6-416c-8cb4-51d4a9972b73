package cn.loveapp.print.service.bo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 电子面单取号 response Bo
 *
 * <AUTHOR>
 */
@Data
public class WaybillGetResponseBo {

    /**
     * 面单的服务商
     */
    private String provider;

    /**
     * 面单请求id
     */
    private String objectId;

    /**
     * 快运字母单的母单号
     */
    private String parentWaybillCode;

    /**
     * 面单号（快运下为子单号）
     */
    private String waybillCode;

    /**
     * 打印内容
     */
    private String printData;

    private boolean isSuccess;

    private String errMsg;

    private ApiErrorBo apiError;

    /**
     * 物流公司code
     */
    private String realCpCode;

    /**
     * 电子面单订单id(微信视频号专有字段,用于取号/取消取号)
     */
    private String waybillOrderId;

}
