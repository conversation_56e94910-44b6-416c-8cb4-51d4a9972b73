package cn.loveapp.print.service.bo;

import cn.loveapp.common.dto.UserSessionInfo;
import cn.loveapp.print.common.dto.TargetSellerInfo;
import cn.loveapp.print.common.dto.UserMultiInfoDTO;
import cn.loveapp.print.service.utils.ConvertUtil;

import cn.loveapp.print.service.dto.TargetUserInfoDTO;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * 打印业务的用户信息BO
 *
 * <AUTHOR>
 */
@Data
public class UserInfoBo {
    /**
     * 用户Nick
     */
    private String sellerNick;
    /**
     * 商家Id
     */
    private String sellerId;
    /**
     * 平台Id TAO、PDD
     */
    private String storeId;
    /**
     * 应用名称 trade guanDian
     */
    private String appName;

    /**
     * 操作用户
     */
    private String operator;

    /**
     * 操作用户的storeId
     */
    private String operatorStoreId;

    /**
     * 操作用户的登录端
     */
    private String operateTerminal;

    /**
     * 当前session用户的nick
     */
    private String sessionNick;

    /**
     * 当前session用户商家id
     */
    private String sessionSellerId;

    /**
     * 当前session用户的storeId
     */
    private String sessionStoreId;

    /**
     * 当前session用户的appName
     */
    private String sessionAppName;

    /**
     * 当前session用户的子账户nick
     */
    private String sessionSubNick;

    /**
     * 当前session用户的店铺名
     */
    private String sessionMallName;

    private Boolean isMultiShops = false;

    public UserInfoBo() {}

    public static UserInfoBo of(UserSessionInfo sessionInfo) {
        UserInfoBo userInfoBo = new UserInfoBo();
        userInfoBo.setSellerNick(sessionInfo.getNick());
        userInfoBo.setSellerId(sessionInfo.getSellerId());
        userInfoBo.setStoreId(sessionInfo.getStoreId());
        userInfoBo.setAppName(sessionInfo.getAppName());
        userInfoBo.setOperator(
                !StringUtils.isEmpty(sessionInfo.getSubNick()) ? sessionInfo.getSubNick() : sessionInfo.getNick());
        userInfoBo.setOperatorStoreId(sessionInfo.getStoreId());
        userInfoBo.setOperateTerminal(sessionInfo.getLoginTerminal());

        userInfoBo.setSessionNick(sessionInfo.getNick());
        userInfoBo.setSessionSubNick(sessionInfo.getSubNick());
        userInfoBo.setSessionSellerId(sessionInfo.getSellerId());
        userInfoBo.setSessionStoreId(sessionInfo.getStoreId());
        userInfoBo.setSessionAppName(sessionInfo.getAppName());
        userInfoBo.setSessionMallName(sessionInfo.getMallName());
        userInfoBo.setIsMultiShops(sessionInfo.getIsMultiShops());
        return userInfoBo;
    }

    public static UserInfoBo of(UserSessionInfo sessionInfo, TargetUserInfoDTO targetUserInfoDTO) {
        UserInfoBo userInfoBo = of(sessionInfo);
        if (null != targetUserInfoDTO) {
            // 多店替换用户信息
            userInfoBo.setStoreId(targetUserInfoDTO.getStoreId());
            userInfoBo.setSellerId(targetUserInfoDTO.getSellerId());
            userInfoBo.setSellerNick(targetUserInfoDTO.getSellerNick());
            userInfoBo.setAppName(targetUserInfoDTO.getAppName());
        }
        return userInfoBo;
    }

    public static List<UserInfoBo> of(UserSessionInfo sessionInfo, UserMultiInfoDTO userMultiInfoDTO) {
        List<UserInfoBo> userInfoBos = new ArrayList<>();
        if (userMultiInfoDTO != null && userMultiInfoDTO.getTargetSellerList() != null) {
            for (TargetSellerInfo targetSellerInfo : userMultiInfoDTO.getTargetSellerList()) {
                UserInfoBo userInfoBo = of(sessionInfo);
                userInfoBo.setStoreId(targetSellerInfo.getTargetStoreId());
                userInfoBo.setSellerId(targetSellerInfo.getTargetSellerId());
                userInfoBo.setSellerNick(targetSellerInfo.getTargetNick());
                userInfoBo.setAppName(targetSellerInfo.getTargetAppName());
                userInfoBos.add(userInfoBo);
            }
        } else {
            UserInfoBo userInfoBo = of(sessionInfo);
            userInfoBos.add(userInfoBo);
        }
        return userInfoBos;
    }

    public static UserInfoBo of(List<UserInfoBo> userInfoBos, TargetUserInfoDTO targetSellerInfo) {
        for (UserInfoBo userInfoBo : userInfoBos) {
            if (equalBetween(userInfoBo, targetSellerInfo)) {
                return userInfoBo;
            }
        }
        return null;
    }


    private static boolean equalBetween(UserInfoBo userInfoBo, TargetUserInfoDTO targetSellerInfo) {
        if (userInfoBo == null || targetSellerInfo == null) {
            return false;
        }
        String sellerId = userInfoBo.getSellerId();
        String storeId = userInfoBo.getStoreId();

        return !Objects.isNull(sellerId) && sellerId.equals(targetSellerInfo.getSellerId())
            && !Objects.isNull(storeId) && storeId.equals(targetSellerInfo.getStoreId());
    }

    public String getMallOperate(){
        String mallOperate = ConvertUtil.findFirstNotNull(sessionMallName, sessionNick);

        // 没有店铺名或nick，取操作人
        if (StringUtils.isEmpty(mallOperate)) {
            return operator;
        }

        // 子账户登录
        if (StringUtils.isNotEmpty(sessionSubNick)) {
            mallOperate += ":" + sessionSubNick;
        }

        return mallOperate;
    }
}
