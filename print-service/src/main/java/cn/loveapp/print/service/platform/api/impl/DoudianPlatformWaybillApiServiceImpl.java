package cn.loveapp.print.service.platform.api.impl;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSON;
import com.doudian.api.domain.Netsite;
import com.doudian.api.request.LogisticsCancelOrderRequest;
import com.doudian.api.request.LogisticsListShopNetsiteRequest;
import com.doudian.api.request.LogisticsNewCreateOrderRequest;
import com.doudian.api.request.LogisticsWaybillApplyRequest;
import com.doudian.api.response.LogisticsCancelOrderResponse;
import com.doudian.api.response.LogisticsListShopNetsiteResponse;
import com.doudian.api.response.LogisticsNewCreateOrderResponse;
import com.doudian.api.response.LogisticsWaybillApplyResponse;

import cn.loveapp.common.constant.CommonPlatformConstants;
import cn.loveapp.common.platformsdk.doudian.DoudianSDKService;
import cn.loveapp.common.utils.LoggerHelper;
import cn.loveapp.print.service.api.entity.AyWaybillApplySubscriptionInfo;
import cn.loveapp.print.service.api.entity.WaybillCloudPrintInfo;
import cn.loveapp.print.service.api.request.AyWaybillCancelRequest;
import cn.loveapp.print.service.api.request.AyWaybillGetRequest;
import cn.loveapp.print.service.api.request.AyWaybillSearchRequest;
import cn.loveapp.print.service.api.response.AyWaybillCancelResponse;
import cn.loveapp.print.service.api.response.AyWaybillGetResponse;
import cn.loveapp.print.service.api.response.AyWaybillSearchResponse;
import cn.loveapp.print.service.code.ErrorCode;
import cn.loveapp.print.service.platform.api.PlatformWaybillApiService;

/**
 * 电子面单api service 抖店实现类
 *
 * <AUTHOR>
 */
@Service
public class DoudianPlatformWaybillApiServiceImpl implements PlatformWaybillApiService {

    private static final LoggerHelper LOGGER = LoggerHelper.getLogger(DoudianPlatformWaybillApiServiceImpl.class);

    @Autowired
    private DoudianSDKService doudianSDKService;

    @Override
    public AyWaybillSearchResponse waybillSearch(AyWaybillSearchRequest request, String topSession, String platformId,
        String appName) {
        LogisticsListShopNetsiteRequest listShopNetsiteRequest = new LogisticsListShopNetsiteRequest();
        listShopNetsiteRequest.setLogisticsCode(StringUtils.trimToEmpty(request.getCpCode()));
        LogisticsListShopNetsiteResponse listShopNetsiteResponse =
            doudianSDKService.execute(listShopNetsiteRequest, topSession, appName);
        AyWaybillSearchResponse response = new AyWaybillSearchResponse();
        response.init(listShopNetsiteResponse);
        if (!response.isSuccess()) {
            // 失败直接返回
            return response;
        }
        if (CollectionUtils.isNotEmpty(listShopNetsiteResponse.getData().getNetsites())) {
            Map<String, List<Netsite>> netsitesGroupByCompany = listShopNetsiteResponse.getData().getNetsites().stream()
                .collect(Collectors.groupingBy(item -> item.getCompany() + ":" + item.getCompanyType()));
            response.setWaybillApplySubscriptionCols(netsitesGroupByCompany.values().stream().map(
                item -> AyWaybillApplySubscriptionInfo.of(item, item.get(0).getCompany(), item.get(0).getCompanyType()))
                .collect(Collectors.toList()));
        } else {
            response.setWaybillApplySubscriptionCols(Collections.emptyList());
        }
        return response;
    }

    @Override
    public AyWaybillGetResponse waybillGet(AyWaybillGetRequest request, String topSession, String platformId,
        String appName) {
        AyWaybillGetResponse response = new AyWaybillGetResponse();
        LogisticsNewCreateOrderRequest logisticsNewCreateOrderRequest = null;
        try {
            logisticsNewCreateOrderRequest = JSON.parseObject(request.getParamWaybillCloudPrintApplyNewRequest(),
                LogisticsNewCreateOrderRequest.class);
        } catch (Exception e) {
            LOGGER.logError("非法的参数：paramWaybillCloudPrintApplyNewRequest", e);
            response.setErrorCode(ErrorCode.BaseCode.PARAMS_ERR.getCode().toString());
            response.setMsg(
                "非法的参数：paramWaybillCloudPrintApplyNewRequest = " + request.getParamWaybillCloudPrintApplyNewRequest());
            return response;
        }
        String logisticsCode = logisticsNewCreateOrderRequest.getLogisticsCode();
        LogisticsNewCreateOrderResponse logisticsNewCreateOrderResponse =
            doudianSDKService.execute(logisticsNewCreateOrderRequest, topSession, appName);
        response.init(logisticsNewCreateOrderResponse);
        if (!response.isSuccess()) {
            return response;
        }
        if (CollectionUtils.isNotEmpty(logisticsNewCreateOrderResponse.getData().getErrInfos())) {
            // 这里如果有面单取号失败了, 先给他返回失败信息
            LogisticsNewCreateOrderResponse.ErrInfo errInfo =
                logisticsNewCreateOrderResponse.getData().getErrInfos().get(0);
            response.setErrorCode(errInfo.getErrCode());
            response.setMsg(errInfo.getErrMsg());
            return response;
        }

        List<String> trackNos = new ArrayList<>();
        Map<String, String> subWaybill2Parent = new HashMap<>();

        logisticsNewCreateOrderResponse.getData().getEbillInfos().forEach(item -> {
            String trackNo = item.getTrackNo();
            if (item.getSubWaybillCodes() != null) {
                for (String subTrackNo : StringUtils.split(item.getSubWaybillCodes(), ",")) {
                    trackNos.add(subTrackNo);
                    subWaybill2Parent.put(subTrackNo, trackNo);
                }
            }
            trackNos.add(trackNo);
        });

        if (CollectionUtils.isEmpty(trackNos)) {
            return response;
        }

        LogisticsWaybillApplyRequest logisticsWaybillApplyRequest = new LogisticsWaybillApplyRequest();
        logisticsWaybillApplyRequest
                .setWaybillApplies(trackNos.stream().map(trackNo -> {
                    LogisticsWaybillApplyRequest.WaybillApply waybillApply =
                            new LogisticsWaybillApplyRequest.WaybillApply();
                    waybillApply.setTrackNo(trackNo);
                    waybillApply.setLogisticsCode(logisticsCode);
                    return waybillApply;
                }).collect(Collectors.toList()));

        LogisticsWaybillApplyResponse logisticsWaybillApplyResponse =
            doudianSDKService.execute(logisticsWaybillApplyRequest, topSession, appName);
        response.init(logisticsWaybillApplyResponse);;
        if (!response.isSuccess()) {
            return response;
        }
        if (CollectionUtils.isNotEmpty(logisticsWaybillApplyResponse.getData().getErrInfos())) {
            // 这里如果有面单取号失败了, 先给他返回失败信息
            LogisticsWaybillApplyResponse.ErrInfo errInfo =
                logisticsWaybillApplyResponse.getData().getErrInfos().get(0);
            response.setErrorCode(errInfo.getErrCode());
            response.setMsg(errInfo.getErrMsg());
            return response;
        }
        response.setWaybillCloudPrintInfoList(logisticsWaybillApplyResponse.getData().getWaybillInfos().stream()
            .map(waybillInfo -> WaybillCloudPrintInfo.of(waybillInfo, subWaybill2Parent)).collect(Collectors.toList()));
        return response;
    }

    @Override
    public AyWaybillCancelResponse waybillCancel(AyWaybillCancelRequest request, String topSession, String platformId,
        String appName) {
        AyWaybillCancelResponse response = new AyWaybillCancelResponse();
        LogisticsCancelOrderRequest logisticsCancelOrderRequest = new LogisticsCancelOrderRequest();
        logisticsCancelOrderRequest.setLogisticsCode(request.getCpCode());
        logisticsCancelOrderRequest.setTrackNo(request.getWaybillCode());
        LogisticsCancelOrderResponse logisticsCancelOrderResponse =
            doudianSDKService.execute(logisticsCancelOrderRequest, topSession, appName);
        response.init(logisticsCancelOrderResponse);
        if (!response.isSuccess()) {
            return response;
        }
        response.setCancelResult(logisticsCancelOrderResponse.getData().getCancelResult().getSuccess());
        return response;
    }

    @Override
    public String getPlatformId() {
        return CommonPlatformConstants.PLATFORM_DOUDIAN;
    }


}
