package cn.loveapp.print.service.config;

import cn.loveapp.common.constant.CommonAppConstants;
import com.google.common.collect.Lists;
import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 打印服务相关配置项
 * @date 2022/8/29 11:21 上午
 */
@Configuration
@Data
public class PrintConfig {

    /**
     * 需要请求ark host的平台及应用名
     */
    @Value("${pdd.ark.api.appNames:" + CommonAppConstants.APP_GUANDIAN + "," + CommonAppConstants.APP_TRADE + "}")
    private List<String> arkAPiAppNames = Lists.newArrayList();
}
