package cn.loveapp.print.service.service.impl;

import cn.loveapp.print.common.dto.TargetSellerInfo;

import cn.loveapp.shops.api.request.ShopsGetRequest;
import cn.loveapp.shops.api.response.ShopsResponse;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSON;

import cn.loveapp.common.utils.LoggerHelper;
import cn.loveapp.common.web.CommonApiResponse;
import cn.loveapp.common.web.CommonApiStatus;
import cn.loveapp.print.service.service.ShopsService;
import cn.loveapp.shops.api.request.ShopsAuthRequest;
import cn.loveapp.shops.api.service.ShopsRpcInnerApiService;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 多店服务
 *
 * <AUTHOR>
 */
@Service
public class ShopsServiceImpl implements ShopsService {

    private final static LoggerHelper LOGGER = LoggerHelper.getLogger(SerialServiceImpl.class);

    @Autowired
    private ShopsRpcInnerApiService shopsRpcInnerApiService;

    @Override
    public boolean shopsAuth(String sellerNick, String storeId, String appName, String targetNick, String targetStoreId, String targetAppName) {
        ShopsAuthRequest request = new ShopsAuthRequest();
        request.setSellerNick(sellerNick);
        request.setStoreId(storeId);
        request.setAppName(appName);
        request.setTargetNick(targetNick);
        request.setTargetStoreId(targetStoreId);
        request.setTargetAppName(targetAppName);
        return shopsAuthExcute(request);
    }


    @Override
    public boolean multiShopsAuth(String sellerNick, String storeId, String appName, List<TargetSellerInfo> targetSellerList) {
        if (CollectionUtils.isEmpty(targetSellerList)) {
            LOGGER.logInfo(sellerNick, "", "需要鉴权的店铺集合为空");
            return true;
        }
        List<ShopsAuthRequest.TargetSeller> collect = targetSellerList.stream()
            .map(m -> new ShopsAuthRequest.TargetSeller(m.getTargetStoreId(), m.getTargetNick(), m.getTargetAppName()))
            .collect(Collectors.toList());
        ShopsAuthRequest request = new ShopsAuthRequest();
        request.setSellerNick(sellerNick);
        request.setStoreId(storeId);
        request.setAppName(appName);
        request.setTargetSellerList(collect);
        return shopsAuthExcute(request);
    }


    @Override
    public ShopsResponse shopGet(String sellerId, String sellerNick, String storeId, String appName, boolean includeMembers) {
        long start = System.currentTimeMillis();
        try {
            ShopsGetRequest request = new ShopsGetRequest();
            request.setAppName(appName);
            request.setStoreId(storeId);
            request.setSellerNick(sellerNick);
            request.setSellerId(sellerId);
            request.setIncludeMembers(includeMembers);
            LOGGER.logInfo("调shops多店鉴权, request=" + JSON.toJSONString(request));
            CommonApiResponse<ShopsResponse> response = shopsRpcInnerApiService.shopsGet(request);
            LOGGER.logInfo("调shops多店鉴权, response=" + JSON.toJSONString(response));
            if (response != null && response.getCode().equals(CommonApiStatus.Success.code()) && null == response.getSubCode()) {
                return response.getBody();
            }
        } catch (Exception e) {
            LOGGER.logError("网络异常，获取店铺群失败", e);
        } finally {
            long time = System.currentTimeMillis() - start;
            LOGGER.logInfo("获取店铺群 请求耗时 " + time);
        }
        return null;

    }


    private boolean shopsAuthExcute(ShopsAuthRequest request) {
        long start = System.currentTimeMillis();
        try {
            LOGGER.logInfo("调shops多店鉴权, request=" + JSON.toJSONString(request));
            CommonApiResponse response = shopsRpcInnerApiService.shopsAuth(request);
            LOGGER.logInfo("调shops多店鉴权, response=" + JSON.toJSONString(response));
            if (response != null && response.getCode().equals(CommonApiStatus.Success.code()) && null == response.getSubCode()) {
                return true;
            }
        } catch (Exception e) {
            LOGGER.logError("网络异常，多店鉴权失败", e);
        } finally {
            long time = System.currentTimeMillis() - start;
            LOGGER.logInfo("shops多店鉴权 请求耗时 " + time);
        }
        return false;
    }
}
