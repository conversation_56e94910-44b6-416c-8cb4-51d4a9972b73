# 测试用例问题排查指南

## 常见问题及解决方案

### 1. 编译错误

#### 问题：找不到类或方法
```
java: cannot find symbol
```

**解决方案：**
- 检查导入语句是否正确
- 确认依赖的类是否存在
- 验证方法签名是否匹配

#### 问题：UserSessionInfo 字段设置错误
```
java: cannot find symbol method setSellerNick(String)
```

**解决方案：**
UserSessionInfo 使用 `setNick()` 而不是 `setSellerNick()`：
```java
// 错误
sessionInfo.setSellerNick("testUser");

// 正确
sessionInfo.setNick("testUser");
```

### 2. Mock 相关错误

#### 问题：Mock 对象未正确注入
```
NullPointerException at ...
```

**解决方案：**
确保使用正确的注解：
```java
@ExtendWith(MockitoExtension.class)
class MyTest {
    @Mock
    private MyService myService;
    
    @InjectMocks
    private MyController myController;
}
```

#### 问题：Mock 方法调用不匹配
```
Wanted but not invoked: myService.method(...)
```

**解决方案：**
检查参数匹配器：
```java
// 使用 any() 匹配器
verify(service).method(any(String.class));

// 或使用 eq() 精确匹配
verify(service).method(eq("expectedValue"));
```

### 3. 测试数据问题

#### 问题：UserInfoBo 创建失败
**解决方案：**
确保 UserSessionInfo 设置了必要字段：
```java
UserSessionInfo sessionInfo = new UserSessionInfo();
sessionInfo.setNick("testUser");           // 必需
sessionInfo.setSellerId("testSellerId");   // 必需
sessionInfo.setStoreId("TAO");             // 必需
sessionInfo.setAppName("trade");           // 必需
sessionInfo.setLoginTerminal("WEB");       // 推荐
sessionInfo.setMallName("测试店铺");        // 推荐

UserInfoBo userInfoBo = UserInfoBo.of(sessionInfo, null);
```

### 4. 断言错误

#### 问题：集合比较失败
```
Expected: [1, 2, 3]
Actual: [1, 2, 3, 4]
```

**解决方案：**
使用正确的集合断言：
```java
// 检查集合内容
assertThat(actualList).containsExactlyInAnyOrder(1, 2, 3);

// 检查集合大小
assertEquals(3, actualList.size());

// 检查是否包含特定元素
assertTrue(actualList.contains(expectedElement));
```

### 5. 异步测试问题

#### 问题：时间相关的测试不稳定
**解决方案：**
使用时间范围验证：
```java
LocalDateTime before = LocalDateTime.now();
// 执行操作
LocalDateTime after = LocalDateTime.now();

// 验证时间在合理范围内
assertTrue(operateTime.isAfter(before.minusSeconds(1)));
assertTrue(operateTime.isBefore(after.plusSeconds(1)));
```

## 逐步调试方法

### 1. 从简单测试开始
```bash
# 先运行最简单的常量测试
mvn test -Dtest=ElefaceSharingRelationOperateConstantTest

# 然后运行服务层测试
mvn test -Dtest=ElefaceAccountServiceImplTest

# 最后运行复杂的集成测试
mvn test -Dtest=MemoUpdateOperateLogIntegrationTest
```

### 2. 单个测试方法调试
```bash
# 运行特定测试方法
mvn test -Dtest=ElefaceAccountServiceImplTest#testSaveShareMemo_Success

# 查看详细输出
mvn test -Dtest=ElefaceAccountServiceImplTest -X
```

### 3. 使用调试模式
```bash
# 启用调试模式
mvn test -Dtest=ElefaceAccountServiceImplTest -Dmaven.surefire.debug

# 然后在IDE中连接到端口5005进行调试
```

## 测试环境检查

### 1. Maven 依赖
确保以下依赖存在：
```xml
<dependency>
    <groupId>org.junit.jupiter</groupId>
    <artifactId>junit-jupiter</artifactId>
    <scope>test</scope>
</dependency>
<dependency>
    <groupId>org.mockito</groupId>
    <artifactId>mockito-junit-jupiter</artifactId>
    <scope>test</scope>
</dependency>
```

### 2. Java 版本
确保使用兼容的 Java 版本（建议 Java 8+）

### 3. IDE 配置
- 确保 IDE 识别测试目录
- 检查注解处理器是否启用
- 验证 Lombok 插件是否安装

## 常用调试命令

```bash
# 清理并重新编译
mvn clean compile test-compile

# 只编译测试代码
mvn test-compile

# 运行所有测试并生成报告
mvn test surefire-report:report

# 跳过测试编译错误继续运行
mvn test -Dmaven.test.failure.ignore=true

# 并行运行测试
mvn test -T 4

# 运行测试并生成覆盖率报告
mvn test jacoco:report
```

## 修复后的主要变更

1. **UserSessionInfo 字段名修正**：
   - `setSellerNick()` → `setNick()`
   - 添加了 `setLoginTerminal()` 和 `setMallName()`

2. **Mock 对象简化**：
   - 集成测试中直接 Mock 服务接口而不是实现类
   - 减少了复杂的依赖注入

3. **断言优化**：
   - 使用更简单的验证方式
   - 避免复杂的对象内容验证

4. **测试数据完整性**：
   - 确保所有必需字段都有值
   - 使用合理的测试数据

## 如果仍有问题

1. 检查具体的错误信息
2. 确认项目的 Spring Boot 和依赖版本
3. 查看是否有自定义的测试配置
4. 考虑环境差异（本地 vs CI/CD）

记住：测试应该简单、可靠、易于维护。如果测试过于复杂，考虑简化测试逻辑或拆分为更小的测试单元。
