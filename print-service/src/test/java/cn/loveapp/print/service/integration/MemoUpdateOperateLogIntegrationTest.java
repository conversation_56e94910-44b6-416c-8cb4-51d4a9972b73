package cn.loveapp.print.service.integration;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.InOrder;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import cn.loveapp.common.constant.CommonAppConstants;
import cn.loveapp.common.constant.CommonPlatformConstants;
import cn.loveapp.common.dto.UserSessionInfo;
import cn.loveapp.print.service.bo.UserInfoBo;
import cn.loveapp.print.service.constant.ElefaceSharingRelationConstant;
import cn.loveapp.print.service.constant.ElefaceSharingRelationOperateConstant;
import cn.loveapp.print.service.dao.print.ElefaceSharingRelationDao;
import cn.loveapp.print.service.dao.print.ElefaceSharingRelationOperateLogDao;
import cn.loveapp.print.service.dao.redis.ElefaceSharingRelationLockRedisDao;
import cn.loveapp.print.service.entity.ElefaceSharingRelation;
import cn.loveapp.print.service.entity.ElefaceSharingRelationOperateLog;
import cn.loveapp.print.service.service.ElefaceSharingRelationOperateService;
import cn.loveapp.print.service.service.PrintMessageSendService;
import cn.loveapp.print.service.service.impl.ElefaceAccountServiceImpl;
import cn.loveapp.print.service.service.impl.ElefaceSharingRelationOperateServiceImpl;

/**
 * 面单备注修改操作日志集成测试
 * 测试从备注修改到日志记录的完整流程
 *
 * <AUTHOR>
 */
@ExtendWith(MockitoExtension.class)
public class MemoUpdateOperateLogIntegrationTest {

    // 被测试的服务
    @InjectMocks
    private ElefaceAccountServiceImpl elefaceAccountService;

    // Mock 依赖
    @Mock
    private ElefaceSharingRelationDao elefaceSharingRelationDao;
    @Mock
    private ElefaceSharingRelationLockRedisDao sharingRelationLockRedisDao;
    @Mock
    private ElefaceSharingRelationOperateService elefaceSharingRelationOperateService;
    @Mock
    private PrintMessageSendService printMessageSendService;

    // 测试数据
    private UserInfoBo userInfoBo;
    private ElefaceSharingRelation sharingRelation;
    private String shareId = "SHARE_INTEGRATION_TEST_001";
    private String lockValue = "lock_value_integration";

    @BeforeEach
    public void setUp() {
        // 注意：@InjectMocks 会自动注入依赖，这里只需要设置测试数据

        // 初始化用户信息
        UserSessionInfo sessionInfo = new UserSessionInfo();
        sessionInfo.setNick("integrationTestUser");
        sessionInfo.setSellerId("integrationTestSellerId");
        sessionInfo.setStoreId(CommonPlatformConstants.PLATFORM_TAO);
        sessionInfo.setAppName(CommonAppConstants.APP_TRADE);
        sessionInfo.setLoginTerminal("WEB");
        sessionInfo.setMallName("集成测试店铺");
        userInfoBo = UserInfoBo.of(sessionInfo);

        // 初始化面单分享关系
        sharingRelation = new ElefaceSharingRelation();
        sharingRelation.setShareId(shareId);
        sharingRelation.setOwnerSellerId("integrationTestSellerId");
        sharingRelation.setOwnerStoreId(CommonPlatformConstants.PLATFORM_TAO);
        sharingRelation.setOwnerAppName(CommonAppConstants.APP_TRADE);
        sharingRelation.setTargetSellerId("targetSellerId");
        sharingRelation.setTargetSellerNick("targetSellerNick");
        sharingRelation.setTargetStoreId("targetStoreId");
        sharingRelation.setTargetAppName("targetAppName");
        sharingRelation.setShareMemo("原始备注内容");
        sharingRelation.setSalesman("原始业务员");
        sharingRelation.setShareNum(200L);
        sharingRelation.setStatus(ElefaceSharingRelationConstant.STATUS_VALID);
    }

    /**
     * 测试完整的面单备注修改和日志记录流程
     */
    @Test
    public void testCompleteFlowFromMemoUpdateToLogRecord() throws Exception {
        // 准备测试数据
        String newShareMemo = "修改后的备注内容";
        String newSalesman = "修改后的业务员";

        // Mock 外部依赖
        when(sharingRelationLockRedisDao.lockSharingRelation(shareId)).thenReturn(lockValue);
        when(elefaceSharingRelationDao.queryByShareId(shareId)).thenReturn(sharingRelation);
        when(elefaceSharingRelationDao.updateShareMemoOrSalesmanByShareId(shareId, newShareMemo, newSalesman)).thenReturn(1);

        // 执行面单备注修改
        elefaceAccountService.saveShareMemo(userInfoBo, shareId, newShareMemo, newSalesman);

        // 验证数据库更新被调用
        verify(elefaceSharingRelationDao).updateShareMemoOrSalesmanByShareId(shareId, newShareMemo, newSalesman);

        // 验证操作日志服务被调用
        verify(elefaceSharingRelationOperateService).saveElefaceSharingRelationOperateLog(
                any(ElefaceSharingRelation.class),
                eq(200L),
                eq(ElefaceSharingRelationOperateConstant.MEMO_UPDATE),
                eq(userInfoBo.getMallOperate()),
                eq(userInfoBo.getOperateTerminal())
        );



        // 验证消息推送被调用
        verify(printMessageSendService).pushRelationChangeMessage(any());

        // 验证锁被释放
        verify(sharingRelationLockRedisDao).unlockSharingRelation(shareId, lockValue);
    }

    /**
     * 测试只修改备注的情况
     */
    @Test
    public void testMemoOnlyUpdate() throws Exception {
        // 准备测试数据 - 只修改备注，业务员保持不变
        String newShareMemo = "只修改备注";
        String sameSalesman = "原始业务员";

        // Mock 外部依赖
        when(sharingRelationLockRedisDao.lockSharingRelation(shareId)).thenReturn(lockValue);
        when(elefaceSharingRelationDao.queryByShareId(shareId)).thenReturn(sharingRelation);
        when(elefaceSharingRelationDao.updateShareMemoOrSalesmanByShareId(shareId, newShareMemo, sameSalesman)).thenReturn(1);

        // 执行面单备注修改
        elefaceAccountService.saveShareMemo(userInfoBo, shareId, newShareMemo, sameSalesman);

        // 验证操作日志服务被调用
        verify(elefaceSharingRelationOperateService).saveElefaceSharingRelationOperateLog(
                any(ElefaceSharingRelation.class),
                eq(200L),
                eq(ElefaceSharingRelationOperateConstant.MEMO_UPDATE),
                eq(userInfoBo.getMallOperate()),
                eq(userInfoBo.getOperateTerminal())
        );
    }

    /**
     * 测试只修改业务员的情况
     */
    @Test
    public void testSalesmanOnlyUpdate() throws Exception {
        // 准备测试数据 - 只修改业务员，备注保持不变
        String sameMemo = "原始备注内容";
        String newSalesman = "只修改业务员";

        // Mock 外部依赖
        when(sharingRelationLockRedisDao.lockSharingRelation(shareId)).thenReturn(lockValue);
        when(elefaceSharingRelationDao.queryByShareId(shareId)).thenReturn(sharingRelation);
        when(elefaceSharingRelationDao.updateShareMemoOrSalesmanByShareId(shareId, sameMemo, newSalesman)).thenReturn(1);

        // 执行面单备注修改
        elefaceAccountService.saveShareMemo(userInfoBo, shareId, sameMemo, newSalesman);

        // 验证操作日志服务被调用
        verify(elefaceSharingRelationOperateService).saveElefaceSharingRelationOperateLog(
                any(ElefaceSharingRelation.class),
                eq(200L),
                eq(ElefaceSharingRelationOperateConstant.MEMO_UPDATE),
                eq(userInfoBo.getMallOperate()),
                eq(userInfoBo.getOperateTerminal())
        );
    }

    /**
     * 测试操作日志插入失败的情况
     */
    @Test
    public void testLogInsertFailure() throws Exception {
        // 准备测试数据
        String newShareMemo = "新备注";
        String newSalesman = "新业务员";

        // Mock 外部依赖
        when(sharingRelationLockRedisDao.lockSharingRelation(shareId)).thenReturn(lockValue);
        when(elefaceSharingRelationDao.queryByShareId(shareId)).thenReturn(sharingRelation);
        when(elefaceSharingRelationDao.updateShareMemoOrSalesmanByShareId(shareId, newShareMemo, newSalesman)).thenReturn(1);

        // Mock 日志服务调用失败
        doThrow(new RuntimeException("操作日志记录失败")).when(elefaceSharingRelationOperateService)
                .saveElefaceSharingRelationOperateLog(any(), any(), any(), any(), any());

        // 执行面单备注修改 - 即使日志插入失败，主流程也应该成功
        assertDoesNotThrow(() -> {
            elefaceAccountService.saveShareMemo(userInfoBo, shareId, newShareMemo, newSalesman);
        });

        // 验证主要操作仍然被执行
        verify(elefaceSharingRelationDao).updateShareMemoOrSalesmanByShareId(shareId, newShareMemo, newSalesman);
        verify(printMessageSendService).pushRelationChangeMessage(any());
        verify(sharingRelationLockRedisDao).unlockSharingRelation(shareId, lockValue);
    }

    /**
     * 测试并发场景下的锁机制
     */
    @Test
    public void testConcurrentLockMechanism() throws Exception {
        // 准备测试数据
        String newShareMemo = "并发测试备注";
        String newSalesman = "并发测试业务员";

        // Mock 外部依赖
        when(sharingRelationLockRedisDao.lockSharingRelation(shareId)).thenReturn(lockValue);
        when(elefaceSharingRelationDao.queryByShareId(shareId)).thenReturn(sharingRelation);
        when(elefaceSharingRelationDao.updateShareMemoOrSalesmanByShareId(shareId, newShareMemo, newSalesman)).thenReturn(1);

        // 执行面单备注修改
        elefaceAccountService.saveShareMemo(userInfoBo, shareId, newShareMemo, newSalesman);

        // 验证锁的获取和释放
        verify(sharingRelationLockRedisDao).lockSharingRelation(shareId);
        verify(sharingRelationLockRedisDao).unlockSharingRelation(shareId, lockValue);

        // 验证在锁保护下的操作顺序
        InOrder inOrder = inOrder(sharingRelationLockRedisDao, elefaceSharingRelationDao, elefaceSharingRelationOperateService, printMessageSendService);
        inOrder.verify(sharingRelationLockRedisDao).lockSharingRelation(shareId);
        inOrder.verify(elefaceSharingRelationDao).queryByShareId(shareId);
        inOrder.verify(elefaceSharingRelationDao).updateShareMemoOrSalesmanByShareId(shareId, newShareMemo, newSalesman);
        inOrder.verify(elefaceSharingRelationOperateService).saveElefaceSharingRelationOperateLog(any(), any(), any(), any(), any());
        inOrder.verify(printMessageSendService).pushRelationChangeMessage(any());
        inOrder.verify(sharingRelationLockRedisDao).unlockSharingRelation(shareId, lockValue);
    }

    /**
     * 测试操作日志的时间戳准确性
     */
    @Test
    public void testOperateLogTimestamp() throws Exception {
        // 记录测试开始时间
        LocalDateTime testStartTime = LocalDateTime.now();

        // 准备测试数据
        String newShareMemo = "时间戳测试备注";
        String newSalesman = "时间戳测试业务员";

        // Mock 外部依赖
        when(sharingRelationLockRedisDao.lockSharingRelation(shareId)).thenReturn(lockValue);
        when(elefaceSharingRelationDao.queryByShareId(shareId)).thenReturn(sharingRelation);
        when(elefaceSharingRelationDao.updateShareMemoOrSalesmanByShareId(shareId, newShareMemo, newSalesman)).thenReturn(1);

        // 执行面单备注修改
        elefaceAccountService.saveShareMemo(userInfoBo, shareId, newShareMemo, newSalesman);

        // 记录测试结束时间
        LocalDateTime testEndTime = LocalDateTime.now();

        // 验证操作日志服务被调用（时间戳由服务内部处理）
        verify(elefaceSharingRelationOperateService).saveElefaceSharingRelationOperateLog(
                any(ElefaceSharingRelation.class),
                eq(200L),
                eq(ElefaceSharingRelationOperateConstant.MEMO_UPDATE),
                eq(userInfoBo.getMallOperate()),
                eq(userInfoBo.getOperateTerminal())
        );
    }
}
