package cn.loveapp.print.service.integration;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import cn.loveapp.common.constant.CommonAppConstants;
import cn.loveapp.common.constant.CommonPlatformConstants;
import cn.loveapp.common.dto.UserSessionInfo;
import cn.loveapp.print.service.bo.UserInfoBo;
import cn.loveapp.print.service.constant.ElefaceSharingRelationConstant;
import cn.loveapp.print.service.constant.ElefaceSharingRelationOperateConstant;
import cn.loveapp.print.service.dao.print.ElefaceSharingRelationDao;
import cn.loveapp.print.service.dao.print.ElefaceSharingRelationOperateLogDao;
import cn.loveapp.print.service.dao.redis.ElefaceSharingRelationLockRedisDao;
import cn.loveapp.print.service.entity.ElefaceSharingRelation;
import cn.loveapp.print.service.entity.ElefaceSharingRelationOperateLog;
import cn.loveapp.print.service.service.ElefaceSharingRelationOperateService;
import cn.loveapp.print.service.service.PrintMessageSendService;
import cn.loveapp.print.service.service.impl.ElefaceAccountServiceImpl;
import cn.loveapp.print.service.service.impl.ElefaceSharingRelationOperateServiceImpl;

/**
 * 面单备注修改操作日志集成测试
 * 测试从备注修改到日志记录的完整流程
 *
 * <AUTHOR>
 */
@ExtendWith(MockitoExtension.class)
class MemoUpdateOperateLogIntegrationTest {

    // 被测试的服务
    @InjectMocks
    private ElefaceAccountServiceImpl elefaceAccountService;

    @InjectMocks
    private ElefaceSharingRelationOperateServiceImpl operateService;

    // Mock 依赖
    @Mock
    private ElefaceSharingRelationDao elefaceSharingRelationDao;
    @Mock
    private ElefaceSharingRelationLockRedisDao sharingRelationLockRedisDao;
    @Mock
    private ElefaceSharingRelationOperateLogDao operateLogDao;
    @Mock
    private PrintMessageSendService printMessageSendService;

    // 测试数据
    private UserInfoBo userInfoBo;
    private ElefaceSharingRelation sharingRelation;
    private String shareId = "SHARE_INTEGRATION_TEST_001";
    private String lockValue = "lock_value_integration";

    @BeforeEach
    void setUp() {
        // 注意：@InjectMocks 会自动注入依赖，这里只需要设置测试数据

        // 初始化用户信息
        UserSessionInfo sessionInfo = new UserSessionInfo();
        sessionInfo.setSellerNick("integrationTestUser");
        sessionInfo.setSellerId("integrationTestSellerId");
        sessionInfo.setStoreId(CommonPlatformConstants.PLATFORM_TAO);
        sessionInfo.setAppName(CommonAppConstants.APP_NAME_QIANNIU);
        userInfoBo = UserInfoBo.of(sessionInfo, null);

        // 初始化面单分享关系
        sharingRelation = new ElefaceSharingRelation();
        sharingRelation.setShareId(shareId);
        sharingRelation.setOwnerSellerId("integrationTestSellerId");
        sharingRelation.setOwnerStoreId(CommonPlatformConstants.PLATFORM_TAO);
        sharingRelation.setOwnerAppName(CommonAppConstants.APP_NAME_QIANNIU);
        sharingRelation.setTargetSellerId("targetSellerId");
        sharingRelation.setTargetSellerNick("targetSellerNick");
        sharingRelation.setTargetStoreId("targetStoreId");
        sharingRelation.setTargetAppName("targetAppName");
        sharingRelation.setShareMemo("原始备注内容");
        sharingRelation.setSalesman("原始业务员");
        sharingRelation.setShareNum(200L);
        sharingRelation.setStatus(ElefaceSharingRelationConstant.STATUS_VALID);
    }

    /**
     * 测试完整的面单备注修改和日志记录流程
     */
    @Test
    void testCompleteFlowFromMemoUpdateToLogRecord() throws Exception {
        // 准备测试数据
        String newShareMemo = "修改后的备注内容";
        String newSalesman = "修改后的业务员";

        // Mock 外部依赖
        when(sharingRelationLockRedisDao.lockSharingRelation(shareId)).thenReturn(lockValue);
        when(elefaceSharingRelationDao.queryByShareId(shareId)).thenReturn(sharingRelation);
        when(elefaceSharingRelationDao.updateShareMemoOrSalesmanByShareId(shareId, newShareMemo, newSalesman)).thenReturn(1);

        // 执行面单备注修改
        elefaceAccountService.saveShareMemo(userInfoBo, shareId, newShareMemo, newSalesman);

        // 验证数据库更新被调用
        verify(elefaceSharingRelationDao).updateShareMemoOrSalesmanByShareId(shareId, newShareMemo, newSalesman);

        // 验证操作日志被插入
        ArgumentCaptor<ElefaceSharingRelationOperateLog> logCaptor = ArgumentCaptor.forClass(ElefaceSharingRelationOperateLog.class);
        verify(operateLogDao).insert(logCaptor.capture());

        // 验证操作日志的内容
        ElefaceSharingRelationOperateLog capturedLog = logCaptor.getValue();
        assertNotNull(capturedLog);
        assertEquals(shareId, capturedLog.getShareId());
        assertEquals(newShareMemo, capturedLog.getShareMemo());
        assertEquals(ElefaceSharingRelationOperateConstant.MEMO_UPDATE, capturedLog.getOperateType());
        assertEquals(200L, capturedLog.getLastShareNum());
        assertEquals(200L, capturedLog.getNewShareNum());
        assertEquals("integrationTestSellerId", capturedLog.getTargetSellerId());
        assertEquals("targetSellerNick", capturedLog.getTargetSellerNick());
        assertEquals("targetStoreId", capturedLog.getTargetStoreId());
        assertEquals("targetAppName", capturedLog.getTargetAppName());
        assertEquals(userInfoBo.getMallOperate(), capturedLog.getOperator());
        assertEquals(userInfoBo.getOperateTerminal(), capturedLog.getOperateTerminal());
        assertNotNull(capturedLog.getOperatorTime());

        // 验证消息推送被调用
        verify(printMessageSendService).pushRelationChangeMessage(any());

        // 验证锁被释放
        verify(sharingRelationLockRedisDao).unlockSharingRelation(shareId, lockValue);
    }

    /**
     * 测试只修改备注的情况
     */
    @Test
    void testMemoOnlyUpdate() throws Exception {
        // 准备测试数据 - 只修改备注，业务员保持不变
        String newShareMemo = "只修改备注";
        String sameSalesman = "原始业务员";

        // Mock 外部依赖
        when(sharingRelationLockRedisDao.lockSharingRelation(shareId)).thenReturn(lockValue);
        when(elefaceSharingRelationDao.queryByShareId(shareId)).thenReturn(sharingRelation);
        when(elefaceSharingRelationDao.updateShareMemoOrSalesmanByShareId(shareId, newShareMemo, sameSalesman)).thenReturn(1);

        // 执行面单备注修改
        elefaceAccountService.saveShareMemo(userInfoBo, shareId, newShareMemo, sameSalesman);

        // 验证操作日志被插入
        ArgumentCaptor<ElefaceSharingRelationOperateLog> logCaptor = ArgumentCaptor.forClass(ElefaceSharingRelationOperateLog.class);
        verify(operateLogDao).insert(logCaptor.capture());

        // 验证操作日志记录了新的备注内容
        ElefaceSharingRelationOperateLog capturedLog = logCaptor.getValue();
        assertEquals(newShareMemo, capturedLog.getShareMemo());
        assertEquals(ElefaceSharingRelationOperateConstant.MEMO_UPDATE, capturedLog.getOperateType());
    }

    /**
     * 测试只修改业务员的情况
     */
    @Test
    void testSalesmanOnlyUpdate() throws Exception {
        // 准备测试数据 - 只修改业务员，备注保持不变
        String sameMemo = "原始备注内容";
        String newSalesman = "只修改业务员";

        // Mock 外部依赖
        when(sharingRelationLockRedisDao.lockSharingRelation(shareId)).thenReturn(lockValue);
        when(elefaceSharingRelationDao.queryByShareId(shareId)).thenReturn(sharingRelation);
        when(elefaceSharingRelationDao.updateShareMemoOrSalesmanByShareId(shareId, sameMemo, newSalesman)).thenReturn(1);

        // 执行面单备注修改
        elefaceAccountService.saveShareMemo(userInfoBo, shareId, sameMemo, newSalesman);

        // 验证操作日志被插入
        ArgumentCaptor<ElefaceSharingRelationOperateLog> logCaptor = ArgumentCaptor.forClass(ElefaceSharingRelationOperateLog.class);
        verify(operateLogDao).insert(logCaptor.capture());

        // 验证操作日志记录了原始的备注内容（因为备注没有变化）
        ElefaceSharingRelationOperateLog capturedLog = logCaptor.getValue();
        assertEquals(sameMemo, capturedLog.getShareMemo());
        assertEquals(ElefaceSharingRelationOperateConstant.MEMO_UPDATE, capturedLog.getOperateType());
    }

    /**
     * 测试操作日志插入失败的情况
     */
    @Test
    void testLogInsertFailure() throws Exception {
        // 准备测试数据
        String newShareMemo = "新备注";
        String newSalesman = "新业务员";

        // Mock 外部依赖
        when(sharingRelationLockRedisDao.lockSharingRelation(shareId)).thenReturn(lockValue);
        when(elefaceSharingRelationDao.queryByShareId(shareId)).thenReturn(sharingRelation);
        when(elefaceSharingRelationDao.updateShareMemoOrSalesmanByShareId(shareId, newShareMemo, newSalesman)).thenReturn(1);

        // Mock 日志插入失败
        doThrow(new RuntimeException("数据库插入失败")).when(operateLogDao).insert(any());

        // 执行面单备注修改 - 即使日志插入失败，主流程也应该成功
        assertDoesNotThrow(() -> {
            elefaceAccountService.saveShareMemo(userInfoBo, shareId, newShareMemo, newSalesman);
        });

        // 验证主要操作仍然被执行
        verify(elefaceSharingRelationDao).updateShareMemoOrSalesmanByShareId(shareId, newShareMemo, newSalesman);
        verify(printMessageSendService).pushRelationChangeMessage(any());
        verify(sharingRelationLockRedisDao).unlockSharingRelation(shareId, lockValue);
    }

    /**
     * 测试并发场景下的锁机制
     */
    @Test
    void testConcurrentLockMechanism() throws Exception {
        // 准备测试数据
        String newShareMemo = "并发测试备注";
        String newSalesman = "并发测试业务员";

        // Mock 外部依赖
        when(sharingRelationLockRedisDao.lockSharingRelation(shareId)).thenReturn(lockValue);
        when(elefaceSharingRelationDao.queryByShareId(shareId)).thenReturn(sharingRelation);
        when(elefaceSharingRelationDao.updateShareMemoOrSalesmanByShareId(shareId, newShareMemo, newSalesman)).thenReturn(1);

        // 执行面单备注修改
        elefaceAccountService.saveShareMemo(userInfoBo, shareId, newShareMemo, newSalesman);

        // 验证锁的获取和释放
        verify(sharingRelationLockRedisDao).lockSharingRelation(shareId);
        verify(sharingRelationLockRedisDao).unlockSharingRelation(shareId, lockValue);

        // 验证在锁保护下的操作顺序
        var inOrder = inOrder(sharingRelationLockRedisDao, elefaceSharingRelationDao, operateLogDao, printMessageSendService);
        inOrder.verify(sharingRelationLockRedisDao).lockSharingRelation(shareId);
        inOrder.verify(elefaceSharingRelationDao).queryByShareId(shareId);
        inOrder.verify(elefaceSharingRelationDao).updateShareMemoOrSalesmanByShareId(shareId, newShareMemo, newSalesman);
        inOrder.verify(operateLogDao).insert(any());
        inOrder.verify(printMessageSendService).pushRelationChangeMessage(any());
        inOrder.verify(sharingRelationLockRedisDao).unlockSharingRelation(shareId, lockValue);
    }

    /**
     * 测试操作日志的时间戳准确性
     */
    @Test
    void testOperateLogTimestamp() throws Exception {
        // 记录测试开始时间
        LocalDateTime testStartTime = LocalDateTime.now();

        // 准备测试数据
        String newShareMemo = "时间戳测试备注";
        String newSalesman = "时间戳测试业务员";

        // Mock 外部依赖
        when(sharingRelationLockRedisDao.lockSharingRelation(shareId)).thenReturn(lockValue);
        when(elefaceSharingRelationDao.queryByShareId(shareId)).thenReturn(sharingRelation);
        when(elefaceSharingRelationDao.updateShareMemoOrSalesmanByShareId(shareId, newShareMemo, newSalesman)).thenReturn(1);

        // 执行面单备注修改
        elefaceAccountService.saveShareMemo(userInfoBo, shareId, newShareMemo, newSalesman);

        // 记录测试结束时间
        LocalDateTime testEndTime = LocalDateTime.now();

        // 验证操作日志的时间戳
        ArgumentCaptor<ElefaceSharingRelationOperateLog> logCaptor = ArgumentCaptor.forClass(ElefaceSharingRelationOperateLog.class);
        verify(operateLogDao).insert(logCaptor.capture());

        ElefaceSharingRelationOperateLog capturedLog = logCaptor.getValue();
        LocalDateTime operateTime = capturedLog.getOperatorTime();

        // 验证操作时间在测试时间范围内
        assertTrue(operateTime.isAfter(testStartTime.minusSeconds(1)), "操作时间应该在测试开始时间之后");
        assertTrue(operateTime.isBefore(testEndTime.plusSeconds(1)), "操作时间应该在测试结束时间之前");
    }
}
