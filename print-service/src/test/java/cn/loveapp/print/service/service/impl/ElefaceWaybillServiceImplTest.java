package cn.loveapp.print.service.service.impl;

import static org.mockito.Mockito.*;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;

import org.junit.Assert;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.boot.test.mock.mockito.SpyBean;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;

import cn.loveapp.common.constant.CommonAppConstants;
import cn.loveapp.common.constant.CommonPlatformConstants;
import cn.loveapp.common.dto.UserSessionInfo;
import cn.loveapp.print.common.entity.AyElefaceOperatelog;
import cn.loveapp.print.common.exception.PlatformSdkException;
import cn.loveapp.print.common.service.UserCenterService;
import cn.loveapp.print.service.bo.UserInfoBo;
import cn.loveapp.print.common.dao.newprint.AyElefaceOperatelogDao;
import cn.loveapp.print.service.dao.redis.ElefaceSharingRelationLockRedisDao;
import cn.loveapp.print.common.dto.WaybillOperatelogQueryDTO;
import cn.loveapp.print.service.request.ElefaceWaybillGetRequest;
import cn.loveapp.print.service.request.PrintTradeInfoDTO;
import cn.loveapp.print.service.service.ElefaceAccountService;
import cn.loveapp.print.service.platform.api.PlatformWaybillApiService;
import cn.loveapp.print.service.service.PrintlogService;

@RunWith(SpringRunner.class)
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.NONE, classes = {ElefaceWaybillServiceImpl.class})
class ElefaceWaybillServiceImplTest {

    @SpyBean
    ElefaceWaybillServiceImpl elefaceWaybillService;

    @MockBean
    private AyElefaceOperatelogDao ayElefaceOperatelogDao;

    @MockBean
    private ElefaceSharingRelationLockRedisDao sharingRelationLockRedisDao;

    @MockBean
    private ElefaceAccountService elefaceAccountService;

    @MockBean
    private UserCenterService userCenterService;

    @MockBean
    private PlatformWaybillApiService platformWaybillApiService;

    @MockBean
    private PrintlogService printlogService;

    private String cpCode = "ZJS";

    private String branchCode = "123123";

    private String logisticsCompany = "宅急送";

    private String ownerNick = "赵东昊的测试店铺";

    private String cainiaoParamWaybillCloudPrintApplyNewRequest =
        "{\"cp_code\":\"ZJS\",\"need_encrypt\":true,\"sender\":{\"address\":{\"city\":\"上海市\",\"detail\":\"新二路55号11楼\",\"district\":\"宝山区\",\"province\":\"上海\"},\"mobile\":\"***********\",\"name\":\"顾超\",\"phone\":\"\"},\"trade_order_info_dtos\":[{\"object_id\":\"********\",\"order_info\":{\"order_channels_type\":\"TB\",\"trade_order_list\":\"953927170060477590\"},\"package_info\":{\"id\":\"05F87FD1itemBag1\",\"items\":[{\"name\":\"自动化测试专用商品1\",\"count\":1}]},\"recipient\":{\"address\":{\"city\":\"北京市\",\"detail\":\"北七家镇南山敬老院北海幼儿园测试修改地址123466\",\"district\":\"昌平区\",\"province\":\"北京\",\"town\":\"\"},\"mobile\":\"13816691865\",\"name\":\"Bj具体事情哦\",\"phone\":\"\"},\"template_url\":\"http://cloudprint.cainiao.com/template/standard/901/130\",\"user_id\":\"3936370796\"}]}";

    private String pddParamWaybillCloudPrintApplyNewRequest =
        "{\"need_encrypt\":true,\"cp_code\":\"YZXB\",\"sender\":{\"address\":{\"country\":\"中国\",\"province\":\"上海市\",\"city\":\"上海市\",\"district\":\"徐汇区\",\"detail\":\"你好街道你好楼\"},\"mobile\":\"王清\",\"name\":\"15151214277\",\"phone\":\"\"},\"trade_order_info_dtos\":[{\"object_id\":0,\"order_info\":{\"order_channels_type\":\"PDD\",\"trade_order_list\":[\"200424-485002445832982\"]},\"package_info\":{\"id\":\"FF8197CBbag0\",\"items\":[{\"name\":\"1大耳垂 Disney 迪斯尼小123\",\"count\":1}]},\"recipient\":{\"address\":{\"city\":\"上海市\",\"detail\":\"新二路88号\",\"district\":\"宝山区\",\"province\":\"上海市\",\"town\":\"\"},\"mobile\":\"18200001111\",\"name\":\"bling\",\"phone\":\"\"},\"template_url\":\"https://file-link.pinduoduo.com/yzxb_std\",\"user_id\":\"346946926\"}],\"wp_code\":\"YZXB\"}";

    private String topSession = "xxxxxxx";

    private String operator = "赵东昊的测试店铺:guchao";

    private String operatorStoreId = "TAO";

    private String operateTerminal = "pc";

    private String storeId = "TAO";
    private String sellerId = "3936370796";
    private String appName = "trade";
    private String sellerNick = "赵东昊的测试店铺";

    private Integer tradeType = 0;

    private PrintTradeInfoDTO printTradeInfoDTO;

    private UserInfoBo userInfoBo;
    private UserSessionInfo sessionInfo;

    @BeforeEach
    void setUp() {
        printTradeInfoDTO = new PrintTradeInfoDTO();
        printTradeInfoDTO.setTid("918959645698872009");
        printTradeInfoDTO.setOids(Collections.singletonList("918959645698872009"));

        sessionInfo = new UserSessionInfo();
        sessionInfo.setAppName(appName);
        sessionInfo.setStoreId(storeId);
        sessionInfo.setNick(sellerNick);
        sessionInfo.setSellerId(sellerId);
        sessionInfo.setLoginTerminal(operateTerminal);

        userInfoBo = new UserInfoBo();
        userInfoBo.setSellerNick(sellerNick);
        userInfoBo.setAppName(appName);
        userInfoBo.setSellerId(sellerId);
        userInfoBo.setStoreId(storeId);
        userInfoBo.setOperateTerminal(operateTerminal);
        userInfoBo.setOperatorStoreId(operatorStoreId);
        userInfoBo.setOperator(operator);
    }

    public ElefaceWaybillGetRequest elefaceWaybillGetRequest() {
        ElefaceWaybillGetRequest request = new ElefaceWaybillGetRequest();

        String ownerNick = "赵东昊的测试店铺";
        Integer tradeType = 0;
        String cpCode = "ZJS";
        String logsiticsCompany = "宅急送";
        String paramWaybillCloudPrintApplyNewRequest =
            "{\"cp_code\":\"ZJS\",\"need_encrypt\":true,\"sender\":{\"address\":{\"city\":\"上海市\",\"detail\":\"新二路55号11楼\",\"district\":\"宝山区\",\"province\":\"上海\"},\"mobile\":\"***********\",\"name\":\"顾超\",\"phone\":\"\"},\"trade_order_info_dtos\":[{\"object_id\":\"********\",\"order_info\":{\"order_channels_type\":\"TB\",\"trade_order_list\":\"953927170060477590\"},\"package_info\":{\"id\":\"05F87FD1itemBag1\",\"items\":[{\"name\":\"自动化测试专用商品1\",\"count\":1}]},\"recipient\":{\"address\":{\"city\":\"北京市\",\"detail\":\"北七家镇南山敬老院北海幼儿园测试修改地址123466\",\"district\":\"昌平区\",\"province\":\"北京\",\"town\":\"\"},\"mobile\":\"13816691865\",\"name\":\"Bj具体事情哦\",\"phone\":\"\"},\"template_url\":\"http://cloudprint.cainiao.com/template/standard/901/130\",\"user_id\":\"3936370796\"}]}";

        MultiValueMap params = new LinkedMultiValueMap();

        params.set("paramWaybillCloudPrintApplyNewRequestList[0]", paramWaybillCloudPrintApplyNewRequest);
        params.set("tradeType", tradeType.toString());

        params.set("tradeInfoList[0].tid", "967254754555477590");
        params.set("tradeInfoList[0].oids[0]", "967254754555477590");
        params.set("ownerNick", ownerNick);
        params.set("cpCode", cpCode);
        params.set("logisticsCompany", logsiticsCompany);
        PrintTradeInfoDTO printTradeInfoDTO = new PrintTradeInfoDTO();
        printTradeInfoDTO.setTid("967254754555477590");
        printTradeInfoDTO.setOids(Arrays.asList("967254754555477590"));
        request.setTradeInfoList(Arrays.asList(printTradeInfoDTO));
        request.setCpCode(cpCode);
        request.setShareId(null);
        request.setTradeType(tradeType);
        request.setLogisticsCompany(logsiticsCompany);
        request.setParamWaybillCloudPrintApplyNewRequestList(Arrays.asList(paramWaybillCloudPrintApplyNewRequest));
        return request;
    }

    /**
     * 拼多多面单取号-非法的参数
     */
    @Test
    void pddWaybillGetBatchTest2() {
        String appName = CommonAppConstants.APP_GUANDIAN;
        String platformId = CommonPlatformConstants.PLATFORM_PDD;
        String paramRequest = "非法的参数";
        try {
            elefaceWaybillService.doWaybillIiGetBatchSingle(Collections.singletonList(paramRequest), topSession,
                platformId, appName);
        } catch (PlatformSdkException e) {
            Assert.assertEquals("非法的参数：paramWaybillCloudPrintApplyNewRequest = " + paramRequest, e.getMessage());
        }
    }

    private String waybillCode = "9862094323576";

    /**
     * 保存面单的取消日志
     */
    @Test
    void saveWaybillCancelOperateLogTest1() {
        LocalDateTime now = LocalDateTime.now();
        when(elefaceWaybillService.LocalDateTimeNow()).thenReturn(now);
        elefaceWaybillService.saveWaybillCancelOperateLog(new ArrayList<>(), cpCode, waybillCode, userInfoBo, LocalDateTime.now());
        AyElefaceOperatelog cancelOplog = new AyElefaceOperatelog();
        cancelOplog.setCpCode(cpCode);
        cancelOplog.setWaybillCode(waybillCode);
        cancelOplog.setCancelOperator(userInfoBo.getOperator());
        cancelOplog.setCancelOperatorStoreId(userInfoBo.getOperatorStoreId());
        cancelOplog.setCancelOperateTerminal(userInfoBo.getOperateTerminal());
        cancelOplog.setIsCancel(true);
        cancelOplog.setCancelTime(now);
        verify(ayElefaceOperatelogDao).saveCancelInfo(eq(cancelOplog), eq(userInfoBo.getStoreId()),
            eq(userInfoBo.getSellerId()), eq(userInfoBo.getAppName()));
    }

    @Test
    void saveWaybillPrintLogTest() {
        elefaceWaybillService.saveWaybillPrintLog(cpCode, waybillCode, "", userInfoBo);
        verify(ayElefaceOperatelogDao).printCountIncrement(cpCode, waybillCode, "", userInfoBo.getStoreId(),
            userInfoBo.getSellerId(), userInfoBo.getAppName());
    }

    /**
     * 获取面单操作日志
     */
    @Test
    void operatelogListGetTest1() {
        when(ayElefaceOperatelogDao.queryCount(any(), eq(userInfoBo.getStoreId()), eq(userInfoBo.getSellerId()),
            eq(userInfoBo.getAppName()))).thenReturn(2L);
        when(ayElefaceOperatelogDao.queryListOrderByOperateTime(any(), eq(userInfoBo.getStoreId()),
            eq(userInfoBo.getSellerId()), eq(userInfoBo.getAppName())))
                .thenReturn(Arrays.asList(new AyElefaceOperatelog(), new AyElefaceOperatelog()));
        Assert.assertEquals(2, elefaceWaybillService.operatelogListGet(new WaybillOperatelogQueryDTO(), userInfoBo)
            .getOperatelogDTOList().size());
    }

}
