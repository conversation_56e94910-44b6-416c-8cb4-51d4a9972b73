package cn.loveapp.print.service.service.impl;

import static org.mockito.Mockito.when;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;

import org.junit.Assert;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.test.context.junit4.SpringRunner;

import cn.loveapp.print.common.entity.AySerialLog;
import cn.loveapp.print.service.bo.SerialGetBo;
import cn.loveapp.print.service.bo.UserInfoBo;
import cn.loveapp.print.service.dao.newprint.AyTradeSerialLogDao;
import cn.loveapp.print.service.dao.redis.SerialRedisDao;
import cn.loveapp.print.service.request.PrintTradeInfoDTO;
import cn.loveapp.print.service.service.SerialService;

@RunWith(SpringRunner.class)
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.NONE, classes = {SerialServiceImpl.class})
class SerialServiceImplTest {

    @Autowired
    private SerialService serialService;

    @MockBean
    private SerialRedisDao serialRedisDao;

    @MockBean
    private AyTradeSerialLogDao ayTradeSerialLogDao;

    private static final String MERGE_TID = "MTD5645641563";

    private static final String TID1 = "967254754555477590";
    private static final List<String> OIDS1 = Arrays.asList("967254754555477590");

    private static final String TID2 = "967254754555477591";
    private static final List<String> OIDS2 = Arrays.asList("967254754555477592");

    private static final Integer TRADE_TYPE = 0;

    private static final String OPERATOR = "赵东昊的测试店铺:guchao";
    private static final String OPERATOR_STORE_ID = "TAO";
    private static final String OPERATE_TERMINAL = "WW";
    private static final String STORE_ID = "TAO";
    private static final String SELLER_ID = "3936370796";
    private static final String APP_NAME = "trade";
    private static final String SELLER_NICK = "赵东昊的测试店铺";

    private PrintTradeInfoDTO printTradeInfoDTO1 = new PrintTradeInfoDTO();
    private PrintTradeInfoDTO printTradeInfoDTO2 = new PrintTradeInfoDTO();

    private UserInfoBo userInfoBo = new UserInfoBo();

    @BeforeEach
    void setUp() {
        userInfoBo.setAppName(APP_NAME);
        userInfoBo.setSellerId(SELLER_ID);
        userInfoBo.setStoreId(STORE_ID);
        userInfoBo.setSellerId(SELLER_ID);
        userInfoBo.setAppName(APP_NAME);
        userInfoBo.setSellerNick(SELLER_NICK);

        printTradeInfoDTO1.setTid(TID1);
        printTradeInfoDTO1.setOids(OIDS1);

        printTradeInfoDTO2.setTid(TID2);
        printTradeInfoDTO2.setOids(OIDS2);
    }

    /**
     * 普通订单从redis获取订单流水号
     */
    @Test
    void serialGetTest1() {
        String serial = "0400123456";
        Map<String, List<String>> serialToOidsMap = new HashMap<String, List<String>>() {
            {
                put(serial, OIDS1);
            }
        };
        when(serialRedisDao.getTradeSerial(TID1, TRADE_TYPE, userInfoBo)).thenReturn(serialToOidsMap);
        SerialGetBo serialGetBo = new SerialGetBo();
        serialGetBo.setTradeInfoList(Collections.singletonList(printTradeInfoDTO1));
        serialGetBo.setTradeType(TRADE_TYPE);
        Assert.assertEquals(serial, serialService.serialGet(serialGetBo, userInfoBo));
    }

    /**
     * 普通订单从数据库里面获取订单流水号
     */
    @Test
    void serialGetTest2() {
        String serial = "0400123456";
        when(serialRedisDao.getTradeSerial(TID1, TRADE_TYPE, userInfoBo)).thenReturn(null);
        AySerialLog aySerialLog = new AySerialLog();
        aySerialLog.setSerial(serial);
        aySerialLog.setOids(OIDS1);
        when(ayTradeSerialLogDao.queryByTid(TID1, TRADE_TYPE, STORE_ID, SELLER_ID, APP_NAME))
            .thenReturn(Collections.singletonList(aySerialLog));
        SerialGetBo serialGetBo = new SerialGetBo();
        serialGetBo.setTradeInfoList(Collections.singletonList(printTradeInfoDTO1));
        serialGetBo.setTradeType(TRADE_TYPE);
        Assert.assertEquals(serial, serialService.serialGet(serialGetBo, userInfoBo));
    }

    /**
     * 拆单打印没有当前oids组合的流水号
     */
    @Test
    void serialGetTest3() {
        String serial = "0400123456";
        when(serialRedisDao.getTradeSerial(TID1, TRADE_TYPE, userInfoBo)).thenReturn(null);
        AySerialLog aySerialLog = new AySerialLog();
        aySerialLog.setSerial(serial);
        aySerialLog.setOids(Arrays.asList("12345467"));
        when(ayTradeSerialLogDao.queryByTid(TID1, TRADE_TYPE, STORE_ID, SELLER_ID, APP_NAME))
            .thenReturn(Collections.singletonList(aySerialLog));
        Long indexNumber = 1L;
        String newSerial =
            LocalDateTime.now().format(DateTimeFormatter.ofPattern("MMdd")) + String.format("%04d", indexNumber);
        when(serialRedisDao.generateUserSerialIndex(userInfoBo)).thenReturn(indexNumber);
        SerialGetBo serialGetBo = new SerialGetBo();
        serialGetBo.setTradeInfoList(Collections.singletonList(printTradeInfoDTO1));
        serialGetBo.setTradeType(TRADE_TYPE);
        Assert.assertEquals(newSerial, serialService.serialGet(serialGetBo, userInfoBo));
    }

    /**
     * Redis和db都没有查到流水号
     */
    @Test
    void serialGetTest4() {
        when(serialRedisDao.getTradeSerial(TID1, TRADE_TYPE, userInfoBo)).thenReturn(null);
        when(ayTradeSerialLogDao.queryByTid(TID1, TRADE_TYPE, STORE_ID, SELLER_ID, APP_NAME)).thenReturn(null);
        Long indexNumber = 1L;
        String newSerial =
            LocalDateTime.now().format(DateTimeFormatter.ofPattern("MMdd")) + String.format("%04d", indexNumber);
        when(serialRedisDao.generateUserSerialIndex(userInfoBo)).thenReturn(indexNumber);
        SerialGetBo serialGetBo = new SerialGetBo();
        serialGetBo.setTradeInfoList(Collections.singletonList(printTradeInfoDTO1));
        serialGetBo.setTradeType(TRADE_TYPE);
        Assert.assertEquals(newSerial, serialService.serialGet(serialGetBo, userInfoBo));
    }

    /**
     * 合单从Redis获取流水号
     */
    @Test
    void serialGetTest5() {
        String serial = "0400123456";
        Map<String, List<String>> serialToOidsMap1 = new HashMap<String, List<String>>() {
            {
                put(serial, OIDS1);
            }
        };
        Map<String, List<String>> serialToOidsMap2 = new HashMap<String, List<String>>() {
            {
                put(serial, OIDS2);
            }
        };
        when(serialRedisDao.getTradeSerial(TID1, TRADE_TYPE, userInfoBo)).thenReturn(serialToOidsMap1);
        when(serialRedisDao.getTradeSerial(TID2, TRADE_TYPE, userInfoBo)).thenReturn(serialToOidsMap2);
        SerialGetBo serialGetBo = new SerialGetBo();
        serialGetBo.setTradeInfoList(Arrays.asList(printTradeInfoDTO1, printTradeInfoDTO2));
        serialGetBo.setTradeType(TRADE_TYPE);
        serialGetBo.setMergeTid(MERGE_TID);
        Assert.assertEquals(serial, serialService.serialGet(serialGetBo, userInfoBo));
    }

    /**
     * 重新生成合单流水号
     */
    @Test
    void serialGetTest6() {
        String serial = "0400123456";
        Map<String, List<String>> serialToOidsMap1 = new HashMap<String, List<String>>() {
            {
                put(serial, OIDS1);
            }
        };
        when(serialRedisDao.getTradeSerial(TID1, TRADE_TYPE, userInfoBo)).thenReturn(serialToOidsMap1);
        when(serialRedisDao.getTradeSerial(TID2, TRADE_TYPE, userInfoBo)).thenReturn(null);
        Long indexNumber = 10000L;
        String newSerial =
            LocalDateTime.now().format(DateTimeFormatter.ofPattern("MMdd")) + String.format("%04d", indexNumber);
        when(serialRedisDao.generateUserSerialIndex(userInfoBo)).thenReturn(indexNumber);
        SerialGetBo serialGetBo = new SerialGetBo();
        serialGetBo.setTradeInfoList(Arrays.asList(printTradeInfoDTO1, printTradeInfoDTO2));
        serialGetBo.setTradeType(TRADE_TYPE);
        serialGetBo.setMergeTid(MERGE_TID);
        Assert.assertEquals(newSerial, serialService.serialGet(serialGetBo, userInfoBo));
    }

    @Test
    void serialInvalidate() {}
}
