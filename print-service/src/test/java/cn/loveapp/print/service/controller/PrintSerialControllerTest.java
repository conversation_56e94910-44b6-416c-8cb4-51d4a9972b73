package cn.loveapp.print.service.controller;

import static org.mockito.Mockito.any;
import static org.mockito.Mockito.when;

import org.junit.Assert;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.ImportAutoConfiguration;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.core.MethodParameter;
import org.springframework.http.MediaType;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;

import cn.loveapp.common.dto.UserSessionInfo;
import cn.loveapp.common.user.session.autoconfigure.CommonUserSessionAutoConfiguration;
import cn.loveapp.common.user.session.autoconfigure.CommonUserSessionMvcAutoConfiguration;
import cn.loveapp.common.user.session.web.UserSessionHandlerInterceptor;
import cn.loveapp.common.web.CommonApiResponse;
import cn.loveapp.print.common.service.UserCenterService;
import cn.loveapp.print.service.interceptor.ShopsAuthInterceptor;
import cn.loveapp.print.service.service.SerialService;
import cn.loveapp.print.service.service.ShopsService;

@RunWith(SpringRunner.class)
@ImportAutoConfiguration(
    value = {CommonUserSessionAutoConfiguration.class, CommonUserSessionMvcAutoConfiguration.class})
@WebMvcTest(PrintSerialController.class)
class PrintSerialControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @MockBean
    private UserSessionHandlerInterceptor userSessionHandlerInterceptor;

    @MockBean
    private ShopsAuthInterceptor shopsAuthInterceptor;

    @MockBean
    private ShopsService shopsService;

    @MockBean
    private UserCenterService userCenterService;

    @MockBean
    private SerialService serialService;

    private UserSessionInfo sessionInfo;

    private CommonApiResponse getRequest(String url) throws Exception {
        return JSON.parseObject(mockMvc.perform(MockMvcRequestBuilders.get(url).accept(MediaType.APPLICATION_JSON))
            .andReturn().getResponse().getContentAsString(), CommonApiResponse.class);
    }

    private CommonApiResponse postRequest(String url, MultiValueMap params) throws Exception {
        return JSON.parseObject(
            mockMvc.perform(MockMvcRequestBuilders.post(url).params(params).accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse().getContentAsString(),
            CommonApiResponse.class);
    }

    @BeforeEach
    void setUp() throws Exception {
        sessionInfo = new UserSessionInfo();
        sessionInfo.setAppName("trade");
        sessionInfo.setStoreId("TAO");
        sessionInfo.setNick("子小_十一八");
        sessionInfo.setSellerId("1821874596");
        sessionInfo.setLoginTerminal("pc");

        when(userSessionHandlerInterceptor.preHandle(any(), any(), any())).thenReturn(true);
        // UserSessionInfo 才能通过 supportsParameter
        when(userSessionHandlerInterceptor.supportsParameter(any()))
            .then(a -> ((MethodParameter)a.getArgument(0)).getParameterType().isAssignableFrom(UserSessionInfo.class));
        when(userSessionHandlerInterceptor.resolveArgument(any(), any(), any(), any())).thenReturn(sessionInfo);

        when(shopsAuthInterceptor.preHandle(any(), any(), any())).thenReturn(true);
    }

    private String serialGetUrl = "/print/serial/serial.get";

    /**
     * 合单获取流水号
     */
    @Test
    void serialGetTest1() throws Exception {
        String serial = "040100001";

        String mergeTid = "MTD4415245";
        Integer tradeType = 0;

        when(serialService.serialGet(any(), any())).thenReturn(serial);

        MultiValueMap params = new LinkedMultiValueMap();
        params.set("mergeTid", mergeTid);
        params.set("tradeType", tradeType.toString());

        params.set("tradeInfoList[0].tid", "967254754555477590");
        params.set("tradeInfoList[0].oids[0]", "967254754555477590");
        params.set("tradeInfoList[1].tid", "967254754555477578");
        params.set("tradeInfoList[1].oids[0]", "967254754555477578");
        CommonApiResponse response = postRequest(serialGetUrl, params);

        JSONObject body = (JSONObject)response.getBody();

        Assert.assertEquals(serial, body.getString("serial"));
    }

    /**
     * 普通订单取流水号
     *
     * @throws Exception
     */
    @Test
    void serialGetTest2() throws Exception {
        String serial = "040100001";

        String mergeTid = "MTD4415245";
        Integer tradeType = 0;

        when(serialService.serialGet(any(), any())).thenReturn(serial);

        MultiValueMap params = new LinkedMultiValueMap();
        params.set("tradeType", tradeType.toString());

        params.set("tradeInfoList[0].tid", "967254754555477590");
        params.set("tradeInfoList[0].oids[0]", "967254754555477590");
        CommonApiResponse response = postRequest(serialGetUrl, params);

        JSONObject body = (JSONObject)response.getBody();

        Assert.assertEquals(serial, body.getString("serial"));
    }
}
