package cn.loveapp.print.service.entity;

import org.apache.commons.lang3.StringUtils;
import org.junit.Assert;
import org.junit.Test;

import cn.loveapp.print.service.bo.ElefaceSharingCreateBo;

public class ElefaceSharingRelationTest {

    /**
     * 正常
     */
    @Test
    public void of() {
        ElefaceSharingCreateBo sharingCreateBo = new ElefaceSharingCreateBo();
        sharingCreateBo.setSegmentCode("111");
        sharingCreateBo.setShippAddressDistrict("222");
        ElefaceSharingRelation elefaceSharingRelation = ElefaceSharingRelation.of(sharingCreateBo);
        Assert.assertEquals(sharingCreateBo.getSegmentCode(), elefaceSharingRelation.getSegmentCode());
        Assert.assertEquals(sharingCreateBo.getShippAddressDistrict(),
            elefaceSharingRelation.getShippAddressDistrict());
    }

    /**
     * 空白
     */
    @Test
    public void of2() {
        ElefaceSharingCreateBo sharingCreateBo = new ElefaceSharingCreateBo();

        sharingCreateBo.setSegmentCode(null);
        sharingCreateBo.setShippAddressDistrict(null);
        ElefaceSharingRelation elefaceSharingRelation = ElefaceSharingRelation.of(sharingCreateBo);
        Assert.assertEquals(StringUtils.EMPTY, elefaceSharingRelation.getSegmentCode());
        Assert.assertEquals(StringUtils.EMPTY, elefaceSharingRelation.getShippAddressDistrict());

        sharingCreateBo.setSegmentCode("    ");
        sharingCreateBo.setShippAddressDistrict("    ");
        elefaceSharingRelation = ElefaceSharingRelation.of(sharingCreateBo);
        Assert.assertEquals(StringUtils.EMPTY, elefaceSharingRelation.getSegmentCode());
        Assert.assertEquals(StringUtils.EMPTY, elefaceSharingRelation.getShippAddressDistrict());
    }
}
