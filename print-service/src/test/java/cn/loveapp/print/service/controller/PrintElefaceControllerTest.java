package cn.loveapp.print.service.controller;

import static org.mockito.Mockito.*;

import java.util.Arrays;

import org.junit.Assert;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.ImportAutoConfiguration;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.core.MethodParameter;
import org.springframework.http.MediaType;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;

import com.alibaba.fastjson.JSON;

import cn.loveapp.common.dto.UserSessionInfo;
import cn.loveapp.common.user.session.autoconfigure.CommonUserSessionAutoConfiguration;
import cn.loveapp.common.user.session.autoconfigure.CommonUserSessionMvcAutoConfiguration;
import cn.loveapp.common.user.session.web.UserSessionHandlerInterceptor;
import cn.loveapp.common.web.CommonApiResponse;
import cn.loveapp.common.web.CommonApiStatus;
import cn.loveapp.print.common.exception.ElefaceSharingNotExistException;
import cn.loveapp.print.common.exception.PlatformSdkException;
import cn.loveapp.print.common.exception.UnauthorizedException;
import cn.loveapp.print.common.service.UserCenterService;
import cn.loveapp.print.service.bo.UserInfoBo;
import cn.loveapp.print.service.bo.WaybillGetResponseBo;
import cn.loveapp.print.common.dao.newprint.AyElefaceOperatelogDao;
import cn.loveapp.print.service.interceptor.ShopsAuthInterceptor;
import cn.loveapp.print.service.service.PrintlogService;
import cn.loveapp.print.service.service.ShopsService;
import cn.loveapp.print.service.service.impl.ElefaceWaybillServiceImpl;
import cn.loveapp.print.service.web.PrintResponseStatus;

@RunWith(SpringRunner.class)
@ImportAutoConfiguration(
    value = {CommonUserSessionAutoConfiguration.class, CommonUserSessionMvcAutoConfiguration.class})
@WebMvcTest(PrintElefaceController.class)
class PrintElefaceControllerTest {
    @Autowired
    private MockMvc mockMvc;

    @MockBean
    private UserSessionHandlerInterceptor userSessionHandlerInterceptor;

    @MockBean
    private ShopsAuthInterceptor shopsAuthInterceptor;

    @MockBean
    private ShopsService shopsService;

    @MockBean
    private ElefaceWaybillServiceImpl elefaceWaybillService;

    @MockBean
    private PrintlogService printlogService;

    @MockBean
    private UserCenterService userCenterService;

    private UserSessionInfo sessionInfo;
    private UserInfoBo userInfoBo;

    @MockBean
    private AyElefaceOperatelogDao ayElefaceOperatelogDao;

    private CommonApiResponse postRequest(String url, MultiValueMap params) throws Exception {
        return JSON.parseObject(
            mockMvc.perform(MockMvcRequestBuilders.post(url).params(params).accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse().getContentAsString(),
            CommonApiResponse.class);
    }

    @BeforeEach
    void setUp() throws Exception {
        sessionInfo = new UserSessionInfo();
        sessionInfo.setAppName("trade");
        sessionInfo.setStoreId("TAO");
        sessionInfo.setNick("子小_十一八");
        sessionInfo.setSellerId("1821874596");
        sessionInfo.setLoginTerminal("pc");
        userInfoBo = UserInfoBo.of(sessionInfo);

        when(userSessionHandlerInterceptor.preHandle(any(), any(), any())).thenReturn(true);
        // UserSessionInfo 才能通过 supportsParameter
        when(userSessionHandlerInterceptor.supportsParameter(any()))
            .then(a -> ((MethodParameter)a.getArgument(0)).getParameterType().isAssignableFrom(UserSessionInfo.class));
        when(userSessionHandlerInterceptor.resolveArgument(any(), any(), any(), any())).thenReturn(sessionInfo);

        when(shopsAuthInterceptor.preHandle(any(), any(), any())).thenReturn(true);
    }

    private String pddWaybillGetUrl = "/print/eleface/pdd.waybill.get";

    /**
     * 面单关系不存在
     */
    @Test
    void pddWaybillGetTest1() throws Exception {
        Integer tradeType = 0;
        String cpCode = "ZJS";
        String logsiticsCompany = "宅急送";
        String paramWaybillCloudPrintApplyNewRequest =
            "{\"cp_code\":\"ZJS\",\"need_encrypt\":true,\"sender\":{\"address\":{\"city\":\"上海市\",\"detail\":\"新二路55号11楼\",\"district\":\"宝山区\",\"province\":\"上海\"},\"mobile\":\"18621729133\",\"name\":\"顾超\",\"phone\":\"\"},\"trade_order_info_dtos\":[{\"object_id\":\"03510975\",\"order_info\":{\"order_channels_type\":\"TB\",\"trade_order_list\":\"953927170060477590\"},\"package_info\":{\"id\":\"05F87FD1itemBag1\",\"items\":[{\"name\":\"自动化测试专用商品1\",\"count\":1}]},\"recipient\":{\"address\":{\"city\":\"北京市\",\"detail\":\"北七家镇南山敬老院北海幼儿园测试修改地址123466\",\"district\":\"昌平区\",\"province\":\"北京\",\"town\":\"\"},\"mobile\":\"13816691865\",\"name\":\"Bj具体事情哦\",\"phone\":\"\"},\"template_url\":\"http://cloudprint.cainiao.com/template/standard/901/130\",\"user_id\":\"3936370796\"}]}";

        MultiValueMap params = new LinkedMultiValueMap();

        params.set("paramWaybillCloudPrintApplyNewRequestList[0]", paramWaybillCloudPrintApplyNewRequest);
        params.set("tradeType", tradeType.toString());

        params.set("tradeInfoList[0].tid", "967254754555477590");
        params.set("tradeInfoList[0].oids[0]", "967254754555477590");
        params.set("cpCode", cpCode);
        params.set("shareId", "1");
        params.set("logisticsCompany", logsiticsCompany);

        when(elefaceWaybillService.waybillGetBatch(any(), any())).thenThrow(new ElefaceSharingNotExistException());

        CommonApiResponse response = postRequest(pddWaybillGetUrl, params);

        Assert.assertEquals(Integer.valueOf(PrintResponseStatus.ElefaceSharingNotExistError.code()),
            response.getSubCode());
    }

    /**
     * topSession失效
     */
    @Test
    void pddWaybillGetTest2() throws Exception {
        String ownerNick = "赵东昊的测试店铺";
        Integer tradeType = 0;
        String cpCode = "ZJS";
        String logsiticsCompany = "宅急送";
        String paramWaybillCloudPrintApplyNewRequest =
            "{\"cp_code\":\"ZJS\",\"need_encrypt\":true,\"sender\":{\"address\":{\"city\":\"上海市\",\"detail\":\"新二路55号11楼\",\"district\":\"宝山区\",\"province\":\"上海\"},\"mobile\":\"18621729133\",\"name\":\"顾超\",\"phone\":\"\"},\"trade_order_info_dtos\":[{\"object_id\":\"03510975\",\"order_info\":{\"order_channels_type\":\"TB\",\"trade_order_list\":\"953927170060477590\"},\"package_info\":{\"id\":\"05F87FD1itemBag1\",\"items\":[{\"name\":\"自动化测试专用商品1\",\"count\":1}]},\"recipient\":{\"address\":{\"city\":\"北京市\",\"detail\":\"北七家镇南山敬老院北海幼儿园测试修改地址123466\",\"district\":\"昌平区\",\"province\":\"北京\",\"town\":\"\"},\"mobile\":\"13816691865\",\"name\":\"Bj具体事情哦\",\"phone\":\"\"},\"template_url\":\"http://cloudprint.cainiao.com/template/standard/901/130\",\"user_id\":\"3936370796\"}]}";
        String branchCode = "ZJS";

        MultiValueMap params = new LinkedMultiValueMap();

        params.set("paramWaybillCloudPrintApplyNewRequestList[0]", paramWaybillCloudPrintApplyNewRequest);
        params.set("tradeType", tradeType.toString());

        params.set("tradeInfoList[0].tid", "967254754555477590");
        params.set("tradeInfoList[0].oids[0]", "967254754555477590");
        params.set("ownerNick", ownerNick);
        params.set("cpCode", cpCode);
        params.set("logisticsCompany", logsiticsCompany);

        when(elefaceWaybillService.waybillGetBatch(any(), any())).thenThrow(new UnauthorizedException());

        CommonApiResponse response = postRequest(pddWaybillGetUrl, params);

        Assert.assertEquals(Integer.valueOf(CommonApiStatus.UnauthorizedError.code()), response.getSubCode());
    }

    /**
     * SDK执行错误
     */
    @Test
    void pddWaybillGetTest3() throws Exception {
        String ownerNick = sessionInfo.getNick();
        Integer tradeType = 0;
        String cpCode = "ZJS";
        String logsiticsCompany = "宅急送";
        String paramWaybillCloudPrintApplyNewRequest =
            "{\"cp_code\":\"ZJS\",\"need_encrypt\":true,\"sender\":{\"address\":{\"city\":\"上海市\",\"detail\":\"新二路55号11楼\",\"district\":\"宝山区\",\"province\":\"上海\"},\"mobile\":\"18621729133\",\"name\":\"顾超\",\"phone\":\"\"},\"trade_order_info_dtos\":[{\"object_id\":\"03510975\",\"order_info\":{\"order_channels_type\":\"TB\",\"trade_order_list\":\"953927170060477590\"},\"package_info\":{\"id\":\"05F87FD1itemBag1\",\"items\":[{\"name\":\"自动化测试专用商品1\",\"count\":1}]},\"recipient\":{\"address\":{\"city\":\"北京市\",\"detail\":\"北七家镇南山敬老院北海幼儿园测试修改地址123466\",\"district\":\"昌平区\",\"province\":\"北京\",\"town\":\"\"},\"mobile\":\"13816691865\",\"name\":\"Bj具体事情哦\",\"phone\":\"\"},\"template_url\":\"http://cloudprint.cainiao.com/template/standard/901/130\",\"user_id\":\"3936370796\"}]}";

        MultiValueMap params = new LinkedMultiValueMap();

        params.set("paramWaybillCloudPrintApplyNewRequestList[0]", paramWaybillCloudPrintApplyNewRequest);
        params.set("tradeType", tradeType.toString());

        params.set("tradeInfoList[0].tid", "967254754555477590");
        params.set("tradeInfoList[0].oids[0]", "967254754555477590");
        params.set("ownerNick", ownerNick);
        params.set("cpCode", cpCode);
        params.set("logisticsCompany", logsiticsCompany);

        String topSession = "123";

        when(userCenterService.getTopSession(anyString(), anyString(), eq(sessionInfo.getNick()),
            eq(sessionInfo.getSellerId()))).thenReturn(topSession);
        when(elefaceWaybillService.waybillGetBatch(any(), any())).thenThrow(new PlatformSdkException("SDK错误"));

        CommonApiResponse response = postRequest(pddWaybillGetUrl, params);

        Assert.assertEquals(Integer.valueOf(PrintResponseStatus.PlatformSdkError.code()), response.getSubCode());
    }

    /**
     * 成功获取
     *
     * @throws Exception
     */
    @Test
    void pddWaybillGetTest4() throws Exception {
        String ownerNick = sessionInfo.getNick();
        Integer tradeType = 0;
        String cpCode = "ZJS";
        String logsiticsCompany = "宅急送";
        String paramWaybillCloudPrintApplyNewRequest =
            "{\"cp_code\":\"ZJS\",\"need_encrypt\":true,\"sender\":{\"address\":{\"city\":\"上海市\",\"detail\":\"新二路55号11楼\",\"district\":\"宝山区\",\"province\":\"上海\"},\"mobile\":\"18621729133\",\"name\":\"顾超\",\"phone\":\"\"},\"trade_order_info_dtos\":[{\"object_id\":\"03510975\",\"order_info\":{\"order_channels_type\":\"TB\",\"trade_order_list\":\"953927170060477590\"},\"package_info\":{\"id\":\"05F87FD1itemBag1\",\"items\":[{\"name\":\"自动化测试专用商品1\",\"count\":1}]},\"recipient\":{\"address\":{\"city\":\"北京市\",\"detail\":\"北七家镇南山敬老院北海幼儿园测试修改地址123466\",\"district\":\"昌平区\",\"province\":\"北京\",\"town\":\"\"},\"mobile\":\"13816691865\",\"name\":\"Bj具体事情哦\",\"phone\":\"\"},\"template_url\":\"http://cloudprint.cainiao.com/template/standard/901/130\",\"user_id\":\"3936370796\"}]}";

        MultiValueMap params = new LinkedMultiValueMap();

        params.set("paramWaybillCloudPrintApplyNewRequestList[0]", paramWaybillCloudPrintApplyNewRequest);
        params.set("tradeType", tradeType.toString());

        params.set("tradeInfoList[0].tid", "967254754555477590");
        params.set("tradeInfoList[0].oids[0]", "967254754555477590");
        params.set("ownerNick", ownerNick);
        params.set("cpCode", cpCode);
        params.set("logisticsCompany", logsiticsCompany);

        String topSession = "123";

        when(userCenterService.getTopSession(anyString(), anyString(), eq(sessionInfo.getNick()),
            eq(sessionInfo.getSellerId()))).thenReturn(topSession);
        when(elefaceWaybillService.waybillGetBatch(any(), any())).thenReturn(Arrays.asList(new WaybillGetResponseBo()));

        CommonApiResponse response = postRequest(pddWaybillGetUrl, params);

        Assert.assertEquals(Integer.valueOf(CommonApiStatus.Success.code()), response.getCode());
        Assert.assertNull(response.getSubCode());
        Assert.assertNull(response.getSubMessage());

    }

    /**
     * 合单操作成功
     *
     * @throws Exception
     */
    @Test
    void pddWaybillGetTest5() throws Exception {
        String ownerNick = sessionInfo.getNick();
        String mergeTid = "MTD456456165";
        Integer tradeType = 0;
        String cpCode = "ZJS";
        String logsiticsCompany = "宅急送";
        String paramWaybillCloudPrintApplyNewRequest =
            "{\"cp_code\":\"ZJS\",\"need_encrypt\":true,\"sender\":{\"address\":{\"city\":\"上海市\",\"detail\":\"新二路55号11楼\",\"district\":\"宝山区\",\"province\":\"上海\"},\"mobile\":\"18621729133\",\"name\":\"顾超\",\"phone\":\"\"},\"trade_order_info_dtos\":[{\"object_id\":\"03510975\",\"order_info\":{\"order_channels_type\":\"TB\",\"trade_order_list\":\"953927170060477590\"},\"package_info\":{\"id\":\"05F87FD1itemBag1\",\"items\":[{\"name\":\"自动化测试专用商品1\",\"count\":1}]},\"recipient\":{\"address\":{\"city\":\"北京市\",\"detail\":\"北七家镇南山敬老院北海幼儿园测试修改地址123466\",\"district\":\"昌平区\",\"province\":\"北京\",\"town\":\"\"},\"mobile\":\"13816691865\",\"name\":\"Bj具体事情哦\",\"phone\":\"\"},\"template_url\":\"http://cloudprint.cainiao.com/template/standard/901/130\",\"user_id\":\"3936370796\"}]}";

        MultiValueMap params = new LinkedMultiValueMap();

        params.set("paramWaybillCloudPrintApplyNewRequestList[0]", paramWaybillCloudPrintApplyNewRequest);
        params.set("tradeType", tradeType.toString());
        params.set("mergeTid", mergeTid);
        params.set("tradeInfoList[0].tid", "967254754555477590");
        params.set("tradeInfoList[0].oids[0]", "967254754555477590");
        params.set("tradeInfoList[1].tid", "967254754555477591");
        params.set("tradeInfoList[1].oids[0]", "967254754555477591");
        params.set("ownerNick", ownerNick);
        params.set("cpCode", cpCode);
        params.set("logisticsCompany", logsiticsCompany);

        String topSession = "123";

        when(userCenterService.getTopSession(anyString(), anyString(), eq(sessionInfo.getNick()),
            eq(sessionInfo.getSellerId()))).thenReturn(topSession);
        when(elefaceWaybillService.waybillGetBatch(any(), any())).thenReturn(Arrays.asList(new WaybillGetResponseBo()));

        CommonApiResponse response = postRequest(pddWaybillGetUrl, params);

        Assert.assertEquals(Integer.valueOf(CommonApiStatus.Success.code()), response.getCode());
        Assert.assertNull(response.getSubCode());
        Assert.assertNull(response.getSubMessage());
    }

    private String pddWaybillCancelUrl = "/print/eleface/pdd.waybill.cancel";

    /**
     * 面单共享关系不存在
     */
    @Test
    void pddWaybillCancelTest1() throws Exception {
        String ownerNick = "赵东昊的测试店铺";
        String cpCode = "ZJS";
        String waybillCode = "ZJS001559427822";
        String branchCode = "ZJS";

        MultiValueMap params = new LinkedMultiValueMap();
        params.set("ownerNick", ownerNick);
        params.set("cpCode", cpCode);
        params.set("waybillCode", waybillCode);

        when(elefaceWaybillService.waybillCancel(cpCode, waybillCode, null, null, userInfoBo))
            .thenThrow(new ElefaceSharingNotExistException());

        CommonApiResponse response = postRequest(pddWaybillCancelUrl, params);

        Assert.assertEquals(Integer.valueOf(PrintResponseStatus.ElefaceSharingNotExistError.code()),
            response.getSubCode());
    }

    /**
     * topSession失效
     *
     * @throws Exception
     */
    @Test
    void pddWaybillCancelTest2() throws Exception {
        String ownerNick = "赵东昊的测试店铺";
        String cpCode = "ZJS";
        String waybillCode = "ZJS001559427822";
        String branchCode = "ZJS";

        MultiValueMap params = new LinkedMultiValueMap();
        params.set("ownerNick", ownerNick);
        params.set("cpCode", cpCode);
        params.set("waybillCode", waybillCode);

        when(elefaceWaybillService.waybillCancel(cpCode, waybillCode, null, null, userInfoBo))
            .thenThrow(new UnauthorizedException());

        CommonApiResponse response = postRequest(pddWaybillCancelUrl, params);

        Assert.assertEquals(Integer.valueOf(CommonApiStatus.UnauthorizedError.code()), response.getSubCode());
    }

    /**
     * SDK 错误
     *
     * @throws Exception
     */
    @Test
    void pddWaybillCancelTest3() throws Exception {
        String ownerNick = sessionInfo.getNick();
        String cpCode = "ZJS";
        String waybillCode = "ZJS001559427822";

        MultiValueMap params = new LinkedMultiValueMap();
        params.set("ownerNick", ownerNick);
        params.set("cpCode", cpCode);
        params.set("waybillCode", waybillCode);

        String topSession = "123";
        when(elefaceWaybillService.waybillCancel(cpCode, waybillCode, null, null, userInfoBo))
            .thenThrow(new PlatformSdkException("SDK错误"));

        CommonApiResponse response = postRequest(pddWaybillCancelUrl, params);

        Assert.assertEquals(Integer.valueOf(PrintResponseStatus.PlatformSdkError.code()), response.getSubCode());
    }

    /**
     * 执行成功
     *
     * @throws Exception
     */
    @Test
    void pddWaybillCancelTest4() throws Exception {
        String ownerNick = sessionInfo.getNick();
        String cpCode = "ZJS";
        String waybillCode = "ZJS001559427822";

        MultiValueMap params = new LinkedMultiValueMap();
        params.set("ownerNick", ownerNick);
        params.set("cpCode", cpCode);
        params.set("waybillCode", waybillCode);

        String topSession = "123";

        when(elefaceWaybillService.waybillCancel(cpCode, waybillCode, null, null, userInfoBo)).thenReturn(true);

        CommonApiResponse response = postRequest(pddWaybillCancelUrl, params);

        Assert.assertEquals(Integer.valueOf(CommonApiStatus.Success.code()), response.getCode());
    }

    private String waybillOperatelogListGetUrl = "/print/eleface/waybill.operatelog.list.get";

    @Test
    void waybillOperatelogListGetTest1() throws Exception {
        Integer page = 1;
        Integer pageSize = 20;
        MultiValueMap params = new LinkedMultiValueMap();
        params.set("page", page.toString());
        params.set("pageSize", pageSize.toString());
        CommonApiResponse response = postRequest(waybillOperatelogListGetUrl, params);

        Assert.assertEquals(Integer.valueOf(CommonApiStatus.Success.code()), response.getCode());
    }
}
