package cn.loveapp.print.service;

import org.junit.platform.suite.api.SelectClasses;
import org.junit.platform.suite.api.Suite;

import cn.loveapp.print.service.constant.ElefaceSharingRelationOperateConstantTest;
import cn.loveapp.print.service.controller.ElefaceSharingRelationOperateControllerTest;
import cn.loveapp.print.service.dao.print.ElefaceSharingRelationOperateLogDaoTest;
import cn.loveapp.print.service.integration.MemoUpdateOperateLogIntegrationTest;
import cn.loveapp.print.service.service.impl.ElefaceAccountServiceImplTest;

/**
 * 面单备注修改功能测试套件
 * 包含所有相关的单元测试和集成测试
 *
 * <AUTHOR>
 */
@Suite
@SelectClasses({
    // 常量测试
    ElefaceSharingRelationOperateConstantTest.class,
    
    // 服务层测试
    ElefaceAccountServiceImplTest.class,
    
    // 控制器测试
    ElefaceSharingRelationOperateControllerTest.class,
    
    // 数据访问层测试
    ElefaceSharingRelationOperateLogDaoTest.class,
    
    // 集成测试
    MemoUpdateOperateLogIntegrationTest.class
})
public class MemoUpdateFeatureTestSuite {
    // 测试套件类，用于组织和运行所有相关测试
}
