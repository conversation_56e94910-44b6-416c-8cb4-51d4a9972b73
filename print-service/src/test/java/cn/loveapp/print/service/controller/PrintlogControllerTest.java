package cn.loveapp.print.service.controller;

import static org.mockito.Mockito.*;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;

import org.junit.Assert;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.ImportAutoConfiguration;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.core.MethodParameter;
import org.springframework.http.MediaType;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;

import cn.loveapp.common.dto.UserSessionInfo;
import cn.loveapp.common.user.session.autoconfigure.CommonUserSessionAutoConfiguration;
import cn.loveapp.common.user.session.autoconfigure.CommonUserSessionMvcAutoConfiguration;
import cn.loveapp.common.user.session.web.UserSessionHandlerInterceptor;
import cn.loveapp.common.utils.DateUtil;
import cn.loveapp.common.web.CommonApiResponse;
import cn.loveapp.common.web.CommonApiStatus;
import cn.loveapp.print.common.service.UserCenterService;
import cn.loveapp.print.service.bo.GroupElefacePrintLogQueryBo;
import cn.loveapp.print.service.bo.UserInfoBo;
import cn.loveapp.print.service.dto.GroupedElefacePrintLogDTO;
import cn.loveapp.print.service.interceptor.ShopsAuthInterceptor;
import cn.loveapp.print.service.service.OrdersService;
import cn.loveapp.print.service.service.PrintlogService;
import cn.loveapp.print.service.service.ShopsService;

@RunWith(SpringRunner.class)
@ImportAutoConfiguration(
    value = {CommonUserSessionAutoConfiguration.class, CommonUserSessionMvcAutoConfiguration.class})
@WebMvcTest(PrintlogController.class)
class PrintlogControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @MockBean
    private UserSessionHandlerInterceptor userSessionHandlerInterceptor;

    @MockBean
    private ShopsAuthInterceptor shopsAuthInterceptor;

    @MockBean
    private ShopsService shopsService;

    @MockBean
    private UserCenterService userCenterService;

    @MockBean
    private PrintlogService printlogService;

    @MockBean
    private OrdersService ordersService;

    private UserSessionInfo sessionInfo;

    private UserInfoBo userInfoBo;

    @BeforeEach
    void setUp() throws Exception {
        sessionInfo = new UserSessionInfo();
        sessionInfo.setAppName("trade");
        sessionInfo.setStoreId("TAO");
        sessionInfo.setNick("子小_十一八");
        sessionInfo.setSellerId("1821874596");
        sessionInfo.setLoginTerminal("pc");

        userInfoBo = UserInfoBo.of(sessionInfo);

        when(userSessionHandlerInterceptor.preHandle(any(), any(), any())).thenReturn(true);
        // UserSessionInfo 才能通过 supportsParameter
        when(userSessionHandlerInterceptor.supportsParameter(any()))
            .then(a -> ((MethodParameter)a.getArgument(0)).getParameterType().isAssignableFrom(UserSessionInfo.class));
        when(userSessionHandlerInterceptor.resolveArgument(any(), any(), any(), any())).thenReturn(sessionInfo);

        when(shopsAuthInterceptor.preHandle(any(), any(), any())).thenReturn(true);
    }

    private String buyerNick = "子小_十一八";

    private String name = "孙平";

    private String mobile = "111222333444";

    private String city = "上海市";

    private String detailAddress = "新二路55号空间技术产业基地";

    private String district = "宝山区";

    private String province = "上海";

    private String town = "高境镇";

    private String zipCode = "201900";

    private CommonApiResponse postRequest(String url, MultiValueMap params) throws Exception {
        return JSON.parseObject(
            mockMvc.perform(MockMvcRequestBuilders.post(url).params(params).accept(MediaType.APPLICATION_JSON_UTF8))
                .andReturn().getResponse().getContentAsString(),
            CommonApiResponse.class);
    }

    private String elefacePrintUrl = "/print/printlog/eleface.print";

    /**
     * 普通订单打印成功
     */
    @Test
    void elefacePrintTest1() throws Exception {
        Integer tradeType = 0;
        Boolean isSplit = false;
        String printer = "Print to PDF (Mac Desktop)";
        Integer printCount = 1;
        String printData = "printData";
        String cpCode = "ZJS";
        String logisticsCompany = "宅急送";
        String waybillCode = "ZJS001559427822";

        MultiValueMap params = new LinkedMultiValueMap();

        params.set("tradeType", tradeType.toString());

        params.set("tradeInfoList[0].tid", "967254754555477590");
        params.set("tradeInfoList[0].oids[0]", "967254754555477590");
        params.set("recipient.name", name);
        params.set("recipient.mobile", mobile);
        params.set("recipient.city", city);
        params.set("recipient.detailAddress", detailAddress);
        params.set("recipient.district", district);
        params.set("recipient.province", province);
        params.set("recipient.town", town);
        params.set("recipient.zipCode", zipCode);

        params.set("isSplit", isSplit.toString());
        params.set("printer", printer);
        params.set("printCount", printCount.toString());
        params.set("cpCode", cpCode);
        params.set("logisticsCompany", logisticsCompany);
        params.set("waybillCode", waybillCode);
        params.set("printData", printData);

        CommonApiResponse response = postRequest(elefacePrintUrl, params);

        Assert.assertEquals(Integer.valueOf(CommonApiStatus.Success.code()), response.getCode());
    }

    /**
     * 合单订单打印成功
     */
    @Test
    void elefacePrintTest3() throws Exception {
        Integer tradeType = 0;
        Boolean isSplit = false;
        String printer = "Print to PDF (Mac Desktop)";
        Integer printCount = 1;
        String printData = "printData";
        String cpCode = "ZJS";
        String logisticsCompany = "宅急送";
        String waybillCode = "ZJS001559427822";

        MultiValueMap params = new LinkedMultiValueMap();

        params.set("tradeType", tradeType.toString());

        params.set("mergeTid", "MTD5235325");
        params.set("tradeInfoList[0].tid", "967254754555477590");
        params.set("tradeInfoList[0].oids[0]", "967254754555477590");
        params.set("tradeInfoList[1].tid", "967254754555477591");
        params.set("tradeInfoList[1].oids[0]", "967254754555477591");
        params.set("recipient.name", name);
        params.set("recipient.mobile", mobile);
        params.set("recipient.city", city);
        params.set("recipient.detailAddress", detailAddress);
        params.set("recipient.district", district);
        params.set("recipient.province", province);
        params.set("recipient.town", town);
        params.set("recipient.zipCode", zipCode);

        params.set("isSplit", isSplit.toString());
        params.set("printer", printer);
        params.set("printCount", printCount.toString());
        params.set("cpCode", cpCode);
        params.set("logisticsCompany", logisticsCompany);
        params.set("waybillCode", waybillCode);
        params.set("printData", printData);

        CommonApiResponse response = postRequest(elefacePrintUrl, params);

        Assert.assertEquals(Integer.valueOf(CommonApiStatus.Success.code()), response.getCode());
    }

    private String expressPrintUrl = "/print/printlog/express.print";

    /**
     * 普通订单保存成功
     */
    @Test
    void expressPrintTest1() throws Exception {
        Integer tradeType = 0;
        Boolean isSplit = false;
        String printer = "Print to PDF (Mac Desktop)";
        Integer printCount = 1;
        String printData = "printData";
        String logisticsCompany = "宅急送";
        String expressImage = "zhongtong01.jpg";

        MultiValueMap params = new LinkedMultiValueMap();

        params.set("tradeType", tradeType.toString());

        params.set("tradeInfoList[0].tid", "967254754555477590");
        params.set("tradeInfoList[0].oids[0]", "967254754555477590");
        params.set("recipient.name", name);
        params.set("recipient.mobile", mobile);
        params.set("recipient.city", city);
        params.set("recipient.detailAddress", detailAddress);
        params.set("recipient.district", district);
        params.set("recipient.province", province);
        params.set("recipient.town", town);
        params.set("recipient.zipCode", zipCode);

        params.set("isSplit", isSplit.toString());
        params.set("printer", printer);
        params.set("printCount", printCount.toString());
        params.set("logisticsCompany", logisticsCompany);
        params.set("expressImage", expressImage);
        params.set("printData", printData);

        CommonApiResponse response = postRequest(expressPrintUrl, params);

        Assert.assertEquals(Integer.valueOf(CommonApiStatus.Success.code()), response.getCode());
    }

    /**
     * 合单订单保存成功
     */
    @Test
    void expressPrintTest3() throws Exception {
        Integer tradeType = 0;
        Boolean isSplit = false;
        String printer = "Print to PDF (Mac Desktop)";
        Integer printCount = 1;
        String printData = "printData";
        String logisticsCompany = "宅急送";
        String expressImage = "zhongtong01.jpg";

        MultiValueMap params = new LinkedMultiValueMap();

        params.set("tradeType", tradeType.toString());

        params.set("mergeTid", "MTD5235325");
        params.set("tradeInfoList[0].tid", "967254754555477590");
        params.set("tradeInfoList[0].oids[0]", "967254754555477590");
        params.set("tradeInfoList[1].tid", "967254754555477591");
        params.set("tradeInfoList[1].oids[0]", "967254754555477591");
        params.set("recipient.name", name);
        params.set("recipient.mobile", mobile);
        params.set("recipient.city", city);
        params.set("recipient.detailAddress", detailAddress);
        params.set("recipient.district", district);
        params.set("recipient.province", province);
        params.set("recipient.town", town);
        params.set("recipient.zipCode", zipCode);

        params.set("isSplit", isSplit.toString());
        params.set("printer", printer);
        params.set("printCount", printCount.toString());
        params.set("logisticsCompany", logisticsCompany);
        params.set("expressImage", expressImage);
        params.set("printData", printData);

        CommonApiResponse response = postRequest(expressPrintUrl, params);

        Assert.assertEquals(Integer.valueOf(CommonApiStatus.Success.code()), response.getCode());
    }

    private String deliverPrintUrl = "/print/printlog/deliver.print";

    /**
     * 普通订单保存成功
     */
    @Test
    void deliverPrintTest1() throws Exception {
        Integer tradeType = 0;
        Boolean isSplit = false;
        String printer = "Print to PDF (Mac Desktop)";
        Integer printCount = 1;
        String printData = "printData";

        MultiValueMap params = new LinkedMultiValueMap();

        params.set("tradeType", tradeType.toString());

        params.set("tradeInfoList[0].tid", "967254754555477590");
        params.set("tradeInfoList[0].oids[0]", "967254754555477590");
        params.set("recipient.name", name);
        params.set("recipient.mobile", mobile);
        params.set("recipient.city", city);
        params.set("recipient.detailAddress", detailAddress);
        params.set("recipient.district", district);
        params.set("recipient.province", province);
        params.set("recipient.town", town);
        params.set("recipient.zipCode", zipCode);

        params.set("isSplit", isSplit.toString());
        params.set("printer", printer);
        params.set("printCount", printCount.toString());
        params.set("printData", printData);

        CommonApiResponse response = postRequest(deliverPrintUrl, params);

        Assert.assertEquals(Integer.valueOf(CommonApiStatus.Success.code()), response.getCode());
    }

    /**
     * 合单订单保存成功
     */
    @Test
    void deliverPrintTest3() throws Exception {
        Integer tradeType = 0;
        Boolean isSplit = false;
        String printer = "Print to PDF (Mac Desktop)";
        Integer printCount = 1;
        String printData = "printData";

        MultiValueMap params = new LinkedMultiValueMap();

        params.set("tradeType", tradeType.toString());

        params.set("mergeTid", "MTD5235325");
        params.set("tradeInfoList[0].tid", "967254754555477590");
        params.set("tradeInfoList[0].oids[0]", "967254754555477590");
        params.set("tradeInfoList[1].tid", "967254754555477591");
        params.set("tradeInfoList[1].oids[0]", "967254754555477591");
        params.set("recipient.name", name);
        params.set("recipient.mobile", mobile);
        params.set("recipient.city", city);
        params.set("recipient.detailAddress", detailAddress);
        params.set("recipient.district", district);
        params.set("recipient.province", province);
        params.set("recipient.town", town);
        params.set("recipient.zipCode", zipCode);

        params.set("isSplit", isSplit.toString());
        params.set("printer", printer);
        params.set("printCount", printCount.toString());
        params.set("printData", printData);

        CommonApiResponse response = postRequest(deliverPrintUrl, params);

        Assert.assertEquals(Integer.valueOf(CommonApiStatus.Success.code()), response.getCode());
    }

    private String logisticsBindingHistoryGetBatchUrl = "/print/printlog/logistics.binding.history.get.batch";

    @Test
    void logisticsBindingHistoryGetBatchTest1() throws Exception {
        Integer tradeType = 0;
        MultiValueMap params = new LinkedMultiValueMap();
        params.set("tradeType", tradeType.toString());
        params.set("tids[0]", "45413135153453");
        params.set("tids[1]", "45413135153442");

        CommonApiResponse response = postRequest(logisticsBindingHistoryGetBatchUrl, params);
        Assert.assertEquals(Integer.valueOf(CommonApiStatus.Success.code()), response.getCode());
    }

    private String logisticsPrintlogListGetUrl = "/print/printlog/logistics.printlog.list.get";

    @Test
    void logisticsPrintlogListGetTest1() throws Exception {
        Integer page = 1;
        Integer pageSize = 20;
        MultiValueMap params = new LinkedMultiValueMap();
        params.set("page", page.toString());
        params.set("pageSize", pageSize.toString());
        CommonApiResponse response = postRequest(logisticsPrintlogListGetUrl, params);
        Assert.assertEquals(Integer.valueOf(CommonApiStatus.Success.code()), response.getCode());
    }

    private String groupElefacePrintlogUrl = "/print/printlog/group.eleface.printlog";

    @Test
    void groupElefacePrintlog() throws Exception {
        LocalDateTime startTime = DateUtil.parseString("2020-11-24 00:00:00");
        LocalDateTime endTime = DateUtil.parseString("2020-12-24 23:59:59");
        List<String> cpCodeList = Arrays.asList("ZJS", "SFKY");
        List<String> receiverProvinceList = Arrays.asList("上海市", "江苏省");

        MultiValueMap params = new LinkedMultiValueMap();
        params.set("startTime", DateUtil.convertLocalDateTimetoString(startTime));
        params.set("endTime", DateUtil.convertLocalDateTimetoString(endTime));
        params.set("receiverProvinceList[0]", receiverProvinceList.get(0));
        params.set("receiverProvinceList[1]", receiverProvinceList.get(1));
        params.set("cpCodeList[0]", cpCodeList.get(0));
        params.set("cpCodeList[1]", cpCodeList.get(1));

        GroupElefacePrintLogQueryBo queryBo = new GroupElefacePrintLogQueryBo();
        queryBo.setStartTime(startTime);
        queryBo.setEndTime(endTime);
        queryBo.setCpCodeList(cpCodeList);
        queryBo.setReceiverProvinceList(receiverProvinceList);

        GroupedElefacePrintLogDTO groupedValue1 = new GroupedElefacePrintLogDTO();
        groupedValue1.setCpCode("ZJS");
        groupedValue1.setLogisticsCompany("宅急送");
        groupedValue1.setReceiverProvince("上海市");
        groupedValue1.setTotalPrint(10);
        groupedValue1.setTotalPrint(2);

        GroupedElefacePrintLogDTO groupedValue2 = new GroupedElefacePrintLogDTO();
        groupedValue2.setCpCode("SFKY");
        groupedValue2.setLogisticsCompany("江苏省");
        groupedValue2.setLogisticsCompany("顺丰快运");
        groupedValue2.setTotalPrint(10);
        groupedValue2.setTotalPrint(2);

        List<GroupedElefacePrintLogDTO> exceptedResult = Arrays.asList(groupedValue1, groupedValue2);

        when(printlogService.groupElefacePrintLog(eq(queryBo), eq(userInfoBo))).thenReturn(exceptedResult);

        CommonApiResponse response = postRequest(groupElefacePrintlogUrl, params);

        Assert.assertEquals(exceptedResult.size(), ((JSONArray)response.getBody()).size());
    }
}
