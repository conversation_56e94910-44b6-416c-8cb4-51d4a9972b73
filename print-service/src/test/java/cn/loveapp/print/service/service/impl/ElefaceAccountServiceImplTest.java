package cn.loveapp.print.service.service.impl;

import static org.mockito.Mockito.*;
import static org.junit.jupiter.api.Assertions.*;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import cn.loveapp.common.constant.CommonAppConstants;
import cn.loveapp.common.constant.CommonPlatformConstants;
import cn.loveapp.common.dto.UserSessionInfo;
import cn.loveapp.print.common.exception.ElefaceSharingNotExistException;
import cn.loveapp.print.service.bo.UserInfoBo;
import cn.loveapp.print.service.constant.ElefaceSharingRelationConstant;
import cn.loveapp.print.service.constant.ElefaceSharingRelationOperateConstant;
import cn.loveapp.print.service.dao.print.ElefaceSharingRelationDao;
import cn.loveapp.print.service.dao.redis.ElefaceSharingRelationLockRedisDao;
import cn.loveapp.print.service.entity.ElefaceSharingRelation;
import cn.loveapp.print.service.service.ElefaceSharingRelationOperateService;
import cn.loveapp.print.service.service.PrintMessageSendService;

/**
 * ElefaceAccountServiceImpl 测试类
 * 主要测试面单备注修改的操作日志功能
 *
 * <AUTHOR>
 */
@ExtendWith(MockitoExtension.class)
class ElefaceAccountServiceImplTest {

    @InjectMocks
    private ElefaceAccountServiceImpl elefaceAccountService;

    @Mock
    private ElefaceSharingRelationDao elefaceSharingRelationDao;

    @Mock
    private ElefaceSharingRelationLockRedisDao sharingRelationLockRedisDao;

    @Mock
    private ElefaceSharingRelationOperateService elefaceSharingRelationOperateService;

    @Mock
    private PrintMessageSendService printMessageSendService;

    private UserInfoBo userInfoBo;
    private ElefaceSharingRelation sharingRelation;
    private String shareId = "SHARE_TEST_123";
    private String lockValue = "lock_value_123";

    @BeforeEach
    void setUp() {
        // 初始化用户信息
        UserSessionInfo sessionInfo = new UserSessionInfo();
        sessionInfo.setSellerNick("testSellerNick");
        sessionInfo.setSellerId("testSellerId");
        sessionInfo.setStoreId(CommonPlatformConstants.PLATFORM_TAO);
        sessionInfo.setAppName(CommonAppConstants.APP_NAME_QIANNIU);
        
        userInfoBo = UserInfoBo.of(sessionInfo, null);

        // 初始化面单分享关系
        sharingRelation = new ElefaceSharingRelation();
        sharingRelation.setShareId(shareId);
        sharingRelation.setOwnerSellerId("testSellerId");
        sharingRelation.setOwnerStoreId(CommonPlatformConstants.PLATFORM_TAO);
        sharingRelation.setOwnerAppName(CommonAppConstants.APP_NAME_QIANNIU);
        sharingRelation.setShareMemo("原始备注");
        sharingRelation.setSalesman("原始业务员");
        sharingRelation.setShareNum(100L);
        sharingRelation.setStatus(ElefaceSharingRelationConstant.STATUS_VALID);
    }

    /**
     * 测试面单备注修改成功并记录操作日志
     */
    @Test
    void testSaveShareMemo_Success() throws Exception {
        // 准备测试数据
        String newShareMemo = "新的备注内容";
        String newSalesman = "新的业务员";

        // Mock 方法调用
        when(sharingRelationLockRedisDao.lockSharingRelation(shareId)).thenReturn(lockValue);
        when(elefaceSharingRelationDao.queryByShareId(shareId)).thenReturn(sharingRelation);
        when(elefaceSharingRelationDao.updateShareMemoOrSalesmanByShareId(shareId, newShareMemo, newSalesman)).thenReturn(1);

        // 执行测试方法
        elefaceAccountService.saveShareMemo(userInfoBo, shareId, newShareMemo, newSalesman);

        // 验证数据库更新操作被调用
        verify(elefaceSharingRelationDao).updateShareMemoOrSalesmanByShareId(shareId, newShareMemo, newSalesman);

        // 验证操作日志记录被调用
        verify(elefaceSharingRelationOperateService).saveElefaceSharingRelationOperateLog(
                eq(sharingRelation),
                eq(100L), // shareNum
                eq(ElefaceSharingRelationOperateConstant.MEMO_UPDATE),
                eq(userInfoBo.getMallOperate()),
                eq(userInfoBo.getOperateTerminal())
        );

        // 验证消息推送被调用
        verify(printMessageSendService).pushRelationChangeMessage(any());

        // 验证锁被释放
        verify(sharingRelationLockRedisDao).unlockSharingRelation(shareId, lockValue);
    }

    /**
     * 测试只修改备注，不修改业务员
     */
    @Test
    void testSaveShareMemo_OnlyMemoChanged() throws Exception {
        // 准备测试数据
        String newShareMemo = "新的备注内容";
        String sameSalesman = "原始业务员"; // 保持业务员不变

        // Mock 方法调用
        when(sharingRelationLockRedisDao.lockSharingRelation(shareId)).thenReturn(lockValue);
        when(elefaceSharingRelationDao.queryByShareId(shareId)).thenReturn(sharingRelation);
        when(elefaceSharingRelationDao.updateShareMemoOrSalesmanByShareId(shareId, newShareMemo, sameSalesman)).thenReturn(1);

        // 执行测试方法
        elefaceAccountService.saveShareMemo(userInfoBo, shareId, newShareMemo, sameSalesman);

        // 验证操作日志记录被调用
        verify(elefaceSharingRelationOperateService).saveElefaceSharingRelationOperateLog(
                any(ElefaceSharingRelation.class),
                eq(100L),
                eq(ElefaceSharingRelationOperateConstant.MEMO_UPDATE),
                eq(userInfoBo.getMallOperate()),
                eq(userInfoBo.getOperateTerminal())
        );
    }

    /**
     * 测试没有任何修改时不记录操作日志
     */
    @Test
    void testSaveShareMemo_NoChanges() throws Exception {
        // 准备测试数据 - 使用相同的备注和业务员
        String sameShareMemo = "原始备注";
        String sameSalesman = "原始业务员";

        // Mock 方法调用
        when(sharingRelationLockRedisDao.lockSharingRelation(shareId)).thenReturn(lockValue);
        when(elefaceSharingRelationDao.queryByShareId(shareId)).thenReturn(sharingRelation);

        // 执行测试方法
        elefaceAccountService.saveShareMemo(userInfoBo, shareId, sameShareMemo, sameSalesman);

        // 验证数据库更新操作没有被调用
        verify(elefaceSharingRelationDao, never()).updateShareMemoOrSalesmanByShareId(any(), any(), any());

        // 验证操作日志记录没有被调用
        verify(elefaceSharingRelationOperateService, never()).saveElefaceSharingRelationOperateLog(any(), any(), any(), any(), any());

        // 验证消息推送没有被调用
        verify(printMessageSendService, never()).pushRelationChangeMessage(any());

        // 验证锁被释放
        verify(sharingRelationLockRedisDao).unlockSharingRelation(shareId, lockValue);
    }

    /**
     * 测试面单分享关系不存在时抛出异常
     */
    @Test
    void testSaveShareMemo_SharingRelationNotExist() {
        // Mock 方法调用
        when(sharingRelationLockRedisDao.lockSharingRelation(shareId)).thenReturn(lockValue);
        when(elefaceSharingRelationDao.queryByShareId(shareId)).thenReturn(null);

        // 执行测试并验证异常
        assertThrows(ElefaceSharingNotExistException.class, () -> {
            elefaceAccountService.saveShareMemo(userInfoBo, shareId, "新备注", "新业务员");
        });

        // 验证锁被释放
        verify(sharingRelationLockRedisDao).unlockSharingRelation(shareId, lockValue);
    }

    /**
     * 测试用户权限验证失败时抛出异常
     */
    @Test
    void testSaveShareMemo_UserPermissionDenied() {
        // 准备测试数据 - 设置不同的所有者信息
        sharingRelation.setOwnerSellerId("differentSellerId");

        // Mock 方法调用
        when(sharingRelationLockRedisDao.lockSharingRelation(shareId)).thenReturn(lockValue);
        when(elefaceSharingRelationDao.queryByShareId(shareId)).thenReturn(sharingRelation);

        // 执行测试并验证异常
        assertThrows(ElefaceSharingNotExistException.class, () -> {
            elefaceAccountService.saveShareMemo(userInfoBo, shareId, "新备注", "新业务员");
        });

        // 验证锁被释放
        verify(sharingRelationLockRedisDao).unlockSharingRelation(shareId, lockValue);
    }
}
