package cn.loveapp.print.service.dao.print;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import cn.loveapp.print.service.bo.ElefaceSharingRelationQueryBo;
import cn.loveapp.print.service.constant.ElefaceSharingRelationOperateConstant;
import cn.loveapp.print.service.entity.ElefaceSharingRelationOperateLog;

/**
 * ElefaceSharingRelationOperateLogDao 测试类
 * 测试操作类型过滤功能
 *
 * <AUTHOR>
 */
@ExtendWith(MockitoExtension.class)
public class ElefaceSharingRelationOperateLogDaoTest {

    @Mock
    private ElefaceSharingRelationOperateLogDao elefaceSharingRelationOperateLogDao;

    private ElefaceSharingRelationQueryBo queryBo;

    @BeforeEach
    public void setUp() {
        queryBo = new ElefaceSharingRelationQueryBo();
        queryBo.setShareIdList(Arrays.asList("SHARE_TEST_001", "SHARE_TEST_002"));
    }

    /**
     * 测试插入操作日志
     */
    @Test
    public void testInsert() {
        // 准备测试数据
        ElefaceSharingRelationOperateLog operateLog = new ElefaceSharingRelationOperateLog();
        operateLog.setShareId("SHARE_TEST_001");
        operateLog.setTargetSellerId("testSellerId");
        operateLog.setTargetSellerNick("testSellerNick");
        operateLog.setTargetStoreId("testStoreId");
        operateLog.setTargetAppName("testAppName");
        operateLog.setShareMemo("测试备注");
        operateLog.setOperateType(ElefaceSharingRelationOperateConstant.MEMO_UPDATE);
        operateLog.setLastShareNum(100L);
        operateLog.setNewShareNum(100L);
        operateLog.setOperateTerminal("WEB");
        operateLog.setOperator("testOperator");
        operateLog.setOperatorTime(LocalDateTime.now());

        // Mock 返回值
        when(elefaceSharingRelationOperateLogDao.insert(operateLog)).thenReturn(1);

        // 执行测试
        int result = elefaceSharingRelationOperateLogDao.insert(operateLog);

        // 验证结果
        assertEquals(1, result);
        verify(elefaceSharingRelationOperateLogDao).insert(operateLog);
    }

    /**
     * 测试按操作类型过滤查询 - 包含备注更新类型
     */
    @Test
    public void testSearchListByQuery_WithMemoUpdateType() {
        // 准备测试数据 - 包含备注更新类型
        queryBo.setOperateTypeList(Arrays.asList(
                ElefaceSharingRelationOperateConstant.CREATE,
                ElefaceSharingRelationOperateConstant.MEMO_UPDATE
        ));
        queryBo.setLimit(10);
        queryBo.setOffset(0);

        // 准备模拟返回数据
        ElefaceSharingRelationOperateLog log1 = createMockOperateLog("SHARE_TEST_001", ElefaceSharingRelationOperateConstant.CREATE);
        ElefaceSharingRelationOperateLog log2 = createMockOperateLog("SHARE_TEST_002", ElefaceSharingRelationOperateConstant.MEMO_UPDATE);
        List<ElefaceSharingRelationOperateLog> mockResult = Arrays.asList(log1, log2);

        when(elefaceSharingRelationOperateLogDao.searchListByQuery(queryBo)).thenReturn(mockResult);

        // 执行测试
        List<ElefaceSharingRelationOperateLog> result = elefaceSharingRelationOperateLogDao.searchListByQuery(queryBo);

        // 验证结果
        assertNotNull(result);
        assertEquals(2, result.size());
        verify(elefaceSharingRelationOperateLogDao).searchListByQuery(queryBo);
    }

    /**
     * 测试按操作类型过滤查询 - 排除备注更新类型（老客户端兼容）
     */
    @Test
    public void testSearchListByQuery_ExcludeMemoUpdateType() {
        // 准备测试数据 - 排除备注更新类型
        queryBo.setOperateTypeList(Arrays.asList(
                ElefaceSharingRelationOperateConstant.CREATE,
                ElefaceSharingRelationOperateConstant.CANCEL,
                ElefaceSharingRelationOperateConstant.UNTIE,
                ElefaceSharingRelationOperateConstant.UPDATE,
                ElefaceSharingRelationOperateConstant.RECOVERY,
                ElefaceSharingRelationOperateConstant.CANCEL_ADD
        ));
        queryBo.setLimit(10);
        queryBo.setOffset(0);

        // 准备模拟返回数据 - 不包含备注更新类型的日志
        ElefaceSharingRelationOperateLog log1 = createMockOperateLog("SHARE_TEST_001", ElefaceSharingRelationOperateConstant.CREATE);
        ElefaceSharingRelationOperateLog log2 = createMockOperateLog("SHARE_TEST_002", ElefaceSharingRelationOperateConstant.UPDATE);
        List<ElefaceSharingRelationOperateLog> mockResult = Arrays.asList(log1, log2);

        when(elefaceSharingRelationOperateLogDao.searchListByQuery(queryBo)).thenReturn(mockResult);

        // 执行测试
        List<ElefaceSharingRelationOperateLog> result = elefaceSharingRelationOperateLogDao.searchListByQuery(queryBo);

        // 验证结果
        assertNotNull(result);
        assertEquals(2, result.size());
        // 验证结果中不包含备注更新类型
        assertFalse(result.stream().anyMatch(log ->
                ElefaceSharingRelationOperateConstant.MEMO_UPDATE.equals(log.getOperateType())));
        verify(elefaceSharingRelationOperateLogDao).searchListByQuery(queryBo);
    }

    /**
     * 测试不指定操作类型过滤
     */
    @Test
    public void testSearchListByQuery_NoOperateTypeFilter() {
        // 准备测试数据 - 不设置操作类型过滤
        queryBo.setOperateTypeList(null);
        queryBo.setLimit(10);
        queryBo.setOffset(0);

        // 准备模拟返回数据
        ElefaceSharingRelationOperateLog log1 = createMockOperateLog("SHARE_TEST_001", ElefaceSharingRelationOperateConstant.CREATE);
        ElefaceSharingRelationOperateLog log2 = createMockOperateLog("SHARE_TEST_002", ElefaceSharingRelationOperateConstant.MEMO_UPDATE);
        List<ElefaceSharingRelationOperateLog> mockResult = Arrays.asList(log1, log2);

        when(elefaceSharingRelationOperateLogDao.searchListByQuery(queryBo)).thenReturn(mockResult);

        // 执行测试
        List<ElefaceSharingRelationOperateLog> result = elefaceSharingRelationOperateLogDao.searchListByQuery(queryBo);

        // 验证结果
        assertNotNull(result);
        assertEquals(2, result.size());
        verify(elefaceSharingRelationOperateLogDao).searchListByQuery(queryBo);
    }

    /**
     * 测试空的操作类型列表
     */
    @Test
    public void testSearchListByQuery_EmptyOperateTypeList() {
        // 准备测试数据 - 空的操作类型列表
        queryBo.setOperateTypeList(Collections.emptyList());
        queryBo.setLimit(10);
        queryBo.setOffset(0);

        // 准备模拟返回数据
        List<ElefaceSharingRelationOperateLog> mockResult = Collections.emptyList();

        when(elefaceSharingRelationOperateLogDao.searchListByQuery(queryBo)).thenReturn(mockResult);

        // 执行测试
        List<ElefaceSharingRelationOperateLog> result = elefaceSharingRelationOperateLogDao.searchListByQuery(queryBo);

        // 验证结果
        assertNotNull(result);
        assertEquals(0, result.size());
        verify(elefaceSharingRelationOperateLogDao).searchListByQuery(queryBo);
    }

    /**
     * 测试计数查询 - 包含操作类型过滤
     */
    @Test
    public void testCountByQuery_WithOperateTypeFilter() {
        // 准备测试数据
        queryBo.setOperateTypeList(Arrays.asList(
                ElefaceSharingRelationOperateConstant.CREATE,
                ElefaceSharingRelationOperateConstant.MEMO_UPDATE
        ));

        when(elefaceSharingRelationOperateLogDao.countByQuery(queryBo)).thenReturn(5);

        // 执行测试
        Integer count = elefaceSharingRelationOperateLogDao.countByQuery(queryBo);

        // 验证结果
        assertEquals(5, count);
        verify(elefaceSharingRelationOperateLogDao).countByQuery(queryBo);
    }

    /**
     * 测试计数查询 - 排除备注更新类型
     */
    @Test
    public void testCountByQuery_ExcludeMemoUpdateType() {
        // 准备测试数据 - 排除备注更新类型
        queryBo.setOperateTypeList(Arrays.asList(
                ElefaceSharingRelationOperateConstant.CREATE,
                ElefaceSharingRelationOperateConstant.CANCEL,
                ElefaceSharingRelationOperateConstant.UPDATE
        ));

        when(elefaceSharingRelationOperateLogDao.countByQuery(queryBo)).thenReturn(3);

        // 执行测试
        Integer count = elefaceSharingRelationOperateLogDao.countByQuery(queryBo);

        // 验证结果
        assertEquals(3, count);
        verify(elefaceSharingRelationOperateLogDao).countByQuery(queryBo);
    }

    /**
     * 测试时间范围过滤结合操作类型过滤
     */
    @Test
    public void testSearchListByQuery_WithTimeRangeAndOperateType() {
        // 准备测试数据
        LocalDateTime startTime = LocalDateTime.now().minusDays(7);
        LocalDateTime endTime = LocalDateTime.now();
        queryBo.setOperateTimeStart(startTime);
        queryBo.setOperateTimeEnd(endTime);
        queryBo.setOperateTypeList(Arrays.asList(ElefaceSharingRelationOperateConstant.MEMO_UPDATE));
        queryBo.setLimit(10);
        queryBo.setOffset(0);

        // 准备模拟返回数据
        ElefaceSharingRelationOperateLog log = createMockOperateLog("SHARE_TEST_001", ElefaceSharingRelationOperateConstant.MEMO_UPDATE);
        List<ElefaceSharingRelationOperateLog> mockResult = Arrays.asList(log);

        when(elefaceSharingRelationOperateLogDao.searchListByQuery(queryBo)).thenReturn(mockResult);

        // 执行测试
        List<ElefaceSharingRelationOperateLog> result = elefaceSharingRelationOperateLogDao.searchListByQuery(queryBo);

        // 验证结果
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals(ElefaceSharingRelationOperateConstant.MEMO_UPDATE, result.get(0).getOperateType());
        verify(elefaceSharingRelationOperateLogDao).searchListByQuery(queryBo);
    }

    /**
     * 创建模拟的操作日志对象
     */
    private ElefaceSharingRelationOperateLog createMockOperateLog(String shareId, Integer operateType) {
        ElefaceSharingRelationOperateLog log = new ElefaceSharingRelationOperateLog();
        log.setShareId(shareId);
        log.setOperateType(operateType);
        log.setTargetSellerId("testSellerId");
        log.setTargetSellerNick("testSellerNick");
        log.setTargetStoreId("testStoreId");
        log.setTargetAppName("testAppName");
        log.setShareMemo("测试备注");
        log.setLastShareNum(100L);
        log.setNewShareNum(100L);
        log.setOperateTerminal("WEB");
        log.setOperator("testOperator");
        log.setOperatorTime(LocalDateTime.now());
        return log;
    }
}
