package cn.loveapp.print.service.constant;

import static org.junit.jupiter.api.Assertions.*;

import org.junit.jupiter.api.Test;

/**
 * ElefaceSharingRelationOperateConstant 测试类
 * 测试操作类型常量的定义
 *
 * <AUTHOR>
 */
class ElefaceSharingRelationOperateConstantTest {

    /**
     * 测试所有操作类型常量的值
     */
    @Test
    void testOperateTypeConstants() {
        // 验证创建操作类型
        assertEquals(Integer.valueOf(1), ElefaceSharingRelationOperateConstant.CREATE);
        
        // 验证取消操作类型
        assertEquals(Integer.valueOf(-1), ElefaceSharingRelationOperateConstant.CANCEL);
        
        // 验证解绑操作类型
        assertEquals(Integer.valueOf(-2), ElefaceSharingRelationOperateConstant.UNTIE);
        
        // 验证修改操作类型
        assertEquals(Integer.valueOf(2), ElefaceSharingRelationOperateConstant.UPDATE);
        
        // 验证恢复操作类型
        assertEquals(Integer.valueOf(3), ElefaceSharingRelationOperateConstant.RECOVERY);
        
        // 验证取消回充操作类型
        assertEquals(Integer.valueOf(4), ElefaceSharingRelationOperateConstant.CANCEL_ADD);
        
        // 验证新增的面单备注修改操作类型
        assertEquals(Integer.valueOf(5), ElefaceSharingRelationOperateConstant.MEMO_UPDATE);
    }

    /**
     * 测试操作类型常量的唯一性
     */
    @Test
    void testOperateTypeUniqueness() {
        // 收集所有操作类型值
        Integer[] operateTypes = {
            ElefaceSharingRelationOperateConstant.CREATE,
            ElefaceSharingRelationOperateConstant.CANCEL,
            ElefaceSharingRelationOperateConstant.UNTIE,
            ElefaceSharingRelationOperateConstant.UPDATE,
            ElefaceSharingRelationOperateConstant.RECOVERY,
            ElefaceSharingRelationOperateConstant.CANCEL_ADD,
            ElefaceSharingRelationOperateConstant.MEMO_UPDATE
        };

        // 验证所有值都不为null
        for (Integer operateType : operateTypes) {
            assertNotNull(operateType, "操作类型常量不应为null");
        }

        // 验证新增的MEMO_UPDATE常量与其他常量值不重复
        assertNotEquals(ElefaceSharingRelationOperateConstant.MEMO_UPDATE, ElefaceSharingRelationOperateConstant.CREATE);
        assertNotEquals(ElefaceSharingRelationOperateConstant.MEMO_UPDATE, ElefaceSharingRelationOperateConstant.CANCEL);
        assertNotEquals(ElefaceSharingRelationOperateConstant.MEMO_UPDATE, ElefaceSharingRelationOperateConstant.UNTIE);
        assertNotEquals(ElefaceSharingRelationOperateConstant.MEMO_UPDATE, ElefaceSharingRelationOperateConstant.UPDATE);
        assertNotEquals(ElefaceSharingRelationOperateConstant.MEMO_UPDATE, ElefaceSharingRelationOperateConstant.RECOVERY);
        assertNotEquals(ElefaceSharingRelationOperateConstant.MEMO_UPDATE, ElefaceSharingRelationOperateConstant.CANCEL_ADD);
    }

    /**
     * 测试操作类型的分类
     */
    @Test
    void testOperateTypeCategories() {
        // 正数操作类型（正常操作）
        assertTrue(ElefaceSharingRelationOperateConstant.CREATE > 0, "创建操作应为正数");
        assertTrue(ElefaceSharingRelationOperateConstant.UPDATE > 0, "修改操作应为正数");
        assertTrue(ElefaceSharingRelationOperateConstant.RECOVERY > 0, "恢复操作应为正数");
        assertTrue(ElefaceSharingRelationOperateConstant.CANCEL_ADD > 0, "取消回充操作应为正数");
        assertTrue(ElefaceSharingRelationOperateConstant.MEMO_UPDATE > 0, "备注修改操作应为正数");

        // 负数操作类型（删除/取消操作）
        assertTrue(ElefaceSharingRelationOperateConstant.CANCEL < 0, "取消操作应为负数");
        assertTrue(ElefaceSharingRelationOperateConstant.UNTIE < 0, "解绑操作应为负数");
    }

    /**
     * 测试新增的MEMO_UPDATE常量的特性
     */
    @Test
    void testMemoUpdateConstant() {
        // 验证MEMO_UPDATE是最新添加的常量，值应该是最大的正数
        assertTrue(ElefaceSharingRelationOperateConstant.MEMO_UPDATE > ElefaceSharingRelationOperateConstant.CREATE);
        assertTrue(ElefaceSharingRelationOperateConstant.MEMO_UPDATE > ElefaceSharingRelationOperateConstant.UPDATE);
        assertTrue(ElefaceSharingRelationOperateConstant.MEMO_UPDATE > ElefaceSharingRelationOperateConstant.RECOVERY);
        assertTrue(ElefaceSharingRelationOperateConstant.MEMO_UPDATE > ElefaceSharingRelationOperateConstant.CANCEL_ADD);
        
        // 验证MEMO_UPDATE的具体值
        assertEquals(5, ElefaceSharingRelationOperateConstant.MEMO_UPDATE.intValue());
    }

    /**
     * 测试常量的类型
     */
    @Test
    void testConstantTypes() {
        // 验证所有常量都是Integer类型
        assertTrue(ElefaceSharingRelationOperateConstant.CREATE instanceof Integer);
        assertTrue(ElefaceSharingRelationOperateConstant.CANCEL instanceof Integer);
        assertTrue(ElefaceSharingRelationOperateConstant.UNTIE instanceof Integer);
        assertTrue(ElefaceSharingRelationOperateConstant.UPDATE instanceof Integer);
        assertTrue(ElefaceSharingRelationOperateConstant.RECOVERY instanceof Integer);
        assertTrue(ElefaceSharingRelationOperateConstant.CANCEL_ADD instanceof Integer);
        assertTrue(ElefaceSharingRelationOperateConstant.MEMO_UPDATE instanceof Integer);
    }
}
