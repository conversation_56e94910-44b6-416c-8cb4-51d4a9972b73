package cn.loveapp.print.service.service.impl;

import static org.mockito.Mockito.*;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import cn.loveapp.print.common.dao.newprint.AyElefaceOperatelogDao;
import com.google.common.collect.Lists;
import org.junit.Assert;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.boot.test.mock.mockito.SpyBean;
import org.springframework.test.context.junit4.SpringRunner;

import cn.loveapp.print.common.constant.PrintTypeConstant;
import cn.loveapp.print.common.dto.RecipientDTO;
import cn.loveapp.print.common.entity.AyElefaceOperatelog;
import cn.loveapp.print.common.entity.AyPrintlog;
import cn.loveapp.print.common.service.AesEncryptionService;
import cn.loveapp.print.service.bo.*;
import cn.loveapp.print.service.dao.newprint.*;
import cn.loveapp.print.service.dto.GroupedElefacePrintLogDTO;
import cn.loveapp.print.service.dto.LogisticsPrintlogQueryDTO;
import cn.loveapp.print.service.request.BasePrintRequest;
import cn.loveapp.print.service.request.PrintTradeInfoDTO;
import cn.loveapp.print.api.response.LogisticsPrintlogResponseDTO;
import cn.loveapp.print.api.dto.TradeLogisticsBindingHistoryResponseDTO;
import cn.loveapp.print.service.service.ElefaceWaybillService;
import cn.loveapp.print.service.service.OrdersService;
import cn.loveapp.print.service.service.PrintlogService;

@RunWith(SpringRunner.class)
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.NONE, classes = {PrintlogServiceImpl.class})
class PrintlogServiceImplTest {

    @SpyBean
    private PrintlogService printlogService;

    @MockBean
    private OrdersService ordersService;

    @MockBean
    private ElefaceWaybillService elefaceWaybillService;

    @MockBean
    private AyPrintlogDao ayPrintlogDao;

    @MockBean
    private AyElefacePrintlogDao ayElefacePrintlogDao;

    @MockBean
    private AyExpressPrintlogDao ayExpressPrintlogDao;

    @MockBean
    private AyDeliverPrintlogDao ayDeliverPrintlogDao;

    @MockBean
    private AyElefaceOperatelogDao ayElefaceOperatelogDao;

    @MockBean
    private AesEncryptionService aesEncryptionService;

    private String operator = "赵东昊的测试店铺:guchao";

    private String operatorStoreId = "TAO";

    private String operateTerminal = "pc";

    private String storeId = "TAO";
    private String sellerId = "3936370796";
    private String appName = "trade";
    private String sellerNick = "赵东昊的测试店铺";

    private String mergeTid = "MTD13123123123";

    private Integer tradeType = 0;

    private PrintTradeInfoDTO printTradeInfoDTO;

    private UserInfoBo userInfoBo;

    private RecipientDTO recipientDTO;

    private BasePrintRequest basePrintRequest;

    private String buyerNick = "子小_十一八";

    private String name = "孙平";

    private String mobile = "111222333444";

    private String city = "上海市";

    private String detailAddress = "新二路55号空间技术产业基地";

    private String district = "宝山区";

    private String provice = "上海";

    private String town = "高境镇";

    private String zipCode = "201900";

    private String printer = "Print to PDF (Mac Desktop)";

    private Integer printCount = 1;

    @BeforeEach
    void setUp() {
        printTradeInfoDTO = new PrintTradeInfoDTO();
        printTradeInfoDTO.setTid("918959645698872009");
        printTradeInfoDTO.setOids(Collections.singletonList("918959645698872009"));

        userInfoBo = new UserInfoBo();
        userInfoBo.setSellerNick(sellerNick);
        userInfoBo.setAppName(appName);
        userInfoBo.setSellerId(sellerId);
        userInfoBo.setStoreId(storeId);
        userInfoBo.setOperateTerminal(operateTerminal);
        userInfoBo.setOperatorStoreId(operatorStoreId);
        userInfoBo.setOperator(operator);

        recipientDTO = new RecipientDTO();
        recipientDTO.setName(name);
        recipientDTO.setMobile(mobile);
        recipientDTO.setCity(city);
        recipientDTO.setDetailAddress(detailAddress);
        recipientDTO.setDistrict(district);
        recipientDTO.setProvince(provice);
        recipientDTO.setTown(town);
        recipientDTO.setZipCode(zipCode);

        basePrintRequest = new BasePrintRequest();
        basePrintRequest.setIsSplit(false);
        basePrintRequest.setTradeType(tradeType);
        basePrintRequest.setSerial("0405222");
        basePrintRequest.setPrinter(printer);
        basePrintRequest.setPrintCount(printCount);
        basePrintRequest.setBuyerNick(buyerNick);
    }

    private String cpCode = "ZJS";
    private String logisticsCompany = "宅急送";
    private String waybillCode = "ZJS001559427822";

    /**
     * 面单打印-普通订单
     *
     * @throws Exception
     */
    @Test
    void elefacePrintTest1() throws Exception {
        ElefacePrintBo elefacePrintBo = new ElefacePrintBo();
        basePrintRequest.setTradeInfoList(Collections.singletonList(new PrintTradeInfoDTO()));
        elefacePrintBo.setBasePrintInfo(basePrintRequest);
        elefacePrintBo.setCpCode(cpCode);
        elefacePrintBo.setLogisticsCompany(logisticsCompany);
        elefacePrintBo.setWaybillCode(waybillCode);
        elefacePrintBo.setChildWaybillCode(null);
        elefacePrintBo.setRecipient(recipientDTO);
        elefacePrintBo.setPrintData("");
        elefacePrintBo.setCustomData("");

        printlogService.elefacePrint(elefacePrintBo, userInfoBo);

        verify(ayElefacePrintlogDao, times(1)).insert(any());
        verify(ayPrintlogDao).insertBatch(anyList());
    }

    /**
     * 面单打印-合单
     *
     * @throws Exception
     */
    @Test
    void elefacePrintTest2() throws Exception {
        ElefacePrintBo elefacePrintBo = new ElefacePrintBo();
        basePrintRequest.setTradeInfoList(Arrays.asList(new PrintTradeInfoDTO(), new PrintTradeInfoDTO()));
        basePrintRequest.setMergeTid(mergeTid);
        elefacePrintBo.setBasePrintInfo(basePrintRequest);
        elefacePrintBo.setCpCode(cpCode);
        elefacePrintBo.setLogisticsCompany(logisticsCompany);
        elefacePrintBo.setWaybillCode(waybillCode);
        elefacePrintBo.setChildWaybillCode(null);
        elefacePrintBo.setRecipient(recipientDTO);
        elefacePrintBo.setPrintData("");
        elefacePrintBo.setCustomData("");

        printlogService.elefacePrint(elefacePrintBo, userInfoBo);

        verify(ayElefacePrintlogDao, times(1)).insert(any());
        verify(ayPrintlogDao).insertBatch(anyList());
    }

    private String expressImage = "zhongtong01.jpg";

    /**
     * 快递单打印-普通订单
     *
     * @throws Exception
     */
    @Test
    void expressPrintTest1() throws Exception {
        ExpressPrintBo expressPrintBo = new ExpressPrintBo();
        basePrintRequest.setTradeInfoList(Collections.singletonList(new PrintTradeInfoDTO()));
        expressPrintBo.setBasePrintInfo(basePrintRequest);
        expressPrintBo.setLogisticsCompany(logisticsCompany);
        expressPrintBo.setExpressImage(expressImage);
        expressPrintBo.setRecipient(recipientDTO);
        expressPrintBo.setPrintData("");
        expressPrintBo.setPrintModule("");

        printlogService.expressPrint(expressPrintBo, userInfoBo);

        verify(ayExpressPrintlogDao, times(1)).insert(any());
        verify(ayPrintlogDao).insertBatch(anyList());
    }

    /**
     * 快递单打印-普通订单
     *
     * @throws Exception
     */
    @Test
    void expressPrintTest2() throws Exception {
        ExpressPrintBo expressPrintBo = new ExpressPrintBo();
        basePrintRequest.setTradeInfoList(Arrays.asList(new PrintTradeInfoDTO(), new PrintTradeInfoDTO()));
        basePrintRequest.setMergeTid(mergeTid);
        expressPrintBo.setBasePrintInfo(basePrintRequest);
        expressPrintBo.setLogisticsCompany(logisticsCompany);
        expressPrintBo.setExpressImage(expressImage);
        expressPrintBo.setRecipient(recipientDTO);
        expressPrintBo.setPrintData("");
        expressPrintBo.setPrintModule("");

        printlogService.expressPrint(expressPrintBo, userInfoBo);

        verify(ayExpressPrintlogDao, times(1)).insert(any());
        verify(ayPrintlogDao).insertBatch(anyList());
    }

    /**
     * 发货单打印-普通订单
     *
     * @throws Exception
     */
    @Test
    void deliverPrintTest1() throws Exception {
        DeliverPrintBo deliverPrintBo = new DeliverPrintBo();
        basePrintRequest.setTradeInfoList(Collections.singletonList(new PrintTradeInfoDTO()));
        deliverPrintBo.setBasePrintInfo(basePrintRequest);
        deliverPrintBo.setRecipient(recipientDTO);
        deliverPrintBo.setPrintData("");
        deliverPrintBo.setPrintModule("");

        printlogService.deliverPrint(deliverPrintBo, userInfoBo);

        verify(ayDeliverPrintlogDao, times(1)).insert(any());
        verify(ayPrintlogDao).insertBatch(anyList());
    }

    /**
     * 发货单打印-普通订单
     *
     * @throws Exception
     */
    @Test
    void deliverPrintTest2() throws Exception {
        DeliverPrintBo deliverPrintBo = new DeliverPrintBo();
        basePrintRequest.setTradeInfoList(Arrays.asList(new PrintTradeInfoDTO(), new PrintTradeInfoDTO()));
        basePrintRequest.setMergeTid(mergeTid);
        deliverPrintBo.setBasePrintInfo(basePrintRequest);
        deliverPrintBo.setRecipient(recipientDTO);
        deliverPrintBo.setPrintData("");
        deliverPrintBo.setPrintModule("");

        printlogService.deliverPrint(deliverPrintBo, userInfoBo);

        verify(ayDeliverPrintlogDao, times(1)).insert(any());
        verify(ayPrintlogDao).insertBatch(anyList());
    }

    /**
     * 保存面单取消状态
     */
    @Test
    void saveWaybillIsCancelTest1() {
        String cpCode = "ZJS";
        String waybillCode = "ZJS001559427822";
        printlogService.saveWaybillIsCancel(cpCode, waybillCode, userInfoBo);
        verify(ayPrintlogDao, times(1)).updateWaybillIsCancel(true, cpCode, waybillCode, userInfoBo.getStoreId(),
            userInfoBo.getSellerId(), userInfoBo.getAppName());
    }

    @Test
    void queryTradeLogisticsBindingHistoryBatchTest1() {
        String tid1 = "200414-254571971631188";
        String tid2 = "200424-485002445832982";
        List<String> tids = Arrays.asList(tid1, tid2);
        AyPrintlog ayPrintlog1 = new AyPrintlog();
        ayPrintlog1.setTid(tid1);
        AyPrintlog ayPrintlog2 = new AyPrintlog();
        ayPrintlog2.setTid(tid2);
        List<AyPrintlog> ayPrintlogList = Arrays.asList(ayPrintlog1, ayPrintlog2);
        when(ayPrintlogDao.queryByTidsAndPrintType(tids, tradeType, PrintTypeConstant.EXPRESS, userInfoBo.getStoreId(),
            userInfoBo.getSellerId(), userInfoBo.getAppName())).thenReturn(ayPrintlogList);

        AyElefaceOperatelog ayElefaceOperatelog1 = new AyElefaceOperatelog();
        ayElefaceOperatelog1.setTid(tid1);

        AyElefaceOperatelog ayElefaceOperatelog2 = new AyElefaceOperatelog();
        ayElefaceOperatelog2.setTid(tid2);

        List<AyElefaceOperatelog> ayElefaceOperatelogList = Arrays.asList(ayElefaceOperatelog1, ayElefaceOperatelog2);
        when(ayElefaceOperatelogDao.queryByTids(tids, tradeType, userInfoBo.getStoreId(), userInfoBo.getSellerId(),
            userInfoBo.getAppName())).thenReturn(ayElefaceOperatelogList);

        TradeLogisticsBindingHistoryResponseDTO responseDTO =
            printlogService.queryTradeLogisticsBindingHistoryBatch(tids, tradeType, true, userInfoBo);
        Assert.assertEquals(2, responseDTO.getTradeLogisticsBindingHistoryList().size());
        Assert.assertEquals(2, responseDTO.getTradeLogisticsBindingHistoryList().get(0).getBindingHistoryList().size());
        Assert.assertEquals(2, responseDTO.getTradeLogisticsBindingHistoryList().get(1).getBindingHistoryList().size());

    }

    @Test
    void logisticsPrintlogListGetTest1() {
        LogisticsPrintlogQueryDTO queryDTO = new LogisticsPrintlogQueryDTO();

        Long totalResult = 2L;

        when(ayPrintlogDao.queryLogisticsCount(queryDTO, userInfoBo.getStoreId(), userInfoBo.getSellerId(),
            userInfoBo.getAppName())).thenReturn(totalResult);

        when(ayPrintlogDao.queryLogisticsListOrderByPrintTime(queryDTO, userInfoBo.getStoreId(),
            userInfoBo.getSellerId(), userInfoBo.getAppName()))
                .thenReturn(Arrays.asList(new AyPrintlog(), new AyPrintlog()));

        LogisticsPrintlogResponseDTO responseDTO = printlogService.logisticsPrintLogListGetByESSearch(queryDTO, Lists.newArrayList(userInfoBo));

        Assert.assertEquals(totalResult, responseDTO.getTotalResult());

        Assert.assertEquals(2, responseDTO.getPrintlogList().size());
    }

    /**
     * 电子面单打印记录聚合 group by cp_code
     */
    @Test
    void groupElefacePrintLogTest1() {
        GroupElefacePrintLogQueryBo queryBo = new GroupElefacePrintLogQueryBo();
        GroupedElefacePrintLogDTO groupedValue1 = new GroupedElefacePrintLogDTO();
        groupedValue1.setCpCode("ZJS");
        groupedValue1.setLogisticsCompany("宅急送");
        groupedValue1.setTotalPrint(10);
        groupedValue1.setTotalPrint(2);

        GroupedElefacePrintLogDTO groupedValue2 = new GroupedElefacePrintLogDTO();
        groupedValue2.setCpCode("SFKY");
        groupedValue2.setLogisticsCompany("顺丰快运");
        groupedValue2.setTotalPrint(10);
        groupedValue2.setTotalPrint(2);

        List<GroupedElefacePrintLogDTO> exceptedResult = Arrays.asList(groupedValue1, groupedValue2);

        queryBo.setCpCodeList(Arrays.asList("ZJS", "SFKY"));
        when(ayPrintlogDao.groupElefacePrintLogByCpCode(eq(queryBo), eq(userInfoBo.getSellerId()),
            eq(userInfoBo.getStoreId()), eq(userInfoBo.getAppName()))).thenReturn(exceptedResult);

        Assert.assertEquals(exceptedResult, printlogService.groupElefacePrintLog(queryBo, userInfoBo));
    }

    /**
     * 电子面单打印记录聚合 group by cp_code, receiver_province
     */
    @Test
    void groupElefacePrintLogTest2() {
        GroupElefacePrintLogQueryBo queryBo = new GroupElefacePrintLogQueryBo();
        queryBo.setCpCodeList(Arrays.asList("ZJS", "SFKY"));
        queryBo.setReceiverProvinceList(Arrays.asList("上海市", "江苏省"));

        GroupedElefacePrintLogDTO groupedValue1 = new GroupedElefacePrintLogDTO();
        groupedValue1.setCpCode("ZJS");
        groupedValue1.setLogisticsCompany("宅急送");
        groupedValue1.setReceiverProvince("上海市");
        groupedValue1.setTotalPrint(10);
        groupedValue1.setTotalPrint(2);

        GroupedElefacePrintLogDTO groupedValue2 = new GroupedElefacePrintLogDTO();
        groupedValue2.setCpCode("SFKY");
        groupedValue2.setLogisticsCompany("江苏省");
        groupedValue2.setLogisticsCompany("顺丰快运");
        groupedValue2.setTotalPrint(10);
        groupedValue2.setTotalPrint(2);

        List<GroupedElefacePrintLogDTO> exceptedResult = Arrays.asList(groupedValue1, groupedValue2);

        when(ayPrintlogDao.groupElefacePrintLogByCpCodeAndReceiverProvince(eq(queryBo), eq(userInfoBo.getSellerId()),
            eq(userInfoBo.getStoreId()), eq(userInfoBo.getAppName()))).thenReturn(exceptedResult);

        Assert.assertEquals(exceptedResult, printlogService.groupElefacePrintLog(queryBo, userInfoBo));
    }

}
