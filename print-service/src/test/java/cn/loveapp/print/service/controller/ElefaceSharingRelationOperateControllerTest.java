package cn.loveapp.print.service.controller;

import cn.loveapp.common.constant.CommonAppConstants;
import cn.loveapp.common.constant.CommonPlatformConstants;
import cn.loveapp.common.dto.UserSessionInfo;
import cn.loveapp.common.web.CommonApiResponse;
import cn.loveapp.print.service.bo.ElefaceSharingRelationQueryBo;
import cn.loveapp.print.service.constant.ElefaceSharingRelationOperateConstant;
import cn.loveapp.print.service.dto.ElefaceSharingRelationOperateDTO;
import cn.loveapp.print.service.dto.TargetUserInfoDTO;
import cn.loveapp.print.service.request.ElefaceSharingRelationOperateQueryRequest;
import cn.loveapp.print.service.response.ElefaceSharingRelationOperateListResponse;
import cn.loveapp.print.service.service.ElefaceSharingRelationOperateService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

/**
 * ElefaceSharingRelationOperateController 测试类
 * 主要测试操作类型兼容性处理
 *
 * <AUTHOR>
 */
@ExtendWith(MockitoExtension.class)
public class ElefaceSharingRelationOperateControllerTest {

    @InjectMocks
    private ElefaceSharingRelationOperateController controller;

    @Mock
    private ElefaceSharingRelationOperateService elefaceSharingRelationOperateService;

    private UserSessionInfo sessionInfo;
    private TargetUserInfoDTO targetUserInfoDTO;

    @BeforeEach
    public void setUp() {
        // 初始化用户会话信息
        sessionInfo = new UserSessionInfo();
        sessionInfo.setNick("testSellerNick");
        sessionInfo.setSellerId("testSellerId");
        sessionInfo.setStoreId(CommonPlatformConstants.PLATFORM_TAO);
        sessionInfo.setAppName(CommonAppConstants.APP_TRADE);
        sessionInfo.setLoginTerminal("WEB");
        sessionInfo.setMallName("控制器测试店铺");

        targetUserInfoDTO = new TargetUserInfoDTO();
    }

    /**
     * 测试老客户端兼容性 - 不传递操作类型列表
     * 应该默认排除备注更新类型
     */
    @Test
    public void testGetAllBranches_OldClientCompatibility() throws Exception {
        // 准备测试数据
        ElefaceSharingRelationOperateQueryRequest request = new ElefaceSharingRelationOperateQueryRequest();
        request.setOperateTypeList(null); // 老客户端不传递操作类型列表

        List<ElefaceSharingRelationOperateDTO> mockResult = Collections.emptyList();
        when(elefaceSharingRelationOperateService.searchOperateListGet(any(), any())).thenReturn(mockResult);

        // 执行测试方法
        CommonApiResponse<List<ElefaceSharingRelationOperateDTO>> response =
                controller.getAllBranches(request, sessionInfo, targetUserInfoDTO);

        // 验证响应成功
        assertTrue(response.isSuccess());

        // 捕获传递给服务层的查询对象
        ArgumentCaptor<ElefaceSharingRelationQueryBo> queryBoCaptor = ArgumentCaptor.forClass(ElefaceSharingRelationQueryBo.class);
        verify(elefaceSharingRelationOperateService).searchOperateListGet(queryBoCaptor.capture(), any());

        // 验证操作类型列表包含除备注更新外的所有类型
        ElefaceSharingRelationQueryBo capturedQueryBo = queryBoCaptor.getValue();
        List<Integer> expectedOperateTypes = Arrays.asList(
                ElefaceSharingRelationOperateConstant.CREATE,
                ElefaceSharingRelationOperateConstant.CANCEL,
                ElefaceSharingRelationOperateConstant.UNTIE,
                ElefaceSharingRelationOperateConstant.UPDATE,
                ElefaceSharingRelationOperateConstant.RECOVERY,
                ElefaceSharingRelationOperateConstant.CANCEL_ADD
        );
        assertEquals(expectedOperateTypes, capturedQueryBo.getOperateTypeList());
        assertFalse(capturedQueryBo.getOperateTypeList().contains(ElefaceSharingRelationOperateConstant.MEMO_UPDATE));
    }

    /**
     * 测试新客户端 - 传递完整的操作类型列表
     * 应该使用客户端传递的类型列表
     */
    @Test
    public void testGetAllBranches_NewClientWithFullTypes() throws Exception {
        // 准备测试数据 - 新客户端传递包含备注更新的完整类型列表
        ElefaceSharingRelationOperateQueryRequest request = new ElefaceSharingRelationOperateQueryRequest();
        List<Integer> fullOperateTypes = Arrays.asList(
                ElefaceSharingRelationOperateConstant.CREATE,
                ElefaceSharingRelationOperateConstant.CANCEL,
                ElefaceSharingRelationOperateConstant.UNTIE,
                ElefaceSharingRelationOperateConstant.UPDATE,
                ElefaceSharingRelationOperateConstant.RECOVERY,
                ElefaceSharingRelationOperateConstant.CANCEL_ADD,
                ElefaceSharingRelationOperateConstant.MEMO_UPDATE // 包含备注更新类型
        );
        request.setOperateTypeList(fullOperateTypes);

        List<ElefaceSharingRelationOperateDTO> mockResult = Collections.emptyList();
        when(elefaceSharingRelationOperateService.searchOperateListGet(any(), any())).thenReturn(mockResult);

        // 执行测试方法
        CommonApiResponse<List<ElefaceSharingRelationOperateDTO>> response =
                controller.getAllBranches(request, sessionInfo, targetUserInfoDTO);

        // 验证响应成功
        assertTrue(response.isSuccess());

        // 捕获传递给服务层的查询对象
        ArgumentCaptor<ElefaceSharingRelationQueryBo> queryBoCaptor = ArgumentCaptor.forClass(ElefaceSharingRelationQueryBo.class);
        verify(elefaceSharingRelationOperateService).searchOperateListGet(queryBoCaptor.capture(), any());

        // 验证操作类型列表与客户端传递的一致
        ElefaceSharingRelationQueryBo capturedQueryBo = queryBoCaptor.getValue();
        assertEquals(fullOperateTypes, capturedQueryBo.getOperateTypeList());
        assertTrue(capturedQueryBo.getOperateTypeList().contains(ElefaceSharingRelationOperateConstant.MEMO_UPDATE));
    }

    /**
     * 测试新客户端 - 传递部分操作类型列表
     * 应该使用客户端传递的类型列表
     */
    @Test
    public void testGetAllBranches_NewClientWithPartialTypes() throws Exception {
        // 准备测试数据 - 新客户端只传递部分类型
        ElefaceSharingRelationOperateQueryRequest request = new ElefaceSharingRelationOperateQueryRequest();
        List<Integer> partialOperateTypes = Arrays.asList(
                ElefaceSharingRelationOperateConstant.CREATE,
                ElefaceSharingRelationOperateConstant.MEMO_UPDATE // 只关注创建和备注更新
        );
        request.setOperateTypeList(partialOperateTypes);

        List<ElefaceSharingRelationOperateDTO> mockResult = Collections.emptyList();
        when(elefaceSharingRelationOperateService.searchOperateListGet(any(), any())).thenReturn(mockResult);

        // 执行测试方法
        CommonApiResponse<List<ElefaceSharingRelationOperateDTO>> response =
                controller.getAllBranches(request, sessionInfo, targetUserInfoDTO);

        // 验证响应成功
        assertTrue(response.isSuccess());

        // 捕获传递给服务层的查询对象
        ArgumentCaptor<ElefaceSharingRelationQueryBo> queryBoCaptor = ArgumentCaptor.forClass(ElefaceSharingRelationQueryBo.class);
        verify(elefaceSharingRelationOperateService).searchOperateListGet(queryBoCaptor.capture(), any());

        // 验证操作类型列表与客户端传递的一致
        ElefaceSharingRelationQueryBo capturedQueryBo = queryBoCaptor.getValue();
        assertEquals(partialOperateTypes, capturedQueryBo.getOperateTypeList());
        assertEquals(2, capturedQueryBo.getOperateTypeList().size());
    }

    /**
     * 测试分页接口的老客户端兼容性
     */
    @Test
    public void testGetSharingRelationOperate_OldClientCompatibility() throws Exception {
        // 准备测试数据
        ElefaceSharingRelationOperateQueryRequest request = new ElefaceSharingRelationOperateQueryRequest();
        request.setPage(1);
        request.setPageSize(20);
        request.setOperateTypeList(null); // 老客户端不传递操作类型列表

        ElefaceSharingRelationOperateListResponse mockResponse = new ElefaceSharingRelationOperateListResponse();
        when(elefaceSharingRelationOperateService.searchOperateListGetByPage(any(), any())).thenReturn(mockResponse);

        // 执行测试方法
        CommonApiResponse<ElefaceSharingRelationOperateListResponse> response =
                controller.getSharingRelationOperate(request, sessionInfo, targetUserInfoDTO);

        // 验证响应成功
        assertTrue(response.isSuccess());

        // 捕获传递给服务层的查询对象
        ArgumentCaptor<ElefaceSharingRelationQueryBo> queryBoCaptor = ArgumentCaptor.forClass(ElefaceSharingRelationQueryBo.class);
        verify(elefaceSharingRelationOperateService).searchOperateListGetByPage(queryBoCaptor.capture(), any());

        // 验证操作类型列表包含除备注更新外的所有类型
        ElefaceSharingRelationQueryBo capturedQueryBo = queryBoCaptor.getValue();
        List<Integer> expectedOperateTypes = Arrays.asList(
                ElefaceSharingRelationOperateConstant.CREATE,
                ElefaceSharingRelationOperateConstant.CANCEL,
                ElefaceSharingRelationOperateConstant.UNTIE,
                ElefaceSharingRelationOperateConstant.UPDATE,
                ElefaceSharingRelationOperateConstant.RECOVERY,
                ElefaceSharingRelationOperateConstant.CANCEL_ADD
        );
        assertEquals(expectedOperateTypes, capturedQueryBo.getOperateTypeList());
        assertFalse(capturedQueryBo.getOperateTypeList().contains(ElefaceSharingRelationOperateConstant.MEMO_UPDATE));

        // 验证分页参数设置正确
        assertEquals(0, capturedQueryBo.getOffset()); // (1-1) * 20 = 0
        assertEquals(20, capturedQueryBo.getLimit());
    }

    /**
     * 测试分页接口的新客户端
     */
    @Test
    public void testGetSharingRelationOperate_NewClientWithFullTypes() throws Exception {
        // 准备测试数据
        ElefaceSharingRelationOperateQueryRequest request = new ElefaceSharingRelationOperateQueryRequest();
        request.setPage(2);
        request.setPageSize(10);
        List<Integer> fullOperateTypes = Arrays.asList(
                ElefaceSharingRelationOperateConstant.CREATE,
                ElefaceSharingRelationOperateConstant.MEMO_UPDATE
        );
        request.setOperateTypeList(fullOperateTypes);

        ElefaceSharingRelationOperateListResponse mockResponse = new ElefaceSharingRelationOperateListResponse();
        when(elefaceSharingRelationOperateService.searchOperateListGetByPage(any(), any())).thenReturn(mockResponse);

        // 执行测试方法
        CommonApiResponse<ElefaceSharingRelationOperateListResponse> response =
                controller.getSharingRelationOperate(request, sessionInfo, targetUserInfoDTO);

        // 验证响应成功
        assertTrue(response.isSuccess());

        // 捕获传递给服务层的查询对象
        ArgumentCaptor<ElefaceSharingRelationQueryBo> queryBoCaptor = ArgumentCaptor.forClass(ElefaceSharingRelationQueryBo.class);
        verify(elefaceSharingRelationOperateService).searchOperateListGetByPage(queryBoCaptor.capture(), any());

        // 验证操作类型列表与客户端传递的一致
        ElefaceSharingRelationQueryBo capturedQueryBo = queryBoCaptor.getValue();
        assertEquals(fullOperateTypes, capturedQueryBo.getOperateTypeList());
        assertTrue(capturedQueryBo.getOperateTypeList().contains(ElefaceSharingRelationOperateConstant.MEMO_UPDATE));

        // 验证分页参数设置正确
        assertEquals(10, capturedQueryBo.getOffset()); // (2-1) * 10 = 10
        assertEquals(10, capturedQueryBo.getLimit());
    }

    /**
     * 测试传递空的操作类型列表
     */
    @Test
    public void testGetAllBranches_EmptyOperateTypeList() throws Exception {
        // 准备测试数据
        ElefaceSharingRelationOperateQueryRequest request = new ElefaceSharingRelationOperateQueryRequest();
        request.setOperateTypeList(Collections.emptyList()); // 传递空列表

        List<ElefaceSharingRelationOperateDTO> mockResult = Collections.emptyList();
        when(elefaceSharingRelationOperateService.searchOperateListGet(any(), any())).thenReturn(mockResult);

        // 执行测试方法
        CommonApiResponse<List<ElefaceSharingRelationOperateDTO>> response =
                controller.getAllBranches(request, sessionInfo, targetUserInfoDTO);

        // 验证响应成功
        assertTrue(response.isSuccess());

        // 捕获传递给服务层的查询对象
        ArgumentCaptor<ElefaceSharingRelationQueryBo> queryBoCaptor = ArgumentCaptor.forClass(ElefaceSharingRelationQueryBo.class);
        verify(elefaceSharingRelationOperateService).searchOperateListGet(queryBoCaptor.capture(), any());

        // 验证空列表被当作老客户端处理，设置默认的操作类型列表
        ElefaceSharingRelationQueryBo capturedQueryBo = queryBoCaptor.getValue();
        List<Integer> expectedOperateTypes = Arrays.asList(
                ElefaceSharingRelationOperateConstant.CREATE,
                ElefaceSharingRelationOperateConstant.CANCEL,
                ElefaceSharingRelationOperateConstant.UNTIE,
                ElefaceSharingRelationOperateConstant.UPDATE,
                ElefaceSharingRelationOperateConstant.RECOVERY,
                ElefaceSharingRelationOperateConstant.CANCEL_ADD
        );
        assertEquals(expectedOperateTypes, capturedQueryBo.getOperateTypeList());
        assertFalse(capturedQueryBo.getOperateTypeList().contains(ElefaceSharingRelationOperateConstant.MEMO_UPDATE));
    }
}
