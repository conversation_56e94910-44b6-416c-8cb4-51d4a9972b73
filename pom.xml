<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <groupId>cn.loveapp.print</groupId>
    <artifactId>print-services-group</artifactId>
    <packaging>pom</packaging>

    <parent>
		<groupId>cn.loveapp.common</groupId>
		<artifactId>common-spring-boot-parent</artifactId>
		<version>1.35.20-SNAPSHOT</version>
    </parent>

    <name>爱用-print</name>
    <description>爱用-print</description>
    <version>1.0-SNAPSHOT</version>

    <organization>
        <name>Loveapp Inc.</name>
        <url>http://www.aiyongbao.com</url>
    </organization>

    <properties>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
        <java.version>1.8</java.version>
        <uac.version>1.16.7-SNAPSHOT</uac.version>
        <pacsdk.version>1.0.6</pacsdk.version>
        <org.mapstruct.version>1.5.5.Final</org.mapstruct.version>
        <rocketmq.version>4.5.0</rocketmq.version>
        <orders-api.version>1.4.5-SNAPSHOT</orders-api.version>
        <shops-api.version>1.5-SNAPSHOT</shops-api.version>
        <print-api.version>1.3-SNAPSHOT</print-api.version>
    </properties>

    <modules>
        <module>print-api</module>
        <module>print-common</module>
        <module>print-service</module>
        <module>print-maintain</module>
        <module>print-general-consumer</module>
    </modules>

    <dependencies>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>cn.loveapp.common</groupId>
            <artifactId>common-platformsdk-starter</artifactId>
        </dependency>

        <dependency>
            <groupId>cn.loveapp.uac</groupId>
            <artifactId>uac-api</artifactId>
            <version>${uac.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>cn.loveapp.common</groupId>
                    <artifactId>common-spring-boot-parent</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter</artifactId>
        </dependency>
        <!-- https://mvnrepository.com/artifact/org.springframework.cloud/spring-cloud-starter-openfeign -->
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-openfeign</artifactId>
        </dependency>

        <dependency>
            <groupId>org.databene</groupId>
            <artifactId>contiperf</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>com.taobao</groupId>
            <artifactId>pacsdk</artifactId>
            <version>${pacsdk.version}</version>
        </dependency>
        <dependency>
            <groupId>org.mapstruct</groupId>
            <artifactId>mapstruct</artifactId>
            <version>${org.mapstruct.version}</version>
        </dependency>
        <dependency>
            <groupId>org.apache.rocketmq</groupId>
            <artifactId>rocketmq-client</artifactId>
            <version>${rocketmq.version}</version>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.8.1</version>
                <configuration>
                    <source>1.8</source> <!-- depending on your project -->
                    <target>1.8</target> <!-- depending on your project -->
                    <annotationProcessorPaths>
                        <path>
                            <groupId>org.projectlombok</groupId>
                            <artifactId>lombok</artifactId>
                            <version>${lombok.version}</version>
                        </path>
                        <path>
                            <groupId>org.mapstruct</groupId>
                            <artifactId>mapstruct-processor</artifactId>
                            <version>${org.mapstruct.version}</version>
                        </path>
                        <!-- other annotation processors -->
                    </annotationProcessorPaths>
                </configuration>
            </plugin>
        </plugins>
    </build>

</project>
