package cn.loveapp.print.maintain.service;

import java.math.BigInteger;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.concurrent.SynchronousQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.dao.DataAccessException;
import org.springframework.jdbc.core.BeanPropertyRowMapper;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import com.google.common.collect.ImmutableList;
import com.google.common.collect.ImmutableMap;
import com.google.common.util.concurrent.ThreadFactoryBuilder;

import cn.loveapp.common.utils.LoggerHelper;
import cn.loveapp.print.maintain.config.PrintMaintainConfig;
import cn.loveapp.print.maintain.entity.NewPrintLog;
import lombok.Data;

/**
 * 新打印表过期数据删除并收缩空间
 *
 * <AUTHOR>
 * @date 2023/7/10
 */
@Component
public class DeleteExpiredNewPrintLogData {
    private static final LoggerHelper LOGGER = LoggerHelper.getLogger(DeleteExpiredNewPrintLogData.class);
    private final static String AY_PRINTLOG = "ay_printlog_";
    private final static List<String> TABLE_LIST =
        ImmutableList.of(AY_PRINTLOG, "ay_eleface_printlog_", "ay_eleface_operatelog_", "ay_serial_log_", "ay_express_printlog_", "ay_deliver_printlog_");
    private final JdbcTemplate newPrintDataSourceJdbc1Template;
    private final JdbcTemplate newPrintDataSourceJdbc2Template;
    private final NamedParameterJdbcTemplate newPrintNamedParameterJdbc1Template;
    private final NamedParameterJdbcTemplate newPrintNamedParameterJdbc2Template;
    @Autowired
    private PrintMaintainConfig config;
    private volatile boolean stopped = false;

    private volatile boolean running = false;

    public DeleteExpiredNewPrintLogData(
        @Qualifier("newPrintDataSourceJdbcTemplate") JdbcTemplate newPrintDataSourceJdbc1Template,
        @Qualifier("newPrintDataSource1JdbcTemplate") JdbcTemplate newPrintDataSource1JdbcTemplate) {

        this.newPrintDataSourceJdbc1Template = newPrintDataSourceJdbc1Template;
        this.newPrintDataSourceJdbc2Template = newPrintDataSource1JdbcTemplate;
        this.newPrintNamedParameterJdbc1Template = new NamedParameterJdbcTemplate(newPrintDataSourceJdbc1Template);
        this.newPrintNamedParameterJdbc2Template = new NamedParameterJdbcTemplate(newPrintDataSource1JdbcTemplate);
    }

    public void start() {
        synchronized (this) {
            if (running) {
                LOGGER.logInfo("清理新打印表任务, 正在运行中, 跳过");
                return;
            }
            running = true;
        }
        try {
            LOGGER.logInfo("开始 清理新打印表任务");
            this.stopped = false;

            deleteAllExpiredPrintLog();

            optimizePrintLog();

            LOGGER.logInfo("清理新打印表任务 结束");
        } finally {
            running = false;
        }
    }

    /**
     * 删除过期打印日志
     */
    private void deleteAllExpiredPrintLog() {
        BeanPropertyRowMapper<NewPrintLog> beanPropertyRowMapper = new BeanPropertyRowMapper<>(NewPrintLog.class);

        int expiredDays = config.getNewPrintLogDeleteExpiredTime();
        // 其他表 需要比 printLog 多存几天, 防止 printLog 比其他表的数据多时导致查找异常
        LocalDateTime printlogExpiredTime = LocalDate.now().atStartOfDay().minusDays(expiredDays);
        LocalDateTime otherTableExpiredTime = LocalDate.now().atStartOfDay().minusDays(expiredDays + 1);

        LOGGER.logInfo("清理新打印表任务, 过期时间: printlogExpiredTime=" + printlogExpiredTime + ", otherTableExpiredTime="
            + otherTableExpiredTime);

        if (!config.isNewPrintLogDeleteRecordEnabled()) {
            LOGGER.logInfo("清理新打印表任务, 开关关闭, 不真实删除记录");
        }
        // 必须先删除 ay_printlog 表
        for (String tablePrefix : TABLE_LIST) {
            ThreadPoolExecutor poolExecutor = createThreadPoolExecutor();
            for (int i = 0; i < 100; i++) {
                if (i < 50) {
                    deleteTableExpiredPrintLog(poolExecutor, beanPropertyRowMapper, printlogExpiredTime,
                        otherTableExpiredTime, i, tablePrefix, newPrintDataSourceJdbc1Template, newPrintNamedParameterJdbc1Template, "清理打印1库表任务");
                } else {
                    deleteTableExpiredPrintLog(poolExecutor, beanPropertyRowMapper, printlogExpiredTime,
                        otherTableExpiredTime, i, tablePrefix, newPrintDataSourceJdbc2Template, newPrintNamedParameterJdbc2Template, "清理打印2库表任务");
                }
                if (stopped) {
                    break;
                }
            }
            poolExecutor.shutdown();
            try {
                poolExecutor.awaitTermination(1, TimeUnit.DAYS);
            } catch (InterruptedException ignored) {
            }
            if (stopped) {
                break;
            }
        }
    }

    private ThreadPoolExecutor createThreadPoolExecutor() {
        ThreadPoolExecutor poolExecutor = new ThreadPoolExecutor(config.getNewPrintLogDeletePoolSize(),
            config.getNewPrintLogDeletePoolSize(), 5L, TimeUnit.SECONDS, new SynchronousQueue<>(),
            new ThreadFactoryBuilder().setNameFormat("DeleteExpiredPrintLog-pool-%d").build(),
            (Runnable r, ThreadPoolExecutor executor) -> {
                if (!executor.isShutdown()) {
                    try {
                        executor.getQueue().put(r);
                    } catch (InterruptedException e) {
                        LOGGER.logError(e.getMessage(), e);
                    }
                }
            });
        return poolExecutor;
    }

    private void deleteTableExpiredPrintLog(ThreadPoolExecutor poolExecutor,
        BeanPropertyRowMapper<NewPrintLog> beanPropertyRowMapper, LocalDateTime printlogExpiredTime,
        LocalDateTime otherTableExpiredTime, int i, String tablePrefix, JdbcTemplate jdbcTemplate,
        NamedParameterJdbcTemplate namedParameterJdbcTemplate, String logPrefix) {

        LocalDateTime expiredTime = tablePrefix.equals(AY_PRINTLOG) ? printlogExpiredTime : otherTableExpiredTime;
        String tableName = tablePrefix + i;
        poolExecutor.execute(() -> {
            MDC.put("tableName", tableName);
            try {
                long lastId = -1;
                long deleteResult = 0;
                long start = System.currentTimeMillis();
                while (!stopped) {
                    int pageSize = config.getNewPrintLogDeletePageSize();
                    ScanResult scanResult =
                        queryPrintLogs(tableName, lastId, pageSize, expiredTime, beanPropertyRowMapper, jdbcTemplate);
                    if (scanResult.getLastId() == null) {
                        break;
                    }
                    try {
                        lastId = scanResult.getLastId();
                        List<Long> printLogIds = scanResult.getIdList();
                        if(CollectionUtils.isEmpty(printLogIds)){
                            continue;
                        }
                        deleteResult += deletePrintLogs(tableName, printLogIds, namedParameterJdbcTemplate);
                    } finally {
                        LOGGER.logInfo(
                            logPrefix +", 正在清理 table=" + tableName + (config.isNewPrintLogDeleteFullScan() ? ", 完整模式" : ", 增量模式") + "，数量=" + deleteResult + ", lastId=" + lastId);
                    }
                }
                LOGGER.logInfo(logPrefix + ", table=" + tableName + (config.isNewPrintLogDeleteFullScan() ? ", 完整模式" : ", 增量模式") + " 清理完毕，总数量=" + deleteResult + ", lastId=" + lastId
                    + ", costTime=" + (System.currentTimeMillis() - start));
            } finally {
                MDC.remove("tableName");
            }
        });
    }

    private ScanResult queryPrintLogs(String tableName, long lastId, int pageSize, LocalDateTime expirationTime,
        BeanPropertyRowMapper<NewPrintLog> beanPropertyRowMapper, JdbcTemplate jdbcTemplate) {
        ScanResult scanResult = new ScanResult();
        try {
            List<NewPrintLog> printLogs = jdbcTemplate.query(
                "select id,gmt_create from " + tableName + " where id >? order by id limit ?",
                new Object[] {lastId, pageSize}, beanPropertyRowMapper);
            if (printLogs.isEmpty()) {
                return scanResult;
            }
            List<Long> expiredResult = printLogs.stream().filter(l -> l.getGmtCreate() != null && l.getGmtCreate().isBefore(expirationTime))
                .map(NewPrintLog::getId).collect(Collectors.toList());

            if(expiredResult.isEmpty()) {
                if(config.isNewPrintLogDeleteFullScan()){
                    // 完整模式继续扫描
                    scanResult.setLastId(printLogs.get(printLogs.size() - 1).getId());
                }
            }else {
                scanResult.setLastId(printLogs.get(printLogs.size() - 1).getId());
                scanResult.setIdList(expiredResult);
            }
        } catch (DataAccessException e) {
            LOGGER.logError("清理新打印表任务, 查询异常: , table=" + tableName + ": " + e.getMessage(), e);
        }
        return scanResult;
    }

    private int deletePrintLogs(String table, List<Long> ids, NamedParameterJdbcTemplate namedParameterJdbcTemplate) {
        try {
            if (!config.isNewPrintLogDeleteRecordEnabled()) {
                LOGGER.logInfo("准备删除的数据 , table=" + table + ", ids=" + ids);
                return ids.size();
            }
            return namedParameterJdbcTemplate.update("delete from " + table + " where id in (:ids) ",
                ImmutableMap.of("ids", ids));
        } catch (DataAccessException e) {
            LOGGER.logError("清理新打印表任务, 删除异常: , table=" + table + ": " + e.getMessage(), e);
        }
        return 0;
    }

    private void optimizePrintLog() {
        if (stopped) {
            return;
        }
        String sql =
            "SELECT TABLE_NAME, DATA_FREE FROM information_schema.TABLES WHERE TABLE_SCHEMA='trade_print' and DATA_FREE > "
                + config.getNewPrintLogDeleteOptimizeFreeSize() + " order by DATA_FREE desc";
        LOGGER.logInfo("清理新打印表任务, 查询需要优化的表 SQL: " + sql);

        // 打印1库
        List<Map<String, Object>> bigTableNames = newPrintDataSourceJdbc1Template.queryForList(sql);
        if (CollectionUtils.isEmpty(bigTableNames)) {
            LOGGER.logInfo("清理打印1库表任务, 没有需要优化的表");
        } else {
            LOGGER.logInfo("清理打印1库表任务, 需要优化的表: " + bigTableNames);
            ThreadPoolExecutor poolExecutor = createThreadPoolExecutor();
            for (Map<String, Object> bigTable : bigTableNames) {
                poolExecutor.execute(() -> optimizeTableSpace(bigTable, newPrintDataSourceJdbc1Template, "清理打印1库表任务"));
                if (stopped) {
                    break;
                }
            }
            poolExecutor.shutdown();
            try {
                poolExecutor.awaitTermination(1, TimeUnit.DAYS);
            } catch (InterruptedException ignored) {
            }
        }

        // 打印二库
        List<Map<String, Object>> bigTableNames1 = newPrintDataSourceJdbc2Template.queryForList(sql);
        if (CollectionUtils.isEmpty(bigTableNames1)) {
            LOGGER.logInfo("清理打印2库表任务, 没有需要优化的表");
        } else {
            LOGGER.logInfo("清理打印2库表任务, 需要优化的表: " + bigTableNames1);
            ThreadPoolExecutor poolExecutor = createThreadPoolExecutor();
            for (Map<String, Object> bigTable : bigTableNames1) {
                poolExecutor.execute(() -> optimizeTableSpace(bigTable, newPrintDataSourceJdbc2Template, "清理打印2库表任务"));
                if (stopped) {
                    break;
                }
            }
            poolExecutor.shutdown();
            try {
                poolExecutor.awaitTermination(1, TimeUnit.DAYS);
            } catch (InterruptedException ignored) {
            }
        }
    }

    protected void optimizeTableSpace(Map<String, Object> bigTable, JdbcTemplate jdbcTemplate, String logPrefix) {
        if (stopped) {
            return;
        }
        String tableName = (String)bigTable.get("TABLE_NAME");
        BigInteger dataFree = (BigInteger)bigTable.get("DATA_FREE");
        MDC.put("tableName", tableName);
        try {
            long start = System.currentTimeMillis();
            try {
                LOGGER.logInfo(logPrefix + ", 开始优化 table=" + tableName + ",  dataFree="
                    + dataFree.divide(BigInteger.valueOf(1024 * 1024)) + "MB");
                jdbcTemplate.update("ALTER TABLE " + tableName + " FORCE, ALGORITHM=INPLACE, LOCK=NONE;");
            } catch (Exception e) {
                LOGGER.logError(logPrefix + ", 优化到 table=" + tableName + ", 失败: " + e.getMessage(), e);
            } finally {
                LOGGER.logInfo(
                    logPrefix + ", 结束优化 table=" + tableName + ", costTime=" + (System.currentTimeMillis() - start));
            }
        } finally {
            MDC.remove("tableName");
        }
    }

    public void stop() {
        if (!stopped) {
            LOGGER.logInfo("停止清理 新打印表");
            this.stopped = true;
        }
    }

    @Data
    private static class ScanResult {
        private List<Long> idList;
        private Long lastId;
    }
}
