package cn.loveapp.print.maintain.dao.print;

import java.util.List;

import org.apache.ibatis.annotations.Param;

/**
 * 打印历史记录(PrintHistroy)表数据库访问层
 *
 * <AUTHOR>
 * @since 2020-07-17 12:18:10
 */
public interface PrintHistroyDao {

    /**
     * 查询过期的数据的id
     *
     * @param expiredDay
     *            多少天前的需要被清理
     * @param limit
     * @return
     */
    List<Long> queryExpiredIdsByLimit(int expiredDay, @Param("limit") int limit);

    /**
     * 通过id批量删除
     *
     * @param ids
     * @return
     */
    int deleteByIds(@Param("ids") List<Long> ids);

}
