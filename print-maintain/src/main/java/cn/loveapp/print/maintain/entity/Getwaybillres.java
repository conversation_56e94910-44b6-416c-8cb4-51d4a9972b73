package cn.loveapp.print.maintain.entity;

import java.io.Serializable;
import java.util.Date;

import lombok.Data;

/**
 * B版获取单号记录(Getwaybillres)实体类
 *
 * <AUTHOR>
 * @since 2020-07-09 16:02:06
 */
@Data
public class Getwaybillres implements Serializable {
    private static final long serialVersionUID = -72377708191287933L;

    private Integer id;
    /**
     * 用户名
     */
    private String nick;
    /**
     * 获取单号类型，批量或者单个
     */
    private String type;
    /**
     * 电子面单单号
     */
    private String waybillnumber;
    /**
     * 单号获取人
     */
    private String subnick;
    /**
     * 获取电子面单的时间
     */
    private Date gettime;
    /**
     * 消耗单号的用户名
     */
    private String sharenick;
    /**
     * 单号请求数据
     */
    private String request;
    /**
     * 电子面单返回数据
     */
    private String result;
    /**
     * 哪个页面获取的
     */
    private String webpage;
    /**
     * 快递公司
     */
    private String cpcode;
    /**
     * 订单编号
     */
    private String tids;
    /**
     * 单个订单编号
     */
    private String objectid;
    /**
     * 单个面单标准返回数据
     */
    private String printData;
    /**
     * 备用字段
     */
    private String remark;
    /**
     * 平台来源
     */
    private String tradeSource;
}
