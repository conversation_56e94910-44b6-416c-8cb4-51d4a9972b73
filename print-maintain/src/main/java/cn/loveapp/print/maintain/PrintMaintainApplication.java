package cn.loveapp.print.maintain;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.scheduling.annotation.EnableScheduling;

/**
 * 爱用-print 维护服务 入口
 *
 * <AUTHOR>
 */
@EnableScheduling
@EnableCaching
@SpringBootApplication(scanBasePackages = {"cn.loveapp.print.maintain", "cn.loveapp.print.common"})
@EnableFeignClients(basePackages = {"cn.loveapp.uac"})
public class PrintMaintainApplication {
    public static void main(String[] args) {

        new SpringApplication(PrintMaintainApplication.class).run(args);
    }
}
