package cn.loveapp.print.maintain.dao.print;

import java.util.List;

import org.apache.ibatis.annotations.Param;

/**
 * B版获取单号记录(Getwaybillres)表数据库访问层
 *
 * <AUTHOR>
 * @since 2020-07-09 16:05:04
 */
public interface GetwaybillresDao {

    /**
     * 查询过期的数据的id
     *
     * @param limit
     * @return
     */
    List<Long> queryExpiredIdsByLimit(int expiredDay, @Param("limit") int limit);

    /**
     * 通过id批量删除
     *
     * @param ids
     * @return
     */
    int deleteByIds(@Param("ids") List<Long> ids);

}
