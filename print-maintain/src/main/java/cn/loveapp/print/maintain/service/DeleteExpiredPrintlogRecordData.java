package cn.loveapp.print.maintain.service;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import cn.loveapp.common.utils.LoggerHelper;
import cn.loveapp.print.maintain.config.PrintMaintainConfig;
import cn.loveapp.print.maintain.dao.temprint.PrintlogRecordNewDao;

/**
 * 清理过期的打印日志
 *
 * <AUTHOR>
 */
@Component
public class DeleteExpiredPrintlogRecordData {
    private static final LoggerHelper LOGGER = LoggerHelper.getLogger(DeleteExpiredPrintlogRecordData.class);

    @Autowired
    private PrintMaintainConfig config;

    @Autowired
    private PrintlogRecordNewDao printlogRecordNewDao;

    private volatile boolean stopped = false;

    public void start() {
        LOGGER.logInfo("开始清理printlog_record_new");
        this.stopped = false;
        int pageNo = 0;
        while (true) {
            if (this.stopped) {
                break;
            }
            List<Long> ids = printlogRecordNewDao.queryExpiredIdsByLimit(config.getDeliverGoodsExpiredTime(),
                config.getDeliverGoodsDeletePageSize());
            if (CollectionUtils.isEmpty(ids)) {
                LOGGER.logInfo("printlog_record_new清理完成，共清理" + pageNo + "页数据，" + "pageSize="
                    + config.getDeliverGoodsDeletePageSize());
                break;
            }
            int result = printlogRecordNewDao.deleteByIds(ids);
            LOGGER.logInfo("正在清理printlog_record_new，result=" + result);
            pageNo++;
        }
    }

    public void stop() {
        if (!stopped) {
            LOGGER.logInfo("停止清理printlog_record_new");
            this.stopped = true;
        }
    }
}
