package cn.loveapp.print.maintain.job;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.context.ApplicationListener;
import org.springframework.context.event.ContextClosedEvent;
import org.springframework.stereotype.Component;

import cn.loveapp.print.maintain.config.PrintMaintainConfig;
import cn.loveapp.print.maintain.service.DeleteExpiredPrintHistroyData;

@Component
public class DeletePrintHistoryApplication implements ApplicationRunner, ApplicationListener<ContextClosedEvent> {

    @Autowired
    private PrintMaintainConfig config;

    @Autowired
    private DeleteExpiredPrintHistroyData deleteExpiredPrintHistroyData;

    /**
     * Callback used to run the bean.
     *
     * @param args
     *            incoming application arguments
     * @throws Exception
     *             on error
     */
    @Override
    public void run(final ApplicationArguments args) throws Exception {
        if (args.containsOption("deletePrintHistory")) {
            deleteExpiredPrintHistroyData.start();
        }
    }

    /**
     * Handle an application event.
     *
     * @param event
     *            the event to respond to
     */
    @Override
    public void onApplicationEvent(final ContextClosedEvent event) {
        deleteExpiredPrintHistroyData.stop();
    }
}
