package cn.loveapp.print.maintain.dao.temprint;

import java.util.List;

import org.apache.ibatis.annotations.Param;

/**
 * (PrintlogRecordNew)表数据库访问层
 *
 * <AUTHOR>
 * @since 2020-07-09 15:10:47
 */
public interface PrintlogRecordNewDao {

    /**
     * 查询过期的数据的id
     *
     * @param limit
     * @return
     */
    List<Long> queryExpiredIdsByLimit(int expiredDay, @Param("limit") int limit);

    /**
     * 通过id批量删除
     *
     * @param ids
     * @return
     */
    int deleteByIds(@Param("ids") List<Long> ids);

}
