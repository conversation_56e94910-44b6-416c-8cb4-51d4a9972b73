package cn.loveapp.print.maintain.service;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

import cn.loveapp.print.common.constant.EsFields;
import cn.loveapp.print.common.dao.es.CommonAyPrintLogSearchESDao;
import cn.loveapp.print.common.entity.AyPrintLogSearchEs;
import org.elasticsearch.index.query.QueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import cn.loveapp.common.utils.LoggerHelper;
import cn.loveapp.print.maintain.config.PrintMaintainConfig;

/**
 * 打印ES过期数据清理任务
 *
 * <AUTHOR>
 * @date 2024/4/19
 */
@Component
public class DeleteExpiredNewPrintLogEsData {
    private static final LoggerHelper LOGGER = LoggerHelper.getLogger(DeleteExpiredNewPrintLogEsData.class);

    @Autowired
    private PrintMaintainConfig config;

    @Autowired
    private CommonAyPrintLogSearchESDao commonAyPrintLogSearchESDao;

    private volatile boolean stopped = false;

    public void start() {
        stopped = false;
        String indexName = AyPrintLogSearchEs.INDEX_NAME_PREFIX;
        MDC.put("indexName", indexName);
        int expiredDays = config.getNewPrintLogDeleteExpiredTime();
        long sum = 0;
        LocalDateTime endTime = LocalDateTime.now().minusDays(expiredDays).withHour(0).withMinute(0).withSecond(0).withNano(0);
        LOGGER.logInfo("开始清理 " + indexName);
        try {
            QueryBuilder queryBuilder = QueryBuilders.rangeQuery(EsFields.printTime)
                .lte(DateTimeFormatter.ofPattern(AyPrintLogSearchEs.MINUTE_SECOND_FORMATTER).format(endTime));

            LOGGER.logInfo(indexName + " 清理脚本: " + queryBuilder.toString());
            long result = 9999;
            while(result > 0 && !stopped){
                int limit = config.getNewPrintLogEsDeletePageSize();
                try {
                    result = commonAyPrintLogSearchESDao.deleteByQuery(AyPrintLogSearchEs.INDEX_NAME_PREFIX, null, queryBuilder, limit);
                    LOGGER.logInfo(indexName + " 删除数量: " + result);
                    sum += result;
                } catch (Exception e) {
                    LOGGER.logError(indexName + " 删除异常: " + e.getMessage(), e);
                }
            }
        } catch (Exception e) {
            LOGGER.logError(indexName + " 消费线程异常" + e.getMessage(), e);
        }finally {
            LOGGER.logInfo(indexName + " 删除结束, 删除总数量: " + sum);
            MDC.clear();
        }
    }

    public void stop() {
        if (!stopped) {
            LOGGER.logInfo("停止清理 " + AyPrintLogSearchEs.INDEX_NAME_PREFIX);
            this.stopped = true;
        }
    }
}
