package cn.loveapp.print.maintain.utils;

import java.time.LocalDateTime;
import java.time.LocalTime;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import cn.loveapp.common.utils.LoggerHelper;
import cn.loveapp.print.maintain.config.PrintMaintainConfig;

/**
 * MaintainUtils
 *
 * <AUTHOR>
 * @date 2019-06-26
 */
@Component
public class MaintainUtils {
    private static final LoggerHelper LOGGER = LoggerHelper.getLogger(MaintainUtils.class);

    @Autowired
    private PrintMaintainConfig config;

    /**
     * 检查当前系统时间是否适合运行任务, 不适合时自动退出
     *
     * @return
     */
    public boolean checkRunTime(boolean needExit) {
        int hour = LocalTime.now().getHour();
        if (hour < config.getBeginTime() || hour >= config.getEndTime()) {
            LOGGER.logError("错误的时间启动任务, 自动退出, 当前时间: " + LocalDateTime.now());
            if (needExit) {
                exit();
            }
            return false;
        }
        return true;
    }

    /**
     * 退出应用
     *
     */
    public void exit() {
        LOGGER.logInfo("自动退出应用, 当前时间: " + LocalDateTime.now());
        System.exit(0);
    }
}
