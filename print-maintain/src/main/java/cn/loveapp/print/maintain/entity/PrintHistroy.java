package cn.loveapp.print.maintain.entity;

import java.io.Serializable;
import java.time.LocalDateTime;

import lombok.Data;

/**
 * 打印历史记录(PrintHistroy)实体类
 *
 * <AUTHOR>
 * @since 2020-07-17 12:18:10
 */
@Data
public class PrintHistroy implements Serializable {
    private static final long serialVersionUID = -24188817724596086L;
    /**
     * 自动编号
     */
    private Integer id;
    /**
     * 卖家nick
     */
    private String sellernick;
    /**
     * 订单编号
     */
    private String tid;
    /**
     * 快递单打印状态 1：已打
     */
    private Integer courier;
    /**
     * 发货单打印状态 1：已打
     */
    private Integer invoice;
    /**
     * 电子面单打印状态 1：已打
     */
    private Integer surface;
    /**
     * 打印时间
     */
    private LocalDateTime optime;
    /**
     * 当天打印发货单编号
     */
    private String printid;
    /**
     * 备注
     */
    private String remack;
    /**
     * 平台来源
     */
    private String tradeSource;
}
