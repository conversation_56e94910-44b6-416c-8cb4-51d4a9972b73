package cn.loveapp.print.maintain.service;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import cn.loveapp.common.utils.LoggerHelper;
import cn.loveapp.print.maintain.config.PrintMaintainConfig;
import cn.loveapp.print.maintain.dao.print.DeliverGoodsDao;

/**
 * 清除过期的发货信息
 *
 * <AUTHOR>
 */
@Component
public class DeleteExpiredDeliverGoodsData {
    private static final LoggerHelper LOGGER = LoggerHelper.getLogger(DeleteExpiredDeliverGoodsData.class);

    @Autowired
    private PrintMaintainConfig config;

    @Autowired
    private DeliverGoodsDao deliverGoodsDao;

    private volatile boolean stopped = false;

    public void start() {
        LOGGER.logInfo("开始清理deliver_goods");
        this.stopped = false;
        int pageNo = 0;
        while (true) {
            if (this.stopped) {
                break;
            }
            List<Long> ids = deliverGoodsDao.queryExpiredIdsByLimit(config.getDeliverGoodsExpiredTime(),
                config.getDeliverGoodsDeletePageSize());
            if (CollectionUtils.isEmpty(ids)) {
                LOGGER.logInfo(
                    "deliver_goods清理完成，共清理" + pageNo + "页数据，" + "pageSize=" + config.getDeliverGoodsDeletePageSize());
                break;
            }
            int result = deliverGoodsDao.deleteByIds(ids);
            LOGGER.logInfo("正在清理deliver_goods，result=" + result);
            pageNo++;
        }
    }

    public void stop() {
        if (!stopped) {
            LOGGER.logInfo("停止清理deliver_goods");
            this.stopped = true;
        }
    }
}
