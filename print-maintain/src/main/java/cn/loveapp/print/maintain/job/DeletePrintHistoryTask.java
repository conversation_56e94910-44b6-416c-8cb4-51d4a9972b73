package cn.loveapp.print.maintain.job;

import java.util.concurrent.SynchronousQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

import cn.loveapp.print.maintain.service.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.context.ApplicationListener;
import org.springframework.context.event.ContextClosedEvent;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import com.google.common.util.concurrent.ThreadFactoryBuilder;

import cn.loveapp.common.utils.LoggerHelper;
import cn.loveapp.print.maintain.config.PrintMaintainConfig;
import cn.loveapp.print.maintain.utils.MaintainUtils;

/**
 * 清理打印库的任务
 *
 * <AUTHOR>
 */
@Component
public class DeletePrintHistoryTask implements <PERSON>Runner, ApplicationListener<ContextClosedEvent> {
    private static final LoggerHelper LOGGER = LoggerHelper.getLogger(DeletePrintHistoryTask.class);

    @Autowired
    private DeleteExpiredDeliverGoodsData deleteExpiredDeliverGoodsData;

    @Autowired
    private DeleteExpiredGetwaybillresData deleteExpiredGetwaybillresData;

    @Autowired
    private DeleteExpiredPrintlogRecordData deleteExpiredPrintlogRecordData;

    @Autowired
    private DeleteExpiredPrintHistroyData deleteExpiredPrintHistroyData;

    @Autowired
    private DeleteExpiredNewPrintLogData deleteExpiredNewPrintLogData;

    @Autowired
    private DeleteExpiredNewPrintLogEsData deleteExpiredNewPrintLogEsData;

    @Autowired
    private MaintainUtils maintainUtils;

    @Autowired
    private PrintMaintainConfig config;

    private ThreadPoolExecutor poolExecutor;

    @Override
    public void run(final ApplicationArguments args) throws Exception {
        LOGGER.logInfo("清理打印库的任务启动成功");
        start();
    }

    /**
     * 每天早上1点开始清理打印库
     */
    @Scheduled(cron = "${print.maintain.deletePrintHistory.start.cron: 0 0 1 * * ?}")
    public void start() {
        if (!config.isScheduledEnable()) {
            return;
        }
        if (!maintainUtils.checkRunTime(false)) {
            return;
        }
        poolExecutor = new ThreadPoolExecutor(config.getThreadSize(), config.getThreadSize(), 0L, TimeUnit.SECONDS,
            new SynchronousQueue(), new ThreadFactoryBuilder().setNameFormat("print-maintain-pool-%d").build(),
            (Runnable r, ThreadPoolExecutor executor) -> {
                if (!executor.isShutdown()) {
                    try {
                        executor.getQueue().put(r);
                    } catch (InterruptedException e) {
                        LOGGER.logError(e.toString(), e);
                        Thread.currentThread().interrupt();
                    }
                }
            });
        poolExecutor.execute(() -> deleteExpiredNewPrintLogData.start());
        poolExecutor.execute(() -> deleteExpiredDeliverGoodsData.start());
        poolExecutor.execute(() -> deleteExpiredGetwaybillresData.start());
        poolExecutor.execute(() -> deleteExpiredPrintlogRecordData.start());
        poolExecutor.execute(() -> deleteExpiredPrintHistroyData.start());
        poolExecutor.execute(() -> deleteExpiredNewPrintLogEsData.start());
    }

    /**
     * 清库脚本开始后每5分钟判断一次运行时间是否在区间内
     */
    @Scheduled(cron = "${print.maintain.deletePrintHistory.checkTime.cron: 0 */5 * * * ?}")
    public void checkTime() {
        if (!config.isScheduledEnable()) {
            return;
        }
        LOGGER.logInfo("检查是否是正确的时间");
        if (!maintainUtils.checkRunTime(false)) {
            stop();
        }
    }

    /**
     * Handle an application event.
     *
     * @param event
     *            the event to respond to
     */
    @Override
    public void onApplicationEvent(final ContextClosedEvent event) {
        stop();
        poolExecutor.shutdown();
        try {
            poolExecutor.awaitTermination(5, TimeUnit.MINUTES);
        } catch (InterruptedException e) {
        }
    }

    /**
     * 停止任务
     */
    private void stop() {
        deleteExpiredNewPrintLogData.stop();
        deleteExpiredDeliverGoodsData.stop();
        deleteExpiredGetwaybillresData.stop();
        deleteExpiredPrintlogRecordData.stop();
        deleteExpiredPrintHistroyData.stop();
        deleteExpiredNewPrintLogEsData.stop();
    }
}
