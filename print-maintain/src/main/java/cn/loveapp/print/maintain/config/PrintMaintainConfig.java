package cn.loveapp.print.maintain.config;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

import lombok.Data;

/**
 * 打印 维护服务的一些配置
 *
 * <AUTHOR>
 */
@Data
@Configuration
public class PrintMaintainConfig {

    @Value("${print.maintain.scheduled.enable:true}")
    private boolean scheduledEnable;

    @Value("${print.maintain.threadSize:6}")
    private int threadSize;

    /**
     * 清理脚本的开始时间区间
     */
    @Value("${print.maintain.enable.beginTime:0}")
    private int beginTime;

    /**
     * 清理脚本的结束时间区间
     */
    @Value("${print.maintain.enable.endTime:6}")
    private int endTime;

    /**
     * 发货记录的过期（需要清理）的时间 单位：天
     */
    @Value("${print.maintain.deliverGoodsExpiredTime:90}")
    private int deliverGoodsExpiredTime;

    /**
     * 一次清除发货记录的pageSize
     */
    @Value("${print.maintain.deliverGoodsDeletePageSize:1000}")
    private int deliverGoodsDeletePageSize;

    /**
     * 面单取号记录的过期（需要清理）的时间 单位：天
     */
    @Value("${print.maintain.gatewaybillresExpiredTime:90}")
    private int gatewaybillresExpiredTime;

    /**
     * 一次清除面单取号记录的pageSize
     */
    @Value("${print.maintain.gatewaybillresDeletePageSize:1000}")
    private int gatewaybillresDeletePageSize;

    /**
     * 打印记录的过期（需要清理）的时间 单位：天
     */
    @Value("${print.maintain.printlogRecordExpiredTime:90}")
    private int printlogRecordExpiredTime;

    /**
     * 一次清除打印记录的pageSize
     */
    @Value("${print.maintain.printlogRecordDeletePageSize:1000}")
    private int printlogRecordDeletePageSize;

    /**
     * 打印历史的过期（需要清理）的时间 单位：天
     */
    @Value("${print.maintain.printHistoryExpiredTime:90}")
    private int printHistoryExpiredTime;

    /**
     * 一次清除打印历史的pageSize
     */
    @Value("${print.maintain.printHistoryDeletePageSize:1000}")
    private int printHistoryDeletePageSize;

    /**
     * 删除新打印记录过期数据的pageSize
     */
    @Value("${print.maintain.newPrintLogDeletePageSize:1000}")
    private int newPrintLogDeletePageSize;

    /**
     * 删除新打印记录 ES 过期数据的pageSize
     */
    @Value("${print.maintain.newPrintLogEsDeletePageSize:3000}")
    private int newPrintLogEsDeletePageSize;

    /**
     * 删除新打印记录过期数据的过期时间 (天)
     */
    @Value("${print.maintain.newPrintLogDeleteExpiredTime:92}")
    private int newPrintLogDeleteExpiredTime;

    /**
     * 删除新打印记录过期数据的线程池大小
     */
    @Value("${print.maintain.newPrintLogDeletePoolSize:6}")
    private int newPrintLogDeletePoolSize;

    /**
     * 删除新打印记录过期数据的空间优化大小阈值 (默认 50MB)
     */
    @Value("${print.maintain.newPrintLogDeleteOptimizeFreeSize:50000000}")
    private long newPrintLogDeleteOptimizeFreeSize;

    /**
     * 删除新打印记录过期数据时是否真实删除数据记录 (测试用)
     */
    @Value("${print.maintain.newPrintLogDeleteRecordEnabled:true}")
    private boolean newPrintLogDeleteRecordEnabled;

    /**
     * 删除新打印记录过期数据时是否扫描完整数据删除
     */
    @Value("${print.maintain.newPrintLogDeleteFullScan:false}")
    private boolean newPrintLogDeleteFullScan;
}
