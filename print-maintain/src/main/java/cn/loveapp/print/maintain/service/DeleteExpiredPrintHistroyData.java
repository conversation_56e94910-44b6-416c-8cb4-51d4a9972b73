package cn.loveapp.print.maintain.service;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import cn.loveapp.common.utils.LoggerHelper;
import cn.loveapp.print.maintain.config.PrintMaintainConfig;
import cn.loveapp.print.maintain.dao.print.PrintHistroyDao;

@Component
public class DeleteExpiredPrintHistroyData {
    private static final LoggerHelper LOGGER = LoggerHelper.getLogger(DeleteExpiredPrintHistroyData.class);

    @Autowired
    private PrintMaintainConfig config;

    @Autowired
    private PrintHistroyDao printHistroyDao;

    private volatile boolean stopped = false;

    public void start() {
        LOGGER.logInfo("开始清理print_history");
        this.stopped = false;
        int pageNo = 0;
        while (true) {
            if (this.stopped) {
                break;
            }
            List<Long> ids = printHistroyDao.queryExpiredIdsByLimit(config.getPrintHistoryExpiredTime(),
                config.getPrintHistoryDeletePageSize());
            if (CollectionUtils.isEmpty(ids)) {
                LOGGER.logInfo(
                    "print_history清理完成，共清理" + pageNo + "页数据，" + "pageSize=" + config.getPrintHistoryDeletePageSize());
                break;
            }
            int result = printHistroyDao.deleteByIds(ids);
            LOGGER.logInfo("正在清理print_history，result=" + result);
            pageNo++;
        }
    }

    public void stop() {
        if (!stopped) {
            LOGGER.logInfo("停止清理print_history");
            this.stopped = true;
        }
    }
}
