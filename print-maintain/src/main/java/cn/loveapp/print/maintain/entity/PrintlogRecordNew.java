package cn.loveapp.print.maintain.entity;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Date;

import lombok.Data;

/**
 * (PrintlogRecordNew)实体类
 *
 * <AUTHOR>
 * @since 2020-07-09 15:08:06
 */
@Data
public class PrintlogRecordNew implements Serializable {
    private static final long serialVersionUID = -71860351164375467L;
    /**
     * 编号
     */
    private Long id;
    /**
     * 用户名
     */
    private String sellernick;
    /**
     * 订单编号
     */
    private String tid;
    /**
     * 运单号(母单号)
     */
    private String voice;
    /**
     * 子单号
     */
    private String voicewaybill;
    /**
     * null:正常运单类型,1:子母运单类型
     */
    private Integer parentwaybillcode;
    /**
     * 打印的子订单号
     */
    private String oids;
    /**
     * 打印时间
     */
    private LocalDateTime operatetime;
    /**
     * 快递单打印状态
     */
    private Integer expfacestatus;
    /**
     * 电子面单打印状态
     */
    private Integer elefacestatus;
    /**
     * 快递单打印次数
     */
    private Integer expprintcounts;
    /**
     * 电子面单打印次数
     */
    private Integer eleprintcounts;
    /**
     * 快递公司
     */
    private String delivercompany;
    /**
     * 是否是拆弹发货
     */
    private Integer issplit;
    /**
     * 是否是批量发货
     */
    private Integer iscombine;
    /**
     * 面单是否已回收
     */
    private String remark;
    /**
     * 电子面单打印顺序
     */
    private Integer orders;
    /**
     * 流水号
     */
    private String spare;
    /**
     * 打印机
     */
    private String printer;
    /**
     * 打印操作人
     */
    private String printeruser;
    /**
     * 面单取消时间
     */
    private Date canceltime;
    /**
     * 面单获取时间
     */
    private Date obtaintime;
    /**
     * 面单取消操作人
     */
    private String canceluser;
    /**
     * 面单获取操作人
     */
    private String obtainuser;
    /**
     * 是否智能匹配物流0不匹配，1是匹配
     */
    private Integer ismatch;
    /**
     * 买家旺旺
     */
    private String buyernick;
    /**
     * 买家姓名
     */
    private String buyername;
    /**
     * 买家电话号码
     */
    private String buyerphone;
    /**
     * 买家地址省份
     */
    private String buyerprovince;
    /**
     * 买家市
     */
    private String buyercity;
    /**
     * 打印快递单的快递模版
     */
    private String expressmodule;
    /**
     * 打印快递单的数据
     */
    private String expressparameter;
    /**
     * 打印面单的菜鸟固定模版
     */
    private String electrocn;
    /**
     * 打印面单的自定义模版
     */
    private String electromodule;
    /**
     * 打印面单自定义内容
     */
    private String electroparameter;
    /**
     * 邮编
     */
    private String zipcode;
    /**
     * 买家的收货地址
     */
    private String buyeraddress;
    /**
     * 店铺名称
     */
    private String shopname;
    /**
     * 是否是自由打印0是正常打印，1是自由打印
     */
    private Integer isfree;
    /**
     * 快递单模板图片名称
     */
    private String moprice;
    /**
     * 买家手机号
     */
    private String buyermobile;
    /**
     * 自由打印生成的md5校验码
     */
    private String customprintmd5;
    /**
     * 平台来源
     */
    private String tradeSource;
    /**
     * 预留数字类型1
     */
    private Integer futureint;
    /**
     * 预留字符串类型1
     */
    private String futurestr;
    /**
     * 预留字符串类型2
     */
    private String morefuturestr;
}
