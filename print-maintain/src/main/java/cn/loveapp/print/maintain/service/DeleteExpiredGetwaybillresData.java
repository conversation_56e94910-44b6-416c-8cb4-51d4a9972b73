package cn.loveapp.print.maintain.service;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import cn.loveapp.common.utils.LoggerHelper;
import cn.loveapp.print.maintain.config.PrintMaintainConfig;
import cn.loveapp.print.maintain.dao.print.GetwaybillresDao;

/**
 * 清除过期的面单获取记录
 *
 * <AUTHOR>
 */
@Component
public class DeleteExpiredGetwaybillresData {
    private static final LoggerHelper LOGGER = LoggerHelper.getLogger(DeleteExpiredGetwaybillresData.class);

    @Autowired
    private PrintMaintainConfig config;

    @Autowired
    private GetwaybillresDao getwaybillresDao;

    private volatile boolean stopped = false;

    public void start() {
        LOGGER.logInfo("开始清理getwaybillres");
        this.stopped = false;
        int pageNo = 0;
        while (true) {
            if (this.stopped) {
                break;
            }
            List<Long> ids = getwaybillresDao.queryExpiredIdsByLimit(config.getDeliverGoodsExpiredTime(),
                config.getDeliverGoodsDeletePageSize());
            if (CollectionUtils.isEmpty(ids)) {
                LOGGER.logInfo(
                    "getwaybillres清理完成，共清理" + pageNo + "页数据，" + "pageSize=" + config.getDeliverGoodsDeletePageSize());
                break;
            }
            int result = getwaybillresDao.deleteByIds(ids);
            LOGGER.logInfo("正在清理getwaybillres，result=" + result);
            pageNo++;
        }
    }

    public void stop() {
        if (!stopped) {
            LOGGER.logInfo("停止清理getwaybillres");
            this.stopped = true;
        }
    }
}
