package cn.loveapp.print.maintain.entity;

import java.io.Serializable;
import java.time.LocalDateTime;

import lombok.Data;

/**
 * 保存用户发货信息(DeliverGoods)实体类
 *
 * <AUTHOR>
 * @since 2020-07-09 16:13:15
 */
@Data
public class DeliverGoods implements Serializable {
    private static final long serialVersionUID = -99750475575631891L;
    /**
     * 编号
     */
    private Integer id;
    /**
     * 用户名
     */
    private String nick;
    /**
     * 订单编号
     */
    private String tid;
    /**
     * 发货单号
     */
    private String courierNumber;
    /**
     * 快递公司
     */
    private String companyCode;
    /**
     * 发货时间
     */
    private LocalDateTime sendtime;
    /**
     * 是否为拆单
     */
    private Integer isSplit;
    /**
     * 线上发货 自己联系
     */
    private String sendcmd;
    /**
     * 机器识别码
     */
    private String feature;
    /**
     * 操作帐号
     */
    private String subnick;
    /**
     * 批量发货还是单个发货
     */
    private String type;
    /**
     * 发货界面
     */
    private String webpage;
    /**
     * 备用字段
     */
    private String remark;
}
