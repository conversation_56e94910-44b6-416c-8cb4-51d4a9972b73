<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.loveapp.print.maintain.dao.temprint.PrintlogRecordNewDao">

    <resultMap type="cn.loveapp.print.maintain.entity.PrintlogRecordNew" id="PrintlogRecordNewMap">
        <result property="id" column="id" jdbcType="INTEGER"/>
        <result property="sellernick" column="sellernick" jdbcType="VARCHAR"/>
        <result property="tid" column="tid" jdbcType="VARCHAR"/>
        <result property="voice" column="voice" jdbcType="VARCHAR"/>
        <result property="voicewaybill" column="voiceWaybill" jdbcType="VARCHAR"/>
        <result property="parentwaybillcode" column="parentWaybillCode" jdbcType="OTHER"/>
        <result property="oids" column="oids" jdbcType="VARCHAR"/>
        <result property="operatetime" column="Operatetime" jdbcType="TIMESTAMP"/>
        <result property="expfacestatus" column="Expfacestatus" jdbcType="INTEGER"/>
        <result property="elefacestatus" column="Elefacestatus" jdbcType="INTEGER"/>
        <result property="expprintcounts" column="Expprintcounts" jdbcType="INTEGER"/>
        <result property="eleprintcounts" column="Eleprintcounts" jdbcType="INTEGER"/>
        <result property="delivercompany" column="Delivercompany" jdbcType="VARCHAR"/>
        <result property="issplit" column="isSplit" jdbcType="INTEGER"/>
        <result property="iscombine" column="isCombine" jdbcType="INTEGER"/>
        <result property="remark" column="remark" jdbcType="VARCHAR"/>
        <result property="orders" column="orders" jdbcType="INTEGER"/>
        <result property="spare" column="spare" jdbcType="VARCHAR"/>
        <result property="printer" column="printer" jdbcType="VARCHAR"/>
        <result property="printeruser" column="printerUser" jdbcType="VARCHAR"/>
        <result property="canceltime" column="cancelTime" jdbcType="TIMESTAMP"/>
        <result property="obtaintime" column="obtainTime" jdbcType="TIMESTAMP"/>
        <result property="canceluser" column="cancelUser" jdbcType="VARCHAR"/>
        <result property="obtainuser" column="obtainUser" jdbcType="VARCHAR"/>
        <result property="ismatch" column="isMatch" jdbcType="INTEGER"/>
        <result property="buyernick" column="buyerNick" jdbcType="VARCHAR"/>
        <result property="buyername" column="buyerName" jdbcType="VARCHAR"/>
        <result property="buyerphone" column="buyerPhone" jdbcType="VARCHAR"/>
        <result property="buyerprovince" column="buyerProvince" jdbcType="VARCHAR"/>
        <result property="buyercity" column="buyerCity" jdbcType="VARCHAR"/>
        <result property="expressmodule" column="ExpressModule" jdbcType="VARCHAR"/>
        <result property="expressparameter" column="ExpressParameter" jdbcType="VARCHAR"/>
        <result property="electrocn" column="ElectroCN" jdbcType="VARCHAR"/>
        <result property="electromodule" column="ElectroModule" jdbcType="VARCHAR"/>
        <result property="electroparameter" column="ElectroParameter" jdbcType="VARCHAR"/>
        <result property="zipcode" column="zipCode" jdbcType="VARCHAR"/>
        <result property="buyeraddress" column="buyerAddress" jdbcType="VARCHAR"/>
        <result property="shopname" column="shopName" jdbcType="VARCHAR"/>
        <result property="isfree" column="isFree" jdbcType="INTEGER"/>
        <result property="moprice" column="moprice" jdbcType="VARCHAR"/>
        <result property="buyermobile" column="buyerMobile" jdbcType="VARCHAR"/>
        <result property="customprintmd5" column="customPrintMD5" jdbcType="VARCHAR"/>
        <result property="tradeSource" column="trade_source" jdbcType="VARCHAR"/>
        <result property="futureint" column="futureInt" jdbcType="INTEGER"/>
        <result property="futurestr" column="futureStr" jdbcType="VARCHAR"/>
        <result property="morefuturestr" column="moreFutureStr" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="tablename">
        printlog_record_new
    </sql>
    <sql id="field">
        id, sellernick, tid, voice, voiceWaybill, parentWaybillCode, oids, Operatetime, Expfacestatus, Elefacestatus,
		Expprintcounts, Eleprintcounts, Delivercompany, isSplit, isCombine, remark, orders, spare, printer, printerUser,
		cancelTime, obtainTime, cancelUser, obtainUser, isMatch, buyerNick, buyerName, buyerPhone, buyerProvince, buyerCity,
		ExpressModule, ExpressParameter, ElectroCN, ElectroModule, ElectroParameter, zipCode, buyerAddress, shopName,
		isFree, moprice, buyerMobile, customPrintMD5, trade_source, futureInt, futureStr, moreFutureStr
    </sql>

    <select id="queryExpiredIdsByLimit" resultType="java.lang.Long">
        select
        id
        from
        <include refid="tablename"/>
        where Operatetime &lt; date_format(date_add(now(),interval -${expiredDay} day),'%Y-%m-%d 00:00:00')
        limit #{limit}
    </select>

    <delete id="deleteByIds">
        delete from
        <include refid="tablename"/>
        where id in
        <foreach collection="ids" index="index" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

</mapper>
