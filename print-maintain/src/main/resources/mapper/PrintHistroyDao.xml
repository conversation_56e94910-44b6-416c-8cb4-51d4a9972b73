<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.loveapp.print.maintain.dao.print.PrintHistroyDao">

    <resultMap type="cn.loveapp.print.maintain.entity.PrintHistroy" id="PrintHistroyMap">
        <result property="id" column="id" jdbcType="INTEGER"/>
        <result property="sellernick" column="sellernick" jdbcType="VARCHAR"/>
        <result property="tid" column="tid" jdbcType="VARCHAR"/>
        <result property="courier" column="courier" jdbcType="INTEGER"/>
        <result property="invoice" column="invoice" jdbcType="INTEGER"/>
        <result property="surface" column="surface" jdbcType="INTEGER"/>
        <result property="optime" column="optime" jdbcType="TIMESTAMP"/>
        <result property="printid" column="printid" jdbcType="VARCHAR"/>
        <result property="remack" column="remack" jdbcType="VARCHAR"/>
        <result property="tradeSource" column="trade_source" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="tablename">
        print_histroy
    </sql>
    <sql id="field">
        id, sellernick, tid, courier, invoice, surface, optime, printid, remack, trade_source
    </sql>

    <select id="queryExpiredIdsByLimit" resultType="java.lang.Long">
        select
        id
        from
        <include refid="tablename"/>
        where optime &lt; date_format(date_add(now(),interval -${expiredDay} day),'%Y-%m-%d 00:00:00')
        limit #{limit}
    </select>

    <delete id="deleteByIds">
        delete from
        <include refid="tablename"/>
        where id in
        <foreach collection="ids" index="index" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

</mapper>
