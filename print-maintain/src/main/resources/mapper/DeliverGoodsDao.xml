<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.loveapp.print.maintain.dao.print.DeliverGoodsDao">

    <resultMap type="cn.loveapp.print.maintain.entity.DeliverGoods" id="DeliverGoodsMap">
        <result property="id" column="id" jdbcType="INTEGER"/>
        <result property="nick" column="nick" jdbcType="VARCHAR"/>
        <result property="tid" column="tid" jdbcType="VARCHAR"/>
        <result property="courierNumber" column="courier_number" jdbcType="VARCHAR"/>
        <result property="companyCode" column="company_code" jdbcType="VARCHAR"/>
        <result property="sendtime" column="sendtime" jdbcType="TIMESTAMP"/>
        <result property="isSplit" column="is_split" jdbcType="INTEGER"/>
        <result property="sendcmd" column="sendcmd" jdbcType="VARCHAR"/>
        <result property="feature" column="feature" jdbcType="VARCHAR"/>
        <result property="subnick" column="subNick" jdbcType="VARCHAR"/>
        <result property="type" column="type" jdbcType="VARCHAR"/>
        <result property="webpage" column="webpage" jdbcType="VARCHAR"/>
        <result property="remark" column="remark" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="tablename">
        deliver_goods
    </sql>
    <sql id="field">
        id, nick, tid, courier_number, company_code, sendtime, is_split, sendcmd, feature, subNick, `type`, webpage, remark
    </sql>

    <select id="queryExpiredIdsByLimit" resultType="java.lang.Long">
        select
        id
        from
        <include refid="tablename"/>
        where sendtime &lt; date_format(date_add(now(),interval -${expiredDay} day),'%Y-%m-%d 00:00:00')
        limit #{limit}
    </select>

    <delete id="deleteByIds">
        delete from
        <include refid="tablename"/>
        where id in
        <foreach collection="ids" index="index" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

</mapper>
