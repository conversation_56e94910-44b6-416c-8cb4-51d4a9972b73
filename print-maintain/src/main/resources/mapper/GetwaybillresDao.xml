<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.loveapp.print.maintain.dao.print.GetwaybillresDao">

    <resultMap type="cn.loveapp.print.maintain.entity.Getwaybillres" id="GetwaybillresMap">
        <result property="id" column="id" jdbcType="INTEGER"/>
        <result property="nick" column="nick" jdbcType="VARCHAR"/>
        <result property="type" column="type" jdbcType="VARCHAR"/>
        <result property="waybillnumber" column="waybillNumber" jdbcType="VARCHAR"/>
        <result property="subnick" column="subNick" jdbcType="VARCHAR"/>
        <result property="gettime" column="getTime" jdbcType="TIMESTAMP"/>
        <result property="sharenick" column="shareNick" jdbcType="VARCHAR"/>
        <result property="request" column="request" jdbcType="VARCHAR"/>
        <result property="result" column="result" jdbcType="VARCHAR"/>
        <result property="webpage" column="webpage" jdbcType="VARCHAR"/>
        <result property="cpcode" column="cpCode" jdbcType="VARCHAR"/>
        <result property="tids" column="tids" jdbcType="VARCHAR"/>
        <result property="objectid" column="objectId" jdbcType="VARCHAR"/>
        <result property="printData" column="print_data" jdbcType="VARCHAR"/>
        <result property="remark" column="remark" jdbcType="VARCHAR"/>
        <result property="tradeSource" column="trade_source" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="tablename">
        getwaybillres
    </sql>
    <sql id="field">
        id, nick, `type`, waybillNumber, subNick, getTime, shareNick, request, result, webpage, cpCode, tids, objectId, print_data, remark, trade_source
    </sql>

    <select id="queryExpiredIdsByLimit" resultType="java.lang.Long">
        select
        id
        from
        <include refid="tablename"/>
        where getTime &lt; date_format(date_add(now(),interval -${expiredDay} day),'%Y-%m-%d 00:00:00')
        limit #{limit}
    </select>

    <delete id="deleteByIds">
        delete from
        <include refid="tablename"/>
        where id in
        <foreach collection="ids" index="index" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

</mapper>
