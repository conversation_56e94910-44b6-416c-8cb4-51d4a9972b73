spring.application.name=print-maintain
spring.profiles.active=dev
## apollo\u529F\u80FD\u5F00\u5173
loveapp.apollo.enabled=true
apollo.bootstrap.enabled=${loveapp.apollo.enabled}
## apollo \u5E94\u7528id\u8BBE\u7F6E
app.id=cn.loveapp.print
## apollo namespace\u8BBE\u7F6E
apollo.bootstrap.namespaces=print-maintain,application,service-registry
env=${spring.profiles.active}
# dubbo
dubbo.enabled=false
#dubbo.application.name=orders-maintain
#dubbo.scan.base-packages=cn.loveapp.orders.maintain
#dubbo.consumer.check=false
spring.cache.jcache.config=classpath:ehcache.xml
