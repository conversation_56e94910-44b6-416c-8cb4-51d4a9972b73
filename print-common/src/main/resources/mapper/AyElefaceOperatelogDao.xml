<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.loveapp.print.common.dao.newprint.AyElefaceOperatelogDao">

    <resultMap type="cn.loveapp.print.common.entity.AyElefaceOperatelog" id="AyElefaceOperatelog">
        <result property="id" column="id"/>
        <result property="appName" column="app_name"/>
        <result property="sellerId" column="seller_id"/>
        <result property="storeId" column="store_id"/>
        <result property="sellerNick" column="seller_nick"/>
        <result property="tid" column="tid"/>
        <result property="tradeType" column="trade_type"/>
        <result property="serial" column="serial"/>
        <result property="mergeTid" column="merge_tid"/>
        <result property="oids" column="oids"/>
        <result property="provider" column="provider"/>
        <result property="logisticsCompany" column="logistics_company"/>
        <result property="cpCode" column="cp_code"/>
        <result property="objectId" column="object_id"/>
        <result property="waybillCode" column="waybill_code"/>
        <result property="isChild" column="is_child"/>
        <result property="childWaybillCode" column="child_waybill_code"/>
        <result property="printData" column="print_data"/>
        <result property="ownerNick" column="owner_nick"/>
        <result property="ownerSellerId" column="owner_seller_id"/>
        <result property="ownerStoreId" column="owner_store_id"/>
        <result property="ownerAppName" column="owner_app_name"/>
        <result property="getOperator" column="get_operator"/>
        <result property="getOperatorStoreId" column="get_operator_store_id"/>
        <result property="getOperateTerminal" column="get_operate_terminal"/>
        <result property="getTime" column="get_time"/>
        <result property="isCancel" column="is_cancel"/>
        <result property="cancelOperator" column="cancel_operator"/>
        <result property="cancelOperatorStoreId" column="cancel_operator_store_id"/>
        <result property="cancelOperateTerminal" column="cancel_operate_terminal"/>
        <result property="cancelTime" column="cancel_time"/>
        <result property="printCount" column="print_count"/>
        <result property="gmtCreate" column="gmt_create"/>
        <result property="gmtModified" column="gmt_modified"/>
        <result property="brandCode" column="brand_code"/>
        <result property="externalInfo" column="external_info"/>
        <result property="targetSellerNick" column="target_nick"/>
        <result property="targetSellerId" column="target_seller_id"/>
        <result property="targetAppName" column="target_app_name"/>
        <result property="targetStoreId" column="target_store_id"/>
        <result property="realCpCode" column="real_cp_code"/>
        <result property="shareId" column="share_id"/>
        <result property="isUseShare" column="is_use_share"/>
        <result property="proxySellerId" column="proxy_seller_id"/>
        <result property="proxySellerNick" column="proxy_seller_nick"/>
        <result property="proxyStoreId" column="proxy_store_id"/>
        <result property="proxyAppName" column="proxy_app_name"/>
        <result property="weight" column="weight"/>
        <result property="freight" column="freight"/>
        <result property="receiverAddress" column="receiver_address"/>
        <result property="logisticsTemplateId" column="logistics_template_id"/>
        <result property="billVersion" column="bill_version"/>
        <result property="otherExternalInfo" column="other_external_info"/>
        <result property="isPrint" column="is_print"/>
        <result property="isSendGood" column="is_send_good"/>
        <result property="gmtCreate" column="gmt_create"/>
        <result property="gmtModified" column="gmt_modified"/>

    </resultMap>

    <sql id="fileds">
        id, app_name, seller_id, store_id, seller_nick, tid, trade_type, serial, merge_tid, oids, provider, logistics_company, cp_code,
        object_id, waybill_code, is_child, child_waybill_code, print_data, owner_nick, owner_seller_id, owner_store_id, owner_app_name, get_operator,
        get_operator_store_id, get_operate_terminal, get_time, is_cancel, cancel_operator,
        cancel_operator_store_id, cancel_operate_terminal, cancel_time, print_count, gmt_create, gmt_modified, brand_code, external_info, real_cp_code,
        target_seller_nick, target_seller_id, target_app_name, target_store_id, share_id, is_use_share, proxy_seller_id, proxy_seller_nick, proxy_store_id, proxy_app_name,
        weight, freight, receiver_address, logistics_template_id, bill_version, other_external_info, is_print, is_send_good, gmt_create, gmt_modified
    </sql>

    <insert id="insert">
        insert into ay_eleface_operatelog(app_name, seller_id, store_id, seller_nick, tid, trade_type, serial,
                                          merge_tid, oids, provider, logistics_company, cp_code,
                                          object_id, waybill_code, is_child, child_waybill_code, print_data, owner_nick,
                                          owner_seller_id, owner_store_id, owner_app_name, get_operator,
                                          get_operator_store_id, get_operate_terminal, get_time, cancel_operator,
                                          cancel_operator_store_id, cancel_operate_terminal, cancel_time, brand_code, real_cp_code,
                                          target_seller_nick, target_seller_id, target_app_name, target_store_id, share_id,
                                          is_use_share, proxy_seller_id, proxy_seller_nick, proxy_store_id, proxy_app_name,
                                          logistics_template_id, bill_version, other_external_info, is_print, is_send_good)
        values (#{appName}, #{sellerId}, #{storeId}, #{sellerNick}, #{tid}, #{tradeType}, #{serial}, #{mergeTid},
                #{oids}, #{provider}, #{logisticsCompany}, #{cpCode},
                #{objectId}, #{waybillCode}, #{isChild}, #{childWaybillCode}, #{printData}, #{ownerNick},
                #{ownerSellerId}, #{ownerStoreId}, #{ownerAppName}, #{getOperator},
                #{getOperatorStoreId}, #{getOperateTerminal}, #{getTime}, #{cancelOperator},
                #{cancelOperatorStoreId}, #{cancelOperateTerminal}, #{cancelTime}, #{brandCode}, #{realCpCode},
                #{targetSellerNick}, #{targetSellerId}, #{targetAppName}, #{targetStoreId}, #{shareId},
                #{isUseShare},#{proxySellerId},#{proxySellerNick},#{proxyStoreId},#{proxyAppName},#{logisticsTemplateId},
                #{billVersion},#{otherExternalInfo},#{isPrint},#{isSendGood})
    </insert>

    <insert id="batchInsert" useGeneratedKeys="true" keyProperty="id" keyColumn="id">
        insert ignore into ay_eleface_operatelog(
        app_name, seller_id, store_id, seller_nick, tid, trade_type, serial, merge_tid, oids, provider,
        logistics_company, cp_code,
        object_id, waybill_code, is_child, child_waybill_code, print_data, owner_nick, owner_seller_id, owner_store_id,
        owner_app_name, get_operator,
        get_operator_store_id, get_operate_terminal, get_time, cancel_operator,
        cancel_operator_store_id, cancel_operate_terminal, cancel_time, brand_code, external_info, real_cp_code,
        target_seller_nick, target_seller_id, target_app_name, target_store_id, share_id,
        is_use_share, proxy_seller_id, proxy_seller_nick, proxy_store_id, proxy_app_name, logistics_template_id,
        bill_version, other_external_info, is_print, is_send_good)
        values
        <foreach collection="list" index="index" item="item" separator=",">
            (
            #{item.appName}, #{item.sellerId}, #{item.storeId}, #{item.sellerNick}, #{item.tid}, #{item.tradeType},
            #{item.serial}, #{item.mergeTid}, #{item.oids}, #{item.provider}, #{item.logisticsCompany}, #{item.cpCode},
            #{item.objectId}, #{item.waybillCode},
            #{item.isChild}, #{item.childWaybillCode}, #{item.printData}, #{item.ownerNick}, #{item.ownerSellerId},
            #{item.ownerStoreId}, #{item.ownerAppName}, #{item.getOperator},
            #{item.getOperatorStoreId},#{item.getOperateTerminal}, #{item.getTime},#{item.cancelOperator},
            #{item.cancelOperatorStoreId}, #{item.cancelOperateTerminal},#{item.cancelTime}, #{item.brandCode}, #{item.externalInfo}, #{item.realCpCode},
            #{item.targetSellerNick}, #{item.targetSellerId}, #{item.targetAppName}, #{item.targetStoreId}, #{item.shareId},
            #{item.isUseShare},#{item.proxySellerId},#{item.proxySellerNick},#{item.proxyStoreId},#{item.proxyAppName},
            #{item.logisticsTemplateId}, #{item.billVersion}, #{item.otherExternalInfo}, #{item.isPrint}, #{item.isSendGood}
            )
        </foreach>
    </insert>

    <update id="saveCancelInfo">
        update ay_eleface_operatelog
        set is_cancel                = #{cancelOplog.isCancel},
            cancel_operator          = #{cancelOplog.cancelOperator},
            cancel_operator_store_id = #{cancelOplog.cancelOperatorStoreId},
            cancel_operate_terminal  = #{cancelOplog.cancelOperateTerminal},
            cancel_time              = #{cancelOplog.cancelTime}
        where waybill_code = #{cancelOplog.waybillCode}
          and cp_code = #{cancelOplog.cpCode}
          and seller_id = #{sellerId}
          and store_id = #{storeId}
          and app_name = #{appName}
    </update>

    <select id="queryListOrderByOperateTime" resultMap="AyElefaceOperatelog">
        select
        <include refid="fileds"/>
        from ay_eleface_operatelog
        where seller_id = #{sellerId}
        and store_id = #{storeId}
        and app_name =#{appName}
        <include refid="commonQuery" />
        <choose>
            <!--查询面单回收记录先按照cancel_time排序-->
            <when test="queryDTO.isCancel == true">
                ORDER BY cancel_time DESC , get_time DESC
            </when>
            <otherwise>
                ORDER BY get_time DESC
            </otherwise>
        </choose>
        limit #{queryDTO.offset}, #{queryDTO.limit}
    </select>

    <select id="queryCount" resultType="long">
        select
        COUNT(*)
        from ay_eleface_operatelog
        where seller_id = #{sellerId}
        and store_id = #{storeId}
        and app_name =#{appName}
        <include refid="commonQuery" />
    </select>

    <select id="queryByTids" resultMap="AyElefaceOperatelog">
        select
        <include refid="fileds"/>
        from ay_eleface_operatelog
        where tid in
        <foreach collection="tids" open="(" separator="," close=")" item="item">
            #{item}
        </foreach>
        and store_id = #{storeId}
        and app_name = #{appName}
        and seller_id = #{sellerId}
        <if test="tradeType != null">
            and trade_type = #{tradeType}
        </if>
    </select>

    <select id="queryByWaybillCode" resultMap="AyElefaceOperatelog">
        select
        <include refid="fileds"/>
        from ay_eleface_operatelog
        where waybill_code = #{waybillCode}
        <if test="cpCode != null">
            and cp_code = #{cpCode}
        </if>
        and seller_id = #{sellerId}
        and store_id = #{storeId}
        and app_name = #{appName}
    </select>

    <select id="queryByWaybillCodeList" resultMap="AyElefaceOperatelog">
        select
        <include refid="fileds"/>
        from ay_eleface_operatelog
        where waybill_code in
        <foreach collection="waybillCodes" open="(" separator="," close=")" item="item">
            #{item}
        </foreach>
        and cp_code = #{cpCode}
        and seller_id = #{sellerId}
        and store_id = #{storeId}
        and app_name = #{appName}
    </select>

    <update id="printCountIncrement">
        update
            ay_eleface_operatelog
        set print_count = print_count + 1
        <if test="externalInfo != null">
            ,external_info = #{externalInfo}
        </if>
        where waybill_code = #{waybillCode}
          and cp_code = #{cpCode}
          and seller_id = #{sellerId}
          and store_id = #{storeId}
          and app_name = #{appName}
    </update>

    <select id="elefaceIsCancelGet" resultType="cn.loveapp.print.api.response.ElefaceIsCancelGetInnerResponse">
        select
        waybill_code as waybillCode,
        child_waybill_code as childWaybillCode,
        is_cancel as isCancel
        from
        ay_eleface_operatelog
        where
        seller_id = #{request.sellerId}
        and store_id = #{request.storeId}
        and app_name = #{request.appName}
        and owner_seller_id = #{request.ownerSellerId}
        and owner_store_id = #{request.ownerStoreId}
        and owner_app_name = #{request.ownerAppName}
        and (
        waybill_code in
        <foreach collection="request.waybillCodeList" open="(" separator="," close=")" item="item">
            #{item}
        </foreach>
        OR
        child_waybill_code in
        <foreach collection="request.waybillCodeList" open="(" separator="," close=")" item="item">
            #{item}
        </foreach>
        )
    </select>

    <sql id="commonQuery">
        <if test="queryDTO.tradeType != null">
            and trade_type = #{queryDTO.tradeType}
        </if>
        <if test="queryDTO.isCancel != null">
            and is_cancel = #{queryDTO.isCancel}
        </if>
        <if test="queryDTO.provider != null and queryDTO.provider != ''">
            and provider = #{queryDTO.provider}
        </if>
        <if test="queryDTO.cpCodes != null and queryDTO.cpCodes.size() > 0">
            and cp_code in
            <foreach collection="queryDTO.cpCodes" open="(" separator="," close=")" item="item">
                #{item}
            </foreach>
        </if>
        <if test="queryDTO.tid != null and queryDTO.tid != ''">
            and tid = #{queryDTO.tid}
        </if>
        <if test="queryDTO.tidList != null and queryDTO.tidList.size() > 0">
            and tid in
            <foreach collection="queryDTO.tidList" open="(" separator="," close=")" item="item">
                #{item}
            </foreach>
        </if>
        <if test="queryDTO.waybillCode != null and queryDTO.waybillCode != ''">
            and waybill_code = #{queryDTO.waybillCode}
        </if>
        <if test="queryDTO.waybillCodeList != null and queryDTO.waybillCodeList.size() > 0">
            and waybill_code in
            <foreach collection="queryDTO.waybillCodeList" open="(" separator="," close=")" item="item">
                #{item}
            </foreach>
        </if>
        <if test="queryDTO.startTime != null and queryDTO.endTime != null">
            and get_time between #{queryDTO.startTime} and #{queryDTO.endTime}
        </if>
        <if test="queryDTO.cancelStartTime != null and queryDTO.cancelEndTime != null">
            and cancel_time between #{queryDTO.cancelStartTime} and #{queryDTO.cancelEndTime}
        </if>
        <if test="queryDTO.ownerSellerId != null and queryDTO.ownerSellerId != ''">
            and owner_seller_id = #{queryDTO.ownerSellerId}
        </if>
        <if test="queryDTO.ownerSellerNick != null and queryDTO.ownerSellerNick != ''">
            and owner_nick = #{queryDTO.ownerSellerNick}
        </if>
        <if test="queryDTO.ownerStoreId != null and queryDTO.ownerStoreId != ''">
            and owner_store_id = #{queryDTO.ownerStoreId}
        </if>
        <if test="queryDTO.ownerAppName != null and queryDTO.ownerAppName != ''">
            and owner_app_name = #{queryDTO.ownerAppName}
        </if>
        <if test="queryDTO.printCount != null">
            and print_count &lt;= #{queryDTO.printCount}
        </if>
    </sql>

    <select id="queryByIds" resultMap="AyElefaceOperatelog">
        select
        <include refid="fileds"/>
        from ay_eleface_operatelog
        where seller_id = #{sellerId} and
        id in
        <foreach collection="ids" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <update id="updateDetailByIds">
        update
        ay_eleface_operatelog
        set receiver_address = #{operateLogDetailsDTO.receiverAddress},
        weight = #{operateLogDetailsDTO.weight},
        freight = #{operateLogDetailsDTO.freight}
        where seller_id = #{sellerId} and
        id in
        <foreach collection="ids" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </update>
    <update id="updatePrintAndSendGoodStatusByIds">
        UPDATE ay_eleface_operatelog
        SET
        <if test="isPrint != null">
            is_print = #{isPrint},
        </if>
        <if test="isSendGood != null">
            is_send_good = #{isSendGood},
        </if>
        id = id
        WHERE seller_id = #{sellerId}
        AND id IN
        <foreach collection="ids" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>

    </update>
</mapper>
