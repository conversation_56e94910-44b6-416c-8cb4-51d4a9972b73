<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.loveapp.print.common.dao.print.ElefaceSharingRelationEntityDao">

    <resultMap type="cn.loveapp.print.common.entity.ElefaceSharingRelationEntity" id="ElefaceSharingRelationMap">
        <result property="id" column="id"/>
        <result property="shareId" column="share_id"/>
        <result property="ownerSellerNick" column="owner_seller_nick"/>
        <result property="ownerSellerId" column="owner_seller_id"/>
        <result property="ownerStoreId" column="owner_store_id"/>
        <result property="ownerAppName" column="owner_app_name"/>
        <result property="ownerMallName" column="owner_mall_name"/>
        <result property="targetSellerNick" column="target_seller_nick"/>
        <result property="targetSellerId" column="target_seller_id"/>
        <result property="targetStoreId" column="target_store_id"/>
        <result property="targetAppName" column="target_app_name"/>
        <result property="targetMallName" column="target_mall_name"/>
        <result property="status" column="status"/>
        <result property="provider" column="provider"/>
        <result property="cpCode" column="cp_code"/>
        <result property="cpType" column="cp_type"/>
        <result property="branchCode" column="branch_code"/>
        <result property="segmentCode" column="segment_code"/>
        <result property="shippAddressProvince" column="shipp_address_province"/>
        <result property="shippAddressCity" column="shipp_address_city"/>
        <result property="shippAddressDistrict" column="shipp_address_district"/>
        <result property="shippAddressDetail" column="shipp_address_detail"/>
        <result property="shippAddressMd5" column="shipp_address_md5"/>
        <result property="shareNum" column="share_num"/>
        <result property="usedNum" column="used_num"/>
        <result property="brandCode" column="brand_code"/>
        <result property="branchName" column="branch_name"/>
        <result property="shareType" column="share_type"/>
        <result property="shareMemo" column="share_memo"/>
        <result property="proxySellerNick" column="proxy_seller_nick"/>
        <result property="proxySellerId" column="proxy_seller_id"/>
        <result property="proxyStoreId" column="proxy_store_id"/>
        <result property="proxyAppName" column="proxy_app_name"/>
        <result property="proxyMallName" column="proxy_mall_name"/>
        <result property="salesman" column="salesman"/>
        <result property="cancelNum" column="cancel_num"/>
        <result property="gmtCreate" column="gmt_create"/>
        <result property="gmtModified" column="gmt_modified"/>
    </resultMap>

    <sql id="tablename">eleface_sharing_relation</sql>

    <sql id="fields">
        id,
        share_id,
        owner_seller_nick,
        owner_seller_id,
        owner_store_id,
        owner_app_name,
        owner_mall_name,
        target_seller_nick,
        target_seller_id,
        target_store_id,
        target_app_name,
        target_mall_name,
        status,
        provider,
        cp_code,
        cp_type,
        branch_code,
        segment_code,
        shipp_address_province,
        shipp_address_city,
        shipp_address_district,
        shipp_address_detail,
        shipp_address_md5,
        share_num,
        used_num,
        cancel_num,
        brand_code,
        share_type,
        share_memo,
        proxy_seller_id,
        proxy_seller_nick,
        proxy_app_name,
        proxy_store_id,
        proxy_mall_name,
        branch_name,
        salesman,
        gmt_create,
        gmt_modified
    </sql>

    <select id="queryByShareId" resultMap="ElefaceSharingRelationMap">
        select
        <include refid="fields"/>
        from
        <include refid="tablename"/>
        where share_id = #{shareId}
    </select>

</mapper>
