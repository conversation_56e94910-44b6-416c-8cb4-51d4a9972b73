package cn.loveapp.print.common.utils;

import cn.loveapp.common.autoconfigure.mq.CommonMQSerializationConfig;
import cn.loveapp.common.autoconfigure.mq.CommonRocketMqHelperProperties;
import cn.loveapp.common.utils.CommonRocketMqQueueHelper;
import org.springframework.stereotype.Component;

/**
 * @program: orders-services-group
 * @description: OnsQueue
 * @author: Jason
 * @create: 2018-11-19 13:57
 **/
@Component
public class RocketMqQueueHelper extends CommonRocketMqQueueHelper {

    public RocketMqQueueHelper(CommonMQSerializationConfig commonMQSerializationConfig, CommonRocketMqHelperProperties commonRocketMqHelperProperties) {
        super(commonMQSerializationConfig, commonRocketMqHelperProperties);
    }
}
