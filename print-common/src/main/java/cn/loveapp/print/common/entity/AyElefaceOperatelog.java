package cn.loveapp.print.common.entity;

import java.time.LocalDateTime;
import java.util.List;

import cn.loveapp.common.utils.LoggerHelper;
import cn.loveapp.print.common.dto.WayBillOperateLogExtDTO;
import com.alibaba.fastjson.JSON;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.CollectionUtils;

import lombok.Data;

/**
 * 电子面单操作日志
 *
 * <AUTHOR>
 */
@Data
public class AyElefaceOperatelog {

    private static final LoggerHelper LOGGER = LoggerHelper.getLogger(AyElefaceOperatelog.class);

    /**
     * 主键自增
     */
    private Long id;

    /**
     * 插件名称
     */
    private String appName;

    /**
     * 商家id
     */
    private String sellerId;

    /**
     * 商家平台 TAO、PDD、JD、1688
     */
    private String storeId;

    /**
     * 商家Nick
     */
    private String sellerNick;

    /**
     * 订单编号
     */
    private String tid;

    /**
     * 订单类型 0-普通订单 1-自由打印订单
     */
    private Integer tradeType;

    /**
     * 流水号
     */
    private String serial;

    /**
     * 合单订单的主单tid
     */
    private String mergeTid;

    /**
     * 购物车子单号
     */
    private String oids;

    /**
     * 面单的服务商 CAINIAO、PDD
     */
    private String provider;

    /**
     * 物流公司名称
     */
    private String logisticsCompany;

    /**
     * 物流公司code
     */
    private String cpCode;

    /**
     * 面单请求id
     */
    private String objectId;

    /**
     * 面单号，字母件模式下为子面单号
     */
    private String waybillCode;

    /**
     * 是否是子母件
     */
    private Boolean isChild;

    /**
     * 字母单的子单号
     */
    private String childWaybillCode;

    /**
     * 面单信息
     */
    private String printData;

    /**
     * 面单所有者用户Nick
     */
    private String ownerNick;

    /**
     * 面单所有者的sellerId
     */
    private String ownerSellerId;

    /**
     * 面单所有者的平台id
     */
    private String ownerStoreId;

    /**
     * 面单所有者的应用名称
     */
    private String ownerAppName;

    /**
     * 面单被分享者用户Nick
     */
    private String targetSellerNick;

    /**
     * 面单被分享者的sellerId
     */
    private String targetSellerId;

    /**
     * 面单被分享者的平台id
     */
    private String targetStoreId;

    /**
     * 面单被分享者的应用名称
     */
    private String targetAppName;

    /**
     * 使用的面单分享Id
     */
    private String shareId;

    /**
     * 面单获取的操作用户
     */
    private String getOperator;

    /**
     * 操作用户的平台 TAO、PDD、JD、1688
     */
    private String getOperatorStoreId;

    /**
     * 操作终端
     */
    private String getOperateTerminal;

    /**
     * 面单获取时间
     */
    private LocalDateTime getTime;

    /**
     * 面单是否取消
     */
    private Boolean isCancel;

    /**
     * 面单取消的操作用户
     */
    private String cancelOperator;

    /**
     * 操作用户的平台 TAO、PDD、JD、1688
     */
    private String cancelOperatorStoreId;

    /**
     * 操作终端
     */
    private String cancelOperateTerminal;

    /**
     * 面单取消时间
     */
    private LocalDateTime cancelTime;

    /**
     * 电子面单打印数量
     */
    private Integer printCount;

    /**
     * 常规-记录创建时间
     */
    private LocalDateTime gmtCreate;

    /**
     * 常规-记录最后修改时间
     */
    private LocalDateTime gmtModified;

    /**
     * 子品牌
     */
    private String brandCode;

    /**
     * 交易（TAO） 扩展字段
     */
    private String externalInfo;

    /**
     * 真实快递公司code
     */
    private String realCpCode;

    /**
     * 是否使用自己（代理）的面单取号、非分享
     */
    private Boolean isUseShare;

    /**
     * 代理用户id
     */
    private String proxySellerId;

    /**
     * 代理用户nick
     */
    private String proxySellerNick;

    /**
     * 代理用户平台
     */
    private String proxyStoreId;

    /**
     * 代理用户应用
     */
    private String proxyAppName;

    /**
     * 分享面单备注
     */
    private String shareMemo;

    /**
     * 收货地址
     */
    private String receiverAddress;

    /**
     * 重量
     */
    private String weight;

    /**
     * 运费
     */
    private String freight;

    /**
     * 合单主单对应的子单id
     */
    private List<Long> mergeSubIds;

    /**
     * 订单号列表（合单子订单号）
     */
    private List<String> mergeSubTidList;

    /**
     * 网点名称
     */
    private String branchName;

    /**
     * 业务员
     */
    private String salesman;

    /**
     * 打印模板Id
     */
    private String logisticsTemplateId;

    /**
     * 电子面单版本号，1-默认值旧版电子面单 2-新版电子面单 (XHS使用)
     */
    private Integer billVersion;

    /**
     * 其他扩展信息包含以下：
     * 1. 收件人信息（省市区、详细地址、手机号、收件人）
     * 2. 发件人信息（省市区、详细地址、手机号、收件人）
     */
    private String otherExternalInfo;

    /**
     * 扩展信息对象
     */
    private WayBillOperateLogExtDTO otherExternalInfoDTO;

    /**
     * 是否已打印
     */
    private Boolean isPrint;

    /**
     * 是否已发货
     */
    private Boolean isSendGood;

    public void setOids(String oids) {
        this.oids = oids;
    }

    public void setOids(List<String> oids) {
        if (!CollectionUtils.isEmpty(oids)) {
            this.oids = String.join(",", oids);
        }
    }

    public void setOtherExternalInfoDTO(WayBillOperateLogExtDTO otherExternalInfoDTO) {
        this.otherExternalInfoDTO = otherExternalInfoDTO;
        this.otherExternalInfo = JSON.toJSONString(otherExternalInfoDTO);
    }

    public void setOtherExternalInfo(String otherExternalInfo) {
        this.otherExternalInfo = otherExternalInfo;
        if (StringUtils.isEmpty(otherExternalInfo)) {
            otherExternalInfoDTO = null;
        } else {
            try {
                otherExternalInfoDTO = JSON.parseObject(otherExternalInfo, WayBillOperateLogExtDTO.class);
            } catch (Exception e) {
                LOGGER.logInfo(sellerNick, waybillCode, "解析面单扩展数据失败, 跳过");
                otherExternalInfoDTO = null;
            }
        }
    }
}
