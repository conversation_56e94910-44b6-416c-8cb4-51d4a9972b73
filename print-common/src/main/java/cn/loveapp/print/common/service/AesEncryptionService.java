package cn.loveapp.print.common.service;

/**
 * 加解密服务
 *
 * <AUTHOR>
 */
public interface AesEncryptionService {
    /**
     * 解密交易订单字段
     *
     * @param trade
     * @return
     */
    String decryptForTrade(String trade);

    /**
     * 解密联系方式字段
     *
     * @param phone
     * @return
     */
    String decryptForPhone(String phone);

    /**
     * 加密交易订单字段
     *
     * @param trade
     * @return
     */
    String encryptForTrade(String trade);

    /**
     * 加密联系方式字段
     *
     * @param phone
     * @return
     */
    String encryptForPhone(String phone);
}
