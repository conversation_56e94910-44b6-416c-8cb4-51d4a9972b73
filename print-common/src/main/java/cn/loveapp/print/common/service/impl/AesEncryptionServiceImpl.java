package cn.loveapp.print.common.service.impl;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import cn.loveapp.common.utils.AesUtil;
import cn.loveapp.common.utils.LoggerHelper;
import cn.loveapp.print.common.service.AesEncryptionService;

/**
 * <AUTHOR>
 */
@Service
public class AesEncryptionServiceImpl implements AesEncryptionService {

    @Value("${loveapp.platformsdk.taobao.trade.sessionkey}")
    private String sessionkey;

    private final static LoggerHelper LOGGER = LoggerHelper.getLogger(AesEncryptionServiceImpl.class);

    @Override
    public String decryptForTrade(String trade) {
        return AesUtil.getInstance().decryptForTrade(trade);
    }

    @Override
    public String decryptForPhone(String phone) {
        try {
            return AesUtil.getInstance().decryptForPhone(phone, sessionkey);
        } catch (Exception e) {
            LOGGER.logError("-", "-", "解密联系方式失败，phone=" + phone, e);
            return phone;
        }
    }

    @Override
    public String encryptForTrade(String trade) {
        if (null == trade) {
            return null;
        }
        return AesUtil.getInstance().encryptForTrade(trade);
    }

    @Override
    public String encryptForPhone(String phone) {
        if (null == phone) {
            return null;
        }
        try {
            return AesUtil.getInstance().encryptForPhone(phone, sessionkey);
        } catch (Exception e) {
            LOGGER.logError("-", "-", "加密联系方式失败，phone=" + phone, e);
            return phone;
        }
    }
}
