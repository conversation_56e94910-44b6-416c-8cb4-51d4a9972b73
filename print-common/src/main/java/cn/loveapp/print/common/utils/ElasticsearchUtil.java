package cn.loveapp.print.common.utils;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.regex.Pattern;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import com.google.common.collect.Lists;

import cn.loveapp.common.utils.LoggerHelper;

/**
 * <AUTHOR>
 * @date 2024-01-21 14:44
 * @description: es搜索工具类
 */
public class ElasticsearchUtil {
    private static final LoggerHelper LOGGER = LoggerHelper.getLogger(ElasticsearchUtil.class);

    /**
     * 数字字母正则
     */
    public static final Pattern ALPHANUMERIC_PATTERN = Pattern.compile("([\\w|\\d])");

    private static final Pattern NUM_PATTERN = Pattern.compile("\\d+");


    /**
     * 匹配字母a
     */
    public static final Pattern A_PATTERN = Pattern.compile("(a)", Pattern.CASE_INSENSITIVE);

    public static final Pattern ALL_PATTERN = Pattern.compile("(.)");

    public static <E> ArrayList<E> toList(E ob) {
        if (ob == null) {
            return null;
        }
        return Lists.newArrayList(ob);
    }

    public static <E> ArrayList<E> toList(Collection<E> cl) {
        if (CollectionUtils.isEmpty(cl)) {
            return null;
        }
        return new ArrayList<>(cl);
    }

    public static String toString(Collection<String> cl) {
        if (CollectionUtils.isEmpty(cl)) {
            return null;
        }
        return String.join(",", cl);
    }

    /**
     * 在数字和字母离两边添加空格, 防止分词无法查询不规整的数字字母 字母A替换为AA, 防止单个字母A被分词忽略
     *
     * @return
     */
    public static void splitAlphanumeric(List<String> texts) {
        if (CollectionUtils.isNotEmpty(texts)) {
            try {
                for (int i = 0; i < texts.size(); i++) {
                    String text = texts.get(i);
                    if (StringUtils.isEmpty(text)) {
                        continue;
                    }
                    texts.set(i,
                        A_PATTERN.matcher(ALPHANUMERIC_PATTERN.matcher(text).replaceAll(" $1 ")).replaceAll("$1$1"));
                }
            } catch (Exception e) {
                LOGGER.logError(e.getMessage(), e);
            }
        }
    }

    /**
     * 在数字和字母离两边添加空格, 防止分词无法查询不规整的数字字母 字母A替换为AA, 防止单个字母A被分词忽略
     *
     * @param text
     * @return
     */
    public static String splitAlphanumeric(String text) {
        if (StringUtils.isEmpty(text)) {
            return text;
        }
        try {
            return A_PATTERN.matcher(ALPHANUMERIC_PATTERN.matcher(text).replaceAll(" $1 ")).replaceAll("$1$1");
        } catch (Exception e) {
            LOGGER.logError(e.getMessage(), e);
            return text;
        }
    }

    /**
     * 在所有字符两边添加空格, 防止分词无法查询不规整的数字字母 字母A替换为AA, 防止单个字母A被分词忽略
     *
     * @param text
     * @return
     */
    public static String splitAll(String text) {
        if (StringUtils.isEmpty(text)) {
            return text;
        }
        try {
            return A_PATTERN.matcher(ALL_PATTERN.matcher(text.toLowerCase()).replaceAll("$1 ")).replaceAll("$1$1");
        } catch (Exception e) {
            LOGGER.logError(e.getMessage(), e);
            return text;
        }
    }


    /**
     * es存储 人名脱敏
     * @param name
     * @return
     */
    public static String desensitiseName(String name) {
        // 收件人姓名 保留收件人首尾 2 个 汉字，中间以"*"拼接
        String left = StringUtils.left(name, 1);
        if (name.length() == 2) {
            return left + "*";
        } else if (name.length() > 2) {
            String right = StringUtils.right(name, 1);
            return left + "*" + right;
        }
        return name;
    }

    /**
     * 手机号脱敏
     *
     * @param phone
     * @return
     */
    public static String desensitisePhone(String phone) {
        if (phone == null) {
            return null;
        }

        int length = phone.length();
        if (length <= 7) {
            return phone;
        }

        String left = StringUtils.left(phone, 3);
        String right = StringUtils.right(phone, 4);
        return left + "****" + right;
    }

    /**
     * es存储 地址脱敏
     * @param address
     * @return
     */
    public static String desensitiseAddress(String address) {
        if (StringUtils.isBlank(address)) {
            return address;
        }
        // 收件详细地址 替换数字为*
        return NUM_PATTERN.matcher(address).replaceAll("*");
    }

}
