package cn.loveapp.print.common.entity;

import java.time.LocalDateTime;
import java.util.List;

import org.springframework.util.CollectionUtils;

import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class AySerialLog {

    /**
     * 主键自增
     */
    private Long id;

    /**
     * 插件名称
     */
    private String appName;

    /**
     * 商家id
     */
    private String sellerId;

    /**
     * 平台 TAO、PDD、JD、1688
     */
    private String storeId;

    /**
     * 商家Nick
     */
    private String sellerNick;

    /**
     * 订单编号
     */
    private String tid;

    /**
     * 订单类型 0-普通订单 1-自由打印订单
     */
    private Integer tradeType;

    /**
     * 合单订单主单的tid
     */
    private String mergeTid;

    /**
     * 购物车子单单号（拆单打印情况下会使用）
     */
    private String oids;

    /**
     * 商家当天的流水号编号，从1开始
     */
    private Long indexNumber;

    /**
     * 流水号
     */
    private String serial;

    /**
     * 流水号是否作废（合单和拆单打印情境下会使用）
     */
    private Boolean isInvalid;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 操作用户
     */
    private String operator;

    /**
     * 操作用户平台 TAO、PDD、JD、1688
     */
    private String operatorStoreId;

    /**
     * 操作终端
     */
    private String operateTerminal;

    /**
     * 常规-记录创建时间
     */
    private LocalDateTime gmtCreate;

    /**
     * 常规-记录最后修改时间
     */
    private LocalDateTime gmtModified;

    public void setOids(List<String> oids) {
        if (!CollectionUtils.isEmpty(oids)) {
            this.oids = String.join(",", oids);
        }
    }

    public void setOids(String oids) {
        this.oids = oids;
    }
}
