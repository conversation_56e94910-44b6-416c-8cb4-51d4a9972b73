package cn.loveapp.print.common.rocketmq;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * RocketMQ 通用队列 配置
 * <AUTHOR>
 * @date 2022/3/17 17:18
 */
@Component
@ConfigurationProperties(prefix = "rocketmq.print.general.config")
@Data
public class RocketMQGeneralConfig extends RocketMQDefaultConfig {

    private Integer delayTimeLevel = 5;

}
