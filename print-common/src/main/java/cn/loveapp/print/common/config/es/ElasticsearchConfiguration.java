package cn.loveapp.print.common.config.es;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024-01-21 14:58
 * @description: 打印es配置
 */
@Data
@Configuration
public class ElasticsearchConfiguration {
    /**
     * 是否启用ES Filter
     */
    @Value("${items.elasticsearch.filter.enable:true}")
    private boolean filterEnable = true;
}
