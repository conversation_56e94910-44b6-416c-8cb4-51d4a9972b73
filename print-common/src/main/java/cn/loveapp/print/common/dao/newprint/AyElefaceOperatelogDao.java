package cn.loveapp.print.common.dao.newprint;

import java.util.List;

import cn.loveapp.print.api.request.ElefaceIsCancelGetInnerRequest;
import cn.loveapp.print.api.response.ElefaceIsCancelGetInnerResponse;
import cn.loveapp.print.common.dto.WaybillOperateLogDetailsDTO;
import cn.loveapp.print.common.dto.WaybillOperatelogQueryDTO;
import cn.loveapp.print.common.entity.AyElefaceOperatelog;
import org.apache.ibatis.annotations.Param;

/**
 * <AUTHOR>
 */
public interface AyElefaceOperatelogDao {
    /**
     * 插入一条数据
     *
     * @param ayElefaceOperatelog
     * @return
     */
    int insert(AyElefaceOperatelog ayElefaceOperatelog);

    /**
     * 批量插入数据
     *
     * @param ayElefaceOperatelogList
     * @return
     */
    int batchInsert(List<AyElefaceOperatelog> ayElefaceOperatelogList);

    /**
     * 修改取消面单的记录
     *
     * @param cancelOplog
     * @param sellerId
     * @param appName
     * @param storeId
     * @return
     */
    int saveCancelInfo(AyElefaceOperatelog cancelOplog, String storeId, String sellerId, String appName);

    /**
     * 查询面单操作日志列表（按操作时间排序）
     *
     * @param queryDTO
     * @param storeId
     * @param sellerId
     * @param appName
     * @return
     */
    List<AyElefaceOperatelog> queryListOrderByOperateTime(WaybillOperatelogQueryDTO queryDTO, String storeId,
                                                          String sellerId, String appName);

    /**
     * 查询总数
     *
     * @param queryDTO
     * @param storeId
     * @param sellerId
     * @param appName
     * @return
     */
    Long queryCount(WaybillOperatelogQueryDTO queryDTO, String storeId, String sellerId, String appName);

    /**
     * 查询指定订单列表的面单操作日志
     *
     * @param tids
     * @param tradeType
     * @param storeId
     * @param sellerId
     * @param appName
     * @return
     */
    List<AyElefaceOperatelog> queryByTids(List<String> tids, Integer tradeType, String storeId, String sellerId,
        String appName);

    /**
     * 查询指定运单号的操作日志
     *
     * @param cpCode
     * @param waybillCode
     * @param storeId
     * @param sellerId
     * @param appName
     * @return
     */
    List<AyElefaceOperatelog> queryByWaybillCode(String cpCode, String waybillCode, String storeId, String sellerId,
        String appName);

    /**
     * 查询指定运单号的操作日志
     *
     * @param cpCode
     * @param waybillCodes
     * @param storeId
     * @param sellerId
     * @param appName
     * @return
     */
    List<AyElefaceOperatelog> queryByWaybillCodeList(String cpCode, List<String> waybillCodes, String storeId, String sellerId,
                                                     String appName);

    /**
     * 打印次数自增
     *
     * @param cpCode
     * @param waybillCode
     * @param storeId
     * @param sellerId
     * @param appName
     * @return
     */
    int printCountIncrement(String cpCode, String waybillCode, String externalInfo, String storeId, String sellerId, String appName);

    /**
     * 批量查询面单是否被回收
     *
     * @param request
     * @return
     */
    List<ElefaceIsCancelGetInnerResponse> elefaceIsCancelGet(@Param("request") ElefaceIsCancelGetInnerRequest request);

    /**
     * 查询指定id列表数据
     *
     * @param sellerId
     * @param ids
     * @return
     */
    List<AyElefaceOperatelog> queryByIds(String sellerId, List<Long> ids);

    /**
     * 更新操作日志详情
     *
     * @param ids
     * @param operateLogDetailsDTO
     * @return
     */
    int updateDetailByIds(@Param("sellerId") String sellerId, @Param("ids") List<Long> ids, @Param("operateLogDetailsDTO") WaybillOperateLogDetailsDTO operateLogDetailsDTO);

    /**
     * 通过id更新打印和发货状态
     *
     * @param sellerId
     * @param ids
     * @param isPrint
     * @param isSendGood
     * @return
     */
    int updatePrintAndSendGoodStatusByIds(@Param("sellerId") String sellerId, @Param("ids") List<Long> ids,
        @Param("isPrint") Boolean isPrint, @Param("isSendGood") Boolean isSendGood);

}
