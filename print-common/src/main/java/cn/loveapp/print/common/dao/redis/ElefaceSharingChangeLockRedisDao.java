package cn.loveapp.print.common.dao.redis;

import cn.loveapp.common.utils.LoggerHelper;
import cn.loveapp.common.utils.RedisUtil;
import cn.loveapp.print.common.exception.LockElefaceSharingRelationException;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

import javax.validation.constraints.NotEmpty;
import java.time.Duration;
import java.util.concurrent.TimeUnit;

/**
 * 面单共享关系更新 redis锁 dao
 *
 * <AUTHOR>
 */
@Component
public class ElefaceSharingChangeLockRedisDao {
    private static final LoggerHelper LOGGER = LoggerHelper.getLogger(ElefaceSharingChangeLockRedisDao.class);

    /**
     * 面单共享关系更新 锁 前缀
     */
    private static final String PREFIX_SHARING_RELATION_CHANGE_KEY = "eleface:sharing:relation:change:shareId:";

    /**
     * 面单共享关系更新当前最新时间戳 前缀
     */
    private static final String PREFIX_SHARING_RELATION_CURRENT_KEY = "eleface:sharing:relation:current:time:value:";

    @Autowired
    private StringRedisTemplate stringRedisTemplate;


    /**
     * 初始化 key
     *
     * @param shareId 面单共享关系 shareId
     * @return
     */
    private String initKey(@NotEmpty String shareId) {
        return PREFIX_SHARING_RELATION_CHANGE_KEY + shareId;
    }

    /**
     * 初始化 key
     *
     * @param shareId 面单共享关系 shareId
     * @return
     */
    private String initTimeValueKey(@NotEmpty String shareId) {
        return PREFIX_SHARING_RELATION_CURRENT_KEY + shareId;
    }

    /**
     * 给 面单共享关系 操作加锁
     * <p>
     * 默认无限制重试
     *
     * @param shareId
     * @return
     */
    public String lockSharingRelation(String shareId, Integer timeout) throws LockElefaceSharingRelationException {
        return lockSharingRelation(shareId, -1, timeout);
    }

    /**
     * 延续锁时长
     * @param shareId
     * @param timeout
     */
    public void continueLockSharingRelation(String shareId,  Integer timeout) {
        if (StringUtils.isEmpty(shareId)) {
            return;
        }
        String key = initKey(shareId);
        stringRedisTemplate.expire(key, timeout, TimeUnit.SECONDS);
    }

    /**
     * 给 面单共享关系 操作加锁
     *
     * @param shareId
     * @param retryCount 重试次数
     * @return 用来解锁的value
     */
    public String lockSharingRelation(String shareId, int retryCount, Integer timeout) throws LockElefaceSharingRelationException {
        if (StringUtils.isEmpty(shareId)) {
            return null;
        }
        String key = initKey(shareId);
        String lockValue = null;
        try {
            lockValue = RedisUtil.timedLock(key, Duration.ofSeconds(timeout), retryCount,
                    stringRedisTemplate);
        } catch (Exception e) {
            LOGGER.logError("-", shareId, "面单分享关系锁定失败", e);
            throw new LockElefaceSharingRelationException("面单分享关系锁定失败: " + shareId, e);
        }
        if (lockValue == null) {
            throw new LockElefaceSharingRelationException("面单分享关系锁定失败: " + shareId);
        }
        return lockValue;
    }

    /**
     * 面单共享关系 解锁
     *
     * @param shareId
     * @param lockValue
     * @return
     */
    public void unlockSharingRelation(String shareId, String lockValue) {
        if (StringUtils.isAnyEmpty(shareId, lockValue)) {
            return;
        }
        String key = initKey(shareId);
        try {
            RedisUtil.timedUnlock(key, lockValue, stringRedisTemplate);
        } catch (Exception e) {
            LOGGER.logError("-", shareId, "面单分享关系解锁失败", e);
        }
    }

    /**
     * 更新时间戳到redis，如果非最新，则跳过
     * @param shareId
     * @param timestampInMillis
     * @return
     */
    public boolean getOrSetTimeValue(String shareId, long timestampInMillis) {
        if (StringUtils.isAnyEmpty(shareId)) {
            return false;
        }

        String key = initTimeValueKey(shareId);

        // 获取 Redis 中的时间戳
        String existingTimestampStr = stringRedisTemplate.opsForValue().get(key);
        long existingTimestamp = existingTimestampStr != null ? Long.parseLong(existingTimestampStr) : Long.MIN_VALUE;

        // 比较时间戳
        if (timestampInMillis < existingTimestamp) {
            return false; // 入参时间戳小于等于已有时间戳，不更新
        } else if (timestampInMillis == existingTimestamp) {
            return true;
        } else {
            // 更新 Redis 中的时间戳
            stringRedisTemplate.opsForValue().set(key, String.valueOf(timestampInMillis), 1, TimeUnit.DAYS);
            return true; // 更新成功
        }
    }

}
