package cn.loveapp.print.common.exception;

/**
 * 需要重新发送的重发消息异常
 *
 * <AUTHOR>
 * @date 2020/5/21
 */
public class ResendMessageException extends RuntimeException{
	/**
	 * 延时队列等级, 0不延时
	 */
	private int delayLevel;

	/**
	 * 消息重试时是否强制处理标志，默认false
	 */
	private Boolean forceHandleFlag = false;

	public ResendMessageException(String message, int delayLevel){
		super(message);
		this.delayLevel = delayLevel;
	}

	public ResendMessageException(int delayLevel, String message, Throwable throwable){
		super(message, throwable);
		this.delayLevel = delayLevel;
	}

	public int getDelayLevel() {
		return delayLevel;
	}

	public Boolean isForceHandleFlag() {
		return forceHandleFlag;
	}

	public void setForceHandleFlag(Boolean forceHandleFlag) {
		this.forceHandleFlag = forceHandleFlag;
	}
}
