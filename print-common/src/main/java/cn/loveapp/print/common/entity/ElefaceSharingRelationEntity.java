package cn.loveapp.print.common.entity;

import cn.loveapp.common.constant.CommonPlatformConstants;
import cn.loveapp.print.common.constant.BillVersionEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.util.Arrays;
import java.util.Objects;

/**
 * (eleface_sharing_relation) 电子面单共享关系 实体类
 *
 * <AUTHOR>
 */
@ApiModel
@Data
public class ElefaceSharingRelationEntity implements Serializable {
    protected static final long serialVersionUID = -1578080710712539966L;
    protected static final String DEFAULT_BRAND_CODE = "default";
    /**
     * 主键自增id
     */
    @ApiModelProperty("主键自增id")
    protected Long id;

    /**
     * 分享关系id
     */
    @ApiModelProperty("分享关系id")
    protected String shareId;

    /**
     * 面单所有者的sellerNick
     */
    @ApiModelProperty("面单所有者的sellerNick")
    protected String ownerSellerNick;

    /**
     * 面单所有者的sellerId
     */
    @ApiModelProperty("面单所有者的sellerId")
    protected String ownerSellerId;

    /**
     * 面单所有者的平台id
     */
    @ApiModelProperty("面单所有者的平台id")
    protected String ownerStoreId;

    /**
     * 面单所有者的应用名称
     */
    @ApiModelProperty("面单所有者的应用名称")
    protected String ownerAppName;

    /**
     * 被分享用户sellerNick
     */
    @ApiModelProperty("被分享用户sellerNick")
    protected String targetSellerNick;

    /**
     * 被分享用户sellerId
     */
    @ApiModelProperty("被分享用户sellerId")
    protected String targetSellerId;

    /**
     * 被分享用户平台id
     */
    @ApiModelProperty("被分享用户平台id")
    protected String targetStoreId;

    /**
     * 被分享用户应用名称
     */
    @ApiModelProperty("被分享用户应用名称")
    protected String targetAppName;

    /**
     * 面单共享状态 1-有效的 0-无效待删除的
     */
    @ApiModelProperty("面单共享状态")
    protected Integer status;

    /**
     * 面单服务商 CN PDD
     */
    @ApiModelProperty("面单服务商")
    protected String provider;

    /**
     * 快递公司code
     */
    @ApiModelProperty("快递公司code")
    protected String cpCode;

    /**
     * 物流服务商 业务类型
     */
    @ApiModelProperty("物流服务商 业务类型")
    protected Long cpType;

    /**
     * 物流网点code
     */
    @ApiModelProperty("物流网点code")
    protected String branchCode;

    /**
     * 号段信息 菜鸟专有字段 其他平台为空字符串
     */
    @ApiModelProperty("号段信息")
    protected String segmentCode;

    /**
     * 发货地址 省
     */
    @ApiModelProperty("发货地址 省")
    protected String shippAddressProvince;

    /**
     * 发货地址 市
     */
    @ApiModelProperty("发货地址 市")
    protected String shippAddressCity;

    /**
     * 发货地址 区
     */
    @ApiModelProperty("发货地址 区")
    protected String shippAddressDistrict;

    /**
     * 发货地址 详细
     */
    @ApiModelProperty("发货地址 详细")
    protected String shippAddressDetail;

    /**
     * 网点发货信息MD5 cpCode cpType branchCode segmentCode shippAddressProvince shippAddressCity shippAddressDistrict
     * shippAddressDetail 取MD5
     */
    @ApiModelProperty("网点发货信息MD5")
    protected String shippAddressMd5;

    /**
     * 共享的面单数量（-1表示不限制数量）
     */
    @ApiModelProperty("共享的面单数量（-1表示不限制数量）")
    protected Long shareNum;

    /**
     * 已使用的数量
     */
    @ApiModelProperty("已使用的数量")
    protected Long usedNum;

    /**
     * 子品牌code
     */
    @ApiModelProperty("子品牌code")
    protected String brandCode;

    /**
     * 变更版本号
     */
    @ApiModelProperty("变更版本号")
    protected Integer version;

    /**
     * 分享类型， 1: 标志代理使用
     */
    @ApiModelProperty("分享类型， 1: 标志代理使用")
    protected Integer shareType;

    /**
     * 分享备注
     */
    @ApiModelProperty("分享备注")
    protected String shareMemo;

    /**
     * 代理用户id
     */
    @ApiModelProperty("代理用户id")
    protected String proxySellerId;

    /**
     * 代理用户nick
     */
    @ApiModelProperty("代理用户nick")
    protected String proxySellerNick;

    /**
     * 代理用户平台
     */
    @ApiModelProperty("代理用户id")
    protected String proxyStoreId;

    /**
     * 代理用户应用
     */
    @ApiModelProperty("代理用户id")
    protected String proxyAppName;

    /**
     * 网点名称
     */
    @ApiModelProperty("网点名称")
    protected String branchName;

    /**
     * 业务员备注
     */
    @ApiModelProperty("业务员备注")
    protected String salesman;

    /**
     * 已取消的数量
     */
    @ApiModelProperty("已取消的数量")
    protected Long cancelNum;

    /**
     * 面单账户名
     */
    @ApiModelProperty("面单账户名")
    protected String ownerMallName;

    /**
     * 面单被分享账户名
     */
    @ApiModelProperty("面单被分享账户名")
    protected String targetMallName;

    /**
     * 面单代理账户名
     */
    @ApiModelProperty("面单代理账户名")
    protected String proxyMallName;

    @ApiModelProperty("创建时间")
    protected String gmtCreate;

    @ApiModelProperty("更新时间")
    protected String gmtModified;

    @ApiModelProperty("电子面单版本号")
    private Integer billVersion;


    public String getShareId() {
        if (shareId == null && cpType != null && branchCode != null && StringUtils.isNoneEmpty(cpCode, ownerSellerId,
                ownerStoreId, ownerAppName, targetSellerId, targetStoreId, targetAppName, shippAddressMd5)) {

            String mainSeller = StringUtils.join(Arrays.asList(ownerSellerId, ownerStoreId, ownerAppName), "_");

            if (StringUtils.isNoneEmpty(proxySellerId, proxyStoreId, proxyAppName)) {
                // 代理模式下，shareId根据代理生成
                mainSeller = StringUtils.join(Arrays.asList(mainSeller, proxySellerId, proxyStoreId, proxyAppName), "_");
            }

            String shareIdStr = "SHARE_" + StringUtils.join(
                    Arrays.asList(mainSeller, targetSellerId, targetStoreId, targetAppName,
                            cpCode, java.lang.String.valueOf(cpType), branchCode, StringUtils.trimToEmpty(segmentCode), shippAddressMd5),
                    "_");
            if (StringUtils.isNotEmpty(brandCode)) {
                // 兼容旧数据
                shareIdStr = shareIdStr + "_" + brandCode;
            }

            if (!Objects.isNull(billVersion) && billVersion > BillVersionEnum.oldVersion.val) {
                // 小红书新版电子面单不兼容老版本通过版本号区分下识别
                shareIdStr = shareIdStr + "_" + billVersion;
            }

            return shareIdStr;
        }
        return shareId;
    }

    public String getSegmentCode() {
        return StringUtils.trimToEmpty(segmentCode);
    }

    /**
     * 生成shareId
     */
    public void generateShareId() {
        this.setShareId(getShareId());
    }

    /**
     * 子品牌默认为空（淘宝默认default，和多平台对其统一设置为null）
     *
     * @param brandCode
     */
    public void setBrandCode(String brandCode) {
        if (DEFAULT_BRAND_CODE.equals(brandCode)) {
            this.brandCode = null;
        } else {
            this.brandCode = brandCode;
        }
    }

    /**
     * 获取分享网点唯一key（判断去重用）
     *
     * @return
     */
    public String getSoleKey() {
        String shareIdStr = StringUtils.join(
                Arrays.asList(ownerSellerId, ownerStoreId, ownerAppName, cpCode, java.lang.String.valueOf(cpType), branchCode, StringUtils.trimToEmpty(segmentCode), shippAddressMd5),
                "_");
        if (StringUtils.isNotEmpty(brandCode)) {
            // 兼容旧数据
            shareIdStr = shareIdStr + "_" + brandCode;
        }
        return shareIdStr;
    }

}
