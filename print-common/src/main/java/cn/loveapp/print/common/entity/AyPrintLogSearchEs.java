package cn.loveapp.print.common.entity;

import java.io.Serializable;
import java.util.*;
import java.util.stream.Collectors;

import javax.validation.constraints.NotBlank;

import cn.loveapp.common.utils.LoggerHelper;
import cn.loveapp.print.common.constant.ElefaceLogUserRole;
import cn.loveapp.print.common.constant.PrintTypeConstant;
import cn.loveapp.print.common.constant.WebConstant;
import cn.loveapp.print.common.convert.CommonMapper;
import cn.loveapp.print.common.utils.ListUtil;
import cn.loveapp.print.common.dto.WayBillOperateLogExtDTO;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.annotation.Id;
import org.springframework.data.elasticsearch.annotations.*;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonFormat;

import cn.loveapp.common.utils.DateUtil;
import cn.loveapp.print.common.utils.ElasticsearchUtil;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2024-01-18 11:08
 * @description: 打印日志搜索-ES实体类
 */
@Data
@Mapping
@Document(indexName = AyPrintLogSearchEs.INDEX_NAME_PREFIX, type = "_doc", createIndex = false)
@NoArgsConstructor
public class AyPrintLogSearchEs implements Serializable, ElasticsearchEntity {

    private static final LoggerHelper LOGGER = LoggerHelper.getLogger(AyPrintLogSearchEs.class);

    /**
     * 索引名前缀
     */
    public static final String INDEX_NAME_PREFIX = "ay_printlog_search";

    /**
     * gmt时间格式
     */
    public static final String GMT_FORMATTER = "yyyy-MM-dd'T'HH:mm:ss.SSS";

    /**
     * 分秒格式
     */
    public static final String MINUTE_SECOND_FORMATTER = "yyyy-MM-dd'T'HH:mm:ss";


    /**
     * 合单订单主单前缀
     */
    private static final String TDM = "TDM";

    /**
     * 默认字符串
     */
    private static final String DEFAULT = "DEFAULT";

    /**
     * 主键（storeId + spuId）
     */
    @Id
    @Field(type = FieldType.Keyword)
    private String id;

    /**
     * 是否已经被删除
     */
    @Field(type = FieldType.Boolean)
    private Boolean isDeleted;

    /**
     * 数据创建时间
     */
    @Field(type = FieldType.Date, format = DateFormat.date_hour_minute_second_millis)
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = GMT_FORMATTER)
    @JSONField(format = GMT_FORMATTER)
    private Date gmtCreate;

    /**
     * 数据最新修改时间
     */
    @Field(type = FieldType.Date, format = DateFormat.date_hour_minute_second_millis)
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = GMT_FORMATTER)
    @JSONField(format = GMT_FORMATTER)
    private Date gmtModified;

    /**
     * 卖家id
     */
    @Field(type = FieldType.Keyword)
    private String ayUserId;

    /**
     * 卖家id
     */
    @Field(type = FieldType.Keyword)
    private String sellerId;

    /**
     * 卖家nick
     */
    @Field(type = FieldType.Keyword)
    private String sellerNick;

    /**
     * 平台id
     */
    @Field(type = FieldType.Keyword)
    private String storeId;

    /**
     * 应用id
     */
    @Field(type = FieldType.Keyword)
    private String appName;

    /**
     * ay_printlog表主键id
     */
    @Field(type = FieldType.Keyword)
    private Long ayPrintLogId;

    /**
     * ay_printlog表主键id(合单)
     */
    @Field(type = FieldType.Keyword)
    private List<Long> mergeSubPrintLogId;

    /**
     * 打印类型
     */
    @Field(type = FieldType.Keyword)
    private String printType;

    /**
     * 爱用主订单号
     */
    @MultiField(mainField = @Field(type = FieldType.Keyword))
    private String tid;

    /**
     * 爱用主订单号
     */
    @MultiField(mainField = @Field(type = FieldType.Keyword))
    private List<String> mergeSubTidList;

    /**
     * 订单类型
     */
    @Field(type = FieldType.Keyword)
    private Integer tradeType;

    /**
     * 面单号
     */
    @MultiField(mainField = @Field(type = FieldType.Keyword))
    private String waybillNumber;

    /**
     * 操作人姓名
     */
    @MultiField(mainField = @Field(type = FieldType.Text, analyzer = "single_analyzer"),
        otherFields = {@InnerField(suffix = "keyword", type = FieldType.Keyword)})
    private String operatorName;

    /**
     * 操作人平台
     */
    @Field(type = FieldType.Keyword)
    private String operatorStoreId;

    /**
     * 收件人姓名
     */
    @MultiField(mainField = @Field(type = FieldType.Text, analyzer = "single_analyzer"),
        otherFields = {@InnerField(suffix = "keyword", type = FieldType.Keyword)})
    private String receiverName;

    /**
     * 收件人姓名(加密)
     */
    @Field(type = FieldType.Keyword)
    private String encryptReceiverName;

    /**
     * 收件人地址（省市区街道+详细地址）
     */
    @MultiField(mainField = @Field(type = FieldType.Text, analyzer = "ik_max_word"),
        otherFields = {@InnerField(suffix = "keyword", type = FieldType.Keyword)})
    private String receiverAddress;

    /**
     * 收件人手机号（虚拟号）
     */
    @MultiField(mainField = @Field(type = FieldType.Text, analyzer = "ngram_analyzer"),
        otherFields = {@InnerField(suffix = "keyword", type = FieldType.Keyword)})
    private String receiverMobile;

    /**
     * 收件人固定电话
     */
    @MultiField(mainField = @Field(type = FieldType.Text, analyzer = "ngram_analyzer"),
        otherFields = {@InnerField(suffix = "keyword", type = FieldType.Keyword)})
    private String receiverPhone;

    /**
     * 收件人省份
     */
    @Field(type = FieldType.Keyword)
    private String receiverProvince;

    /**
     * 收件人城市
     */
    @Field(type = FieldType.Keyword)
    private String receiverCity;

    /**
     * 发货人姓名
     */
    @MultiField(mainField = @Field(type = FieldType.Text, analyzer = "single_analyzer"),
        otherFields = {@InnerField(suffix = "keyword", type = FieldType.Keyword)})
    private String senderName;

    /**
     * 发货人地址（省市区街道+详细地址）
     */
    @MultiField(mainField = @Field(type = FieldType.Text, analyzer = "ik_max_word"),
        otherFields = {@InnerField(suffix = "keyword", type = FieldType.Keyword)})
    private String senderAddress;

    /**
     * 发货人手机号（虚拟号）
     */
    @MultiField(mainField = @Field(type = FieldType.Text, analyzer = "ngram_analyzer"),
        otherFields = {@InnerField(suffix = "keyword", type = FieldType.Keyword)})
    private String senderMobile;

    /**
     * 发货人固定电话
     */
    @MultiField(mainField = @Field(type = FieldType.Text, analyzer = "ngram_analyzer"),
        otherFields = {@InnerField(suffix = "keyword", type = FieldType.Keyword)})
    private String senderPhone;

    /**
     * 打印机序号
     */
    @Field(type = FieldType.Keyword)
    private String printerNumber;

    /**
     * 是否重复打印
     */
    @Field(type = FieldType.Boolean)
    private Boolean isRepetitionPrint;

    /**
     * 是否取消打印
     */
    @Field(type = FieldType.Boolean)
    private Boolean isCancel;

    /**
     * 打印时间
     */
    @Field(type = FieldType.Date, format = DateFormat.date_hour_minute_second)
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = MINUTE_SECOND_FORMATTER)
    @JSONField(format = MINUTE_SECOND_FORMATTER)
    private Date printTime;

    /**
     * 物流公司（物流单）
     */
    @Field(type = FieldType.Keyword)
    private String logisticsCompany;

    /**
     * 物流公司code（电子面单）
     */
    @Field(type = FieldType.Keyword)
    private String logisticsCompanyCode;

    /**
     * 面单服务商
     */
    @Field(type = FieldType.Keyword)
    private String elefaceProvider;

    /**
     * 爱用分销信息
     */
    @Field(type = FieldType.Object)
    private AyDistribute ayDistribute;

    /**
     * 合单订单的主单tid
     */
    @Field(type = FieldType.Keyword)
    private String mergeTid;

    /**
     * 是否合单主单 1是
     */
    @Field(type = FieldType.Keyword)
    private Integer mergeTradeStatus;

    /**
     * 使用的面单分享记录Id
     */
    @Field(type = FieldType.Keyword)
    private String shareId;

    /**
     * 真实快递公司code
     */
    @Field(type = FieldType.Keyword)
    private String realCpCode;

    /**
     * 批次号
     */
    @Field(type = FieldType.Keyword)
    private String batchId;

    /**
     * 当前批次序号
     */
    @Field(type = FieldType.Integer)
    private ArrayList<Integer> numbersInBatch;


    /**
     * 面单操作记录相关
     */
    @Field(type = FieldType.Object)
    private ElefaceOperateLog elefaceOperateLog;

    @Data
    public static class AyDistribute {

        /**
         * 分销来源订单id
         */
        @Field(type = FieldType.Keyword)
        private String tid;

        /**
         * 分销店铺id
         */
        @Field(type = FieldType.Keyword)
        private String ayUserId;

        /**
         * 分销商id(爱用账号)
         */
        @Field(type = FieldType.Keyword)
        private String distributeAyUserId;
    }

    /**
     * 操作日志
     */
    @Data
    public static class ElefaceOperateLog {
        /**
         * 日志用户角色 （1：分享者 0: 使用者）
         */
        @Field(type = FieldType.Keyword)
        private Integer elefaceLogUserRole;

        /**
         * 取号订单的用户id（elefaceLogUserRole = 1 时存在，搜索数据库分片）
         */
        @Field(type = FieldType.Keyword)
        private String orderSellerId;

        /**
         * 流水号
         */
        @Field(type = FieldType.Keyword)
        private String serial;

        /**
         * 面单获取时间（操作记录）
         */
        @Field(type = FieldType.Date, format = DateFormat.date_hour_minute_second)
        @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = MINUTE_SECOND_FORMATTER)
        @JSONField(format = MINUTE_SECOND_FORMATTER)
        private Date elefaceGetTime;

        /**
         * 面单取消时间（操作记录）
         */
        @Field(type = FieldType.Date, format = DateFormat.date_hour_minute_second)
        @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = MINUTE_SECOND_FORMATTER)
        @JSONField(format = MINUTE_SECOND_FORMATTER)
        private Date elefaceCancelTime;

        /**
         * 分享面单备注
         */
        @MultiField(mainField = @Field(type = FieldType.Text, analyzer = "single_analyzer"),
                otherFields = {@InnerField(suffix = "keyword", type = FieldType.Keyword)})
        private String shareMemo;

        /**
         * 分享业务员
         */
        @MultiField(mainField = @Field(type = FieldType.Text, analyzer = "single_analyzer"),
                otherFields = {@InnerField(suffix = "keyword", type = FieldType.Keyword)})
        private String salesman;

        /**
         * 网点名称
         */
        @MultiField(mainField = @Field(type = FieldType.Text, analyzer = "single_analyzer"))
        private String branchName;

        /**
         * 面单账号id
         */
        @Field(type = FieldType.Keyword)
        private String ownerSellerId;

        /**
         * 面单被分享者id
         */
        @Field(type = FieldType.Keyword)
        private String targetSellerId;

        /**
         * 面单模板名称
         */
        @MultiField(mainField = @Field(type = FieldType.Text, analyzer = "ik_max_word"),
            otherFields = {@InnerField(suffix = "keyword", type = FieldType.Keyword)})
        @Field(type = FieldType.Keyword)
        private String elefaceTemplateName;

        /**
         * 是否已打印
         */
        @Field(type = FieldType.Boolean)
        private Boolean isPrint;

        /**
         * 是否已发货
         */
        @Field(type = FieldType.Boolean)
        private Boolean isSendGood;
    }

    public static AyPrintLogSearchEs of(AyPrintlog ayPrintlog) {
        AyPrintLogSearchEs ayPrintLogSearchEs = new AyPrintLogSearchEs();
        String storeId = ayPrintlog.getStoreId();
        String appName = ayPrintlog.getAppName();

        ayPrintLogSearchEs.setId(createId(ayPrintlog.getTid(), String.valueOf(ayPrintlog.getId()), storeId, appName));
        ayPrintLogSearchEs.setSellerId(ayPrintlog.getSellerId());
        ayPrintLogSearchEs.setStoreId(storeId);
        ayPrintLogSearchEs.setAppName(appName);
        ayPrintLogSearchEs.setAyPrintLogId(ayPrintlog.getId());
        ayPrintLogSearchEs.setSellerNick(ayPrintlog.getSellerNick());
        ayPrintLogSearchEs.setAyUserId(createAyUserId(ayPrintlog.getSellerId(), storeId, appName));
        ayPrintLogSearchEs.setAyPrintLogId(ayPrintlog.getId());
        ayPrintLogSearchEs.setPrintType(ayPrintlog.getPrintType());
        ayPrintLogSearchEs.setTid(ayPrintlog.getTid());
        ayPrintLogSearchEs.setWaybillNumber(ayPrintlog.getWaybillCode());
        ayPrintLogSearchEs.setOperatorName(ayPrintlog.getOperator());
        ayPrintLogSearchEs.setOperatorStoreId(ayPrintlog.getOperatorStoreId());
        ayPrintLogSearchEs.setOperatorStoreId(ayPrintlog.getOperatorStoreId());
        // 收件人信息
        ayPrintLogSearchEs.setReceiverName(ayPrintlog.getReceiverName());
        ayPrintLogSearchEs.setReceiverMobile(ayPrintlog.getReceiverMobile());
        ayPrintLogSearchEs.setReceiverPhone(ayPrintlog.getReceiverPhone());
        ayPrintLogSearchEs.setReceiverProvince(ayPrintlog.getReceiverProvince());
        ayPrintLogSearchEs.setReceiverCity(ayPrintlog.getReceiverCity());
        ayPrintLogSearchEs.setReceiverAddress(ayPrintlog.getReceiverDetail());
        // 省+市+区+详细地址
        ayPrintLogSearchEs
            .setReceiverAddress(createReceiverAddress(ayPrintlog.getReceiverProvince(), ayPrintlog.getReceiverCity(),
                ayPrintlog.getReceiverDistrict(), ayPrintlog.getReceiverTown(), ayPrintlog.getReceiverDetail()));

        // 发货人信息
        ayPrintLogSearchEs.setSenderName(ayPrintlog.getSenderName());
        ayPrintLogSearchEs.setSenderMobile(ayPrintlog.getSenderMobile());
        ayPrintLogSearchEs.setSenderPhone(ayPrintlog.getSenderPhone());
        ayPrintLogSearchEs
            .setSenderAddress(createReceiverAddress(ayPrintlog.getSenderProvince(), ayPrintlog.getSenderCity(),
                ayPrintlog.getSenderDetail(), ayPrintlog.getSenderTown(), ayPrintlog.getSenderDistrict()));

        // 打印机编号
        ayPrintLogSearchEs.setPrinterNumber(ayPrintlog.getPrinter());
        // 时间
        ayPrintLogSearchEs.setPrintTime(DateUtil.convertLocalDateTimetoDate(ayPrintlog.getPrintTime()));
        ayPrintLogSearchEs.setLogisticsCompanyCode(ayPrintlog.getCpCode());
        ayPrintLogSearchEs.setLogisticsCompany(ayPrintlog.getLogisticsCompany());
        ayPrintLogSearchEs.setIsCancel(ayPrintlog.getWaybillIsCancel());
        ayPrintLogSearchEs.setGmtModified(DateUtil.convertLocalDateTimetoDate(ayPrintlog.getGmtModified()));
        if (ayPrintlog.getGmtCreate() != null) {
            ayPrintLogSearchEs.setGmtCreate(DateUtil.convertLocalDateTimetoDate(ayPrintlog.getGmtCreate()));
        }

        if (ayPrintlog.getMergeTid() != null && ayPrintlog.getTid().contains(TDM)) {
            // 合单主单标识
            ayPrintLogSearchEs.setMergeTradeStatus(1);
            ayPrintLogSearchEs.setMergeTid(ayPrintlog.getMergeTid());
        }

        ayPrintLogSearchEs.setTradeType(ayPrintlog.getTradeType());
        ayPrintLogSearchEs.setElefaceProvider(ayPrintlog.getElefaceProvider());
        if (StringUtils.isNotEmpty(ayPrintlog.getAyDistribute())) {
            AyDistributeInfo ayDistributeInfo = JSON.parseObject(ayPrintlog.getAyDistribute(), AyDistributeInfo.class);
            AyDistribute ayDistribute = new AyDistribute();
            ayDistribute.setAyUserId(createAyUserId(ayDistributeInfo.getSellerId(), ayDistributeInfo.getStoreId(), ayDistributeInfo.getAppName()));
            ayDistribute.setDistributeAyUserId(createAyUserId(ayDistributeInfo.getDistributeSellerId(), ayDistributeInfo.getDistributeStoreId(), ayDistributeInfo.getDistributeAppName()));
            ayDistribute.setTid(ayDistributeInfo.getTid());
            ayPrintLogSearchEs.setAyDistribute(ayDistribute);
        }

        if (StringUtils.isNotEmpty(ayPrintlog.getNumbersInBatch())) {
            ayPrintLogSearchEs.setNumbersInBatch(ElasticsearchUtil.toList(ListUtil.convertWithComma(ayPrintlog.getNumbersInBatch(), Integer::parseInt)));
        }
        ayPrintLogSearchEs.setBatchId(ayPrintlog.getBatchId());

        return ayPrintLogSearchEs;
    }

    public static List<AyPrintLogSearchEs> of(List<AyElefaceOperatelog> operatelogList) {
        if (CollectionUtils.isEmpty(operatelogList)) {
            return null;
        }

        List<AyPrintLogSearchEs> saveLogEs = new ArrayList<>();

        Map<String, List<AyElefaceOperatelog>> merge2Log = operatelogList.stream()
                .collect(Collectors.groupingBy(log -> log.getMergeTid() == null ? DEFAULT : log.getMergeTid()));

        merge2Log.forEach((mergeTid, subOperateLogs) ->{
            if (StringUtils.isNotEmpty(mergeTid) && mergeTid.startsWith(TDM)) {
                // 合单es只保存主单
                Map<String, List<AyElefaceOperatelog>> collect = subOperateLogs.stream().collect(Collectors.groupingBy(AyElefaceOperatelog::getWaybillCode));
                for (Map.Entry<String, List<AyElefaceOperatelog>> entry : collect.entrySet()) {
                    List<AyElefaceOperatelog> mergeLog = entry.getValue();

                    // 每个单号保存一条
                    Set<Long> subIds = new HashSet<>();
                    Set<String> tidList = new HashSet<>();
                    for (AyElefaceOperatelog operateLog : mergeLog) {
                        if (operateLog.getId() != null) {
                            subIds.add(operateLog.getId());
                        }

                        if (operateLog.getTid() != null) {
                            tidList.add(operateLog.getTid());
                        }
                    }
                    // 存在合单, 生成合单主单
                    AyElefaceOperatelog ayElefaceOperatelog = CommonMapper.INSTANCE.copyElefaceOperateLog(mergeLog.get(0));

                    ayElefaceOperatelog.setTid(mergeTid);
                    ayElefaceOperatelog.setMergeSubIds(Lists.newArrayList(subIds));
                    ayElefaceOperatelog.setMergeSubTidList(Lists.newArrayList(tidList));
                    List<AyPrintLogSearchEs> logSearchEs = AyPrintLogSearchEs.of(ayElefaceOperatelog);
                    saveLogEs.addAll(logSearchEs);
                }
            } else {
                for (AyElefaceOperatelog operateLog: subOperateLogs) {
                    List<AyPrintLogSearchEs> logSearchEs = AyPrintLogSearchEs.of(operateLog);
                    saveLogEs.addAll(logSearchEs);
                }
            }
        });
        return saveLogEs;
    }


    public static List<AyPrintLogSearchEs> of(AyElefaceOperatelog operateLog) {
        // 面单操作记录需生成两条es记录，一条对应面单分享者，一条对应面单使用者者
        AyPrintLogSearchEs targetOperateLog = generalTargetOperateLog(operateLog);
        String orderSellerId = operateLog.getSellerId();

        ElefaceOperateLog elefaceOperate = targetOperateLog.getElefaceOperateLog();
        if (elefaceOperate == null) {
            elefaceOperate = new ElefaceOperateLog();
        }
        elefaceOperate.setElefaceLogUserRole(ElefaceLogUserRole.ELEFACE_USER);
        targetOperateLog.setElefaceOperateLog(elefaceOperate);

        // 如果不是使用分享面单，只记录一条使用者的日志
        if (BooleanUtils.isNotTrue(operateLog.getIsUseShare())) {
            return Lists.newArrayList(targetOperateLog);
        }

        // 记录分享者or代理分享者的日志索引
        AyPrintLogSearchEs ownerOperateLog = CommonMapper.INSTANCE.copyAyPrintLogSearchEs(targetOperateLog);
        ElefaceOperateLog ownerElefaceOperate = CommonMapper.INSTANCE.copyElefaceOperateLog(elefaceOperate);
        ownerElefaceOperate.setElefaceLogUserRole(ElefaceLogUserRole.ELEFACE_SHARER);
        ownerElefaceOperate.setOrderSellerId(orderSellerId);
        ownerOperateLog.setElefaceOperateLog(ownerElefaceOperate);
        String sellerId = operateLog.getProxySellerId();
        String sellerNick = operateLog.getProxySellerNick();
        String storeId = operateLog.getProxyStoreId();
        String appName = operateLog.getProxyAppName();

        if (StringUtils.isAnyEmpty(sellerId, storeId, appName)) {
            // 不存在代理
            sellerId = operateLog.getOwnerSellerId();
            sellerNick = operateLog.getOwnerNick();
            storeId= operateLog.getOwnerStoreId();
            appName = operateLog.getOwnerAppName();
        }
        List<Long> opIds = CollectionUtils.isNotEmpty(operateLog.getMergeSubIds()) ? operateLog.getMergeSubIds() : Lists.newArrayList(operateLog.getId());
        ownerOperateLog.setId(createOperateId(operateLog.getTid(), opIds, sellerId, storeId, appName));
        ownerOperateLog.setSellerId(sellerId);
        ownerOperateLog.setStoreId(storeId);
        ownerOperateLog.setAppName(appName);
        ownerOperateLog.setSellerNick(sellerNick);
        ownerOperateLog.setAyUserId(createAyUserId(sellerId, storeId, appName));

        return Lists.newArrayList(targetOperateLog, ownerOperateLog);
    }


    private static AyPrintLogSearchEs generalTargetOperateLog(AyElefaceOperatelog operateLog) {
        AyPrintLogSearchEs ayPrintLogSearchEs = new AyPrintLogSearchEs();
        String storeId = operateLog.getStoreId();
        String appName = operateLog.getAppName();
        String sellerId = operateLog.getSellerId();

        List<Long> opIds = CollectionUtils.isNotEmpty(operateLog.getMergeSubIds()) ? operateLog.getMergeSubIds() : Lists.newArrayList(operateLog.getId());
        ayPrintLogSearchEs.setId(createOperateId(operateLog.getTid(), opIds, sellerId, storeId, appName));
        ayPrintLogSearchEs.setSellerId(operateLog.getSellerId());
        ayPrintLogSearchEs.setStoreId(storeId);
        ayPrintLogSearchEs.setAppName(appName);
        ayPrintLogSearchEs.setAyPrintLogId(operateLog.getId());
        ayPrintLogSearchEs.setSellerNick(operateLog.getSellerNick());
        ayPrintLogSearchEs.setAyUserId(createAyUserId(operateLog.getSellerId(), storeId, appName));
        ayPrintLogSearchEs.setAyPrintLogId(operateLog.getId());
        ayPrintLogSearchEs.setPrintType(PrintTypeConstant.GET_ELEFACE);
        ayPrintLogSearchEs.setTid(operateLog.getTid());
        ayPrintLogSearchEs.setWaybillNumber(operateLog.getWaybillCode());
        ayPrintLogSearchEs.setOperatorName(operateLog.getGetOperator());
        ayPrintLogSearchEs.setOperatorStoreId(operateLog.getGetOperatorStoreId());
        ayPrintLogSearchEs.setOperatorStoreId(operateLog.getGetOperatorStoreId());

        ayPrintLogSearchEs.setLogisticsCompanyCode(operateLog.getCpCode());
        ayPrintLogSearchEs.setLogisticsCompany(operateLog.getLogisticsCompany());
        ayPrintLogSearchEs.setIsCancel(operateLog.getIsCancel());
        ayPrintLogSearchEs.setGmtModified(DateUtil.convertLocalDateTimetoDate(operateLog.getGmtModified()));
        if (operateLog.getGmtCreate() != null) {
            ayPrintLogSearchEs.setGmtCreate(DateUtil.convertLocalDateTimetoDate(operateLog.getGmtCreate()));
        }

        ayPrintLogSearchEs.setTradeType(operateLog.getTradeType());
        ayPrintLogSearchEs.setElefaceProvider(operateLog.getProvider());

        if (operateLog.getMergeTid() != null && operateLog.getTid().contains(TDM)) {
            // 合单主单标识
            ayPrintLogSearchEs.setMergeTradeStatus(1);
            ayPrintLogSearchEs.setMergeSubTidList(operateLog.getMergeSubTidList());
            ayPrintLogSearchEs.setMergeSubPrintLogId(operateLog.getMergeSubIds());
        }
        if (StringUtils.isNotEmpty(operateLog.getMergeTid()) && !WebConstant.UNDEFINED.equals(operateLog.getMergeTid())) {
            ayPrintLogSearchEs.setMergeTid(operateLog.getMergeTid());
        }
        ayPrintLogSearchEs.setShareId(operateLog.getShareId());
        ayPrintLogSearchEs.setRealCpCode(operateLog.getRealCpCode());

        // 操作日志
        ElefaceOperateLog operateEleface = new ElefaceOperateLog();
        operateEleface.setElefaceGetTime(DateUtil.convertLocalDateTimetoDate(operateLog.getGetTime()));
        operateEleface.setElefaceCancelTime(DateUtil.convertLocalDateTimetoDate(operateLog.getCancelTime()));
        operateEleface.setSerial(operateLog.getSerial());
        operateEleface.setShareMemo(operateLog.getShareMemo());
        operateEleface.setOwnerSellerId(operateLog.getOwnerSellerId());
        operateEleface.setTargetSellerId(operateLog.getTargetSellerId());
        operateEleface.setSalesman(operateLog.getSalesman());
        operateEleface.setBranchName(operateLog.getBranchName());
        operateEleface.setIsPrint(operateLog.getIsPrint());
        operateEleface.setIsSendGood(operateLog.getIsSendGood());

        // 存下额外信息
        WayBillOperateLogExtDTO wayBillOperateLogExtDTO = operateLog.getOtherExternalInfoDTO();
        if (wayBillOperateLogExtDTO != null) {
            ayPrintLogSearchEs.setReceiverName(wayBillOperateLogExtDTO.getReceiverName());
            ayPrintLogSearchEs.setReceiverMobile(wayBillOperateLogExtDTO.getReceiverMobile());
            ayPrintLogSearchEs.setReceiverPhone(wayBillOperateLogExtDTO.getReceiverPhone());
            ayPrintLogSearchEs.setReceiverProvince(wayBillOperateLogExtDTO.getReceiverProvince());
            ayPrintLogSearchEs.setReceiverCity(wayBillOperateLogExtDTO.getReceiverCity());
            // 省+市+区+详细地址
            ayPrintLogSearchEs.setReceiverAddress(createReceiverAddress(wayBillOperateLogExtDTO.getReceiverProvince(),
                wayBillOperateLogExtDTO.getReceiverCity(), wayBillOperateLogExtDTO.getReceiverDistrict(),
                wayBillOperateLogExtDTO.getReceiverTown(), wayBillOperateLogExtDTO.getReceiverDetail()));

            operateEleface.setElefaceTemplateName(wayBillOperateLogExtDTO.getElefaceTemplateName());
        }


        ayPrintLogSearchEs.setElefaceOperateLog(operateEleface);
        return ayPrintLogSearchEs;
    }



    /**
     * 将打印日志列表转换为打印日志搜索列表
     *
     * @param ayPrintLogList
     * @return
     */
    public static List<AyPrintLogSearchEs> ayPrintLogListToAyPrintLogSearchEsList(List<AyPrintlog> ayPrintLogList,
        Date gmtModified, Boolean isRepetitionPrint) {
        List<AyPrintLogSearchEs> ayPrintLogSearchEsList = new ArrayList<>();
        for (AyPrintlog ayPrintlog : ayPrintLogList) {
            AyPrintLogSearchEs ayPrintLogSearchEs = AyPrintLogSearchEs.of(ayPrintlog);

            if (BooleanUtils.isTrue(isRepetitionPrint)) {
                ayPrintLogSearchEs.setIsRepetitionPrint(isRepetitionPrint);
            }

            if (gmtModified != null) {
                ayPrintLogSearchEs.setGmtModified(gmtModified);
            }

            ayPrintLogSearchEsList.add(ayPrintLogSearchEs);
        }
        return ayPrintLogSearchEsList;

    }

    /**
     * 获取爱用用户id
     *
     * @param sellerId
     * @param storeId
     * @param appName
     * @return
     */
    public static String createAyUserId(String sellerId, String storeId, String appName) {
        return storeId + appName + sellerId;
    }

    /**
     * 生成es唯一id（打印日志）
     *
     * @param tid
     * @param ayPrintLogId
     * @param storeId
     * @param appName
     * @return
     */
    public static String createId(@NotBlank String tid, @NotBlank String ayPrintLogId, @NotBlank String storeId,
        @NotBlank String appName) {
        return storeId + appName + tid + ayPrintLogId;
    }

    /**
     * 生成面单操作日志es唯一id
     * @param tid
     * @param ayPrintLogIds
     * @param sellerId
     * @param storeId
     * @param appName
     * @return
     */
    public static String createOperateId(@NotBlank String tid, @NotBlank List<Long> ayPrintLogIds, @NotBlank String sellerId, @NotBlank String storeId,
                                         @NotBlank String appName) {
        String ayPrintLogMd5 = null;
        if (CollectionUtils.isNotEmpty(ayPrintLogIds)) {
           if (ayPrintLogIds.size() > 1) {
               // 合单
               LOGGER.logError(tid + " ayPrintLogIds = " + ayPrintLogIds);
               ayPrintLogIds.sort(null);
               ayPrintLogMd5 = DigestUtils.md5Hex(ayPrintLogIds.toString()).toUpperCase();
           } else {
               ayPrintLogMd5 = String.valueOf(ayPrintLogIds.get(0));
           }
        }
        return sellerId + storeId + appName + tid + ayPrintLogMd5;
    }

    public static String createReceiverAddress(String receiverProvince, String receiverCity, String receiverDetail,
        String receiverTown, String receiverDistrict) {
        StringBuilder receiverAddressBuilder = new StringBuilder();
        if (!StringUtils.isBlank(receiverProvince)) {
            receiverAddressBuilder.append(receiverProvince);
        }

        if (!StringUtils.isBlank(receiverCity)) {
            receiverAddressBuilder.append(receiverCity);
        }

        if (!StringUtils.isBlank(receiverDetail)) {
            receiverAddressBuilder.append(receiverDetail);
        }

        if (!StringUtils.isBlank(receiverTown)) {
            receiverAddressBuilder.append(receiverTown);
        }

        if (!StringUtils.isBlank(receiverDistrict)) {
            receiverAddressBuilder.append(receiverDistrict);
        }
        return receiverAddressBuilder.toString();
    }

    @Override
    public void initDefault() {
        initSpecial();
    }

    @Override
    public void initSpecial() {
        receiverAddress = ElasticsearchUtil.splitAlphanumeric(receiverAddress);
        senderAddress = ElasticsearchUtil.splitAlphanumeric(senderAddress);
        if (elefaceOperateLog != null) {
            elefaceOperateLog.setElefaceTemplateName(
                ElasticsearchUtil.splitAlphanumeric(elefaceOperateLog.getElefaceTemplateName()));
        }
    }

    @Override
    public Object clone() {
        return null;
    }
}
