package cn.loveapp.print.common.utils;

import com.alibaba.druid.util.StringUtils;

import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 集合工具类
 *
 * <AUTHOR>
 * @Date 2024/7/25 8:46 PM
 */
public class ListUtil {

    public static final String DELIMITER_COMMA = ",";


    public static <T> void addListIfNotNull(List<T> target, T source) {
        if (target == null || source == null) {
            return;
        }
        target.add(source);
    }

    public static <T> void addListIfNotNull(List<T> target, List<T> source) {
        if (target == null || source == null) {
            return;
        }
        target.addAll(source);
    }

    /**
     * 通用类型转换方法
     *
     * @param input     输入字符串
     * @param converter 类型转换函数（需自行处理异常返回null）
     * @param <T>       目标类型
     * @return 转换后的非空集合或null
     */
    public static <T> List<T> convertWithComma(String input, Function<String, T> converter) {
        return convertWithComma(input, DELIMITER_COMMA, converter);
    }


    /**
     * 通用类型转换方法
     *
     * @param input     输入字符串
     * @param delimiter 自定义分隔符
     * @param converter 类型转换函数（需自行处理异常返回null）
     * @param <T>       目标类型
     * @return 转换后的非空集合或null
     */
    public static <T> List<T> convertWithComma(String input, String delimiter, Function<String, T> converter) {
        if (StringUtils.isEmpty(input)) {
            return null;
        }

        List<T> result = Arrays.stream(input.split(delimiter))
                .map(String::trim)
                .map(s -> {
                    try {
                        return converter.apply(s);
                    } catch (Exception e) {
                        return null;
                    }
                })
                .filter(Objects::nonNull)
                .collect(Collectors.toList());

        return result.isEmpty() ? null : result;
    }

}
