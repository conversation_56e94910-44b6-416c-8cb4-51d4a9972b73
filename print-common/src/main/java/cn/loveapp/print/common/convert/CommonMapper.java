package cn.loveapp.print.common.convert;

import cn.loveapp.print.common.entity.AyElefaceOperatelog;
import cn.loveapp.print.common.entity.AyPrintLogSearchEs;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * Mapper映射工具接口
 *
 * <AUTHOR>
 * @Date 2023/11/8 3:31 PM
 */
@Mapper
public interface CommonMapper {

    CommonMapper INSTANCE = Mappers.getMapper(CommonMapper.class);

    /**
     * 复制打印es对象
     *
     * @param operateLog
     * @return
     */
    AyPrintLogSearchEs copyAyPrintLogSearchEs(AyPrintLogSearchEs operateLog);

    AyPrintLogSearchEs.ElefaceOperateLog copyElefaceOperateLog(AyPrintLogSearchEs.ElefaceOperateLog elefaceOperate);

    AyElefaceOperatelog copyElefaceOperateLog(AyElefaceOperatelog ayElefaceOperatelog);
}
