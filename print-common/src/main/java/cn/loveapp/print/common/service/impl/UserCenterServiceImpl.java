package cn.loveapp.print.common.service.impl;

import cn.loveapp.common.web.CommonApiStatus;
import cn.loveapp.print.common.dto.BaseUserInfoDTO;
import cn.loveapp.print.common.dto.TargetSellerInfo;
import cn.loveapp.print.common.dto.response.MultiShopsSessionCheckResponse;
import cn.loveapp.uac.domain.UserSettingDTO;
import cn.loveapp.uac.request.BatchRequest;
import cn.loveapp.uac.request.BatchSettingGetRequest;
import cn.loveapp.uac.response.UserFullInfoResponse;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import cn.loveapp.common.utils.LoggerHelper;
import cn.loveapp.common.web.CommonApiResponse;
import cn.loveapp.print.common.service.UserCenterService;
import cn.loveapp.uac.request.UserInfoRequest;
import cn.loveapp.uac.response.UserInfoResponse;
import cn.loveapp.uac.service.UserCenterInnerApiService;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.alibaba.fastjson.JSON.toJSON;

/**
 * <AUTHOR>
 */
@Service
public class UserCenterServiceImpl implements UserCenterService {
    private final static LoggerHelper LOGGER = LoggerHelper.getLogger(UserCenterServiceImpl.class);

    @Autowired
    private UserCenterInnerApiService userCenterInnerApiService;

    @Override
    public String getTopSession(String platformId, String appName, String sellerNick, String sellerId) {
        UserInfoResponse userInfoResponse = getUserInfoContainTopSession(platformId, appName, sellerNick, sellerId);
        if (userInfoResponse == null) {
            return null;
        }
        return userInfoResponse.getTopSession();
    }

    @Override
    public String getTopSession(String platformId, String appName, String sellerNick) {
        return getTopSession(platformId, appName, sellerNick, null);
    }

    @Override
    public UserInfoResponse getUserInfo(String platformId, String appName, String sellerNick) {
        return getUserInfo(platformId, appName, sellerNick, null);
    }

    @Override
    public UserInfoResponse getUserInfo(String platformId, String appName, String sellerNick, String sellerId) {
        UserInfoRequest request = new UserInfoRequest();
        request.setPlatformId(platformId);
        request.setApp(appName);
        request.setSellerNick(sellerNick);
        request.setSubSellerId(sellerId);
        try {
            CommonApiResponse<UserInfoResponse> response = userCenterInnerApiService.getUserInfo(request);
            return response.getBody();
        } catch (Exception e) {
            return null;
        }
    }

    @Override
    public UserInfoResponse getUserInfoContainTopSession(String platformId, String appName, String sellerNick) {
        return getUserInfoContainTopSession(platformId, appName, sellerNick, null);
    }

    @Override
    public UserInfoResponse getUserInfoContainTopSession(String platformId, String appName, String sellerNick,
        String sellerId) {
        UserInfoRequest request = new UserInfoRequest();
        request.setPlatformId(platformId);
        request.setApp(appName);
        request.setSellerNick(sellerNick);
        request.setSubSellerId(sellerId);
        try {
            CommonApiResponse<UserInfoResponse> response = userCenterInnerApiService.getTopSession(request);
            return response.getBody();
        } catch (Exception e) {
            return null;
        }
    }

    @Override
    public String getSellerId(String platformId, String appName, String sellerNick) {
        UserInfoResponse userInfoResponse = getUserInfo(platformId, appName, sellerNick);
        if (userInfoResponse == null) {
            return null;
        }
        return userInfoResponse.getSellerId();
    }

    @Override
    public List<UserInfoResponse> getUserInfo(List<TargetSellerInfo> targetSellerInfoList) {
        try {
            List<UserInfoRequest> collect = targetSellerInfoList.stream().map(m -> {
                UserInfoRequest request = new UserInfoRequest();
                request.setPlatformId(m.getTargetStoreId());
                request.setApp(m.getTargetAppName());
                request.setSellerNick(m.getTargetNick());
                request.setSellerId(m.getTargetSellerId());
                return request;
            }).collect(Collectors.toList());
            LOGGER.logInfo("调uac获取batchUserInfo, request=" + JSON.toJSONString(targetSellerInfoList));
            CommonApiResponse<List<UserInfoResponse>> response = userCenterInnerApiService.batchGetUserInfo(collect);
            LOGGER.logInfo("调uac获取batchUserInfo, response=" + toJSON(response));
            if (response != null && response.getCode().equals(CommonApiStatus.Success.code()) && null == response.getSubCode()) {
                return response.getBody();
            }
        } catch (Exception e) {
            LOGGER.logError("网络异常，调uac获取batchUserInfo失败:" + e.getMessage(), e);
        }
        return Collections.emptyList();
    }

    @Override
    public UserFullInfoResponse getSellerNickByMallName(String mallName, String storeId, String appName) {
        UserInfoRequest request = new UserInfoRequest();
        request.setPlatformId(storeId);
        request.setApp(appName);
        request.setMallName(mallName);
        try {
            CommonApiResponse<UserFullInfoResponse> response = userCenterInnerApiService.getUserByMallName(request);
            if (response == null || response.getBody() == null) {
                return null;
            }
            return response.getBody();
        } catch (Exception e) {
            return null;
        }
    }

    @Override
    public MultiShopsSessionCheckResponse checkMultiShopsTopSession(List<BaseUserInfoDTO> targetUserInfoList) {

        if (CollectionUtils.isEmpty(targetUserInfoList)) {
            LOGGER.logInfo("多店鉴权, 目标用户列表为空, 鉴权失败");
            return null;
        }

        BatchRequest<UserInfoRequest> batchRequest = new BatchRequest<>();
        List<UserInfoRequest> UserInfoRequestList = Lists.newArrayList();
        for (BaseUserInfoDTO userInfoDTO : targetUserInfoList) {
            UserInfoRequest userInfoRequest = new UserInfoRequest();
            userInfoRequest.setSellerId(userInfoDTO.getSellerId());
            userInfoRequest.setSellerNick(userInfoDTO.getSellerNick());
            userInfoRequest.setPlatformId(userInfoDTO.getStoreId());
            userInfoRequest.setApp(userInfoDTO.getAppName());
            UserInfoRequestList.add(userInfoRequest);
        }
        batchRequest.setRequestList(UserInfoRequestList);

        CommonApiResponse<List<UserInfoResponse>> batchGetTopSessionResponse = null;
        try {
            batchGetTopSessionResponse = userCenterInnerApiService.batchGetTopSession(batchRequest);
        } catch (Exception e) {
            LOGGER.logError("多店鉴权，批量获取目标用户session失败, 鉴权失败: " + e.getMessage(), e);
            return null;
        }

        if (batchGetTopSessionResponse == null || CollectionUtils.isEmpty(batchGetTopSessionResponse.getBody())) {
            LOGGER.logInfo("多店鉴权, 批量获取目标用户session失败, 鉴权失败");
            return null;
        }

        List<UserInfoResponse> batchGetTopSessionBody = batchGetTopSessionResponse.getBody();
        Map<Boolean, List<UserInfoResponse>> sessionInfoGroupMap = batchGetTopSessionBody.stream()
            .collect(Collectors.groupingBy(s -> StringUtils.isNotEmpty(s.getTopSession())));

        List<UserInfoResponse> topSessionEnptyList = sessionInfoGroupMap.get(false);
        if (CollectionUtils.isNotEmpty(topSessionEnptyList)) {
            LOGGER.logInfo("调uac获取batchGetTopSession,授权过期的用户集合：" + JSON.toJSONString(topSessionEnptyList));
        }

        MultiShopsSessionCheckResponse response = new MultiShopsSessionCheckResponse();
        List<BaseUserInfoDTO> targetUserInfos = Lists.newArrayList();
        List<UserInfoResponse> userInfoResponses = sessionInfoGroupMap.get(true);
        for (UserInfoResponse targetUseruserInfoResponse : userInfoResponses) {
            BaseUserInfoDTO targetUserInfo = new BaseUserInfoDTO();
            targetUserInfo.setSellerId(targetUseruserInfoResponse.getSellerId());
            targetUserInfo.setSellerNick(targetUseruserInfoResponse.getSellerNick());
            targetUserInfo.setStoreId(targetUseruserInfoResponse.getPlatformId());
            targetUserInfo.setAppName(targetUseruserInfoResponse.getAppName());
            targetUserInfos.add(targetUserInfo);
        }

        response.setTagerUserInfoList(targetUserInfos);
        return response;
    }

    @Override
    public Map<String, String> getUserSettings(List<String> settings, String sellerId, String sellerNick, String appName, String platformId) {

        if (StringUtils.isAnyEmpty(sellerId, platformId)) {
            return null;
        }

        BatchSettingGetRequest batchSettingGetRequest = new BatchSettingGetRequest();
        batchSettingGetRequest.setApp(appName);
        batchSettingGetRequest.setPlatformId(platformId);
        batchSettingGetRequest.setUserId(sellerId);
        batchSettingGetRequest.setSettings(settings);
        try {
            LOGGER.logInfo("调uac获取batchSettingGet, request=" + JSON.toJSONString(batchSettingGetRequest));
            CommonApiResponse<List<UserSettingDTO>> response = userCenterInnerApiService.batchSettingGet(batchSettingGetRequest);
            LOGGER.logInfo("调uac获取batchSettingGet, response=" + toJSON(response));
            if (response != null && response.getCode().equals(CommonApiStatus.Success.code()) && null == response.getSubCode()) {
                return response.getBody().stream().collect(Collectors.toMap(UserSettingDTO::getKey, UserSettingDTO::getValue));
            }
        } catch (Exception e) {
            LOGGER.logError("网络异常，调uac获取batchSettingGet失败:" + e.getMessage(), e);
        }
        return Collections.emptyMap();
    }
}
