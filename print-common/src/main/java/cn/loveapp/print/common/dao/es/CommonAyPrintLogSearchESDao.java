package cn.loveapp.print.common.dao.es;

import java.io.IOException;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.validation.constraints.NotNull;

import cn.loveapp.common.utils.LoggerHelper;
import cn.loveapp.print.common.constant.EsFields;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.google.common.collect.Lists;
import org.apache.commons.lang3.StringUtils;
import org.elasticsearch.action.bulk.BulkRequest;
import org.elasticsearch.action.index.IndexRequest;
import org.elasticsearch.action.update.UpdateRequest;
import org.elasticsearch.client.RequestOptions;
import org.elasticsearch.client.Requests;
import org.elasticsearch.client.RestHighLevelClient;
import org.elasticsearch.client.core.CountRequest;
import org.elasticsearch.client.core.CountResponse;
import org.elasticsearch.common.xcontent.XContentType;
import org.elasticsearch.index.VersionType;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.reindex.BulkByScrollResponse;
import org.elasticsearch.index.reindex.UpdateByQueryRequest;
import org.elasticsearch.script.Script;
import org.elasticsearch.script.ScriptType;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.springframework.data.elasticsearch.ElasticsearchException;
import org.springframework.data.elasticsearch.core.ElasticsearchOperations;
import org.springframework.data.elasticsearch.core.ResultsMapper;
import org.springframework.data.elasticsearch.core.query.NativeSearchQuery;
import org.springframework.data.elasticsearch.core.query.UpdateQuery;
import org.springframework.data.elasticsearch.core.query.UpdateQueryBuilder;
import org.springframework.stereotype.Repository;

import cn.loveapp.print.common.config.es.ElasticsearchConfiguration;
import cn.loveapp.print.common.entity.AyPrintLogSearchEs;
import org.springframework.util.CollectionUtils;

import static org.elasticsearch.index.query.QueryBuilders.boolQuery;
import static org.elasticsearch.index.query.QueryBuilders.termQuery;

/**
 * <AUTHOR>
 * @date 2024-01-21 15:25
 * @description: 通用爱用打印日志es搜索Dao
 */
@Repository
public class CommonAyPrintLogSearchESDao extends BaseElasticsearchDao<AyPrintLogSearchEs> {

    private static final LoggerHelper LOGGER = LoggerHelper.getLogger(CommonAyPrintLogSearchESDao.class);

    /**
     * es字段-分享备注
     */
    private static final String ES_FIELDS_SHARE_MEMO = "shareMemo";

    /**
     * es字段-分享备注
     */
    private static final String ES_FIELDS_SALESMAN = "salesman";

    private static final String SCRIPT_LANG = "painless";

    /**
     * 面单更新脚本
     */
    private static final String SHARE_UPDATE_ID_OR_CODE = "ctx._source.elefaceOperateLog.shareMemo = params.shareMemo; " +
            "ctx._source.elefaceOperateLog.salesman = params.salesman;";

    /**
     * ES对象字段分割符
     */
    public static final String ES_OBJECT_TYPE_FIELD_SEPARATOR = ".";


    public CommonAyPrintLogSearchESDao(ElasticsearchConfiguration configuration, ElasticsearchOperations operations,
        RestHighLevelClient client, ResultsMapper mapper) {
        super(configuration, operations, client, mapper);
    }

    @Override
    public String getIndexName(AyPrintLogSearchEs entity) {
        return getIndexName();
    }

    @Override
    public String getIndexName() {
        return operations.getPersistentEntityFor(AyPrintLogSearchEs.class).getIndexName();
    }

    @Override
    public String getRouting(AyPrintLogSearchEs entity) {
        return entity.getSellerId();
    }

    protected String getHashRoutingKey(String sellerId) {
        return sellerId;
    }

    /**
     * 打印日志es入库
     *
     * @param ayPrintLogSearchEsList
     * @param isVerifyVersion
     */
    public void saveAll(@NotNull List<AyPrintLogSearchEs> ayPrintLogSearchEsList, boolean isVerifyVersion) {
        BulkRequest bulkRequest = new BulkRequest();
        try {
            Date nowDate = new Date();
            for (AyPrintLogSearchEs ayPrintLogSearchEs : ayPrintLogSearchEsList) {
                if (ayPrintLogSearchEs.getGmtCreate() == null) {
                    ayPrintLogSearchEs.setGmtCreate(nowDate);
                    ayPrintLogSearchEs.setGmtModified(nowDate);
                }

                Date gmtModified = ayPrintLogSearchEs.getGmtModified();
                ayPrintLogSearchEs.initDefault();
                IndexRequest indexRequest = new IndexRequest(getIndexName()).id(ayPrintLogSearchEs.getId())
                    .routing(getRouting(ayPrintLogSearchEs)).source(
                        resultsMapper.getEntityMapper().mapToString(ayPrintLogSearchEs), Requests.INDEX_CONTENT_TYPE);

                // 是否开启版本检验
                if (isVerifyVersion && gmtModified != null) {
                    indexRequest.version(gmtModified.getTime());
                    indexRequest.versionType(VersionType.EXTERNAL);
                }

                bulkRequest.add(indexRequest);
            }

            client.bulk(bulkRequest, RequestOptions.DEFAULT);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }

    }

    /**
     * 批量修改爱用日志
     *
     * @param AyPrintLogSearchEsList
     */
    public void batchUpdateByIdsWithNotNull(@NotNull List<AyPrintLogSearchEs> AyPrintLogSearchEsList) {
        BulkRequest bulkRequest = new BulkRequest();
        try {
            for (AyPrintLogSearchEs ayPrintLogSearchEs : AyPrintLogSearchEsList) {
                UpdateQueryBuilder updateQueryBuilder = new UpdateQueryBuilder();
                updateQueryBuilder.withDoUpsert(false);
                String doc = notNullObjectMapper.writeValueAsString(ayPrintLogSearchEs);
                UpdateRequest updateRequest = new UpdateRequest();
                updateRequest.doc(doc, XContentType.JSON);
                updateQueryBuilder.withUpdateRequest(updateRequest);
                bulkRequest.add(updateRequest);
            }

            client.bulk(bulkRequest, RequestOptions.DEFAULT);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 通过文档id批量更新指定字段
     *
     * @param ayPrintLogSearchESList
     * @param fields
     */
    public void batchUpdateByIdsWithNotNull(List<AyPrintLogSearchEs> ayPrintLogSearchESList,
        List<String> fields) {

        if (CollectionUtils.isEmpty(ayPrintLogSearchESList)) {
            return;
        }

        List<UpdateQuery> queries = Lists.newArrayList();
        try {

            for (AyPrintLogSearchEs ayPrintLogSearchEs : ayPrintLogSearchESList) {
                String docId = ayPrintLogSearchEs.getId();
                if (StringUtils.isEmpty(docId)) {
                    continue;
                }

                UpdateRequest updateRequest = new UpdateRequest(AyPrintLogSearchEs.INDEX_NAME_PREFIX, docId);
                String doc = buildUpdateDoc(ayPrintLogSearchEs, fields);
                updateRequest.doc(doc, XContentType.JSON);
                updateRequest.docAsUpsert(false);
                updateRequest.routing(getRouting(ayPrintLogSearchEs));
                updateRequest.retryOnConflict(3);
                UpdateQuery updateQuery =
                    new UpdateQueryBuilder().withId(docId).withIndexName(AyPrintLogSearchEs.INDEX_NAME_PREFIX)
                        .withDoUpsert(false).withUpdateRequest(updateRequest).build();
                queries.add(updateQuery);
            }

            operations.bulkUpdate(queries);
        } catch (ElasticsearchException e) {
            if (e.getFailedDocuments() != null) {
                LOGGER.logError("Elasticsearch 批量更新失败, 失败数量: " + e.getFailedDocuments().size() + ", 总数量: "
                    + ayPrintLogSearchESList.size(), e);
            }
        } catch (IOException e) {
            throw new org.springframework.data.elasticsearch.ElasticsearchException(
                "Error while deleting item request: " + queries.toString(), e);
        }
    }

    /**
     * 构建修改对象（支持对象字段值修改：xxx.xx）
     *
     * @param entity
     * @param fields
     * @return
     * @throws IOException
     */
    private String buildUpdateDoc(AyPrintLogSearchEs entity, List<String> fields) throws IOException {
        // 初始化默认值
        entity.initDefault();
        // 将对象映射为 Map
        Map<String, Object> fullMap = resultsMapper.getEntityMapper().mapObject(entity);
        ObjectMapper objectMapper = new ObjectMapper();
        // 如果 fields 为空则全量更新
        if (CollectionUtils.isEmpty(fields)) {
            fullMap.remove(EsFields.gmtCreate);
            fullMap.put(EsFields.gmtModified, formatter.format(LocalDateTime.now()));
            return objectMapper.writeValueAsString(fullMap);
        }

        ObjectNode rootNode = objectMapper.createObjectNode();
        for (String fieldPath : fields) {
            Object value = getValueByPath(fullMap, fieldPath);
            if (value != null) {
                applyNestedField(rootNode, fieldPath, objectMapper.valueToTree(value));
            }
        }

        // 设置修改时间
        rootNode.remove(EsFields.gmtCreate);
        rootNode.put(EsFields.gmtModified, formatter.format(LocalDateTime.now()));
        return objectMapper.writeValueAsString(rootNode);
    }

    private Object getValueByPath(Map<String, Object> map, String path) {
        String[] parts = StringUtils.split(path, ES_OBJECT_TYPE_FIELD_SEPARATOR);
        Object current = map;
        for (String part : parts) {
            if (!(current instanceof Map)) {
                return null;
            }
            current = ((Map<?, ?>)current).get(part);
            if (current == null) {
                return null;
            }
        }

        return current;
    }

    private void applyNestedField(ObjectNode root, String path, JsonNode value) {
        String[] parts = StringUtils.split(path, ES_OBJECT_TYPE_FIELD_SEPARATOR);
        ObjectNode currentNode = root;
        for (int i = 0; i < parts.length - 1; i++) {
            String part = parts[i];
            JsonNode node = currentNode.get(part);
            if (node == null || !node.isObject()) {
                node = currentNode.putObject(part);
            }

            currentNode = (ObjectNode)node;
        }

        currentNode.set(parts[parts.length - 1], value);
    }

    /**
     * 打印日志根据条件统计总数
     *
     * @param query
     * @return
     */
    protected long count(NativeSearchQuery query) {
        CountRequest countRequest = new CountRequest(AyPrintLogSearchEs.INDEX_NAME_PREFIX + "*");
        countRequest.routing(query.getRoute());
        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
        searchSourceBuilder.query(query.getQuery());
        countRequest.source(searchSourceBuilder);
        try {
            CountResponse response = client.count(countRequest, RequestOptions.DEFAULT);
            return response.getCount();
        } catch (IOException e) {
            throw new org.springframework.data.elasticsearch.ElasticsearchException(
                    "Error while count for request: " + searchSourceBuilder.toString(), e);
        }
    }


    /**
     * 批量更新面单操作记录的备注和业务员信息
     * @param sellerId
     * @param shareId
     * @param shareMemo
     * @param salesman
     * @param limit
     * @return
     */
    public boolean updateShareMemoAndSalesman(String sellerId, String shareId, String shareMemo, String salesman, int limit) {
        Map<String, Object> params = new HashMap<>();
        params.put(ES_FIELDS_SHARE_MEMO, shareMemo);
        params.put(ES_FIELDS_SALESMAN, salesman);

        Script script = new Script(ScriptType.INLINE, SCRIPT_LANG, SHARE_UPDATE_ID_OR_CODE, params);

        // 创建查询条件
        BoolQueryBuilder query = boolQuery();
        query.must(boolQuery()
                .should(boolQuery().mustNot(termQuery(EsFields.elefaceOperateLogShareMemoKeyWord, shareMemo)))
                .should(boolQuery().mustNot(termQuery(EsFields.elefaceOperateLogSalesmanKeyWord, salesman))));
        query.must(termQuery(EsFields.shareId, shareId));
        query.must(termQuery(EsFields.sellerId, sellerId));
        long count = batchUpdateByScriptAndQuery(sellerId, script, query, limit);
        return count > 0;
    }

    protected long batchUpdateByScriptAndQuery(String routing, Script script, BoolQueryBuilder query, int size) {
        UpdateByQueryRequest request = new UpdateByQueryRequest(AyPrintLogSearchEs.INDEX_NAME_PREFIX + "*");
        request.setRouting(routing);
        request.setScript(script);
        request.setQuery(query);
        request.setBatchSize(size);
        // 执行更新
        try {
            BulkByScrollResponse response = client.updateByQuery(request, RequestOptions.DEFAULT);
            return response.getUpdated();
        } catch (IOException e) {
            throw new org.springframework.data.elasticsearch.ElasticsearchException(
                    "Error while count for request: " + request.toString(), e);
        }
    }
}
