package cn.loveapp.print.common.utils;

import java.util.Objects;

import org.apache.commons.lang3.StringUtils;

/***
 * <AUTHOR>
 * @Description sharding 工具类
 * @Date 14:57 2023/9/1
 **/
public class ShardingUtils {

    private static final String TABLE_SEPARATOR = "_";

    public static final String DATABASE_DS_0 = "ds_0";
    private static final String DATABASE_DS_1 = "ds_1";

    private static final Integer TABLE_COUNT = 100;

    private static final Integer DATABASE_COUNT = 2;

    /**
     * 获取分表后的实际表名
     *
     * @param sellerId
     *            分表键
     * @param originTableName
     *            原始表名（未分表前的表名）
     * @return
     * @throws Exception
     */
    public static String getShardingTableName(String sellerId, String originTableName) {

        if (StringUtils.isEmpty(sellerId)) {
            throw new IllegalArgumentException("分表字段为空");
        }

        return originTableName + TABLE_SEPARATOR + getShardingTableSuffix(sellerId);
    }

    /**
     * 获取数据库后缀
     *
     * @param sellerId
     * @return
     */
    public static Integer getShardingTableSuffix(String sellerId) {
        String shardingStr = sellerId;
        if (!StringUtils.isNumeric(sellerId)) {
            // 如果 sellerId 不是纯数字 取hashCode 来分表
            shardingStr = generateSharingValueByHashCode(sellerId);
        }
        return Integer
            .valueOf(shardingStr.substring(shardingStr.length() - (String.valueOf(TABLE_COUNT).length() - 1)));
    }

    /**
     * 获取数据源名
     *
     * @param sellerId
     * @return
     */
    public static String getShardingDatabaseName(String sellerId) {
        Integer shardingValue = getShardingTableSuffix(sellerId);

        if (shardingValue < 0 || shardingValue > TABLE_COUNT) {
            throw new IllegalArgumentException("分表结果超出预定范围");
        }

        if (shardingValue < (TABLE_COUNT / DATABASE_COUNT)) {
            return DATABASE_DS_0;
        } else {
            return DATABASE_DS_1;
        }
    }

    private static String generateSharingValueByHashCode(String sellerId) {
        return String.valueOf(10000 + Math.abs(Objects.hashCode(sellerId)));
    }

    public static Long addLongs(Long a, Long b) {
        return (a == null ? 0L : a) + (b == null ? 0L : b);
    }
}
