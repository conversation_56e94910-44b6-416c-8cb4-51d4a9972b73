package cn.loveapp.print.common.controller;

import java.sql.Connection;
import java.sql.Statement;
import java.util.List;
import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;

import javax.sql.DataSource;

import org.apache.shardingsphere.shardingjdbc.jdbc.core.datasource.ShardingDataSource;
import org.springframework.beans.factory.ObjectProvider;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.web.context.WebServerInitializedEvent;
import org.springframework.context.ApplicationListener;
import org.springframework.data.redis.connection.RedisConnectionCommands;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RequestMapping;

import com.google.common.collect.Lists;
import com.google.common.util.concurrent.ThreadFactoryBuilder;

import cn.loveapp.common.utils.LoggerHelper;
import cn.loveapp.common.utils.NetworkUtil;
import cn.loveapp.print.common.web.WarmUpConfiguration;
import cn.loveapp.print.common.web.WarnUpRequestData;

/**
 * 探活Controller
 *
 * <AUTHOR>
 * @date 2019-04-16
 */
public abstract class BaseCloudNativeController implements ApplicationListener<WebServerInitializedEvent> {
    private static final LoggerHelper LOGGER = LoggerHelper.getLogger(BaseCloudNativeController.class);
    public static final String PONG = "PONG";

    @Value("${print.service.probe.always-ok:false}")
    private boolean alwaysOk = false;

    @Autowired
    private StringRedisTemplate stringRedisTemplate;

    @Autowired
    private WarmUpConfiguration warmUpConfiguration;

    /**
     * 是否首次请求Readness
     */
    private volatile boolean isFristRequestReadness = true;

    /**
     * 是否预热结束
     */
    private volatile boolean hasWarnUp;

    private volatile AtomicInteger warmUpProcess = new AtomicInteger();

    private int serverPort;

    protected ThreadPoolExecutor executor = null;

    protected List<DataSource> dataSources = Lists.newArrayList();

    public BaseCloudNativeController(ObjectProvider<List<DataSource>> provider) {
        List<DataSource> list = provider.getIfAvailable();
        if (list == null) {
            return;
        }
        for (DataSource dataSource : list) {
            if (!(dataSource instanceof ShardingDataSource)) {
                dataSources.add(dataSource);
            }
        }
        executor = new ThreadPoolExecutor(50, 50, 5L, TimeUnit.SECONDS, new ArrayBlockingQueue<>(100),
            new ThreadFactoryBuilder().setNameFormat("warnUp-pool-%d").build());
        executor.allowCoreThreadTimeOut(true);
    }

    @RequestMapping("/liveness")
    public ResponseEntity liveness() {
        if (alwaysOk) {
            return ResponseEntity.status(HttpStatus.OK).build();
        }
        return ResponseEntity.status(checkLiveness()).build();
    }

    @RequestMapping("/readness")
    public ResponseEntity readness() {
        if (isFristRequestReadness) {
            warnUp();
        }
        if (!hasWarnUp && warmUpConfiguration.isEnable()) {
            checkReadNess();
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
        if (alwaysOk) {
            return ResponseEntity.status(HttpStatus.OK).build();
        }
        return ResponseEntity.status(checkReadNess()).build();
    }

    /**
     * 检查liveness
     *
     * @return
     */
    protected HttpStatus checkLiveness() {
        return HttpStatus.OK;
    }

    /**
     * 检查readness
     *
     * @return
     */
    protected HttpStatus checkReadNess() {
        try {
            if (!PONG.equalsIgnoreCase(stringRedisTemplate.execute(RedisConnectionCommands::ping, true))) {
                LOGGER.logError("readness异常: redis响应异常");
                return HttpStatus.INTERNAL_SERVER_ERROR;
            }
            for (DataSource dataSource : dataSources) {
                try (Connection connection = dataSource.getConnection()) {
                    try (Statement statement = connection.createStatement()) {
                        statement.executeQuery("SELECT 1").close();
                    }
                }
            }
        } catch (Exception e) {
            LOGGER.logError("readness异常: " + e.getMessage(), e);
            return HttpStatus.INTERNAL_SERVER_ERROR;
        }
        return HttpStatus.OK;
    }

    @Override
    public void onApplicationEvent(WebServerInitializedEvent event) {
        if (event.getApplicationContext().getParent() == null) {
            // 获取端口号
            serverPort = event.getWebServer().getPort();
        }
    }

    /**
     * 预热各个controller
     */
    protected synchronized void warnUp() {
        if (isFristRequestReadness && warmUpConfiguration.isEnable()) {
            isFristRequestReadness = false;
            try {
                LOGGER.logInfo("开始预热");
                List<WarnUpRequestData> requestDatas = warmUpConfiguration.getDatas();
                if (requestDatas == null || requestDatas.isEmpty()) {
                    hasWarnUp = true;
                    LOGGER.logInfo("预热结束");
                    return;
                }
                changeWarmUpRequestData(requestDatas);
                int warnUpCount = warmUpConfiguration.getEachUrlRequestCount();
                String host = "http://localhost:" + serverPort + "/";

                warmUpProcess.set(requestDatas.size() * warnUpCount);
                for (WarnUpRequestData requestData : requestDatas) {
                    if (requestData == null) {
                        continue;
                    }
                    executor.execute(() -> {
                        for (int i = 0; i < warnUpCount; i++) {
                            try {
                                String url = host + requestData.getPath();
                                try {
                                    NetworkUtil.http(url, requestData.getPostParams(), requestData.isPost(), null, null,
                                        false, false, null);
                                } catch (Exception e) {
                                    LOGGER.logError("预热失败, url=" + url + ", error: " + e.getMessage(), e);
                                }
                            } finally {
                                if (warmUpProcess.decrementAndGet() <= 0) {
                                    try {
                                        executor.shutdown();
                                    } catch (Exception e) {
                                    }
                                    LOGGER.logInfo("预热结束");
                                    hasWarnUp = true;
                                }
                            }
                        }
                    });
                }
            } catch (Exception e) {
                LOGGER.logError("启动预热失败" + e.getMessage(), e);
                hasWarnUp = true;
            }
        }
    }

    /**
     * 修改预热数据
     *
     * @param requestDatas
     */
    protected void changeWarmUpRequestData(List<WarnUpRequestData> requestDatas) {}

}
