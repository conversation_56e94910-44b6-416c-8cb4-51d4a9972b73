package cn.loveapp.print.common.config.db;

import java.util.Collection;

import org.apache.shardingsphere.api.sharding.standard.PreciseShardingAlgorithm;
import org.apache.shardingsphere.api.sharding.standard.PreciseShardingValue;

import cn.loveapp.common.utils.LoggerHelper;
import cn.loveapp.print.common.utils.ShardingUtils;

/**
 * <AUTHOR>
 * @date 2024-05-06 16:47
 * @description: Sharding分库策略配置
 */
public class DatabaseShardingAlgorithm implements PreciseShardingAlgorithm<String> {

    private static final LoggerHelper LOGGER = LoggerHelper.getLogger(PreciseShardingAlgorithm.class);

    @Override
    public String doSharding(Collection<String> collection, PreciseShardingValue<String> preciseShardingValue) {
        String shardingDatabaseName = ShardingUtils.getShardingDatabaseName(preciseShardingValue.getValue());
        LOGGER.logInfo("-", "所使用的数据源", shardingDatabaseName);
        return shardingDatabaseName;
    }
}
