package cn.loveapp.print.common.config.db;

import java.util.Collection;

import org.apache.shardingsphere.api.sharding.standard.PreciseShardingAlgorithm;
import org.apache.shardingsphere.api.sharding.standard.PreciseShardingValue;

import cn.loveapp.common.utils.LoggerHelper;
import cn.loveapp.print.common.utils.ShardingUtils;

/**
 * 分表算法
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @create 2019-09-15 下午7:24
 */
public class TableShardingAlgorithm implements PreciseShardingAlgorithm<String> {
    private static final LoggerHelper LOGGER = LoggerHelper.getLogger(TableShardingAlgorithm.class);

    @Override
    public String doSharding(Collection<String> availableTargetNames, PreciseShardingValue<String> shardingValue) {
        String tableName =
            ShardingUtils.getShardingTableName(shardingValue.getValue(), shardingValue.getLogicTableName());
        LOGGER.logInfo("-", "所使用的表", tableName);
        return tableName;
    }
}
