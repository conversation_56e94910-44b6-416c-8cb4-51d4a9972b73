package cn.loveapp.print.common.rocketmq.producer;

import cn.loveapp.common.utils.LoggerHelper;
import cn.loveapp.print.common.rocketmq.RocketMQCommonConfig;
import org.apache.rocketmq.client.producer.DefaultMQProducer;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.CommandLineRunner;
import org.springframework.context.ApplicationListener;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.event.ContextClosedEvent;
import org.springframework.core.Ordered;


/**
 * @program: orders-services-group
 * @description: OrdersDefaultProducerConfig
 * @author: Jason
 * @create: 2018-12-07 17:51
 **/
@Configuration
public class OrdersDefaultProducerConfig {
	private static final LoggerHelper LOGGER = LoggerHelper.getLogger(OrdersDefaultProducerConfig.class);

	@Autowired
	private RocketMQCommonConfig rocketMQCommonConfig;

	@Autowired
	private RocketMQDefaultProducerConfig rocketMQDefaultProducerConfig;

	private DefaultMQProducer producer = null;

	@Bean(destroyMethod = "", name = "orderDefaultProducer")
	public DefaultMQProducer orderDefaultProducer(){
		//启动ONS消息队列
		try{
			producer = new DefaultMQProducer(rocketMQDefaultProducerConfig.getProducerId());
			producer.setSendMsgTimeout(5000);
			producer.setNamesrvAddr(rocketMQCommonConfig.getNamesrvAddr());
		}catch(Exception e){
			LOGGER.logError("create ordersDefaultProducer failed", e);
		}
		return producer;
	}



	@Bean(name = "orderDefaultProducerLifeCycleManager")
	public OnsLifeCycleManager onsLifeCycleManager(){
		return new OnsLifeCycleManager();
	}

	/**
	 * Ons 生命周期管理
	 *
	 * <AUTHOR>
	 * @date 2018/11/9
	 */
	public static class OnsLifeCycleManager implements CommandLineRunner, ApplicationListener<ContextClosedEvent>,
		Ordered {
		private static final LoggerHelper LOGGER = LoggerHelper.getLogger(OnsLifeCycleManager.class);
		@Autowired(required = false)
		@Qualifier("orderDefaultProducer")
		private DefaultMQProducer orderDefaultProducer;



		@Override
		public void run(String... args) throws Exception {
			//启动订单ONS生产者
			if (orderDefaultProducer != null) {
				orderDefaultProducer.start();
				LOGGER.logInfo("ordersDefaultProducer startted");
			}
		}

		@Override
		public void onApplicationEvent(ContextClosedEvent event) {
			if(event.getApplicationContext() !=null && event.getApplicationContext().getParent() != null){
				return;
			}
			if(orderDefaultProducer != null){
				LOGGER.logInfo("正在关闭ordersDefaultProducer...");
				try {
					orderDefaultProducer.shutdown();
				} catch (IllegalStateException e) {
				} catch (Exception e) {
					LOGGER.logError(e.getMessage(), e);
				}
				LOGGER.logInfo("ordersDefaultProducer已关闭");
			}

		}

		@Override
		public int getOrder() {
			return Ordered.LOWEST_PRECEDENCE;
		}
	}
}
