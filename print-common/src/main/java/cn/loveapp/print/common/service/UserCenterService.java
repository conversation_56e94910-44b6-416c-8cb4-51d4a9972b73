package cn.loveapp.print.common.service;

import cn.loveapp.print.common.dto.BaseUserInfoDTO;
import cn.loveapp.print.common.dto.TargetSellerInfo;
import cn.loveapp.print.common.dto.response.MultiShopsSessionCheckResponse;
import cn.loveapp.uac.response.UserFullInfoResponse;
import cn.loveapp.uac.response.UserInfoResponse;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
public interface UserCenterService {
    /**
     * 获取用户的topSession
     *
     * @param platformId
     * @param appName
     * @param sellerNick
     * @param sellerId
     * @return
     */
    String getTopSession(String platformId, String appName, String sellerNick, String sellerId);

    /**
     * 获取用户的topsession
     *
     * @param platformId
     * @param appName
     * @param sellerNick
     * @return
     */
    String getTopSession(String platformId, String appName, String sellerNick);

    /**
     * 获取用户的sellerId
     *
     * @param platformId
     * @param appName
     * @param sellerNick
     * @return
     */
    String getSellerId(String platformId, String appName, String sellerNick);

    /**
     * 获取用户信息
     *
     * @param platformId
     * @param appName
     * @param sellerNick
     * @return
     */
    UserInfoResponse getUserInfo(String platformId, String appName, String sellerNick);

    /**
     * 获取用户信息
     *
     * @param platformId
     * @param appName
     * @param sellerNick
     * @param sellerId
     * @return
     */
    UserInfoResponse getUserInfo(String platformId, String appName, String sellerNick, String sellerId);

    /**
     * 获取包含topSession的用户信息
     *
     * @param platformId
     * @param appName
     * @param sellerNick
     * @return
     */
    UserInfoResponse getUserInfoContainTopSession(String platformId, String appName, String sellerNick);

    /**
     * 获取包含topSession的用户信息
     *
     * @param platformId
     * @param appName
     * @param sellerNick
     * @param sellerId
     * @return
     */
    UserInfoResponse getUserInfoContainTopSession(String platformId, String appName, String sellerNick,
        String sellerId);

    /**
     * 获取用户信息
     *
     * @param targetSellerInfoList
     * @return
     */
    List<UserInfoResponse> getUserInfo(List<TargetSellerInfo> targetSellerInfoList);


    /**
     * 获取用户信息
     *
     * @param mallName
     * @param storeId
     * @param appName
     * @return
     */
    UserFullInfoResponse getSellerNickByMallName(String mallName, String storeId, String appName);

    /**
     * 多店铺鉴权
     *
     * @param targetUserInfoList
     */
    MultiShopsSessionCheckResponse checkMultiShopsTopSession(List<BaseUserInfoDTO> targetUserInfoList);

    /**
     * 获取用户配置信息
     * @param settings
     * @param sellerId
     * @param sellerNick
     * @param appName
     * @param platformId
     * @return
     */
    Map<String, String> getUserSettings(List<String> settings, String sellerId, String sellerNick, String appName, String platformId);

}
