package cn.loveapp.print.common.constant;

import java.util.Arrays;
import java.util.List;

import cn.loveapp.common.constant.CommonPlatformConstants;

/**
 * 面单服务商 常量
 *
 * <AUTHOR>
 */
public class ElefaceProviderConstant {
    /**
     * 菜鸟
     */
    public static final String CAINIAO = "CN";

    /**
     * 拼多多
     */
    public static final String PDD = "PDD";

    /**
     * 抖店
     */
    public static final String DOUDIAN = "DOUDIAN";

    /**
     * 快手
     */
    public static final String KWAISHOP = "KWAISHOP";

    /**
     * 小红书
     */
    public static final String XHS = "XHS";

    /**
     * 微信视频号
     */
    public static final String WXVIDEOSHOP = "WXVIDEOSHOP";

    /**
     * 京东
     */
    public static final String JD = "JD";

    /**
     * 获取已启用的平台列表
     *
     * @return
     */
    public static List<String> getEnabledPlatforms() {
        return Arrays.asList(CommonPlatformConstants.PLATFORM_TAO, CommonPlatformConstants.PLATFORM_PDD,
                CommonPlatformConstants.PLATFORM_DOUDIAN, CommonPlatformConstants.PLATFORM_KWAISHOP,
                CommonPlatformConstants.PLATFORM_XHS, CommonPlatformConstants.PLATFORM_WXVIDEOSHOP,
                CommonPlatformConstants.PLATFORM_JD);
    }

    /**
     * 根据平台获取面单服务商
     *
     * @param platform
     * @return
     */
    public static String getProvider(String platform) {
        if (CommonPlatformConstants.PLATFORM_TAO.equals(platform)) {
            return CAINIAO;
        } else if (CommonPlatformConstants.PLATFORM_PDD.equals(platform)) {
            return PDD;
        } else if (CommonPlatformConstants.PLATFORM_DOUDIAN.equals(platform)) {
            return DOUDIAN;
        } else if (CommonPlatformConstants.PLATFORM_KWAISHOP.equals(platform)) {
            return KWAISHOP;
        } else if (CommonPlatformConstants.PLATFORM_XHS.equals(platform)) {
            return XHS;
        }else if (CommonPlatformConstants.PLATFORM_WXVIDEOSHOP.equals(platform)){
            return WXVIDEOSHOP;
        }else if (CommonPlatformConstants.PLATFORM_JD.equals(platform)) {
            return JD;
        }
        return null;
    }
}
