package cn.loveapp.print.common.entity;

import java.time.LocalDateTime;

import cn.loveapp.print.common.dto.RecipientDTO;
import lombok.Data;

/**
 * 发货单打印日志
 *
 * <AUTHOR>
 */
@Data
public class AyDeliverPrintlog {
    /**
     * 主键自增
     */
    private Long id;

    /**
     * 插件名称
     */
    private String appName;

    /**
     * 商家Id
     */
    private String sellerId;

    /**
     * 平台 TAO、PDD、JD、1688
     */
    private String storeId;

    /**
     * 商家Nick
     */
    private String sellerNick;

    /**
     * 打印机
     */
    private String printer;

    /**
     * 打印数量
     */
    private Integer printCount;

    /**
     * 操作用户
     */
    private String operator;

    /**
     * 操作用户平台 TAO、PDD、JD、1688
     */
    private String operatorStoreId;

    /**
     * 操作终端
     */
    private String operateTerminal;

    /**
     * 打印时间
     */
    private LocalDateTime printTime;

    /**
     * 收件人手机号
     */
    private String receiverMobile;

    /**
     * 收件人姓名
     */
    private String receiverName;

    /**
     * 收件人固话
     */
    private String receiverPhone;

    /**
     * 收件人城市
     */
    private String receiverCity;

    /**
     * 收件人详细地址
     */
    private String receiverDetail;

    /**
     * 收件人区
     */
    private String receiverDistrict;

    /**
     * 收件人省
     */
    private String receiverProvince;

    /**
     * 收件人街道
     */
    private String receiverTown;

    /**
     * 收件人邮编
     */
    private String receiverZip;

    /**
     * 打印模板数据
     */
    private String printModule;

    /**
     * 打印内容
     */
    private String printData;

    /**
     * 常规-记录创建时间
     */
    private LocalDateTime gmtCreate;

    /**
     * 常规-记录最后修改时间
     */
    private LocalDateTime gmtModified;

    public void saveRecipient(RecipientDTO recipientDTO) {
        this.receiverName = recipientDTO.getName();
        this.receiverCity = recipientDTO.getCity();
        this.receiverDetail = recipientDTO.getDetailAddress();
        this.receiverDistrict = recipientDTO.getDistrict();
        this.receiverMobile = recipientDTO.getMobile();
        this.receiverPhone = recipientDTO.getPhone();
        this.receiverProvince = recipientDTO.getProvince();
        this.receiverTown = recipientDTO.getTown();
        this.receiverZip = recipientDTO.getZipCode();
    }
}
