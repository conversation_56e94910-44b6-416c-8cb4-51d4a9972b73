package cn.loveapp.print.common.entity;

import java.time.LocalDateTime;
import java.util.List;

import cn.loveapp.print.common.dto.RecipientDTO;
import org.springframework.util.CollectionUtils;

import cn.loveapp.print.common.constant.PrintTypeConstant;
import lombok.Data;

/**
 * 订单打印日志
 *
 * <AUTHOR>
 */
@Data
public class AyPrintlog {
    /**
     * 主键自增
     */
    private Long id;

    /**
     * 插件名称
     */
    private String appName;

    /**
     * 商家id
     */
    private String sellerId;

    /**
     * 平台 TAO、PDD、JD、1688
     */
    private String storeId;

    /**
     * 商家Nick
     */
    private String sellerNick;

    /**
     * 订单tid
     */
    private String tid;

    /**
     * 订单类型：0-普通订单 1-自由打印订单
     */
    private Integer tradeType;

    /**
     * 打印的合单主单的tid
     */
    private String mergeTid;

    /**
     * 打印流水号
     */
    private String serial;

    /**
     * 买家Nick
     */
    private String buyerNick;

    /**
     * 是否为拆单打印
     */
    private Boolean isSplit;

    /**
     * 拆单打印的子单
     */
    private String oids;

    /**
     * 打印类型 express-快递单 eleface-电子面单 deliver-发货单
     */
    private String printType;

    /**
     * 关联打打印日志的id
     */
    private Long printId;

    /**
     * 打印机
     */
    private String printer;

    /**
     * 打印数量
     */
    private Integer printCount;

    /**
     * 操作用户
     */
    private String operator;

    /**
     * 操作用户平台 TAO、PDD、JD、1688
     */
    private String operatorStoreId;

    /**
     * 操作端
     */
    private String operateTerminal;

    /**
     * 打印时间
     */
    private LocalDateTime printTime;

    /**
     * 物流公司Code
     */
    private String cpCode;

    /**
     * 物流公司
     */
    private String logisticsCompany;

    /**
     * 面单号
     */
    private String waybillCode;

    /**
     * 是否是字母单
     */
    private Boolean isChild;

    /**
     * 子单面单号
     */
    private String childWaybillCode;

    /**
     * 面单服务商 {@link cn.loveapp.print.common.constant.ElefaceProviderConstant}
     */
    private String elefaceProvider;

    /**
     * 面单是否被回收
     */
    private Boolean waybillIsCancel;

    /**
     * 收件人手机号
     */
    private String receiverMobile;

    /**
     * 收件人姓名
     */
    private String receiverName;

    /**
     * 收件人固定电话
     */
    private String receiverPhone;

    /**
     * 收件人城市
     */
    private String receiverCity;

    /**
     * 收件人详细地址
     */
    private String receiverDetail;

    /**
     * 收件人区
     */
    private String receiverDistrict;

    /**
     * 收件人省
     */
    private String receiverProvince;

    /**
     * 收件人街道
     */
    private String receiverTown;

    /**
     * 收件人邮编
     */
    private String receiverZip;

    /**
     * 发货人手机号
     */
    private String senderMobile;

    /**
     * 发货人姓名
     */
    private String senderName;

    /**
     * 发货人固话
     */
    private String senderPhone;

    /**
     * 发货人城市
     */
    private String senderCity;

    /**
     * 发货人详细地址
     */
    private String senderDetail;

    /**
     * 发货人区
     */
    private String senderDistrict;

    /**
     * 发货人省
     */
    private String senderProvince;

    /**
     * 发货人街道
     */
    private String senderTown;

    /**
     * 发货人邮编
     */
    private String senderZip;

    /**
     * 批次号
     */
    private String batchId;

    /**
     * 当前批次序号(存在多个用逗号隔开： 1，2，3)
     */
    private String numbersInBatch;

    /**
     * 当前批次总数
     */
    private Integer batchTotals;

    /**
     * 常规-记录创建时间
     */
    private LocalDateTime gmtCreate;

    /**
     * 常规-记录最后修改时间
     */
    private LocalDateTime gmtModified;

    /**
     * 物流模板名
     */
    private String logisticsTemplateName;

    /**
     * 爱用分销单信息 JSON
     * {@link cn.loveapp.print.common.entity.AyDistributeInfo}
     */
    private String ayDistribute;

    public void saveOids(List<String> oids) {
        if (!CollectionUtils.isEmpty(oids)) {
            this.oids = String.join(",", oids);
        }
    }

    /**
     * 保存打印信息
     *
     * @param ayElefacePrintlog
     *            面单打印信息
     */
    public void savePrintInfo(AyElefacePrintlog ayElefacePrintlog) {
        this.printId = ayElefacePrintlog.getId();
        this.printType = PrintTypeConstant.ELEFACE;
        this.printer = ayElefacePrintlog.getPrinter();
        this.printCount = ayElefacePrintlog.getPrintCount();
        this.operator = ayElefacePrintlog.getOperator();
        this.operatorStoreId = ayElefacePrintlog.getOperatorStoreId();
        this.operateTerminal = ayElefacePrintlog.getOperateTerminal();
        this.printTime = ayElefacePrintlog.getPrintTime();
        this.elefaceProvider = ayElefacePrintlog.getProvider();
        this.cpCode = ayElefacePrintlog.getCpCode();
        this.logisticsCompany = ayElefacePrintlog.getLogisticsCompany();
        this.waybillCode = ayElefacePrintlog.getWaybillCode();
        this.isChild = ayElefacePrintlog.getIsChild();
        this.childWaybillCode = ayElefacePrintlog.getChildWaybillCode();
        this.receiverMobile = ayElefacePrintlog.getReceiverMobile();
        this.receiverName = ayElefacePrintlog.getReceiverName();
        this.receiverPhone = ayElefacePrintlog.getReceiverPhone();
        this.receiverCity = ayElefacePrintlog.getReceiverCity();
        this.receiverDetail = ayElefacePrintlog.getReceiverDetail();
        this.receiverDistrict = ayElefacePrintlog.getReceiverDistrict();
        this.receiverProvince = ayElefacePrintlog.getReceiverProvince();
        this.receiverTown = ayElefacePrintlog.getReceiverTown();
        this.receiverZip = ayElefacePrintlog.getReceiverZip();
    }

    /**
     * 保存打印信息
     *
     * @param ayExpressPrintlog
     *            快递单打印信息
     */
    public void savePrintInfo(AyExpressPrintlog ayExpressPrintlog) {
        this.printId = ayExpressPrintlog.getId();
        this.printType = PrintTypeConstant.EXPRESS;
        this.printer = ayExpressPrintlog.getPrinter();
        this.printCount = ayExpressPrintlog.getPrintCount();
        this.operator = ayExpressPrintlog.getOperator();
        this.operatorStoreId = ayExpressPrintlog.getOperatorStoreId();
        this.operateTerminal = ayExpressPrintlog.getOperateTerminal();
        this.printTime = ayExpressPrintlog.getPrintTime();
        this.logisticsCompany = ayExpressPrintlog.getLogisticsCompany();
        this.receiverMobile = ayExpressPrintlog.getReceiverMobile();
        this.receiverName = ayExpressPrintlog.getReceiverName();
        this.receiverPhone = ayExpressPrintlog.getReceiverPhone();
        this.receiverCity = ayExpressPrintlog.getReceiverCity();
        this.receiverDetail = ayExpressPrintlog.getReceiverDetail();
        this.receiverDistrict = ayExpressPrintlog.getReceiverDistrict();
        this.receiverProvince = ayExpressPrintlog.getReceiverProvince();
        this.receiverTown = ayExpressPrintlog.getReceiverTown();
        this.receiverZip = ayExpressPrintlog.getReceiverZip();
    }

    public void savePrintInfo(AyDeliverPrintlog ayDeliverPrintlog) {
        this.printId = ayDeliverPrintlog.getId() == null ? -1L : ayDeliverPrintlog.getId();
        this.printType = PrintTypeConstant.DELIVER;
        this.printer = ayDeliverPrintlog.getPrinter();
        this.printCount = ayDeliverPrintlog.getPrintCount();
        this.operator = ayDeliverPrintlog.getOperator();
        this.operatorStoreId = ayDeliverPrintlog.getOperatorStoreId();
        this.operateTerminal = ayDeliverPrintlog.getOperateTerminal();
        this.printTime = ayDeliverPrintlog.getPrintTime();
        this.receiverMobile = ayDeliverPrintlog.getReceiverMobile();
        this.receiverName = ayDeliverPrintlog.getReceiverName();
        this.receiverPhone = ayDeliverPrintlog.getReceiverPhone();
        this.receiverCity = ayDeliverPrintlog.getReceiverCity();
        this.receiverDetail = ayDeliverPrintlog.getReceiverDetail();
        this.receiverDistrict = ayDeliverPrintlog.getReceiverDistrict();
        this.receiverProvince = ayDeliverPrintlog.getReceiverProvince();
        this.receiverTown = ayDeliverPrintlog.getReceiverTown();
        this.receiverZip = ayDeliverPrintlog.getReceiverZip();
    }

    public void saveSendsInfo(RecipientDTO sender) {
        if (sender == null) {
            return;
        }

        // 发件人信息
        this.senderName = sender.getName();
        this.senderMobile = sender.getMobile();
        this.senderPhone = sender.getPhone();
        this.senderCity = sender.getCity();
        this.senderDetail = sender.getDetailAddress();
        this.senderDistrict = sender.getDistrict();
        this.senderProvince = sender.getProvince();
        this.senderTown = sender.getTown();
        this.senderZip = sender.getZipCode();
    }
}
