package cn.loveapp.print.common.rocketmq.producer;

import cn.loveapp.print.common.rocketmq.RocketMQDefaultConfig;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;


/**
 * mq默认生产者配置
 * @program: orders-services-group
 * @description: DefaultMqProducerConfig
 * @author: Jason
 * @create: 2019-11-18 21:30
 **/
@Component
@ConfigurationProperties(prefix = "rocketmq.default.producer.config")
@Data
public class RocketMQDefaultProducerConfig extends RocketMQDefaultConfig {

}
