package cn.loveapp.print.common.web;

import java.util.List;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import com.google.common.collect.Lists;

import lombok.Data;

/**
 * Controller热身配置
 *
 * <AUTHOR>
 * @date 2019-05-17
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "loveapp.web.warm-up")
public class WarmUpConfiguration {
    /**
     * 每个预热url的请求次数
     */
    private int eachUrlRequestCount = 50;

    private boolean enable = true;

    private List<WarnUpRequestData> datas = Lists.newArrayList();

    public int getEachUrlRequestCount() {
        if (eachUrlRequestCount <= 0) {
            eachUrlRequestCount = 10;
        }
        return eachUrlRequestCount;
    }
}
