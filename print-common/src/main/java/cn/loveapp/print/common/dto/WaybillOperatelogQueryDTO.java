package cn.loveapp.print.common.dto;

import java.time.LocalDateTime;
import java.util.List;

import com.google.common.collect.Lists;
import lombok.Data;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

/**
 * 电子面单操作日志查询
 *
 * <AUTHOR>
 */
@Data
public class WaybillOperatelogQueryDTO {

    private Integer offset;

    private Integer limit;

    /**
     * 订单类型
     */
    private Integer tradeType;

    /**
     * 面单是否被取消
     */
    private Boolean isCancel;

    /**
     * 面单服务商
     */
    private String provider;

    /**
     * 物流公司编码
     */
    private List<String> cpCodes;

    /**
     * 订单号
     */
    private String tid;

    /**
     * 订单号列表
     */
    private List<String> tidList;

    /**
     * 运单号
     */
    private String waybillCode;

    /**
     * 运单号列表
     */
    private List<String> waybillCodeList;

    /**
     * 开始时间
     */
    private LocalDateTime startTime;

    /**
     * 结束时间
     */
    private LocalDateTime endTime;

    /**
     * 面单取消开始时间
     */
    private LocalDateTime cancelStartTime;

    /**
     * 面单取消结束时间
     */
    private LocalDateTime cancelEndTime;

    /**
     * 面单所有者商家id
     */
    private String ownerSellerId;

    /**
     * 面单所有者Nick
     */
    private String ownerSellerNick;

    /**
     * 面单所有者 targetId
     */
    private String ownerStoreId;

    /**
     * 面单所有者 appName
     */
    private String ownerAppName;

    /**
     * 电子面单打印数量
     */
    private Integer printCount;

    public void addCpCodes(String cpCode) {
        if (!StringUtils.isEmpty(cpCode)) {
            if (this.cpCodes == null) {
                this.cpCodes = Lists.newArrayList();
            }
            this.cpCodes.add(cpCode);
        }
    }

    public void addCpCodes(List<String> cpCode) {
        if (!CollectionUtils.isEmpty(cpCode)) {
            if (this.cpCodes == null) {
                this.cpCodes = Lists.newArrayList();
            }
            this.cpCodes.addAll(cpCode);
        }
    }
}
