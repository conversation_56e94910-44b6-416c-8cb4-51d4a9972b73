package cn.loveapp.print.common.consumer;

import cn.loveapp.common.mq.AbstractCommonMQBaseConsumer;
import cn.loveapp.common.serialization.MessageDeserializationResult;
import cn.loveapp.common.serialization.SerializationType;
import cn.loveapp.common.utils.LoggerHelper;
import cn.loveapp.print.common.exception.ResendMessageException;
import cn.loveapp.print.common.utils.RocketMqQueueHelper;
import com.google.common.collect.Maps;
import io.micrometer.core.instrument.MeterRegistry;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.rocketmq.client.consumer.listener.ConsumeConcurrentlyStatus;
import org.apache.rocketmq.client.producer.DefaultMQProducer;
import org.apache.rocketmq.common.message.MessageExt;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;

import javax.validation.ValidationException;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2024/9/26 2:50 PM
 */
public abstract class BaseOnsConsumer extends AbstractCommonMQBaseConsumer {
    private static final LoggerHelper LOGGER = LoggerHelper.getLogger(BaseOnsConsumer.class);

    /**
     * 消息重试时是否强制处理标志 true/false，默认false
     */
    public final static String FORCE_HANDLE_FLAG_FIELD = "forceHandleFlag";

    /**
     * 消息业务异常重试次数
     */
    public final static String RESEND_TIMES_FIELD = "resendTimes";

    @Autowired
    private RocketMqQueueHelper rocketMqQueueHelper;

    @Autowired
    private DefaultMQProducer defaultMQProducer;

    public BaseOnsConsumer(MeterRegistry registry, String timerName, String rateLimitKey,
                           Environment environment, boolean enableMultiMessage, boolean enableRateLimit) {
        super(registry, timerName, rateLimitKey, environment, enableMultiMessage, enableRateLimit);
    }

    @Override
    protected ConsumeConcurrentlyStatus handleExecuteException(Exception e, MessageExt message,
                                                               MessageDeserializationResult messageDeserializationResult) {
        if (e instanceof ValidationException) {
            LOGGER.logError(message.getTags(), message.getMsgId(), "消息参数出错", e);
            return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
        } else if (e instanceof ResendMessageException) {
            ResendMessageException resendMessageException = (ResendMessageException) e;
            Map<String, String> properties = message.getProperties();
            String topic = message.getTopic();
            String tag = message.getTags();
            String messageId = message.getMsgId();
            String messageLog = messageDeserializationResult.getMessageLog();
            int reconsumeTimes = 0;
            if (MapUtils.isNotEmpty(properties)) {
                String timesStr = properties.get(RESEND_TIMES_FIELD);
                reconsumeTimes = StringUtils.isNotEmpty(timesStr) ? Integer.valueOf(timesStr) : 0;
            }
            try {
                Map<String, String> newProperties = serializationProperties(message);
                setForceHandleFlag(newProperties, resendMessageException.isForceHandleFlag());
                newProperties.put(RESEND_TIMES_FIELD, String.valueOf(reconsumeTimes + 1));
                String newMsgId = rocketMqQueueHelper.pushBackToQueue(topic, tag, message.getBody(), defaultMQProducer,
                        resendMessageException.getDelayLevel(), newProperties, messageLog);
                LOGGER.logError("-", messageId, "消息重发回队列: " + e.getMessage() + " newMsgId: " + newMsgId, e.getCause());
                return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
            } catch (Exception ex) {
                LOGGER.logError("-", messageId, "重发消息异常: " + ex.getMessage(), ex);
                return ConsumeConcurrentlyStatus.RECONSUME_LATER;
            }
        } else {
            LOGGER.logError(message.getTags(), message.getMsgId(), "消费失败，业务处理程序异常出错: " + e.getMessage(), e);
            return ConsumeConcurrentlyStatus.RECONSUME_LATER;
        }
    }


    protected void setForceHandleFlag(Map<String, String> properties, Boolean forceHandleFlag) {
        setUserProperty(properties, FORCE_HANDLE_FLAG_FIELD, forceHandleFlag.toString());
    }

    protected void setUserProperty(Map<String, String> properties, String key, String value) {
        if (properties == null) {
            properties = Maps.newHashMap();
        }
        properties.put(key, value);
    }

    protected boolean getForceHandleFlag(Map<String, String> properties) {
        String forceHandleFlag = getUserProperty(properties, FORCE_HANDLE_FLAG_FIELD);
        return "true".equals(forceHandleFlag);
    }

    /**
     * 获取当前业务消息重新发送次数
     *
     * @param properties 消息属性
     * @return 重新发送次数
     */
    protected int getResendTimes(Map<String, String> properties) {
        String resendTimes = getUserProperty(properties, RESEND_TIMES_FIELD);
        if (StringUtils.isNotEmpty(resendTimes)) {
            return Integer.valueOf(resendTimes);
        } else {
            return -1;
        }
    }

    protected String getUserProperty(Map<String, String> properties, String key) {
        return properties == null ? null : properties.get(key);
    }

    /**
     * 获取序列化方式存入Map
     *
     * @param message
     * @return
     */
    protected Map<String, String> serializationProperties(MessageExt message) {
        Map<String, String> newProperties = new HashMap<>();
        SerializationType serializationType = RocketMqQueueHelper.getSerializationType(message);
        newProperties.put(RocketMqQueueHelper.SERIALIZATION_TYPE, serializationType.valueToString());
        return newProperties;
    }

}
