package cn.loveapp.print.common.entity;

import com.alibaba.fastjson.JSON;
import lombok.Data;

/**
 * 爱用分销信息实体
 *
 * <AUTHOR>
 * @Date 2024/4/9 9:44 AM
 */
@Data
public class AyDistributeInfo {

    /**
     * 订单号
     */
    private String tid;

    /**
     * 分销订单店铺id
     */
    private String sellerId;

    /**
     * 分销订单店铺nick
     */
    private String sellerNick;

    /**
     * 分销订单店铺应用
     */
    private String appName;

    /**
     * 分销订单店铺平台
     */
    private String storeId;

    /**
     * 分销爱用账号id
     */
    private String distributeSellerId;

    /**
     * 分销爱用账号nick
     */
    private String distributeSellerNick;

    /**
     * 分销爱用账号平台
     */
    private String distributeStoreId;

    /**
     * 分销爱用账号应用
     */
    private String distributeAppName;

    /**
     * 分销来源订单类型
     */
    private Integer sourceTradeType;

    @Override
    public String toString() {
        return JSON.toJSONString(this);
    }
}

