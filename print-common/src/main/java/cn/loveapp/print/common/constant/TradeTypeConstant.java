package cn.loveapp.print.common.constant;

import java.util.Arrays;
import java.util.List;

/**
 * 订单类型常量
 *
 * <AUTHOR>
 */
public class TradeTypeConstant {

    /**
     * 普通订单（平台订单）
     */
    public static final Integer NORMAL_TRADE = 0;

    /**
     * 自由打印订单
     */
    public static final Integer CUSTOM_TRADE = 1;

    /**
     * 分销订单（供应商端的采购单）
     */
    public static final Integer DISTRIBUTE_TRADE = 2;

    /**
     * 爱用手工单
     */
    public static final Integer AY_CUSTOM_TRADE = 3;

    public static List<Integer> getAll() {
        return Arrays.asList(NORMAL_TRADE, CUSTOM_TRADE, DISTRIBUTE_TRADE, AY_CUSTOM_TRADE);
    }
}
