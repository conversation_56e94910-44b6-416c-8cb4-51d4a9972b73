package cn.loveapp.print.common.config.db;

import java.sql.SQLException;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

import javax.sql.DataSource;

import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.apache.shardingsphere.api.config.sharding.ShardingRuleConfiguration;
import org.apache.shardingsphere.api.config.sharding.TableRuleConfiguration;
import org.apache.shardingsphere.api.config.sharding.strategy.StandardShardingStrategyConfiguration;
import org.apache.shardingsphere.shardingjdbc.api.ShardingDataSourceFactory;
import org.springframework.beans.factory.ObjectProvider;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.env.ConfigurableEnvironment;

import com.alibaba.druid.pool.DruidDataSource;

import lombok.Data;

/**
 * 分库分表配置
 *
 * <AUTHOR>
 */

@Configuration
@Data
public class ShardingDataSourceConfig {

    private static final String DS_NODE = "ds_";

    private static final String DB_NAME_PREFIX = "newPrintDataSource";

    @Autowired
    private TableShardingAlgorithm tableShardingAlgorithmConfig;


    private List<String> getAllShareTables() {
        return Arrays.asList("ay_deliver_printlog", "ay_eleface_operatelog", "ay_eleface_printlog",
            "ay_express_printlog", "ay_printlog", "ay_serial_log");
    }

    /**
     * 分表规则
     * @return
     */
    @Bean
    public TableShardingAlgorithm tableShardingAlgorithm() {
        return new TableShardingAlgorithm();
    }

    /**
     * 分库规则
     * @return
     */
    @Bean
    public DatabaseShardingAlgorithm databaseShardingAlgorithm() {
        return new DatabaseShardingAlgorithm();
    }

    @Bean(name = "dynamicPrintDatasource")
    @ConditionalOnMissingBean(name = "dynamicPrintDatasource")
    public DataSource dynamicDatasource(ObjectProvider<Map<String, DruidDataSource>> allDataSourceProvider,
        TableShardingAlgorithm tableShardingAlgorithm, DatabaseShardingAlgorithm databaseShardingAlgorithm)
        throws SQLException {
        // TODO 配置项加载顺序
        List<Pair<String, DataSource>> currentDataSources = getCurrentDataSources(allDataSourceProvider);

        Map<String, DataSource> dataSourceMap;
        if (currentDataSources.size() > 1) {
            dataSourceMap = IntStream.range(0, currentDataSources.size()).boxed()
                .collect(Collectors.toMap(i -> DS_NODE + i, i -> currentDataSources.get(i).getValue()));
        } else {
            // 只有一个库
            dataSourceMap = Collections.singletonMap(DS_NODE + "0", currentDataSources.get(0).getValue());
        }

        ShardingRuleConfiguration shardingRuleConfig = new ShardingRuleConfiguration();
        shardingRuleConfig.getTableRuleConfigs()
            .addAll(getPrintTableRuleConfiguration(tableShardingAlgorithm, databaseShardingAlgorithm));

        // 设置默认库
        shardingRuleConfig.setDefaultDataSourceName(DS_NODE + "0");
        Properties properties = new Properties();
        // 是否打印真实sql
//        if (shardingSqlShowEnable) {
//            properties.put(ShardingPropertiesConstant.SQL_SHOW.getKey(), true);
//        }

        // 获取数据源对象
        DataSource dataSource =
            ShardingDataSourceFactory.createDataSource(dataSourceMap, shardingRuleConfig, properties);
        return dataSource;
    }

    private List<Pair<String, DataSource>>
        getCurrentDataSources(ObjectProvider<Map<String, DruidDataSource>> allDataSourceProvider) {
        Map<String, DruidDataSource> allDataSources = allDataSourceProvider.getIfAvailable();

        List<Pair<String, DataSource>> currentDataSources = new ArrayList<>();

        for (Map.Entry<String, DruidDataSource> entry : allDataSources.entrySet()) {
            String dataSourceName = entry.getKey();
            if (StringUtils.startsWithIgnoreCase(dataSourceName, DB_NAME_PREFIX)) {
                currentDataSources.add(Pair.of(dataSourceName, entry.getValue()));
            }
        }
        return currentDataSources;
    }

    /**
     * 配置分库分表规则
     *
     * @return
     */
    private List<TableRuleConfiguration> getPrintTableRuleConfiguration(TableShardingAlgorithm tableShardingAlgorithm,
        DatabaseShardingAlgorithm databaseShardingAlgorithm) {
        List<TableRuleConfiguration> configurationList = new ArrayList<>();

        for (String tableName : getAllShareTables()) {
            TableRuleConfiguration printTableRuleConfig =
                new TableRuleConfiguration(tableName, "ds_${0..1}." + tableName + "_${0..99}");
            // 分库策略
            printTableRuleConfig.setDatabaseShardingStrategyConfig(
                new StandardShardingStrategyConfiguration("seller_id", databaseShardingAlgorithm));
            // 分表策略
            printTableRuleConfig.setTableShardingStrategyConfig(
                new StandardShardingStrategyConfiguration("seller_id", tableShardingAlgorithm));

            configurationList.add(printTableRuleConfig);
        }
        return configurationList;
    }
}
