package cn.loveapp.print.common.dto;

import javax.validation.constraints.NotEmpty;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 收件人信息
 *
 * <AUTHOR>
 */
@Data
@ApiModel(value = "收件人信息")
public class RecipientDTO {

    /**
     * 收件人姓名
     */
    @ApiModelProperty(value = "收件人姓名", required = true)
    @NotEmpty
    private String name;

    /**
     * 收件人手机号
     */
    @ApiModelProperty(value = "收件人手机号")
    private String mobile;

    /**
     * 收件人固定电话
     */
    @ApiModelProperty(value = "收件人固定电话")
    private String phone;

    /**
     * 收件人城市
     */
    @ApiModelProperty(value = "收件人城市")
    private String city;

    /**
     * 收件人详细地址
     */
    @ApiModelProperty(value = "收件人详细地址", required = true)
    @NotEmpty
    private String detailAddress;

    /**
     * 收件人区
     */
    @ApiModelProperty(value = "收件人区")
    private String district;

    /**
     * 收件人省
     */
    @ApiModelProperty(value = "收件人省", required = true)
    @NotEmpty
    private String province;

    /**
     * 收件人街道
     */
    @ApiModelProperty(value = "收件人街道")
    private String town;

    /**
     * 收件人邮编
     */
    @ApiModelProperty(value = "收件人邮编")
    private String zipCode;
}
