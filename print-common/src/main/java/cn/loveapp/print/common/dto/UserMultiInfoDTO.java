package cn.loveapp.print.common.dto;

import lombok.Data;

import java.util.List;

/**
 * 目标用户信息的DTO 用于最终(多店鉴权后的)存储用户信息
 *
 * <AUTHOR>
 * @date 2020-02-10 21:07:13
 */
@Data
public class UserMultiInfoDTO {
    /**
     * 当前用户Nick
     */
    private String nick;

    /**
     * 当前用户id
     */
    private String sellerId;

    /**
     * 当前用户 storeId
     */
    private String storeId;

    /**
     * 当前用户 appName
     */
    private String appName;

    /**
     * 目标商家信息,淘宝用户appName为null
     */
    private List<TargetSellerInfo> targetSellerList;

}
