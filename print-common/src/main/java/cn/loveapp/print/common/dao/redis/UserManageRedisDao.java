package cn.loveapp.print.common.dao.redis;

import java.net.URLEncoder;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.redis.core.HashOperations;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

import cn.loveapp.common.utils.LoggerHelper;

/**
 * @program: orders-services-group
 * @description: UserManageRedisDao
 * @author: Jason
 * @create: 2018-12-26 21:06
 **/
@Component
public class UserManageRedisDao {
    private static final LoggerHelper LOGGER = LoggerHelper.getLogger(UserManageRedisDao.class);
    private static final String VIPFLAG_KEY = "vipflag";
    private static final String SELLER_ID_KEY = "taobao_user_id";

    @Autowired
    @Qualifier("stringRedisTemplate")
    private StringRedisTemplate stringRedisTemplate;

    protected String initKey(String sellerNick) {
        try {
            return URLEncoder.encode(sellerNick, "utf-8");
        } catch (Exception e) {
            LOGGER.logError(sellerNick, "-", "初始化Key失败", e);
            return null;
        }
    }

    /**
     * 从redis中获取vipflag
     *
     * @param sellerNick
     * @return
     */
    public Integer getVipflag(String sellerNick) {
        String key = initKey(sellerNick);
        if (!StringUtils.isEmpty(key)) {
            HashOperations<String, String, String> op = stringRedisTemplate.opsForHash();
            String vipflagStr = op.get(key, VIPFLAG_KEY);
            if (StringUtils.isNotEmpty(vipflagStr)) {
                try {
                    return Integer.parseInt(vipflagStr);
                } catch (NumberFormatException e) {
                    LOGGER.logError(sellerNick, "", "redis中获取vipflag失败, vipflag不是数字: " + vipflagStr);
                }
            } else {
                LOGGER.logError(sellerNick, "", "redis中获取vipflag失败, 缺少数据");
            }
        }
        return null;
    }

    /**
     * 从Redis获取sellerId
     *
     * @param sellerNick
     * @return
     */
    public String getSellerId(String sellerNick) {
        String key = initKey(sellerNick);

        if (!StringUtils.isEmpty(key)) {
            HashOperations<String, String, String> op = stringRedisTemplate.opsForHash();
            String value = op.get(key, SELLER_ID_KEY);
            if (StringUtils.isEmpty(value)) {
                LOGGER.logError(sellerNick, "", "redis中获取sellerId失败, 缺少数据");
            } else {
                return value;
            }
        }
        return null;
    }
}
