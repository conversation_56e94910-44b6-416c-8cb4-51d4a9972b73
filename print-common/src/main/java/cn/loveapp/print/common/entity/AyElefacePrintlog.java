package cn.loveapp.print.common.entity;

import java.time.LocalDateTime;

import cn.loveapp.print.common.dto.RecipientDTO;
import lombok.Data;

/**
 * 电子面单打印日志
 *
 * <AUTHOR>
 */
@Data
public class AyElefacePrintlog {
    /**
     * 主键自增
     */
    private Long id;

    /**
     * 插件名称
     */
    private String appName;

    /**
     * 商家Id
     */
    private String sellerId;

    /**
     * 平台 TAO、PDD、JD、1688
     */
    private String storeId;

    /**
     * 商家Nick
     */
    private String sellerNick;

    /**
     * 打印机
     */
    private String printer;

    /**
     * 打印数量
     */
    private Integer printCount;

    /**
     * 操作用户
     */
    private String operator;

    /**
     * 操作用户平台 TAO、PDD、JD、1688
     */
    private String operatorStoreId;

    /**
     * 操作终端
     */
    private String operateTerminal;

    /**
     * 打印时间
     */
    private LocalDateTime printTime;

    /**
     * 面单服务商
     */
    private String provider;

    /**
     * 物流公司Code
     */
    private String cpCode;

    /**
     * 物流公司
     */
    private String logisticsCompany;

    /**
     * 面单号，字母件模式下为母面单号
     */
    private String waybillCode;

    /**
     * 是否为字母件
     */
    private Boolean isChild;

    /**
     * 字母件中的子面单号
     */
    private String childWaybillCode;

    /**
     * 收件人手机号
     */
    private String receiverMobile;

    /**
     * 收件人姓名
     */
    private String receiverName;

    /**
     * 收件人固话
     */
    private String receiverPhone;

    /**
     * 收件人城市
     */
    private String receiverCity;

    /**
     * 收件人详细地址
     */
    private String receiverDetail;

    /**
     * 收件人区
     */
    private String receiverDistrict;

    /**
     * 收件人省
     */
    private String receiverProvince;

    /**
     * 收件人街道
     */
    private String receiverTown;

    /**
     * 收件人邮编
     */
    private String receiverZip;

    /**
     * 面单打印类容
     */
    private String printData;

    /**
     * 面单自定义区域打印内容
     */
    private String customData;

    /**
     * 常规-记录创建时间
     */
    private LocalDateTime gmtCreate;

    /**
     * 常规-记录最后修改时间
     */
    private LocalDateTime gmtModified;

    /**
     * 子品牌
     */
    private String brandCode;

    /**
     * 交易（TAO） 扩展字段
     */
    private String externalInfo;

    /**
     * 真实快递公司code
     */
    private String realCpCode;

    /**
     * 电子面单版本号，1-默认值旧版电子面单 2-新版电子面单 (XHS使用)
     */
    private Integer billVersion;

    /**
     * @param recipientDTO
     */
    public void saveRecipient(RecipientDTO recipientDTO) {
        if (recipientDTO == null) {
            return;
        }

        this.receiverName = recipientDTO.getName();
        this.receiverCity = recipientDTO.getCity();
        this.receiverDetail = recipientDTO.getDetailAddress();
        this.receiverDistrict = recipientDTO.getDistrict();
        this.receiverMobile = recipientDTO.getMobile();
        this.receiverPhone = recipientDTO.getPhone();
        this.receiverProvince = recipientDTO.getProvince();
        this.receiverTown = recipientDTO.getTown();
        this.receiverZip = recipientDTO.getZipCode();
    }
}
