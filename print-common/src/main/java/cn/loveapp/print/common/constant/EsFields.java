package cn.loveapp.print.common.constant;

/**
 * <AUTHOR>
 * @date 2024-01-21 15:00
 * @description: es字段常量
 */
public class EsFields {

    public static final String id = "id";
    public static final String gmtModified = "gmtModified";
    public static final String gmtCreate = "gmtCreate";
    /**
     * 删除表示
     */
    public static final String isDeleted = "isDeleted";

    /**
     * 爱用用户id
     */
    public static final String ayUserId = "ayUserId";

    /**
     * 爱用用户id
     */
    public static final String sellerId = "sellerId";

    /**
     * 打印时间
     */
    public static final String printTime = "printTime";

    /**
     * 爱用打印日志mysql存储id
     */
    public static final String ayPrintLogId = "ayPrintLogId";

    /**
     * 合单子单ID列表
     */
    public static final String mergeSubPrintLogId = "mergeSubPrintLogId";

    /**
     * 打印类型
     */
    public static final String printType = "printType";

    /**
     * 订单号
     */
    public static final String tid = "tid";

    /**
     * 订单号
     */
    public static final String mergeSubTidList = "mergeSubTidList";

    /**
     * 订单类型
     */
    public static final String tradeType = "tradeType";

    /**
     * 收件人姓名
     */
    public static final String receiverName = "receiverName";

    /**
     * 收件人姓名(加密)
     */
    public static final String encryptReceiverName = "encryptReceiverName";

    /**
     * 收件人地址
     */
    public static final String receiverAddress = "receiverAddress";

    /**
     * 收件人手机号
     */
    public static final String receiverMobile = "receiverMobile";

    /**
     * 收件人固定电话
     */
    public static final String receiverPhone = "receiverPhone";

    /**
     * 收件人所在省份
     */
    public static final String receiverProvince = "receiverProvince";

    /**
     * 收件人所在城市
     */
    public static final String receiverCity = "receiverCity";

    /**
     * 是否重复打印
     */
    public static final String isRepetitionPrint = "isRepetitionPrint";

    /**
     * 是否取消电子面单
     */
    public static final String isCancel = "isCancel";

    /**
     * 物流公司名称
     */
    public static final String logisticsCompany = "logisticsCompany";

    /**
     * 物流公司code
     */
    public static final String logisticsCompanyCode = "logisticsCompanyCode";

    /**
     * 面单服务商
     */
    public static final String elefaceProvider = "elefaceProvider";

    /**
     * 电子面单号
     */
    public static final String waybillNumber = "waybillNumber";

    /**
     * 操作用户
     */
    public static final String operatorName = "operatorName";
    public static final String operatorNameKeyWord = "operatorName.keyword";

    /**
     * 打印机
     */
    public static final String printerNumber = "printerNumber";

    /**
     * 操作用户平台
     */
    public static final String operatorStoreId = "operatorStoreId";

    /**
     * 分销商爱用id
     */
    public static final String ayDistributeAyUserId = "ayDistribute.distributeAyUserId";

    /**
     * 批次号
     */
    public static final String batchId = "batchId";

    /**
     * 当前批次序号
     */
    public static final String numbersInBatch = "numbersInBatch";

    /**
     * 发货人姓名
     */
    public static final String senderName = "senderName";

    /**
     * 发货人电话
     */
    public static final String senderPhone = "senderPhone";

    /**
     * 发货人手机号
     */
    public static final String senderMobile = "senderMobile";

    /**
     * 发货人地址信息
     */
    public static final String senderAddress = "senderAddress";



    /*------面单分享日志字段 start----------------*/

    /**
     * 面单获取时间
     */
    public static final String elefaceOperateLogElefaceGetTime = "elefaceOperateLog.elefaceGetTime";

    /**
     * 面单分享id
     */
    public static final String shareId = "shareId";

    /**
     * 面单身份（分享者，使用者）
     */
    public static final String elefaceOperateLogElefaceLogUserRole = "elefaceOperateLog.elefaceLogUserRole";

    /**
     * 面单分享备注
     */
    public static final String elefaceOperateLogShareMemo = "elefaceOperateLog.shareMemo";

    /**
     * 面单分享备注
     */
    public static final String elefaceOperateLogShareMemoKeyWord = "elefaceOperateLog.shareMemo.keyword";

    /**
     * 打印订单用户id
     */
    public static final String elefaceOperateOrderSellerId = "elefaceOperateLog.orderSellerId";

    /**
     * 面单账号id
     */
    public static final String elefaceOperateOwnerSellerId = "elefaceOperateLog.ownerSellerId";

    /**
     * 面单被分享者id
     */
    public static final String elefaceOperateTargetSellerId = "elefaceOperateLog.targetSellerId";

    /**
     * 面单分享业务员
     */
    public static final String elefaceOperateLogSalesman = "elefaceOperateLog.salesman";

    /**
     * 面单分享业务员
     */
    public static final String elefaceOperateLogSalesmanKeyWord = "elefaceOperateLog.salesman.keyword";

    /**
     * 面单网点名称
     */
    public static final String elefaceOperateLogBranchName = "elefaceOperateLog.branchName";

    /**
     * 面单模板名称
     */
    public static final String elefaceOperateLogElefaceTemplateName = "elefaceOperateLog.elefaceTemplateName";

    /**
     * 是否打印
     */
    public static final String elefaceOperateLogIsPrint = "elefaceOperateLog.isPrint";

    /**
     * 是否发货
     */
    public static final String elefaceOperateLogIsSendGood = "elefaceOperateLog.isSendGood";

    /*------面单分享日志字段 end----------------*/

}
