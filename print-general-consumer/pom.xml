<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>cn.loveapp.print</groupId>
        <artifactId>print-services-group</artifactId>
        <version>1.0-SNAPSHOT</version>
        <relativePath>../pom.xml</relativePath>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>print-general-consumer</artifactId>
    <name>爱用-print-general-consumer</name>
    <description>打印服务-通用消息消费</description>
    <version>1.0-SNAPSHOT</version>

    <dependencies>

        <dependency>
            <groupId>cn.loveapp.print</groupId>
            <artifactId>print-api</artifactId>
            <version>${print-api.version}</version>
        </dependency>
        <dependency>
            <groupId>cn.loveapp.print</groupId>
            <artifactId>print-common</artifactId>
            <version>1.0-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>cn.loveapp.common</groupId>
            <artifactId>common-spring-boot-web-starter</artifactId>
        </dependency>
        <!--测试-->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
            </plugin>
        </plugins>
    </build>


</project>
