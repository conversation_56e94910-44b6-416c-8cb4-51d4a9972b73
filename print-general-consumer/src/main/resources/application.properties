## \u5E94\u7528\u540D\u79F0
spring.application.name=${APPLICATION_NAME:print-general-consumer}

spring.profiles.active=dev

# \u662F\u5426\u5141\u8BB8apollo
loveapp.apollo.enabled=true
# apollo \u57FA\u7840\u914D\u7F6E
app.id=cn.loveapp.print
apollo.bootstrap.enabled = ${loveapp.apollo.enabled}
# \u516C\u5171namespace\u5FC5\u987B\u653E\u540E\u9762
apollo.bootstrap.namespaces=print-consumer,application,service-registry
env=${spring.profiles.active}

## ehcache\u914D\u7F6E\u6587\u4EF6
spring.cache.jcache.config=classpath:ehcache.xml
mybatis.configuration.log-impl=org.apache.ibatis.logging.stdout.StdOutImpl

# \u7AEF\u53E3
server.port=8080

loveapp.mybatis.print.mapper-package = cn.loveapp.print.common.dao.print
loveapp.mybatis.print.datasource = printDataSource

