package cn.loveapp.print.general.config;

import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

/**
 * 打印general配置类
 *
 * <AUTHOR>
 *
 * @date 2024-09-26 10:49
 * @Description:
 */
@Data
@Configuration
public class PrintGeneralConfig {

	/**
	 * es更新限制
	 */
	@Value("${print.general.log.es.change.limit:1000}")
	private Integer printLogEsChangeLimit;

	/**
	 * es更新检测频率，没N次检测是否中断更新
	 */
	@Value("${print.general.log.es.change.check.frequency:20}")
	private Integer printLogEsChangeCheckFrequency;

	/**
	 * 面单关系更新锁定超时时间（秒）
	 */
	@Value("${print.general.log.es.change.lock.timeout:10}")
	private Integer printLogEsChangeLockTimeout;
}
