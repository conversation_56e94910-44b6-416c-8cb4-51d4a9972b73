package cn.loveapp.print.general.config;

import cn.loveapp.common.utils.LoggerHelper;

import cn.loveapp.print.common.rocketmq.RocketMQCommonConfig;
import cn.loveapp.print.common.rocketmq.RocketMQGeneralConfig;
import cn.loveapp.print.common.utils.RocketMqQueueHelper;
import cn.loveapp.print.general.consumer.PrintGeneralConsumer;
import org.apache.rocketmq.client.consumer.DefaultMQPushConsumer;
import org.apache.rocketmq.client.consumer.rebalance.AllocateMessageQueueAveragelyByCircle;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.CommandLineRunner;
import org.springframework.context.ApplicationListener;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.event.ContextClosedEvent;
import org.springframework.core.Ordered;

/**
 * 打印通用消息队列消费配置类
 * <AUTHOR>
 * @date 2024/9/26 15:35
 */
@Configuration
public class PrintGeneralConsumerConfiguration {
	private static final LoggerHelper LOGGER = LoggerHelper.getLogger(PrintGeneralConsumerConfiguration.class);

	@Autowired
	private RocketMQCommonConfig mqCommonConfig;

	@Autowired
	private RocketMQGeneralConfig rocketMQGeneralConfig;

	private DefaultMQPushConsumer consumer = null;

	@Bean(destroyMethod = "", name = "generalConsumer")
	public DefaultMQPushConsumer generalConsumer() {
		//启动ONS消息队列
		try {
			consumer = new DefaultMQPushConsumer(rocketMQGeneralConfig.getConsumerId());
			consumer.setNamesrvAddr(mqCommonConfig.getNamesrvAddr());
			consumer.setConsumeThreadMax(rocketMQGeneralConfig.getMaxThreadNum());
			consumer.setConsumeThreadMin(rocketMQGeneralConfig.getMaxThreadNum());
			consumer.setAllocateMessageQueueStrategy(new AllocateMessageQueueAveragelyByCircle());
		} catch (Exception e) {
			LOGGER.logError("create printGeneralConsumer failed", e);
		}
		return consumer;
	}

	@Bean(name = "generalConsumerLifeCycleManager")
	public OnsLifeCycleManager onsLifeCycleManager() {
		return new OnsLifeCycleManager();
	}

	/**
	 * Ons 生命周期管理
	 *
	 * <AUTHOR>
	 * @date 2021/3/31
	 */
	public static class OnsLifeCycleManager implements CommandLineRunner, ApplicationListener<ContextClosedEvent>,
			Ordered {
		private static final LoggerHelper LOGGER = LoggerHelper.getLogger(OnsLifeCycleManager.class);

		@Autowired(required = false)
		@Qualifier("generalConsumer")
		private DefaultMQPushConsumer generalConsumer;

		@Autowired(required = false)
		private PrintGeneralConsumer printGeneralConsumer;

		@Autowired
		private RocketMQGeneralConfig rocketMQGeneralConfig;

		@Autowired
		private RocketMqQueueHelper rocketMqQueueHelper;

		@Override
		public void run(String... args) throws Exception {
			//启动订单ONS消费者
			if (generalConsumer != null) {
				generalConsumer
						.subscribe(rocketMQGeneralConfig.getTopic(), rocketMQGeneralConfig.getTag());
				generalConsumer.registerMessageListener(printGeneralConsumer);
				LOGGER.logInfo("ordersgeneralConsumer is startting");
				generalConsumer.start();
				LOGGER.logInfo(
						"ordersgeneralConsumer is started, Topic=" + rocketMQGeneralConfig.getTopic()
								+ " Consumerid=" + rocketMQGeneralConfig.getConsumerId() + " Tag="
								+ rocketMQGeneralConfig.getTag());
			}
		}

		@Override
		public void onApplicationEvent(ContextClosedEvent event) {
			if (event.getApplicationContext() != null && event.getApplicationContext().getParent() != null) {
				return;
			}
			if (generalConsumer != null) {
				LOGGER.logInfo("正在关闭消息通用消费 ...");
				try {
					printGeneralConsumer.stop();
					rocketMqQueueHelper.stopOnsConsumer(generalConsumer);
				} catch (Exception e) {
					LOGGER.logError(e.getMessage(), e);
				}
				LOGGER.logInfo("消息通用消费 已关闭");
			}
		}


		@Override
		public int getOrder() {
			return Ordered.HIGHEST_PRECEDENCE;
		}
	}
}
