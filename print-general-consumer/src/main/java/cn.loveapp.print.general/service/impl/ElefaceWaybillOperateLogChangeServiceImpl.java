package cn.loveapp.print.general.service.impl;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import cn.loveapp.print.common.constant.EsFields;
import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import cn.loveapp.common.utils.LoggerHelper;
import cn.loveapp.print.api.proto.WaybillOperateLogStatusChangeRequestProto;
import cn.loveapp.print.common.dao.es.CommonAyPrintLogSearchESDao;
import cn.loveapp.print.common.dao.newprint.AyElefaceOperatelogDao;
import cn.loveapp.print.common.entity.AyElefaceOperatelog;
import cn.loveapp.print.common.entity.AyPrintLogSearchEs;
import cn.loveapp.print.general.service.ElefaceWaybillOperateLogChangeService;

/**
 * <AUTHOR>
 * @date 2025-03-03 10:29
 * @description: 面单操作日志变更服务实现类
 */
@Service
public class ElefaceWaybillOperateLogChangeServiceImpl implements ElefaceWaybillOperateLogChangeService {

    private static final LoggerHelper LOGGER = LoggerHelper.getLogger(ElefaceWaybillOperateLogChangeServiceImpl.class);

    @Autowired
    private AyElefaceOperatelogDao ayElefaceOperatelogDao;
    @Autowired
    private CommonAyPrintLogSearchESDao commonAyPrintLogSearchESDao;

    @Override
    public void waybillOperateLogChangeProcess(WaybillOperateLogStatusChangeRequestProto proto) {
        if (proto == null) {
            return;
        }

        String storeId = proto.getStoreId();
        String appName = proto.getAppName();
        String sellerNick = proto.getSellerNick();
        String sellerId = proto.getSellerId();
        Boolean isPrint = proto.getIsPrint();
        Boolean isSendGood = proto.getIsSendGood();
        String cpCode = proto.getCpCode();
        String waybillCode = proto.getWaybillCode();
        if (StringUtils.isAnyEmpty(sellerId, waybillCode)) {
            LOGGER.logError("操作日志更新打印或发货状态, 参数异常, 跳过");
            return;
        }
        List<AyElefaceOperatelog> dbAyElefaceOperatelogs =
            ayElefaceOperatelogDao.queryByWaybillCode(cpCode, waybillCode, storeId, sellerId, appName);
        List<AyElefaceOperatelog> needUpdateOperatelogList = null;
        if (BooleanUtils.isTrue(isPrint)) {
            needUpdateOperatelogList = dbAyElefaceOperatelogs.stream().filter(s -> !BooleanUtils.isTrue(s.getIsPrint()))
                .peek(s -> s.setIsPrint(Boolean.TRUE)).collect(Collectors.toList());
        } else {
            needUpdateOperatelogList =
                dbAyElefaceOperatelogs.stream().filter(s -> !BooleanUtils.isTrue(s.getIsSendGood()))
                    .peek(s -> s.setIsSendGood(Boolean.TRUE)).collect(Collectors.toList());
        }
        if (CollectionUtils.isNotEmpty(needUpdateOperatelogList)) {
            Map<String, List<AyElefaceOperatelog>> sellerIdAndAyElefaceOperatelogListMap =
                needUpdateOperatelogList.stream().collect(Collectors.groupingBy(AyElefaceOperatelog::getSellerId));
            for (String dbSellerId : sellerIdAndAyElefaceOperatelogListMap.keySet()) {
                List<AyElefaceOperatelog> ayElefaceOperatelogs = sellerIdAndAyElefaceOperatelogListMap.get(dbSellerId);
                List<Long> ids =
                    ayElefaceOperatelogs.stream().map(AyElefaceOperatelog::getId).collect(Collectors.toList());

                // 更新mysql
                ayElefaceOperatelogDao.updatePrintAndSendGoodStatusByIds(sellerId, ids, isPrint, isSendGood);
                // 更新es
                List<AyPrintLogSearchEs> logSearchEs = AyPrintLogSearchEs.of(needUpdateOperatelogList);
                if (CollectionUtils.isNotEmpty(logSearchEs)) {
                    List<String> updateFields = Lists.newArrayList();
                    if (BooleanUtils.isTrue(isPrint)) {
                        updateFields.add(EsFields.elefaceOperateLogIsPrint);
                    } else {
                        updateFields.add(EsFields.elefaceOperateLogIsSendGood);
                    }

                    commonAyPrintLogSearchESDao.batchUpdateByIdsWithNotNull(logSearchEs, updateFields);
                }
            }
        }
    }
}
