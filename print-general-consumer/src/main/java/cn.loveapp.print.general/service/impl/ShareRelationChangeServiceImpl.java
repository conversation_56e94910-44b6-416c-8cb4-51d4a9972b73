package cn.loveapp.print.general.service.impl;

import cn.loveapp.common.utils.LoggerHelper;
import cn.loveapp.print.api.proto.ShareRelationChangeRequestProto;
import cn.loveapp.print.common.dao.es.CommonAyPrintLogSearchESDao;
import cn.loveapp.print.common.dao.print.ElefaceSharingRelationEntityDao;
import cn.loveapp.print.common.dao.redis.ElefaceSharingChangeLockRedisDao;
import cn.loveapp.print.common.entity.ElefaceSharingRelationEntity;
import cn.loveapp.print.common.exception.LockElefaceSharingRelationException;
import cn.loveapp.print.common.exception.ResendMessageException;
import cn.loveapp.print.common.rocketmq.RocketMQGeneralConfig;
import cn.loveapp.print.general.config.PrintGeneralConfig;
import cn.loveapp.print.general.service.ShareRelationChangeService;
import com.google.common.util.concurrent.RateLimiter;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
/**
 * <AUTHOR>
 * @Date 2024/9/27 12:09 PM
 */
@Service
public class ShareRelationChangeServiceImpl implements ShareRelationChangeService {

    private static final LoggerHelper LOGGER = LoggerHelper.getLogger(ShareRelationChangeServiceImpl.class);

    private final RateLimiter rateLimiter = RateLimiter.create(Integer.MAX_VALUE);

    @Resource
    private ElefaceSharingRelationEntityDao elefaceSharingRelationEntityDao;

    @Resource
    private ElefaceSharingChangeLockRedisDao lockRedisDao;

    @Resource
    private CommonAyPrintLogSearchESDao commonAyPrintLogSearchESDao;

    @Autowired
    private PrintGeneralConfig printGeneralConfig;

    @Autowired
    private RocketMQGeneralConfig rocketMQGeneralConfig;

    /**
     * 迁移限流
     */
    @Value("${print.update.es.rate.limit:10}")
    private void listenLimit(double limit) {
        rateLimiter.setRate(limit);
    }

    @Override
    public void shareRelationChangeProcess(ShareRelationChangeRequestProto proto) {

        if (proto == null) {
            return;
        }

        String shareId = proto.getShareId();
        String newShareMemo = proto.getNewShareMemo();
        String newSalesman = proto.getNewSalesman();

        if (StringUtils.isEmpty(shareId)) {
            LOGGER.logError("分享id为空，跳过");
            return;
        }


        ElefaceSharingRelationEntity sharingRelation = elefaceSharingRelationEntityDao.queryByShareId(shareId);

        if (sharingRelation == null) {
            LOGGER.logError("分享关系不存在，跳过， shareId:" + shareId);
            return;
        }

        boolean needUpdate = false;

        // 当前消息更新的信息，与库里对比一致，表示为最新的消息
        if (newShareMemo != null && newShareMemo.equals(sharingRelation.getShareMemo())) {
            needUpdate = true;
        }
        if (newSalesman != null && newSalesman.equals(sharingRelation.getSalesman())) {
            needUpdate = true;
        }

        if (!needUpdate) {
            LOGGER.logError("非最新消息， 跳过") ;
            return;
        }

        // 开始更新
        startChangeProcess(sharingRelation);
    }

    private void startChangeProcess(ElefaceSharingRelationEntity sharingRelation) {
        String shareId = sharingRelation.getShareId();
        String proxySellerId = sharingRelation.getProxySellerId();
        String shareMemo = sharingRelation.getShareMemo();
        String salesman = sharingRelation.getSalesman();
        if (proxySellerId == null) {
            return;
        }

        // 设置向量 shareId + 时间戳
        long timestampInMillis = System.currentTimeMillis();
        if (!lockRedisDao.getOrSetTimeValue(shareId, timestampInMillis)) {
            return;
        }

        String lockValue = null;
        int whileCount = 0;
        try {
            lockValue = lockRedisDao.lockSharingRelation(shareId, printGeneralConfig.getPrintLogEsChangeLockTimeout());
            while (commonAyPrintLogSearchESDao.updateShareMemoAndSalesman(proxySellerId, shareId, shareMemo, salesman, printGeneralConfig.getPrintLogEsChangeLimit())) {
                lockRedisDao.continueLockSharingRelation(shareId, printGeneralConfig.getPrintLogEsChangeLockTimeout());
                whileCount++;
                // 每执行N次，校验是否需要中断
                if (whileCount % printGeneralConfig.getPrintLogEsChangeCheckFrequency() == 0 && !lockRedisDao.getOrSetTimeValue(shareId, timestampInMillis)) {
                    break;
                }
                // 更新限流
                rateLimiter.acquire();
            }
            LOGGER.logInfo(proxySellerId, "", "更新完成：shareMemo:" + shareMemo + ", salesman:" + salesman);
        } catch (LockElefaceSharingRelationException e) {
            throw new ResendMessageException("获取更新面单锁失败", rocketMQGeneralConfig.getDelayTimeLevel());
        } finally {
            lockRedisDao.unlockSharingRelation(shareId, lockValue);
        }
    }



}
