package cn.loveapp.print.general;

import cn.loveapp.common.utils.LoggerHelper;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.builder.SpringApplicationBuilder;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.util.StringUtils;

/**
 * <AUTHOR>
 */
@EnableScheduling
@EnableCaching
@EnableFeignClients(basePackages = {"cn.loveapp.uac"})
@SpringBootApplication(scanBasePackages = {"cn.loveapp.print.general", "cn.loveapp.print.common"})
public class PrintGeneralConsumerApplication {
	private static final LoggerHelper LOGGER = LoggerHelper.getLogger(PrintGeneralConsumerApplication.class);
	private static final String APOLLO_ENV = "env";

	/**
	 * Description 程序主入口
	 *
	 * <AUTHOR>
	 * @date 2018-09-21 23:37
	 */
	public static void main(String[] args) {
		new SpringApplicationBuilder(PrintGeneralConsumerApplication.class)
			.initializers((ConfigurableApplicationContext applicationContext) -> {
				//初始化apollo的env配置
				if (StringUtils.isEmpty(System.getProperty(APOLLO_ENV))) {
					String env = applicationContext.getEnvironment().getProperty(APOLLO_ENV);
					if (!StringUtils.isEmpty(env)) {
						System.setProperty(APOLLO_ENV, env);
					}
				}
		}).application().run(args);
	}

}
