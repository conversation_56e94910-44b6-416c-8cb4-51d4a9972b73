package cn.loveapp.print.general.consumer;

import cn.loveapp.common.serialization.MessageDeserializationResult;
import cn.loveapp.common.utils.LoggerHelper;
import cn.loveapp.print.api.proto.ShareRelationChangeRequestProto;
import cn.loveapp.print.api.proto.WaybillOperateLogStatusChangeRequestProto;
import cn.loveapp.print.common.constant.PrintGeneralConsumerConstant;
import cn.loveapp.print.common.consumer.BaseOnsConsumer;
import cn.loveapp.print.general.service.ElefaceWaybillOperateLogChangeService;
import cn.loveapp.print.general.service.ShareRelationChangeService;
import io.micrometer.core.instrument.MeterRegistry;
import org.apache.rocketmq.client.consumer.listener.ConsumeConcurrentlyStatus;
import org.apache.rocketmq.common.message.MessageExt;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Component;


/**
 * 打印通用消息队列消费
 *
 * <AUTHOR>
 * @date 2024/9/29 15:31
 */
@Component
public class PrintGeneralConsumer extends BaseOnsConsumer {

    private static LoggerHelper LOGGER = LoggerHelper.getLogger(PrintGeneralConsumer.class);

    //通用消息消费限流
    public static final String ROCKETMQ_PRINT_RATELIMIT_GENERAL = "print.general.ratelimit";

    @Autowired
    private ShareRelationChangeService shareRelationChangeService;

    @Autowired
    private ElefaceWaybillOperateLogChangeService elefaceWaybillOperateLogChangeService;

    /**
     * 业务处理：真正处理的数据类
     *
     * @param message
     * @param messageDeserializationResult
     * @throws Exception
     */
    @Override
    protected ConsumeConcurrentlyStatus execute(MessageExt message, MessageDeserializationResult messageDeserializationResult) throws Exception {
        try {
            PrintGeneralConsumerConstant printGeneralConsumerConstant = Enum.valueOf(PrintGeneralConsumerConstant.class, message.getTags());
            switch (printGeneralConsumerConstant) {
                case SHARE_OPERATE_UPDATE:
                    ShareRelationChangeRequestProto proto = (ShareRelationChangeRequestProto) messageDeserializationResult.getContent();
                    shareRelationChangeService.shareRelationChangeProcess(proto);
                    break;
                case WAYBILL_OPERATELOG_STATUS_CHANGE:
                    // 面单取号日志状态更新（是否发货、是否打印）
                    WaybillOperateLogStatusChangeRequestProto waybillOperateLogStatusChangeRequestProto =
                        (WaybillOperateLogStatusChangeRequestProto)messageDeserializationResult.getContent();
                    elefaceWaybillOperateLogChangeService
                        .waybillOperateLogChangeProcess(waybillOperateLogStatusChangeRequestProto);
                    break;
                default:
            }
        } catch (IllegalArgumentException e) {
            LOGGER.logError("消费到了未知的消息类型：" + message.getTags());
        }
        return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
    }


    public PrintGeneralConsumer(MeterRegistry registry, Environment environment) {
        super(registry, "打印通用消息队列接收.QPS", ROCKETMQ_PRINT_RATELIMIT_GENERAL,
                environment, true, true);
    }

    @Override
    protected Class<?> getClassForDeserialization(MessageExt message) {
        PrintGeneralConsumerConstant orderGeneralConsumerConstant = Enum.valueOf(PrintGeneralConsumerConstant.class, message.getTags());
        switch (orderGeneralConsumerConstant) {
            case SHARE_OPERATE_UPDATE:
                return ShareRelationChangeRequestProto.class;
            case WAYBILL_OPERATELOG_STATUS_CHANGE:
                return WaybillOperateLogStatusChangeRequestProto.class;
            default:
        }
        return null;
    }


}
