 mvn clean package  -Dmaven.test.skip=true -f ../../../../../pom.xml

rm -f ./aiyong-service.jar

CP ../../../../../print-service/target/print-service-1.0-SNAPSHOT.jar ./aiyong-service.jar

docker build -t registryzjk.aiyongtech.com/print-service:master .

docker push registryzjk.aiyongtech.com/print-service:master


#docker pull registryzjk.aiyongtech.com/print-service:master
#docker tag registryzjk.aiyongtech.com/print-service:master registry-vpc.cn-zhangjiakou.aliyuncs.com/cn_loveapp_outer/print-service-biyao:master
#docker push registry-vpc.cn-zhangjiakou.aliyuncs.com/cn_loveapp_outer/print-service-biyao:master
#docker rmi --force registryzjk.aiyongtech.com/print-service:master
#docker rmi --force  registry-vpc.cn-zhangjiakou.aliyuncs.com/cn_loveapp_outer/print-service-biyao:master
