FROM registryzjk.aiyongtech.com/javaservice:base

RUN mkdir -p /data/srv/orders-service && mkdir -p /tmp

WORKDIR /tmp

COPY aiyong-service.jar /data/srv/orders-service/aiyong-service.jar

CMD ["sh","-c","crond && java -Xmx3G -Dapollo.bootstrap.namespaces=print-service,application,service-registry -Dspring.application.name=print-service -Djava.library.path=/usr/local/apr/lib:/usr/jav/packages/lib/amd64:/usr/lib64:/lib64:/lib:/usr/lib -XX:+UnlockExperimentalVMOptions -XX:+UseCGroupMemoryLimitForHeap -XX:+UseG1GC -Dspring.profiles.active=prod -Dfile.encoding='UTF-8' -jar /data/srv/orders-service/aiyong-service.jar"]
